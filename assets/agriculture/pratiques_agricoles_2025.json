{"title": "Pratiques Agricoles - Guide Pratique 2025", "description": "Cas pratiques et scénarios réels pour la comptabilité et fiscalité agricole marocaine", "date_mise_a_jour": "2025-01-01", "reference": "Code Général de Normalisation Comptable - Secteur Agricole", "cas_pratiques": {"exploitation_cerealiere": {"titre": "Exploitation Céréalière - Cycle Complet", "contexte": {"description": "Exploitation de 50 hectares spécialisée en blé dur et orge", "regime_fiscal": "<PERSON><PERSON><PERSON> simplifié", "chiffre_affaires": "850 000 MAD", "periode": "Campagne 2024-2025"}, "scenario": {"problematique": "Comptabilisation d'un cycle complet de production céréalière avec gestion des stocks et subventions", "operations": [{"date": "2024-09-15", "operation": "<PERSON><PERSON>t semences blé dur", "montant": 45000, "details": "1500 kg à 30 MAD/kg, TVA 20%"}, {"date": "2024-10-01", "operation": "Travaux de semis", "montant": 25000, "details": "Prestation externe, TVA 20%"}, {"date": "2024-11-15", "operation": "<PERSON><PERSON><PERSON>", "montant": 60000, "details": "<PERSON><PERSON><PERSON>, TVA 20%"}, {"date": "2025-03-01", "operation": "Traitement phytosanitaire", "montant": 18000, "details": "Herbicide et fongicide"}, {"date": "2025-06-20", "operation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "montant": 35000, "details": "Moissonnage-battage externe"}, {"date": "2025-07-01", "operation": "Vente récolte", "montant": 420000, "details": "140 tonnes à 3000 MAD/tonne"}]}, "solution": {"ecritures_comptables": [{"date": "2024-09-15", "libelle": "<PERSON><PERSON>t semences blé dur", "debit": [{"compte": "311", "intitule": "Semences et plants", "montant": 45000}, {"compte": "34552", "intitule": "TVA récupérable", "montant": 9000}], "credit": [{"compte": "4411", "intitule": "Fournisseurs", "montant": 54000}]}, {"date": "2024-10-01", "libelle": "Travaux de semis", "debit": [{"compte": "3311", "intitule": "Céréales en végétation", "montant": 25000}, {"compte": "34552", "intitule": "TVA récupérable", "montant": 5000}], "credit": [{"compte": "4411", "intitule": "Fournisseurs", "montant": 30000}]}, {"date": "2024-10-01", "libelle": "Consommation semences", "debit": [{"compte": "3311", "intitule": "Céréales en végétation", "montant": 45000}], "credit": [{"compte": "311", "intitule": "Semences et plants", "montant": 45000}]}, {"date": "2024-11-15", "libelle": "Achat et consommation engrais", "debit": [{"compte": "3311", "intitule": "Céréales en végétation", "montant": 60000}, {"compte": "34552", "intitule": "TVA récupérable", "montant": 12000}], "credit": [{"compte": "4411", "intitule": "Fournisseurs", "montant": 72000}]}, {"date": "2025-03-01", "libelle": "Traitement phytosanitaire", "debit": [{"compte": "3311", "intitule": "Céréales en végétation", "montant": 18000}, {"compte": "34552", "intitule": "TVA récupérable", "montant": 3600}], "credit": [{"compte": "4411", "intitule": "Fournisseurs", "montant": 21600}]}, {"date": "2025-06-20", "libelle": "Frais de récolte", "debit": [{"compte": "3311", "intitule": "Céréales en végétation", "montant": 35000}, {"compte": "34552", "intitule": "TVA récupérable", "montant": 7000}], "credit": [{"compte": "4411", "intitule": "Fournisseurs", "montant": 42000}]}, {"date": "2025-06-20", "libelle": "Transfert en produits finis", "debit": [{"compte": "3411", "intitule": "Céréales stockées", "montant": 183000}], "credit": [{"compte": "3311", "intitule": "Céréales en végétation", "montant": 183000}]}, {"date": "2025-07-01", "libelle": "Vente de blé dur", "debit": [{"compte": "4411", "intitule": "Clients", "montant": 420000}], "credit": [{"compte": "7121", "intitule": "Ventes produits agricoles", "montant": 420000}]}, {"date": "2025-07-01", "libelle": "Sortie de stock", "debit": [{"compte": "6124", "intitule": "Coût des ventes", "montant": 183000}], "credit": [{"compte": "3411", "intitule": "Céréales stockées", "montant": 183000}]}], "calculs": {"cout_production": {"semences": 45000, "travaux_semis": 25000, "engrais": 60000, "traitements": 18000, "recolte": 35000, "total": 183000}, "rendement": {"production_totale": "140 tonnes", "surface": "50 hectares", "rendement_hectare": "2.8 tonnes/ha", "cout_tonne": "1307 MAD/tonne"}, "resultat": {"chiffre_affaires": 420000, "cout_production": 183000, "marge_brute": 237000, "taux_marge": "56.4%"}}}, "points_attention": ["Capitalisation des charges en cours de production jusqu'à la récolte", "Transfert en produits finis au moment de la récolte", "Évaluation du stock au coût de production", "Récupération de la TVA sur les intrants agricoles", "Suivi du rendement pour contrôle de cohérence"], "implications_fiscales": {"ir": "Bénéfice imposable = 237 000 MAD (avant charges de structure)", "tva": "TVA récupérable = 36 600 MAD sur les achats", "is": "Non applicable (exploitation individuelle)"}}, "elevage_bovin": {"titre": "Élevage Bovin <PERSON> - Gestion du Cheptel", "contexte": {"description": "Exploitation laitière de 40 vaches avec renouvellement du cheptel", "regime_fiscal": "Réel normal", "production": "300 000 litres/an", "periode": "Exercice 2025"}, "scenario": {"problematique": "Comptabilisation des naissances, croissance du cheptel, et ventes d'animaux", "operations": [{"date": "2025-01-15", "operation": "Naissance de 3 veaux", "details": "Valorisation forfaitaire à 800 MAD/veau"}, {"date": "2025-02-28", "operation": "Achat aliments concentrés", "montant": 45000, "details": "Aliments pour 2 mois"}, {"date": "2025-03-31", "operation": "Charges d'élevage trimestrielles", "montant": 25000, "details": "Vétérinaire, insémination, divers"}, {"date": "2025-06-15", "operation": "Vente d'un taurillon", "montant": 8500, "details": "18 mois, poids 450 kg"}, {"date": "2025-12-31", "operation": "Inventaire cheptel", "details": "Réévaluation selon barème"}]}, "solution": {"ecritures_comptables": [{"date": "2025-01-15", "libelle": "Naissance de 3 veaux", "debit": [{"compte": "3321", "intitule": "<PERSON><PERSON><PERSON> bovins", "montant": 2400}], "credit": [{"compte": "7128", "intitule": "Production immobilisée", "montant": 2400}]}, {"date": "2025-02-28", "libelle": "Achat aliments concentrés", "debit": [{"compte": "314", "intitule": "Aliments pour animaux", "montant": 45000}, {"compte": "34552", "intitule": "TVA récupérable", "montant": 9000}], "credit": [{"compte": "4411", "intitule": "Fournisseurs", "montant": 54000}]}, {"date": "2025-02-28", "libelle": "Consommation aliments", "debit": [{"compte": "3321", "intitule": "<PERSON><PERSON><PERSON> bovins", "montant": 15000}, {"compte": "614", "intitule": "<PERSON><PERSON> cheptel reproducteur", "montant": 30000}], "credit": [{"compte": "314", "intitule": "Aliments pour animaux", "montant": 45000}]}, {"date": "2025-03-31", "libelle": "Charges d'élevage", "debit": [{"compte": "3321", "intitule": "<PERSON><PERSON><PERSON> bovins", "montant": 8000}, {"compte": "625", "intitule": "Frais vétérinaires", "montant": 17000}], "credit": [{"compte": "4411", "intitule": "Fournisseurs", "montant": 25000}]}, {"date": "2025-06-15", "libelle": "<PERSON><PERSON> taurillon", "debit": [{"compte": "4411", "intitule": "Clients", "montant": 8500}], "credit": [{"compte": "7122", "intitule": "Ventes animaux", "montant": 8500}]}, {"date": "2025-06-15", "libelle": "Sortie de stock taurillon", "debit": [{"compte": "6125", "intitule": "Coût des ventes animaux", "montant": 6200}], "credit": [{"compte": "3321", "intitule": "<PERSON><PERSON><PERSON> bovins", "montant": 6200}]}], "calculs": {"cout_elevage_veau": {"valeur_naissance": 800, "aliments_6_mois": 5000, "soins_veterinaires": 2667, "total_18_mois": 6200}, "resultat_vente": {"prix_vente": 8500, "cout_elevage": 6200, "plus_value": 2300, "taux_marge": "27%"}}}, "gestion_cheptel": {"valorisation_naissances": {"methode": "Valeur forfaitaire selon espèce et race", "veaux_laitiers": "800 MAD", "veaux_viande": "1000 MAD", "agneaux": "200 MAD"}, "suivi_croissance": {"principe": "Incorporation des coûts d'élevage", "elements": ["Aliments", "Soins vétérinaires", "Part charges indirectes"], "frequence": "<PERSON><PERSON><PERSON> ou trimestrielle"}, "amortissement_reproducteurs": {"duree": "5 à 8 ans selon espèce", "methode": "Linéaire", "valeur_residuelle": "Valeur de réforme"}}, "points_attention": ["Valorisation des naissances en production immobilisée", "Répartition des aliments entre reproducteurs et jeunes", "Suivi individuel ou par lot des animaux", "Contrôle de cohérence avec registre d'élevage", "Provision pour mortalité si taux anormal"]}, "subventions_investissement": {"titre": "Subventions d'Investissement - Matériel Agricole", "contexte": {"description": "Acquisition d'un tracteur avec subvention du Plan Maroc Vert", "montant_investissement": 450000, "taux_subvention": "40%", "duree_amortissement": "8 ans"}, "scenario": {"problematique": "Comptabilisation de l'investissement, de la subvention et de l'étalement", "operations": [{"date": "2025-03-01", "operation": "Commande tracteur", "montant": 450000, "details": "Acompte 30% versé"}, {"date": "2025-04-15", "operation": "<PERSON><PERSON><PERSON> tracteur", "montant": 315000, "details": "Solde à payer"}, {"date": "2025-05-30", "operation": "Notification subvention", "montant": 180000, "details": "40% du montant HT"}, {"date": "2025-12-31", "operation": "Amortissement annuel", "details": "Premi<PERSON> année (9 mois)"}]}, "solution": {"ecritures_comptables": [{"date": "2025-03-01", "libelle": "Acompte tracteur", "debit": [{"compte": "2387", "intitule": "Avances sur immobilisations", "montant": 135000}, {"compte": "34552", "intitule": "TVA récupérable", "montant": 27000}], "credit": [{"compte": "5141", "intitule": "Banque", "montant": 162000}]}, {"date": "2025-04-15", "libelle": "<PERSON><PERSON><PERSON> tracteur", "debit": [{"compte": "2341", "intitule": "Matériel agricole mobile", "montant": 450000}, {"compte": "34552", "intitule": "TVA récupérable", "montant": 63000}], "credit": [{"compte": "2387", "intitule": "Avances sur immobilisations", "montant": 135000}, {"compte": "4411", "intitule": "Fournisseurs", "montant": 378000}]}, {"date": "2025-05-30", "libelle": "Notification subvention", "debit": [{"compte": "3451", "intitule": "Subventions à recevoir", "montant": 180000}], "credit": [{"compte": "141", "intitule": "Subventions d'équipement agricole", "montant": 180000}]}, {"date": "2025-12-31", "libelle": "Amortissement tracteur (9 mois)", "debit": [{"compte": "6812", "intitule": "Amortissements matériel agricole", "montant": 42188}], "credit": [{"compte": "28341", "intitule": "Amort. matériel agricole", "montant": 42188}]}, {"date": "2025-12-31", "libelle": "Reprise subvention (9 mois)", "debit": [{"compte": "141", "intitule": "Subventions d'équipement agricole", "montant": 16875}], "credit": [{"compte": "7514", "intitule": "Reprises subventions investissement", "montant": 16875}]}], "calculs": {"amortissement": {"base": 450000, "duree": 8, "annuite": 56250, "neuf_mois": 42188}, "reprise_subvention": {"base": 180000, "duree": 8, "annuite": 22500, "neuf_mois": 16875}, "impact_resultat": {"charge_amortissement": -42188, "produit_subvention": 16875, "impact_net": -25313}}}, "points_attention": ["Subvention comptabilisée dès notification officielle", "Étalement sur même durée que l'amortissement", "TVA récupérable sur montant total HT", "Contrôle respect conditions d'attribution", "Remboursement si non-respect des engagements"]}}, "guides_procedures": {"comptabilisation_recoltes": {"titre": "Procédure de Comptabilisation des Récoltes", "etapes": [{"numero": 1, "titre": "Préparation de la récolte", "actions": ["Arr<PERSON><PERSON> les charges en cours de production", "Calculer le coût total de production", "Est<PERSON>r le rendement prévisionnel", "<PERSON><PERSON><PERSON>er les comptes de transfert"]}, {"numero": 2, "titre": "Constatation de la récolte", "actions": ["Peser et mesurer la production effective", "Constater les pertes éventuelles", "Transférer en produits finis au coût de production", "Ajuster si écart significatif avec prévisions"]}, {"numero": 3, "titre": "Évaluation du stock", "actions": ["Appliquer la règle du coût ou marché", "Constituer provisions si nécessaire", "Documenter les méthodes d'évaluation", "Contrôler la cohérence avec données physiques"]}], "ecritures_types": [{"operation": "Transfert en cours → produits finis", "debit": "341 - Produits agricoles récoltés", "credit": "331 - Cultures en cours", "montant": "Coût de production total"}, {"operation": "Constatation pertes récolte", "debit": "6524 - <PERSON><PERSON> sur stocks", "credit": "331 - Cultures en cours", "montant": "Coût des pertes"}]}, "gestion_tva_agricole": {"titre": "Gestion de la TVA en Agriculture", "regimes": [{"type": "Franchise", "conditions": "CA < 500 000 MAD", "avantages": ["Pas de TVA à facturer", "Simplicité administrative"], "inconvenients": ["Pas de récupération TVA", "Limitation développement"]}, {"type": "Régime normal", "conditions": "CA > 500 000 MAD ou option", "obligations": ["Facturation TVA", "Déclarations mensuelles", "Tenue registres"], "avantages": ["Récupération TVA intrants", "Crédibilité commerciale"]}], "taux_applicables": {"produits_base": {"taux": "0%", "produits": ["Céréales", "Légumineuses", "<PERSON><PERSON>", "Viande fraîche"]}, "produits_transformes": {"taux": "20%", "produits": ["Conserves", "Produits laitiers transformés", "<PERSON><PERSON>"]}, "services": {"taux": "20%", "services": ["Transport", "Stockage", "Transformation"]}}, "procedure_recuperation": [{"etape": "Vérification conditions", "details": "Assujettissement, utilisation professionnelle, facture conforme"}, {"etape": "Comptabilisation", "details": "Débit 34552 - TVA récupérable sur charges"}, {"etape": "Déclaration", "details": "Report sur déclaration mensuelle, déduction TVA due"}, {"etape": "Crédit de TVA", "details": "Demande remboursement si crédit > 5000 MAD"}]}, "inventaire_cheptel": {"titre": "Procédure d'Inventaire du Cheptel", "preparation": ["Mise à jour du registre d'élevage", "Préparation des fiches de comptage", "Organisation des équipes de comptage", "Définition des méthodes d'évaluation"], "comptage_physique": [{"categorie": "Reproducteurs", "methode": "Comptage individuel avec identification", "valorisation": "Valeur nette comptable"}, {"categorie": "<PERSON><PERSON><PERSON> animaux", "methode": "Comptage par lot d'âge", "valorisation": "Coût d'élevage cumulé"}, {"categorie": "Animaux de vente", "methode": "Pesée et estimation", "valorisation": "<PERSON><PERSON><PERSON> ou valeur de march<PERSON>"}], "ajustements": [{"type": "Mortalité normale", "traitement": "Sortie de stock en charges d'exploitation", "compte": "6524 - <PERSON><PERSON> sur stocks"}, {"type": "Mortalité exceptionnelle", "traitement": "Charges exceptionnelles avec provision", "compte": "6714 - <PERSON><PERSON>"}, {"type": "Croissance", "traitement": "Réévaluation selon barème", "compte": "Variation de stock"}]}}, "erreurs_frequentes": {"comptabilisation": [{"erreur": "Charges en cours non capitalisées", "description": "Comptabilisation directe en charges des coûts de production", "consequence": "Sous-évaluation des stocks et distorsion du résultat", "correction": "Capitaliser en compte 33 jusqu'à la récolte", "prevention": "Procédure de suivi des cycles de production"}, {"erreur": "Mauvaise répartition des charges", "description": "Affectation incorrecte entre cultures ou animaux", "consequence": "Coûts de production erronés", "correction": "Clés de répartition basées sur surfaces/effectifs", "prevention": "Comptabilité analytique par atelier"}, {"erreur": "Subventions mal comptabilisées", "description": "Confusion entre subventions d'exploitation et d'investissement", "consequence": "Impact fiscal incorrect", "correction": "Analyser la nature de chaque subvention", "prevention": "Formation sur typologie des aides"}], "evaluation": [{"erreur": "Stocks surévalués", "description": "Non-application de la règle coût/marché", "consequence": "Résultat artificiellement gonflé", "correction": "Provision pour dépréciation", "prevention": "Veille sur cours des marchés"}, {"erreur": "Amortissements incorrects", "description": "Durées inadaptées aux actifs biologiques", "consequence": "Charges mal réparties dans le temps", "correction": "Révision des durées d'amortissement", "prevention": "Barème spécifique agriculture"}], "fiscalite": [{"erreur": "TVA non récupérée", "description": "Oubli de déduction sur intrants agricoles", "consequence": "Surcoût fiscal", "correction": "Régularisation sur déclarations suivantes", "prevention": "Contrôle systématique des factures"}, {"erreur": "Régime fiscal inadapté", "description": "Maintien en forfait malgré dépassement seuils", "consequence": "Redressement fiscal", "correction": "Option pour régime réel", "prevention": "Suivi annuel du chiffre d'affaires"}]}, "considerations_saisonnieres": {"cycle_cultural": {"automne": {"operations": ["Semis céréales d'hiver", "Plantation arbres fruitiers"], "comptabilite": ["Ouverture comptes en-cours", "Capitalisation charges"], "fiscalite": ["Provisions pour aléas", "Planification investissements"]}, "hiver": {"operations": ["Entretien plantations", "Préparation sols"], "comptabilite": ["Suivi charges en cours", "Inventaire stocks"], "fiscalite": ["Déclarations annuelles", "Optimisation fiscale"]}, "printemps": {"operations": ["Semis cultures printemps", "Traitements"], "comptabilite": ["Poursuite capitalisation", "Gestion trésorerie"], "fiscalite": ["Acomptes IS/IR", "Déclarations TVA"]}, "ete": {"operations": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Commercialisation"], "comptabilite": ["Transfert produits finis", "Constatation ventes"], "fiscalite": ["Encaissement subventions", "Provisions résultat"]}}, "gestion_tresorerie": {"periode_charges": "Automne/Hiver - Besoins importants", "periode_recettes": "Été - Concentration des ventes", "outils": ["<PERSON><PERSON><PERSON>", "Avances sur récolte", "Lissage TVA"], "planification": "Budget prévisionnel par trimestre"}}, "petites_exploitations": {"seuils_simplification": {"comptabilite_simplifiee": "CA < 750 000 MAD", "franchise_tva": "CA < 500 000 MAD", "forfait_ir": "Selon barème par hectare/tête"}, "procedures_allégées": {"tenue_comptable": ["Livre journal simplifié", "Registre des immobilisations", "Suivi stocks par nature", "Rapprochements bancaires"], "pieces_justificatives": ["Factures fournisseurs", "Bordereaux de vente", "Relevés bancaires", "Déclarations subventions"]}, "cooperatives": {"avantages_fiscaux": ["Exonération IS sur opérations avec adhérents", "TVA réduite sur certains services", "Déductions spécifiques"], "obligations": ["Comptabilité séparée adhérents/tiers", "Répartition excédents", "Déclarations spécifiques"]}}, "references_croisees": {"comptabilite_fiscalite": [{"operation": "Amortissement matériel agricole", "comptable": "Durée économique réelle", "fiscal": "Durées minimales légales", "reconciliation": "Amortissements dérogatoires si nécessaire"}, {"operation": "Provisions pour aléas", "comptable": "Risques identifiés et quantifiables", "fiscal": "Déductibilité selon conditions strictes", "reconciliation": "Réintégration si non déductible"}, {"operation": "Subventions d'investissement", "comptable": "Étalement sur durée d'amortissement", "fiscal": "Imposition immédiate ou étalement", "reconciliation": "Option fiscale à exercer"}], "controles_coherence": ["Stocks comptables vs inventaires physiques", "Charges par hectare vs références techniques", "Rendements vs moyennes régionales", "TVA déductible vs nature des achats", "Subventions comptabilisées vs notifications"]}, "outils_gestion": {"tableaux_bord": [{"indicateur": "Coût de production par hectare", "calcul": "Total charges / Surface cultivée", "utilite": "Comparaison avec références"}, {"indicateur": "Marge brute par atelier", "calcul": "Produits - Charges directes", "utilite": "Rentabilité comparative"}, {"indicateur": "Rotation des stocks", "calcul": "Coût des ventes / Stock moyen", "utilite": "Efficacité commerciale"}], "documents_gestion": ["Plan de financement prévisionnel", "Budget de trésorerie mensuel", "Compte de résultat par atelier", "Tableau de bord mensuel", "Analyse des écarts"]}}