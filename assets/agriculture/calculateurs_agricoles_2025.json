{"title": "Calculateurs Agricoles 2025", "description": "Configuration des calculateurs interactifs pour la fiscalité et comptabilité agricoles au Maroc", "date_mise_a_jour": "2025-01-01", "version": "1.0", "calculateurs": [{"id": "ir_agricole", "nom": "Calculateur IR Agricole", "description": "Calcul de l'Impôt sur le Revenu pour les revenus agricoles avec barème progressif", "icone": "calculate", "couleur": "#4CAF50", "parametres_entree": [{"id": "revenus_bruts", "nom": "Revenus agricoles bruts", "type": "number", "unite": "MAD", "obligatoire": true, "min": 0, "max": 10000000, "aide": "Total des revenus agricoles avant déduction des charges"}, {"id": "charges_deductibles", "nom": "Charges déductibles", "type": "number", "unite": "MAD", "obligatoire": false, "min": 0, "valeur_defaut": 0, "aide": "Semences, engrais, carburants, frais vétérinaires, etc."}, {"id": "regime_evaluation", "nom": "Régime d'évaluation", "type": "select", "obligatoire": true, "options": [{"valeur": "reel", "libelle": "Régime du bénéfice réel"}, {"valeur": "forfaitaire", "libelle": "Régime forfaitaire"}, {"valeur": "moyenne_triennale", "libelle": "Moyenne triennale"}], "aide": "Méthode d'évaluation des revenus agricoles"}, {"id": "situation_familiale", "nom": "Situation familiale", "type": "select", "obligatoire": true, "options": [{"valeur": "celibataire", "libelle": "Célibataire"}, {"valeur": "marie", "libelle": "<PERSON><PERSON>(e)"}, {"valeur": "divorce", "libelle": "Di<PERSON><PERSON>(e)"}, {"valeur": "veuf", "libelle": "Veuf/Veuve"}]}, {"id": "nombre_enfants", "nom": "Nombre d'enfants à charge", "type": "number", "obligatoire": false, "min": 0, "max": 6, "valeur_defaut": 0, "aide": "Maximum 6 enfants pris en compte"}, {"id": "zone_montagne", "nom": "Exploitation en zone de montagne", "type": "boolean", "obligatoire": false, "valeur_defaut": false, "aide": "Altitude > 800m, abattement de 25% applicable"}, {"id": "jeune_agriculteur", "nom": "Statut jeune agriculteur", "type": "boolean", "obligatoire": false, "valeur_defaut": false, "aide": "Âge < 35 ans, première installation, exonération 5 ans"}, {"id": "agriculture_bio", "nom": "Agriculture biologique certifiée", "type": "boolean", "obligatoire": false, "valeur_defaut": false, "aide": "Certification bio, abattement 50% pendant 10 ans"}], "regles_validation": [{"condition": "revenus_bruts > 0", "message": "Les revenus bruts doivent être supérieurs à 0"}, {"condition": "charges_deductibles <= revenus_bruts", "message": "Les charges ne peuvent pas dépasser les revenus bruts"}, {"condition": "nombre_enfants <= 6", "message": "Maximum 6 enfants pris en compte pour l'abattement"}], "calcul": {"etapes": [{"id": "revenus_nets", "nom": "Calcul des revenus nets", "formule": "revenus_bruts - charges_deductibles", "description": "Revenus agricoles après déduction des charges"}, {"id": "abattements", "nom": "Application des abattements", "sous_etapes": [{"id": "abattement_familial", "nom": "Abattement familial", "formule": "30000 + (nombre_enfants * 5000)", "plafond": 60000}, {"id": "abattement_petite_exploitation", "nom": "Abattement petite exploitation", "condition": "revenus_nets < 120000", "montant": 24000}, {"id": "abattement_zone_montagne", "nom": "Abattement zone de montagne", "condition": "zone_montagne && revenus_nets < 100000", "formule": "revenus_nets * 0.25"}]}, {"id": "base_imposable", "nom": "Base imposable", "formule": "max(0, revenus_nets - total_abattements)"}, {"id": "ir_brut", "nom": "IR brut selon barème", "bareme": [{"limite": 30000, "taux": 0}, {"limite": 50000, "taux": 10}, {"limite": 60000, "taux": 20}, {"limite": 80000, "taux": 30}, {"limite": 180000, "taux": 34}, {"limite": null, "taux": 38}]}, {"id": "reductions", "nom": "Application des réductions", "sous_etapes": [{"id": "exoneration_jeune_agriculteur", "condition": "jeune_agriculteur", "reduction": 100, "duree": "5 ans"}, {"id": "reduction_agriculture_bio", "condition": "agriculture_bio", "reduction": 50, "duree": "10 ans"}]}, {"id": "ir_final", "nom": "IR final à payer", "formule": "ir_brut - reductions"}]}, "format_resultats": {"resume": [{"libelle": "Revenus bruts", "valeur": "revenus_bruts", "format": "currency"}, {"libelle": "Charges déductibles", "valeur": "charges_deductibles", "format": "currency"}, {"libelle": "Revenus nets", "valeur": "revenus_nets", "format": "currency"}, {"libelle": "Total abattements", "valeur": "total_abattements", "format": "currency"}, {"libelle": "Base imposable", "valeur": "base_imposable", "format": "currency"}, {"libelle": "IR à payer", "valeur": "ir_final", "format": "currency", "style": "highlight"}], "detail_bareme": true, "graphique_tranches": true}}, {"id": "is_agricole", "nom": "Calculateur IS Agricole", "description": "Calcul de l'Impôt sur les Sociétés pour entreprises agricoles avec régimes spéciaux", "icone": "business", "couleur": "#2196F3", "parametres_entree": [{"id": "resultat_fiscal", "nom": "Résultat fiscal", "type": "number", "unite": "MAD", "obligatoire": true, "aide": "Bénéfice imposable après retraitements fiscaux"}, {"id": "type_entreprise", "nom": "Type d'entreprise", "type": "select", "obligatoire": true, "options": [{"valeur": "cooperative", "libelle": "Coopérative agricole"}, {"valeur": "nouvelle_entreprise", "libelle": "Nouvelle entreprise agricole"}, {"valeur": "zone_speciale", "libelle": "Zone agricole spéciale"}, {"valeur": "investissement_strategique", "libelle": "Investissement stratégique"}, {"valeur": "droit_commun", "libelle": "Droit commun"}]}, {"id": "chiffre_affaires", "nom": "<PERSON><PERSON><PERSON> d'affaires", "type": "number", "unite": "MAD", "obligatoire": true, "min": 0, "aide": "CA hors taxes pour calcul cotisation minimale"}, {"id": "annee_creation", "nom": "Année de <PERSON>réation", "type": "number", "obligatoire": false, "min": 2020, "max": 2025, "aide": "Pour déterminer l'éligibilité aux exonérations"}, {"id": "investissement_initial", "nom": "Investissement initial", "type": "number", "unite": "MAD", "obligatoire": false, "min": 0, "aide": "Montant de l'investissement pour nouvelles entreprises"}, {"id": "emplois_crees", "nom": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "obligatoire": false, "min": 0, "aide": "Nombre d'emplois permanents créés"}, {"id": "zone_geographique", "nom": "Zone géographique", "type": "select", "obligatoire": false, "options": [{"valeur": "souss_massa", "libelle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"valeur": "oriental", "libelle": "Oriental"}, {"valeur": "beni_mellal", "libelle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"valeur": "draa_tafilalet", "libelle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"valeur": "<PERSON><PERSON><PERSON><PERSON>", "libelle": "Laâyoune-<PERSON><PERSON><PERSON>"}, {"valeur": "guelmim", "libelle": "Guelmi<PERSON><PERSON><PERSON><PERSON>"}, {"valeur": "dakhla", "libelle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"valeur": "autre", "libelle": "Autre région"}]}, {"id": "export_pourcentage", "nom": "Pourcentage export", "type": "number", "unite": "%", "obligatoire": false, "min": 0, "max": 100, "aide": "Part du CA à l'export pour investissements stratégiques"}], "regles_validation": [{"condition": "resultat_fiscal >= 0 || type_entreprise != 'nouvelle_entreprise'", "message": "Les nouvelles entreprises ne peuvent avoir de résultat négatif la première année"}, {"condition": "investissement_initial >= 1000000 || type_entreprise != 'nouvelle_entreprise'", "message": "Investissement minimum 1M MAD pour nouvelles entreprises"}, {"condition": "emplois_crees >= 10 || type_entreprise != 'nouvelle_entreprise'", "message": "Minimum 10 emplois pour nouvelles entreprises"}], "calcul": {"etapes": [{"id": "determination_taux", "nom": "Détermination du taux IS", "conditions": [{"type": "cooperative", "taux": 17.5, "description": "Taux préférentiel coopératives agricoles"}, {"type": "nouvelle_entreprise", "condition": "annees_activite <= 5", "taux": 0, "description": "Exonération 5 ans nouvelles entreprises"}, {"type": "nouvelle_entreprise", "condition": "annees_activite > 5 && annees_activite <= 10", "taux": 17.5, "description": "Taux réduit après exonération"}, {"type": "zone_speciale", "taux_variables": {"souss_massa": 15, "draa_tafilalet": 10, "guelmim": 12.5}}, {"type": "investissement_strategique", "condition": "phase_developpement", "taux": 0, "duree": 5}, {"type": "droit_commun", "taux": 31, "description": "Taux normal IS"}]}, {"id": "is_theorique", "nom": "IS théorique", "formule": "resultat_fiscal * taux_is / 100"}, {"id": "cotisation_minimale", "nom": "Cotisation minimale", "formule": "max(1500, chiffre_affaires * 0.0025)", "description": "Taux réduit 0,25% pour secteur agricole"}, {"id": "is_du", "nom": "IS dû", "formule": "max(is_theorique, cotisation_minimale)"}]}, "format_resultats": {"resume": [{"libelle": "Résultat fiscal", "valeur": "resultat_fiscal", "format": "currency"}, {"libelle": "Taux IS applicable", "valeur": "taux_is", "format": "percentage"}, {"libelle": "IS théorique", "valeur": "is_theorique", "format": "currency"}, {"libelle": "Cotisation minimale", "valeur": "cotisation_minimale", "format": "currency"}, {"libelle": "IS à payer", "valeur": "is_du", "format": "currency", "style": "highlight"}], "avantages_fiscaux": true, "comparaison_droit_commun": true}}, {"id": "tva_agricole", "nom": "Calculateur TVA Agricole", "description": "Calcul de la TVA pour produits et services agricoles selon les régimes spéciaux", "icone": "receipt", "couleur": "#FF9800", "parametres_entree": [{"id": "type_operation", "nom": "Type d'opération", "type": "select", "obligatoire": true, "options": [{"valeur": "vente_produits_bruts", "libelle": "Vente produits agricoles bruts"}, {"valeur": "services_agricoles", "libelle": "Services agricoles"}, {"valeur": "cooperative", "libelle": "Opérations coopérative"}, {"valeur": "intrants", "libelle": "Achat intrants agricoles"}, {"valeur": "equipements", "libelle": "Achat équipements agricoles"}, {"valeur": "export", "libelle": "Exportation"}]}, {"id": "montant_ht", "nom": "Montant HT", "type": "number", "unite": "MAD", "obligatoire": true, "min": 0, "aide": "Montant hors taxes de l'opération"}, {"id": "produit_specifique", "nom": "Produit spécifique", "type": "select", "obligatoire": false, "options": [{"valeur": "cereales", "libelle": "Céréales", "taux": 0}, {"valeur": "legumineuses", "libelle": "Légumineuses", "taux": 0}, {"valeur": "fruits_frais", "libelle": "Fruits frais", "taux": 0}, {"valeur": "legumes_frais", "libelle": "Légumes frais", "taux": 0}, {"valeur": "animaux_vivants", "libelle": "Animaux vivants", "taux": 0}, {"valeur": "lait_cru", "libelle": "Lait cru", "taux": 0}, {"valeur": "oeufs", "libelle": "<PERSON><PERSON><PERSON>", "taux": 0}, {"valeur": "miel", "libelle": "<PERSON><PERSON> naturel", "taux": 0}, {"valeur": "labour", "libelle": "Labour/préparation", "taux": 10}, {"valeur": "recolte", "libelle": "Récolte/battage", "taux": 10}, {"valeur": "transport", "libelle": "Transport agricole", "taux": 10}, {"valeur": "semences", "libelle": "Semences", "taux": 0}, {"valeur": "engrais", "libelle": "<PERSON><PERSON><PERSON>", "taux": 0}, {"valeur": "phytosanitaires", "libelle": "Produits phytosanitaires", "taux": 10}, {"valeur": "tracteur", "libelle": "Tracteur agricole", "taux": 0}, {"valeur": "materiel_recolte", "libelle": "Mat<PERSON>l de récolte", "taux": 0}, {"valeur": "equipement_elevage", "libelle": "Équipement élevage", "taux": 10}]}, {"id": "statut_vendeur", "nom": "Statut du vendeur", "type": "select", "obligatoire": false, "options": [{"valeur": "agriculteur_individuel", "libelle": "Agriculteur individuel"}, {"valeur": "cooperative", "libelle": "Coopérative agricole"}, {"valeur": "entreprise_agricole", "libelle": "Entreprise agricole"}, {"valeur": "ne<PERSON>ciant", "libelle": "Négociant"}]}, {"id": "destination", "nom": "Destination", "type": "select", "obligatoire": false, "options": [{"valeur": "marche_local", "libelle": "Marché local"}, {"valeur": "export_ue", "libelle": "Export UE"}, {"valeur": "export_hors_ue", "libelle": "Export hors UE"}]}, {"id": "ca_annuel", "nom": "CA annuel", "type": "number", "unite": "MAD", "obligatoire": false, "aide": "Pour déterminer le régime TVA applicable"}], "calcul": {"etapes": [{"id": "determination_taux", "nom": "Détermination du taux TVA", "regles": [{"condition": "destination == 'export_ue' || destination == 'export_hors_ue'", "taux": 0, "description": "Exportations exonérées"}, {"condition": "type_operation == 'vente_produits_bruts'", "taux": 0, "description": "Produits agricoles bruts exonérés"}, {"condition": "type_operation == 'services_agricoles'", "taux": 10, "description": "Services agricoles à 10%"}, {"condition": "statut_vendeur == 'cooperative' && type_operation == 'cooperative'", "taux": 0, "description": "Opérations coopératives exonérées"}]}, {"id": "regime_applicable", "nom": "Régime TVA applicable", "conditions": [{"condition": "ca_annuel < 500000", "regime": "franchise", "description": "Franchise de TVA"}, {"condition": "ca_annuel >= 500000 && ca_annuel < 1000000", "regime": "trimestriel", "description": "Déclaration trimestrielle"}, {"condition": "ca_annuel >= 1000000", "regime": "mensuel", "description": "Déclaration mensuelle"}]}, {"id": "calcul_tva", "nom": "Calcul de la TVA", "formule": "montant_ht * taux_tva / 100"}, {"id": "montant_ttc", "nom": "Montant TTC", "formule": "montant_ht + tva_calculee"}]}, "format_resultats": {"resume": [{"libelle": "Montant HT", "valeur": "montant_ht", "format": "currency"}, {"libelle": "Taux TVA", "valeur": "taux_tva", "format": "percentage"}, {"libelle": "TVA", "valeur": "tva_calculee", "format": "currency"}, {"libelle": "Montant TTC", "valeur": "montant_ttc", "format": "currency", "style": "highlight"}], "regime_info": true, "obligations_declaratives": true}}, {"id": "amortissement_agricole", "nom": "Calculateur Amortissements Agricoles", "description": "Calcul des amortissements pour actifs agricoles avec règles spécifiques aux actifs biologiques", "icone": "trending_down", "couleur": "#9C27B0", "parametres_entree": [{"id": "type_actif", "nom": "Type d'actif", "type": "select", "obligatoire": true, "options": [{"valeur": "tracteur", "libelle": "<PERSON><PERSON><PERSON><PERSON>", "duree": 5, "taux": 20}, {"valeur": "materiel_travail_sol", "libelle": "Matériel travail du sol", "duree": 7, "taux": 15}, {"valeur": "materiel_recolte", "libelle": "Mat<PERSON>l de récolte", "duree": 8, "taux": 12}, {"valeur": "materiel_elevage", "libelle": "Matériel d'élevage", "duree": 10, "taux": 10}, {"valeur": "serre", "libelle": "Ser<PERSON>", "duree": 10, "taux": 10}, {"valeur": "irrigation", "libelle": "Système d'irrigation", "duree": 12, "taux": 8}, {"valeur": "batiment_elevage", "libelle": "Bâtiment d'élevage", "duree": 20, "taux": 5}, {"valeur": "silo", "libelle": "Silo", "duree": 15, "taux": 6}, {"valeur": "bovins_reproducteurs", "libelle": "<PERSON><PERSON><PERSON> reproducteurs", "duree": 8, "taux": 12}, {"valeur": "ovins_reproducteurs", "libelle": "<PERSON><PERSON><PERSON> reproducteurs", "duree": 5, "taux": 20}, {"valeur": "equins", "libelle": "Équins", "duree": 10, "taux": 10}, {"valeur": "volailles_reproductrices", "libelle": "<PERSON><PERSON><PERSON> reproductrices", "duree": 2, "taux": 50}, {"valeur": "plantation_agrumes", "libelle": "Plantation agrumes", "duree": 25, "taux": 4}, {"valeur": "plantation_oliviers", "libelle": "Plantation oliviers", "duree": 50, "taux": 2}, {"valeur": "plantation_amandiers", "libelle": "Plantation amandiers", "duree": 30, "taux": 3}, {"valeur": "plantation_vignes", "libelle": "Plantation vignes", "duree": 25, "taux": 4}, {"valeur": "palmiers_dattiers", "libelle": "<PERSON><PERSON> dattiers", "duree": 60, "taux": 1.67}]}, {"id": "valeur_acquisition", "nom": "Valeur d'acquisition", "type": "number", "unite": "MAD", "obligatoire": true, "min": 0, "aide": "Prix d'achat ou coût de production"}, {"id": "date_acquisition", "nom": "Date d'acquisition", "type": "date", "obligatoire": true, "aide": "Date de mise en service"}, {"id": "methode_amortissement", "nom": "Méthode d'amortissement", "type": "select", "obligatoire": true, "options": [{"valeur": "lineaire", "libelle": "Linéaire"}, {"valeur": "degressif", "libelle": "Dégressif"}, {"valeur": "variable", "libelle": "Variable (actifs biologiques)"}]}, {"id": "duree_personnalisee", "nom": "<PERSON><PERSON><PERSON>", "type": "number", "unite": "ann<PERSON>", "obligatoire": false, "min": 1, "max": 60, "aide": "Laisser vide pour utiliser la durée standard"}, {"id": "valeur_<PERSON>lle", "nom": "<PERSON><PERSON>", "type": "number", "unite": "MAD", "obligatoire": false, "valeur_defaut": 0, "aide": "Valeur estimée en fin d'utilisation"}, {"id": "phase_improductive", "nom": "Phase improductive", "type": "number", "unite": "ann<PERSON>", "obligatoire": false, "condition": "type_actif.includes('plantation')", "aide": "Période avant première récolte (plantations)"}], "calcul": {"etapes": [{"id": "determination_duree", "nom": "Détermination de la durée", "formule": "duree_personnalisee || duree_standard[type_actif]"}, {"id": "base_amortissable", "nom": "Base amortissable", "formule": "valeur_acquisition - valeur_residuelle"}, {"id": "calcul_annuites", "nom": "Calcul des annuités", "methodes": {"lineaire": {"formule": "base_amortissable / duree_amortissement", "description": "<PERSON><PERSON><PERSON> constante"}, "degressif": {"formule": "valeur_nette_comptable * coefficient_degressif", "coefficients": {"3_4_ans": 1.5, "5_6_ans": 2, "plus_6_ans": 2.5}}, "variable": {"description": "Selon production ou rendement", "application": "Actifs biologiques"}}}, {"id": "tableau_amortissement", "nom": "Tableau d'amortissement", "colonnes": ["<PERSON><PERSON>", "<PERSON><PERSON> début", "<PERSON><PERSON><PERSON>", "Cumul amortissements", "<PERSON>ur nette"]}]}, "format_resultats": {"resume": [{"libelle": "Valeur d'acquisition", "valeur": "valeur_acquisition", "format": "currency"}, {"libelle": "Durée d'amortissement", "valeur": "duree_amortissement", "format": "years"}, {"libelle": "<PERSON><PERSON><PERSON>", "valeur": "annuite", "format": "currency"}, {"libelle": "Cumul amortissements", "valeur": "cumul_amortissements", "format": "currency"}], "tableau_detaille": true, "graphique_evolution": true}}, {"id": "impact_subventions", "nom": "Calculateur Impact Subventions", "description": "Analyse de l'impact fiscal et comptable des subventions agricoles", "icone": "account_balance", "couleur": "#607D8B", "parametres_entree": [{"id": "type_subvention", "nom": "Type de subvention", "type": "select", "obligatoire": true, "options": [{"valeur": "investissement", "libelle": "Subvention d'investissement"}, {"valeur": "exploitation", "libelle": "Subvention d'exploitation"}, {"valeur": "equipement", "libelle": "Aide à l'équipement"}, {"valeur": "installation", "libelle": "Aide à l'installation"}, {"valeur": "modernisation", "libelle": "Prime de modernisation"}, {"valeur": "assurance_recolte", "libelle": "Compensation assurance récolte"}, {"valeur": "calamite", "libelle": "Indemnité calamité agricole"}]}, {"id": "montant_subvention", "nom": "Montant de la subvention", "type": "number", "unite": "MAD", "obligatoire": true, "min": 0}, {"id": "valeur_investissement", "nom": "Valeur de l'investissement", "type": "number", "unite": "MAD", "obligatoire": false, "condition": "type_subvention == 'investissement' || type_subvention == 'equipement'", "aide": "Coût total de l'investissement subventionné"}, {"id": "duree_amortissement", "nom": "Durée d'amortissement", "type": "number", "unite": "ann<PERSON>", "obligatoire": false, "condition": "type_subvention == 'investissement'", "min": 1, "max": 20}, {"id": "regime_fiscal", "nom": "Régime fiscal", "type": "select", "obligatoire": true, "options": [{"valeur": "ir", "libelle": "Impôt sur le Revenu"}, {"valeur": "is", "libelle": "Impôt sur les Sociétés"}]}, {"id": "taux_imposition", "nom": "Taux d'imposition", "type": "number", "unite": "%", "obligatoire": true, "min": 0, "max": 38, "aide": "Taux marginal d'imposition"}, {"id": "date_perception", "nom": "Date de perception", "type": "date", "obligatoire": true}], "calcul": {"etapes": [{"id": "traitement_comptable", "nom": "Traitement comptable", "regles": {"subvention_investissement": {"compte": "131 - Subventions d'investissement", "reprise": "Étalée sur durée d'amortissement", "compte_reprise": "757 - Reprises subventions d'investissement"}, "subvention_exploitation": {"compte": "756 - Subventions d'exploitation", "reprise": "Immédiate en produit", "impact": "Résultat de l'exercice"}}}, {"id": "impact_fiscal", "nom": "Impact fiscal", "calculs": [{"id": "imposable_immediat", "condition": "type_subvention == 'exploitation'", "montant": "montant_subvention", "description": "Imposable intégralement l'année de perception"}, {"id": "imposable_etale", "condition": "type_subvention == 'investissement'", "montant": "montant_subvention / duree_amortissement", "description": "Imposable par fractions annuelles"}]}, {"id": "economie_impot", "nom": "Économie d'impôt", "formule": "montant_imposable * taux_imposition / 100"}, {"id": "impact_tresorerie", "nom": "Impact trésorerie", "elements": [{"libelle": "Subvention perçue", "montant": "montant_subvention", "signe": "+"}, {"libelle": "Impôt supplémentaire", "montant": "economie_impot", "signe": "-"}, {"libelle": "Impact net", "montant": "montant_subvention - economie_impot", "signe": "="}]}]}, "format_resultats": {"resume": [{"libelle": "Montant subvention", "valeur": "montant_subvention", "format": "currency"}, {"libelle": "Montant imposable", "valeur": "montant_imposable", "format": "currency"}, {"libelle": "Impôt supplémentaire", "valeur": "economie_impot", "format": "currency"}, {"libelle": "Avantage net", "valeur": "avantage_net", "format": "currency", "style": "highlight"}], "ecritures_comptables": true, "planning_fiscal": true}}, {"id": "cooperative_agricole", "nom": "Calculateur Coopérative Agricole", "description": "Calculs spécifiques aux coopératives agricoles : IS, TVA, répartition aux adhérents", "icone": "groups", "couleur": "#795548", "parametres_entree": [{"id": "ca_avec_adherents", "nom": "CA avec adhérents", "type": "number", "unite": "MAD", "obligatoire": true, "min": 0, "aide": "Chiffre d'affaires réalisé avec les membres"}, {"id": "ca_avec_tiers", "nom": "CA avec tiers", "type": "number", "unite": "MAD", "obligatoire": true, "min": 0, "aide": "Chiffre d'affaires réalisé avec les non-membres"}, {"id": "charges_exploitation", "nom": "Charges d'exploitation", "type": "number", "unite": "MAD", "obligatoire": true, "min": 0}, {"id": "nombre_adherents", "nom": "Nombre d'adhérents", "type": "number", "obligatoire": true, "min": 1}, {"id": "type_activite", "nom": "Activité principale", "type": "select", "obligatoire": true, "options": [{"valeur": "commercialisation", "libelle": "Commercialisation produits agricoles"}, {"valeur": "approvisionnement", "libelle": "Fourniture intrants agricoles"}, {"valeur": "transformation", "libelle": "Première transformation"}, {"valeur": "services", "libelle": "Services agricoles"}, {"valeur": "mixte", "libelle": "Activité mixte"}]}, {"id": "statut_agree", "nom": "Coopérative agréée", "type": "boolean", "obligatoire": true, "valeur_defaut": true, "aide": "Statut de coopérative agricole agréée"}, {"id": "reserves_legales", "nom": "Réserves légales", "type": "number", "unite": "MAD", "obligatoire": false, "valeur_defaut": 0}, {"id": "ristournes_distribuees", "nom": "Ristournes distribuées", "type": "number", "unite": "MAD", "obligatoire": false, "valeur_defaut": 0, "aide": "Ristournes versées aux adhérents"}], "calcul": {"etapes": [{"id": "ca_total", "nom": "<PERSON><PERSON>re d'affaires total", "formule": "ca_avec_adherents + ca_avec_tiers"}, {"id": "resultat_brut", "nom": "Résultat brut", "formule": "ca_total - charges_exploitation"}, {"id": "calcul_is", "nom": "Calcul IS", "regles": [{"condition": "statut_agree", "taux_is": 17.5, "base": "resultat_operations_tiers", "exoneration": "Opérations avec adhérents exonérées"}, {"condition": "!statut_agree", "taux_is": 31, "base": "resultat_brut", "description": "Taux normal si non agréée"}]}, {"id": "calcul_tva", "nom": "Calcul TVA", "operations": [{"type": "ventes_adherents_produits_bruts", "taux": 0, "description": "Exonérées si produits agricoles bruts"}, {"type": "ventes_tiers", "taux": "selon_produit", "description": "Selon nature du produit vendu"}, {"type": "services_adherents", "taux": 10, "description": "Services facturés aux membres"}]}, {"id": "repartition_excedent", "nom": "Répartition de l'excédent", "regles": [{"poste": "Réserve légale", "taux": 20, "base": "excedent_net", "obligatoire": true}, {"poste": "Ristournes aux adhérents", "taux": "variable", "base": "excedent_distribuable", "calcul": "Proportionnel aux opérations"}, {"poste": "Report à nouveau", "montant": "solde_restant"}]}]}, "format_resultats": {"resume": [{"libelle": "CA total", "valeur": "ca_total", "format": "currency"}, {"libelle": "Résultat brut", "valeur": "resultat_brut", "format": "currency"}, {"libelle": "IS à payer", "valeur": "is_du", "format": "currency"}, {"libelle": "TVA collectée", "valeur": "tva_collectee", "format": "currency"}, {"libelle": "Ristournes distribuables", "valeur": "ristournes_distribuables", "format": "currency", "style": "highlight"}], "repartition_excedent": true, "avantages_fiscaux": true, "ristourne_par_adherent": true}}], "parametres_globaux": {"devise": "MAD", "precision_calculs": 2, "arrondi": "standard", "format_date": "dd/MM/yyyy", "separateur_milliers": " ", "separateur_decimal": ",", "symbole_devise": "DH"}, "aide_contextuelle": {"ir_agricole": {"definition": "L'Impôt sur le Revenu agricole s'applique aux revenus tirés de l'exploitation de biens ruraux et de l'élevage", "liens_utiles": [{"titre": "Guide IR Agricole", "section": "ir_agricole"}, {"titre": "Barème IR 2025", "section": "bareme_ir"}]}, "is_agricole": {"definition": "L'Impôt sur les Sociétés pour entreprises agricoles avec taux préférentiels selon le type d'entité", "liens_utiles": [{"titre": "Guide IS Agricole", "section": "is_agricole"}, {"titre": "<PERSON><PERSON><PERSON><PERSON>", "section": "regimes_speciaux"}]}, "tva_agricole": {"definition": "Régime TVA spécifique au secteur agricole avec nombreuses exonérations", "liens_utiles": [{"titre": "Guide TVA Agricole", "section": "tva_agricole"}, {"titre": "Produits exonérés", "section": "exonerations_tva"}]}}}