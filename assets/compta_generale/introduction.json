{"title": "Introduction à la Comptabilité", "description": "La comptabilité est un outil essentiel de gestion et d'information qui permet de suivre l'activité économique d'une entreprise. Elle constitue un langage universel des affaires et joue un rôle crucial dans la prise de décision.", "definition": "La comptabilité est un système d'organisation de l'information financière permettant de saisir, classer, enregistrer des données de base chiffrées et présenter des états reflétant une image fidèle du patrimoine, de la situation financière et du résultat de l'entité à la date de clôture.", "objectifs": ["Enregistrer les opérations et les mouvements affectant le patrimoine de l'entreprise", "Fournir des informations pertinentes sur la situation financière et les performances", "Calculer le résultat (bénéfice ou perte) de l'exercice", "Établir les états de synthèse conformes à la réglementation", "Satisfaire aux obligations légales et fiscales", "Aider à la prise de décision et au pilotage de l'entreprise"], "methodes_evaluation_stocks": {"title": "Méthodes d'Évaluation des Stocks", "description": "En comptabilité marocaine, plusieurs méthodes sont utilisées pour évaluer les stocks, chacune ayant ses particularités et son impact sur le résultat.", "methodes": [{"nom": "FIFO (First In, First Out)", "description": "Les premiers articles entrés en stock sont considérés comme les premiers sortis", "avantages": ["Reflète mieux la valeur actuelle du stock en période d'inflation", "Méthode simple à appliquer", "Correspond souvent à la réalité physique des flux"], "schema": {"type": "FIFODiagram", "exemple": {"entrees": [{"date": "01/01", "quantite": 100, "prix": 10}, {"date": "15/01", "quantite": 150, "prix": 12}, {"date": "30/01", "quantite": 200, "prix": 15}], "sorties": [{"date": "20/01", "quantite": 180, "valorisation": "100 × 10 + 80 × 12"}]}}}, {"nom": "CUMP (Coût Unitaire Moyen Pondéré)", "description": "Prix moyen pondéré calculé après chaque entrée ou périodiquement", "variantes": [{"nom": "CUMP périodique", "description": "Calculé à la fin d'une période (mois, trimestre)", "formule": "(Stock initial + Entrées de la période) / (Quantité initiale + Quantités entrées)"}, {"nom": "CUMP après chaque entrée", "description": "Recalculé à chaque nouvelle entrée en stock", "formule": "(Valeur du stock restant + Nouvelle entrée) / (Quantité restante + Quantité entrée)"}], "schema": {"type": "CUMPDiagram", "exemple": {"stock_initial": {"quantite": 100, "prix": 10}, "entree": {"quantite": 150, "prix": 12}, "calcul": "CUMP = ((100 × 10) + (150 × 12)) / (100 + 150) = 11,2 DH"}}}], "aspects_pratiques": {"choix_methode": ["Nature des articles (périssables, technologiques)", "Variation des prix d'achat", "Système d'information disponible", "Secteur d'activité"], "contraintes_legales": ["Permanence des méthodes obligatoire", "Changement possible avec justification", "Mention en annexe obligatoire"], "impact_fiscal": ["Influence sur la valeur du stock final", "Impact sur le résultat fiscal", "Nécessité de documentation"]}}, "principes": [{"titre": "Principe de la partie double", "description": "Chaque opération est enregistrée en débit dans un compte et en crédit dans un autre compte, pour un même montant. Ce principe garantit l'équilibre comptable.", "schema": {"type": "DoubleEntryDiagram", "operation": {"description": "Achat de marchandises à crédit pour 10 000 DH", "debit": {"compte": "6111 - <PERSON><PERSON><PERSON> de marchandises", "montant": 10000}, "credit": {"compte": "4411 - <PERSON><PERSON><PERSON><PERSON>", "montant": 10000}}}, "exemple_pratique": {"scenario": "Vente de marchandises au comptant", "montant": 5000, "ecritures": [{"compte": "5141 - <PERSON><PERSON>", "debit": 5000, "credit": 0}, {"compte": "7111 - <PERSON><PERSON><PERSON> marchand<PERSON>", "debit": 0, "credit": 5000}]}}, {"titre": "Principe de prudence", "description": "Les produits ne sont comptabilisés que s'ils sont certains et définitivement acquis ; les charges sont à enregistrer dès qu'elles sont probables.", "schema": {"type": "PrudenceDiagram", "elements": [{"type": "Produits", "condition": "Certains et acquis", "exemple": "Facture client émise et validée"}, {"type": "Charges", "condition": "Probables", "exemple": "Provision pour risque client"}]}, "exemple_pratique": {"scenario": "<PERSON><PERSON><PERSON> douteuse", "montant_creance": 20000, "risque_non_paiement": "40%", "ecriture_provision": {"compte": "6196 - Dotations aux provisions", "debit": 8000, "compte_contrepartie": "3942 - Provisions pour dépréciation des clients", "credit": 8000}}}, {"titre": "Principe d'indépendance des exercices", "description": "Chaque exercice est indépendant de l'autre. Les charges et les produits doivent être rattachés à l'exercice qui les concerne effectivement.", "schema": {"type": "TimelineDiagram", "exercices": [{"annee": "2023", "charges": ["Loyers 2023", "Salaires 2023"], "produits": ["Ventes 2023", "Prestations 2023"]}, {"annee": "2024", "charges": ["Loyers 2024", "Salaires 2024"], "produits": ["Ventes 2024", "Prestations 2024"]}]}, "exemple_pratique": {"scenario": "Prime d'assurance payée d'avance", "periode": "01/10/2023 au 30/09/2024", "montant_total": 12000, "repartition": {"exercice_2023": 3000, "exercice_2024": 9000, "ecritures": [{"date": "01/10/2023", "libelle": "Charge constatée d'avance", "compte": "3491", "debit": 9000, "credit": 0}]}}}, {"titre": "Principe de permanence des méthodes", "description": "Les mêmes règles et procédures sont utilisées pour l'établissement des comptes d'un exercice à l'autre, assurant leur comparabilité.", "schema": {"type": "ConsistencyDiagram", "elements": [{"categorie": "Méthodes d'évaluation", "exemples": ["FIFO", "CUMP"]}, {"categorie": "Méthodes d'amortissement", "exemples": ["Linéaire", "Dégressif"]}]}, "exemple_pratique": {"scenario": "Amortissement d'un équipement", "methode": "Linéaire", "duree": "5 ans", "valeur_origine": 100000, "taux": "20%", "tableau_amortissement": [{"annee": 1, "base": 100000, "dotation": 20000, "cumul": 20000, "vnc": 80000}]}}, {"titre": "Principe du coût historique", "description": "Les biens acquis par l'entreprise sont enregistrés à leur coût d'acquisition ou de production, constituant leur valeur historique.", "schema": {"type": "CostBreakdownDiagram", "exemple": {"bien": "Machine industrielle", "composants": [{"nature": "Prix d'achat HT", "montant": 200000}, {"nature": "Frais de transport", "montant": 5000}, {"nature": "Frais d'installation", "montant": 15000}], "cout_total": 220000}}, "exemple_pratique": {"scenario": "Acquisition d'une machine", "elements_cout": {"prix_achat": 200000, "frais_transport": 5000, "frais_installation": 15000, "ecriture": {"compte": "2332 - Matériel et outillage", "debit": 220000, "credit": 0}}}}, {"titre": "Principe de continuité d'exploitation", "description": "Les comptes sont établis dans la perspective que l'entreprise poursuivra ses activités dans un avenir prévisible.", "schema": {"type": "ContinuityDiagram", "elements": [{"categorie": "Évaluation des actifs", "methode": "Valeur d'utilité"}, {"categorie": "Amortissements", "methode": "Sur durée normale d'utilisation"}, {"categorie": "<PERSON><PERSON>", "methode": "Classification selon échéances"}]}, "exemple_pratique": {"scenario": "Acquisition d'un brevet", "duree_protection": "20 ans", "cout_acquisition": 100000, "traitement": {"type": "Amortissement sur durée d'utilité", "duree": "10 ans", "justification": "Durée d'exploitation prévue"}}}], "reglementation": [{"texte": "Code Général de la Normalisation Comptable (CGNC)", "description": "C<PERSON>re de référence qui définit les principes comptables, le plan des comptes et les règles d'évaluation et de présentation des états de synthèse au Maroc."}, {"texte": "<PERSON><PERSON> 9-88", "description": "Définit les obligations comptables des commerçants, notamment la tenue d'une comptabilité, l'établissement des états de synthèse annuels et leur conservation."}, {"texte": "Code de Commerce", "description": "Fixe les obligations légales des commerçants en matière de comptabilité et définit les documents comptables obligatoires."}, {"texte": "Normes Marocaines de Comptabilité", "description": "Ensemble de règles spécifiques traitant de problématiques comptables particulières, en complément du CGNC."}], "obligations": {"tenue_comptable": ["Enregistrement chronologique des mouvements", "Conservation des pièces justificatives", "Établissement d'un bilan d'ouverture", "Tenue des livres obligatoires", "Établissement des états de synthèse annuels"], "documents_obligatoires": ["Livre-journal", "Grand-livre", "Balance", "Livre d'inventaire", "États de synthèse"]}}