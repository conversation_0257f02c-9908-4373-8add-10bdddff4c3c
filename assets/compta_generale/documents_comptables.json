{"title": "Documents Comptables", "description": "Les documents comptables sont les supports qui permettent d'enregistrer et de suivre les opérations comptables d'une entreprise. Ils constituent la base de la comptabilité et sont essentiels pour assurer la traçabilité des transactions.", "documents": [{"title": "Le Bilan", "widget": "BilanWidget", "description": "Le bilan est un état de synthèse qui donne une image du patrimoine de l'entreprise à une date donnée. Il présente l'actif (ce que l'entreprise possède) et le passif (comment elle a financé ses biens).", "structure": {"Actif": ["Actif immobilisé", "Actif circulant", "Trésorerie-Actif"], "Passif": ["Financement permanent", "Passif circulant", "Trésorerie-Passif"]}, "tips": ["Vérifiez toujours l'égalité entre le total actif et le total passif", "Classez les éléments par ordre de liquidité croissante à l'actif", "Classez les éléments par ordre d'exigibilité croissante au passif", "Assurez-vous que les amortissements et provisions sont correctement déduits", "Vérifiez la cohérence avec les exercices précédents"], "erreurs_courantes": ["Oublier des éléments hors bilan importants", "Mal classer les éléments entre court et long terme", "Ne pas tenir compte des dépréciations", "Confondre les avances et acomptes reçus/versés"], "methode_elaboration": ["1. <PERSON><PERSON><PERSON> tous les comptes de l'entreprise", "2. <PERSON><PERSON><PERSON><PERSON><PERSON> les soldes de chaque compte", "3. <PERSON><PERSON><PERSON> les régularisations nécessaires", "4. Calculez les amortissements et provisions", "5. Classez les éléments selon leur nature", "6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> les totaux par rubriques"], "diagramme": {"type": "SfTreemap", "data": {"Actif": {"Actif immobilisé": 40, "Actif circulant": 35, "Trésorerie-Actif": 25}, "Passif": {"Financement permanent": 45, "Passif circulant": 35, "Trésorerie-Passif": 20}}}, "caractéristiques": ["Document de synthèse obligatoire", "Présentation en deux colonnes : Actif et Passif", "Équilibre obligatoire entre l'actif et le passif", "Établi à la fin de chaque exercice"], "exemple": {"actif": {"Actif immobilisé": [{"rubrique": "Immobilisations incorporelles", "montant": 150000}, {"rubrique": "Immobilisations corporelles", "montant": 450000}, {"rubrique": "Immobilisations financières", "montant": 100000}], "Actif circulant": [{"rubrique": "Stocks", "montant": 200000}, {"rubrique": "Créances clients", "montant": 300000}, {"rubrique": "Autres créances", "montant": 50000}], "Trésorerie-Actif": [{"rubrique": "Banque", "montant": 180000}, {"rubrique": "Caisse", "montant": 20000}]}, "passif": {"Financement permanent": [{"rubrique": "Capital social", "montant": 500000}, {"rubrique": "Réserves", "montant": 150000}, {"rubrique": "Résultat de l'exercice", "montant": 100000}], "Passif circulant": [{"rubrique": "<PERSON><PERSON> four<PERSON>", "montant": 400000}, {"rubrique": "Autres dettes", "montant": 200000}], "Trésorerie-Passif": [{"rubrique": "Découverts bancaires", "montant": 100000}]}}}, {"title": "Le CPC", "widget": "CPCWidget", "description": "Le Compte de Produits et Charges (CPC) est un état de synthèse qui récapitule les produits et les charges de l'exercice. Il permet de déterminer le résultat net de l'entreprise.", "structure": {"Produits": ["Produits d'exploitation", "Produits financiers", "Produits non courants"], "Charges": ["Charges d'exploitation", "Charges financières", "Charges non courantes"]}, "tips": ["Distinguez clairement les trois niveaux de résultat", "Vérifiez la cohérence des soldes intermédiaires", "Assurez-vous que toutes les charges sont rattachées au bon exercice", "Analysez les variations importantes par rapport à l'exercice précédent"], "erreurs_courantes": ["Oublier des charges à payer ou produits à recevoir", "Mal classer les éléments exceptionnels", "Ne pas respecter le principe de rattachement des charges aux produits", "Confondre les produits et charges d'exploitation avec les éléments financiers"], "methode_elaboration": ["1. <PERSON><PERSON><PERSON> tous les produits et charges de l'exercice", "2. Classez-les par nature (exploitation, financier, non courant)", "3. <PERSON><PERSON><PERSON> les soldes intermédiaires de gestion", "4. <PERSON><PERSON><PERSON><PERSON><PERSON> la cohérence des résultats", "5. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> les sous-totaux par catégorie"], "diagramme": {"type": "SfCircularChart", "data": {"Produits": {"Exploitation": 70, "Financiers": 20, "Non courants": 10}, "Charges": {"Exploitation": 65, "Financières": 25, "Non courantes": 10}}}, "caractéristiques": ["Présentation en cascade", "Calcul des différents niveaux de résultat", "Classification par nature des charges et produits", "Période de référence : l'exercice comptable"], "exemple": {"produits": [{"rubrique": "Ventes de marchandises", "montant": 1200000}, {"rubrique": "Production vendue", "montant": 800000}, {"rubrique": "Produits financiers", "montant": 50000}, {"rubrique": "Produits non courants", "montant": 30000}], "charges": [{"rubrique": "Achats de marchandises", "montant": 800000}, {"rubrique": "Charges de personnel", "montant": 400000}, {"rubrique": "Charges financières", "montant": 60000}, {"rubrique": "Charges non courantes", "montant": 20000}]}}, {"title": "La Balance", "widget": "BalanceWidget", "description": "La balance est un document qui reprend tous les comptes du grand livre avec leurs soldes débiteurs et créditeurs. Elle permet de vérifier l'équilibre des écritures et facilite l'établissement des états de synthèse.", "structure": {"Colonnes": ["Numéro de compte", "Intitulé du compte", "Mouvements débiteurs", "Mouvements créditeurs", "<PERSON><PERSON> d<PERSON>", "<PERSON><PERSON> crédit<PERSON>"]}, "tips": ["Vérifiez l'égalité des totaux débiteurs et créditeurs", "Contrôlez la cohérence des soldes avec le grand livre", "Assurez-vous que tous les comptes sont présents", "Vérifiez les reports à nouveau en début d'exercice"], "erreurs_courantes": ["Oublier certains comptes", "Mal reporter les soldes du grand livre", "Ne pas vérifier l'équilibre global", "Confondre les mouvements et les soldes"], "methode_elaboration": ["1. <PERSON><PERSON><PERSON> tous les comptes du grand livre", "2. <PERSON><PERSON><PERSON> les totaux des mouvements débiteurs et créditeurs", "3. <PERSON><PERSON><PERSON><PERSON><PERSON> les soldes débiteurs et créditeurs", "4. Vérifiez l'équilibre de la balance", "5. <PERSON><PERSON><PERSON><PERSON><PERSON> la cohérence avec les documents comptables"], "diagramme": {"type": "SfCartesianChart", "data": {"Mouvements": {"Débiteurs": 1300000, "Créditeurs": 1300000}, "Soldes": {"Débiteurs": 550000, "Créditeurs": 550000}}}, "caractéristiques": ["Égalité entre les totaux débiteurs et créditeurs", "Classement des comptes par ordre croissant", "Outil de contrôle de la comptabilité", "Document de transition vers les états de synthèse"], "exemple": {"comptes": [{"numero": "2111", "intitule": "Terrain", "mouvements_debit": 300000, "mouvements_credit": 0, "solde_debit": 300000, "solde_credit": 0}, {"numero": "3111", "intitule": "Marchandises", "mouvements_debit": 200000, "mouvements_credit": 150000, "solde_debit": 50000, "solde_credit": 0}, {"numero": "5141", "intitule": "Banque", "mouvements_debit": 800000, "mouvements_credit": 600000, "solde_debit": 200000, "solde_credit": 0}]}}, {"title": "Le Grand Livre", "widget": "GrandLivreWidget", "description": "Le grand livre est le recueil de l'ensemble des comptes de l'entreprise. Il regroupe tous les mouvements effectués par compte, permettant ainsi de suivre l'évolution de chaque compte au cours de l'exercice.", "structure": {"Par compte": ["En-tête (numéro et intitulé du compte)", "Date de l'opération", "Libellé de l'opération", "Montants au débit et au crédit", "Solde progressif"]}, "tips": ["Enregistrez chronologiquement toutes les opérations", "Vérifiez la numérotation des pièces justificatives", "Calculez régulièrement les soldes progressifs", "Assurez-vous de la traçabilité des opérations"], "erreurs_courantes": ["Oublier des écritures", "Mal reporter les montants", "Ne pas mettre à jour les soldes progressifs", "Perdre la trace des pièces justificatives"], "methode_elaboration": ["1. <PERSON><PERSON><PERSON> une page par compte", "2. Reportez les écritures du journal chronologiquement", "3. <PERSON><PERSON>z les soldes après chaque opération", "4. Vérifiez la concordance avec le journal", "5. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> les totaux périodiques"], "diagramme": {"type": "SfCartesianChart", "data": {"Evolution": {"Janvier": {"debit": 100000, "credit": 80000}, "Février": {"debit": 150000, "credit": 120000}, "Mars": {"debit": 200000, "credit": 180000}}}}, "caractéristiques": ["Classement chronologique des opérations par compte", "Report des écritures du journal", "<PERSON><PERSON><PERSON> de chaque compte", "Base pour l'établissement de la balance"], "exemple": {"comptes": {"5141": [{"date": "2024-01-15", "libelle": "Virement client ABC", "debit": 50000, "credit": 0, "reference": "VR-001"}, {"date": "2024-01-20", "libelle": "Règlement fournisseur XYZ", "debit": 0, "credit": 30000, "reference": "CH-001"}]}}}, {"title": "Les Comptes en T", "widget": "TAccountWidget", "description": "Le compte en T est une représentation schématique d'un compte qui permet de visualiser facilement les mouvements au débit et au crédit. C'est un outil pédagogique essentiel pour comprendre le fonctionnement des comptes.", "structure": {"Présentation": ["En-tête avec numéro et intitulé", "Partie gauche pour le débit", "Partie droite pour le crédit", "Total et solde en bas"]}, "tips": ["Inscrivez toujours le débit à gauche et le crédit à droite", "Calculez les totaux avant de déterminer le solde", "Utilisez des couleurs différentes pour débit et crédit", "<PERSON><PERSON><PERSON> clairement les reports à nouveau"], "erreurs_courantes": ["Inverser débit et crédit", "Oublier de calculer le solde", "<PERSON>ner les montants", "Ne pas indiquer les dates des opérations"], "methode_elaboration": ["1. <PERSON><PERSON> le T avec l'en-tête", "2. Insc<PERSON>z les opérations au débit et au crédit", "3. <PERSON><PERSON><PERSON> les totaux de chaque c<PERSON>", "4. <PERSON><PERSON><PERSON><PERSON><PERSON> le solde final", "5. <PERSON><PERSON><PERSON> la nature du solde (débiteur ou créditeur)"], "diagramme": {"type": "SfCartesianChart", "data": {"Mouvements": {"Débit": 85000, "Crédit": 45000}}}, "caractéristiques": ["Représentation visuelle claire", "Séparation débit/crédit", "Facilite la compréhension des mouvements", "Outil d'apprentissage efficace"], "exemple": {"numero": "5141", "intitule": "Banque", "debit": [{"date": "15/01/2024", "description": "Virement client ABC", "amount": 50000}, {"date": "18/01/2024", "description": "Encaissement chèque client DEF", "amount": 35000}], "credit": [{"date": "20/01/2024", "description": "Règlement fournisseur XYZ", "amount": 30000}, {"date": "25/01/2024", "description": "Virement charges sociales", "amount": 15000}]}}]}