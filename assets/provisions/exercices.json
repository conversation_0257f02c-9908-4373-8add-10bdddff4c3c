{"exercices": [{"title": "Exercice 1 - Provision pour Dépréciation des Stocks", "description": "Une entreprise possède un stock de marchandises d'une valeur comptable de 80 000 DH. À la fin de l'exercice N, la valeur de marché est estimée à 65 000 DH.", "difficulty": "Facile", "question": "Calculez la provision à constituer et passez l'écriture comptable nécessaire.", "solution": {"calcul": "Provision nécessaire = Valeur comptable - Valeur actuelle = 80 000 - 65 000 = 15 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Constatation de la dépréciation des stocks", "lines": [{"account": "6196", "label": "DEP pour dépréciation de l'actif circulant", "debit": "15 000", "credit": ""}, {"account": "3915", "label": "Provisions pour dépréciation des stocks de marchandises", "debit": "", "credit": "15 000"}]}]}]}}, {"title": "Exercice 2 - Provision pour Dépréciation des Titres", "description": "La société ALPHA détient des titres de participation acquis pour 200 000 DH. À la clôture de l'exercice N, leur valeur d'utilité est estimée à 150 000 DH.", "difficulty": "Facile", "question": "1. Calculez la provision à constituer\n2. Passez l'écriture comptable au 31/12/N", "solution": {"calcul": "Provision nécessaire = 200 000 - 150 000 = 50 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Constatation de la dépréciation", "lines": [{"account": "6194", "label": "DEP pour dépréciation des immobilisations financières", "debit": "50 000", "credit": ""}, {"account": "2951", "label": "Provisions pour dépréciation des titres de participation", "debit": "", "credit": "50 000"}]}]}]}}, {"title": "Exercice 3 - Écart de Conversion sur Créances", "description": "Au 31/12/N, la société MAROC EXPORT a une créance de 50 000 USD sur un client étranger. Cette créance a été comptabilisée au cours de 9,8 DH/USD. À la clôture, le cours est de 10,2 DH/USD.", "difficulty": "<PERSON><PERSON><PERSON>", "question": "1. <PERSON><PERSON><PERSON> l'écart de conversion\n2. Passez l'écriture comptable nécessaire", "solution": {"calcul": "Valeur initiale = 50 000 × 9,8 = 490 000 DH\nValeur au 31/12 = 50 000 × 10,2 = 510 000 DH\nÉcart = 510 000 - 490 000 = 20 000 DH (gain latent)", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Constatation de l'écart de conversion actif", "lines": [{"account": "3721", "label": "Écarts de conversion Actif", "debit": "", "credit": "20 000"}, {"account": "4741", "label": "Augmentation des créances circulantes", "debit": "20 000", "credit": ""}]}]}]}}, {"title": "Exercice 4 - Provision pour Garantie Clients", "description": "La société ELECTRO vend des appareils avec garantie 2 ans. Le coût moyen des réparations est 3% du CA. Le CA de N est 5 000 000 DH. En N+1, les réparations s'élèvent à 120 000 DH.", "difficulty": "<PERSON><PERSON><PERSON>", "question": "1. <PERSON><PERSON>z la provision à constituer\n2. Passez les écritures au 31/12/N et 31/12/N+1", "solution": {"calcul": "Provision = 5 000 000 × 3% = 150 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Dotation à la provision pour garantie", "lines": [{"account": "6195", "label": "DEP pour risques et charges", "debit": "150 000", "credit": ""}, {"account": "1512", "label": "Provisions pour garanties données aux clients", "debit": "", "credit": "150 000"}]}, {"date": "31/12/N+1", "description": "Utilisation de la provision", "lines": [{"account": "1512", "label": "Provisions pour garanties données aux clients", "debit": "120 000", "credit": ""}, {"account": "7195", "label": "Reprises sur provisions pour risques et charges", "debit": "", "credit": "120 000"}]}]}]}}, {"title": "Exercice 5 - Provision pour Litiges", "description": "La société BETA fait l'objet d'un procès d'un client pour défaut de livraison. L'avocat estime le risque de perdre à 80% avec des dommages de 300 000 DH. En N+1, le jugement condamne l'entreprise à 250 000 DH.", "difficulty": "<PERSON><PERSON><PERSON>", "question": "1. <PERSON><PERSON><PERSON> et comptabilisez la provision en N\n2. Passez les écritures lors du paiement en N+1", "solution": {"calcul": "Provision = 300 000 × 80% = 240 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Dotation à la provision pour litige", "lines": [{"account": "6195", "label": "DEP pour risques et charges", "debit": "240 000", "credit": ""}, {"account": "1511", "label": "Provisions pour litiges", "debit": "", "credit": "240 000"}]}, {"date": "31/12/N+1", "description": "Reprise de la provision", "lines": [{"account": "1511", "label": "Provisions pour litiges", "debit": "240 000", "credit": ""}, {"account": "7195", "label": "Reprises sur provisions pour risques et charges", "debit": "", "credit": "240 000"}]}]}, {"nom": "Journal de Banque", "code": "BQ", "ecritures": [{"date": "31/12/N+1", "description": "Paiement des dommages", "lines": [{"account": "6188", "label": "Autres charges externes", "debit": "250 000", "credit": ""}, {"account": "5141", "label": "Banques", "debit": "", "credit": "250 000"}]}]}]}}, {"title": "Exercice 6 - Provisions pour Dépréciation des Créances", "description": "Au 31/12/N, la société GAMMA a les créances douteuses suivantes:\n- Client A: créance de 80 000 DH, probabilité de recouvrement 40%\n- Client B: créance de 120 000 DH, probabilité de recouvrement 70%\nEn N+1, le client A paie 30 000 DH et le client B est déclaré en faillite.", "difficulty": "Difficile", "question": "1. <PERSON><PERSON><PERSON> et comptabilisez les provisions au 31/12/N\n2. <PERSON><PERSON> toutes les écritures nécessaires en N+1", "solution": {"calcul": "Client A: Provision = 80 000 × (1-0.4) = 48 000 DH\nClient B: Provision = 120 000 × (1-0.7) = 36 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Dotation aux provisions pour créances douteuses", "lines": [{"account": "6196", "label": "DEP pour dépréciation de l'actif circulant", "debit": "84 000", "credit": ""}, {"account": "3942", "label": "Provisions pour dépréciation des clients et comptes rattachés", "debit": "", "credit": "84 000"}]}, {"date": "31/12/N+1", "description": "Reprise provision client A", "lines": [{"account": "3942", "label": "Provisions pour dépréciation des clients", "debit": "48 000", "credit": ""}, {"account": "7196", "label": "Reprises sur provisions pour dépréciation de l'actif circulant", "debit": "", "credit": "48 000"}]}]}, {"nom": "Journal de Banque", "code": "BQ", "ecritures": [{"date": "31/12/N+1", "description": "Encaissement partiel client A", "lines": [{"account": "5141", "label": "Banques", "debit": "30 000", "credit": ""}, {"account": "4411", "label": "Clients - ventes de biens ou services", "debit": "", "credit": "30 000"}]}]}]}}, {"title": "Exercice 7 - Provisions Réglementées", "description": "La société DELTA constitue une provision pour investissement de 400 000 DH en N. En N+2, elle réalise l'investissement pour 380 000 DH.", "difficulty": "Difficile", "question": "1. Comptabilisez la constitution de la provision en N\n2. Passez les écritures lors de la réalisation de l'investissement en N+2", "solution": {"calcul": "Provision constituée = 400 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Constitution de la provision pour investissement", "lines": [{"account": "6512", "label": "Dotations aux provisions réglementées", "debit": "400 000", "credit": ""}, {"account": "1311", "label": "Provisions pour investissements", "debit": "", "credit": "400 000"}]}, {"date": "31/12/N+2", "description": "Réalisation de l'investissement et reprise de la provision", "lines": [{"account": "1311", "label": "Provisions pour investissements", "debit": "400 000", "credit": ""}, {"account": "7512", "label": "Reprises sur provisions réglementées", "debit": "", "credit": "400 000"}]}]}]}}, {"title": "Exercice 8 - Cas Complexe Multi-Provisions", "description": "Au 31/12/N, la société OMEGA présente la situation suivante:\n1. Stock de produits finis: valeur comptable 500 000 DH, valeur de marché 420 000 DH\n2. Créance en USD: 100 000 USD comptabilisée à 9,5 DH/USD, cours clôture 9,8 DH/USD\n3. Litige social: risque estimé à 150 000 DH, probabilité 60%\n4. Garantie produits: 2% du CA (CA = 8 000 000 DH)", "difficulty": "Expert", "question": "1. <PERSON><PERSON><PERSON> toutes les provisions nécessaires\n2. <PERSON>ez toutes les écritures au 31/12/N\n3. Présentez un tableau récapitulatif des provisions", "solution": {"calcul": "1. Dépréciation stock = 500 000 - 420 000 = 80 000 DH\n2. <PERSON><PERSON><PERSON> de change = (100 000 × 9,8) - (100 000 × 9,5) = 30 000 DH (gain latent)\n3. Provision litige = 150 000 × 60% = 90 000 DH\n4. Provision garantie = 8 000 000 × 2% = 160 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Dépréciation des stocks", "lines": [{"account": "6196", "label": "DEP pour dépréciation de l'actif circulant", "debit": "80 000", "credit": ""}, {"account": "3915", "label": "Provisions pour dépréciation des stocks de produits finis", "debit": "", "credit": "80 000"}]}, {"date": "31/12/N", "description": "Écart de conversion sur créance en devise", "lines": [{"account": "3721", "label": "Écarts de conversion Actif", "debit": "", "credit": "30 000"}, {"account": "4741", "label": "Augmentation des créances circulantes", "debit": "30 000", "credit": ""}]}, {"date": "31/12/N", "description": "Provision pour litige social", "lines": [{"account": "6195", "label": "DEP pour risques et charges", "debit": "90 000", "credit": ""}, {"account": "1511", "label": "Provisions pour litiges", "debit": "", "credit": "90 000"}]}, {"date": "31/12/N", "description": "Provision pour garantie produits", "lines": [{"account": "6195", "label": "DEP pour risques et charges", "debit": "160 000", "credit": ""}, {"account": "1512", "label": "Provisions pour garanties données aux clients", "debit": "", "credit": "160 000"}]}]}, {"nom": "Journal de Banque", "code": "BQ", "ecritures": [{"date": "31/12/N+1", "description": "Règlements", "lines": [{"account": "5141", "label": "Banques", "debit": "60 000", "credit": ""}, {"account": "3421", "label": "Clients douteux ou litigieux", "debit": "", "credit": "60 000"}, {"account": "6188", "label": "Autres charges externes", "debit": "80 000", "credit": ""}, {"account": "5141", "label": "Banques", "debit": "", "credit": "80 000"}]}]}], "tableau_recapitulatif": {"headers": ["Nature", "Base", "<PERSON><PERSON>", "<PERSON><PERSON>"], "rows": [["Dépréciation stocks", "500 000", "16%", "80 000"], ["<PERSON>cart de <PERSON>", "950 000", "3,16%", "30 000"], ["Litige social", "150 000", "60%", "90 000"], ["Garantie produits", "8 000 000", "2%", "160 000"]]}}}, {"title": "Exercice 9 - Suivi des Provisions sur Plusieurs Exercices", "description": "La société SIGMA présente les situations suivantes sur 3 exercices:\n\nAnnée N:\n1. Acquisition de titres de participation pour 800 000 DH\n2. Créance client douteux de 250 000 DH (chance de recouvrement 30%)\n3. Litige avec un salarié, risque estimé à 100 000 DH (probabilité 70%)\n\nAnnée N+1:\n1. Valeur d'utilité des titres: 600 000 DH\n2. Le client douteux paie 60 000 DH\n3. Le litige est jugé pour 80 000 DH et payé\n\nAnnée N+2:\n1. Valeur d'utilité des titres: 700 000 DH\n2. Le client douteux est déclaré en faillite\n3. Nouveau litige commercial, risque 180 000 DH (probabilité 90%)", "difficulty": "Expert", "question": "1. <PERSON><PERSON><PERSON> et comptabilisez toutes les provisions nécessaires pour chaque année\n2. Présentez un tableau de suivi des provisions\n3. Analysez l'évolution de la situation", "solution": {"calcul": "Année N:\n- Titres: Pas de provision car acquisition dans l'année\n- Client: 250 000 × (1-0.3) = 175 000 DH\n- Litige: 100 000 × 0.7 = 70 000 DH\n\nAnnée N+1:\n- Titres: 800 000 - 600 000 = 200 000 DH\n- Client: Reprise de 41 000 DH (175 000 - (250 000 - 60 000) × (1-0.3))\n- Litige: Reprise totale de 70 000 DH\n\nAnnée N+2:\n- Titres: Reprise de 100 000 DH (200 000 - (800 000 - 700 000))\n- Client: Provision totale de 190 000 DH\n- Nouveau litige: 180 000 × 0.9 = 162 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Dotations aux provisions initiales", "lines": [{"account": "6196", "label": "DEP pour dépréciation de l'actif circulant", "debit": "175 000", "credit": ""}, {"account": "3942", "label": "Provisions pour dépréciation des clients", "debit": "", "credit": "175 000"}, {"account": "6195", "label": "DEP pour risques et charges", "debit": "70 000", "credit": ""}, {"account": "1511", "label": "Provisions pour litiges", "debit": "", "credit": "70 000"}]}, {"date": "31/12/N+1", "description": "Dotations et reprises année N+1", "lines": [{"account": "6194", "label": "DEP pour dépréciation des immobilisations financières", "debit": "200 000", "credit": ""}, {"account": "2951", "label": "Provisions pour dépréciation des titres de participation", "debit": "", "credit": "200 000"}, {"account": "3942", "label": "Provisions pour dépréciation des clients", "debit": "41 000", "credit": ""}, {"account": "7196", "label": "Reprises sur provisions pour dépréciation de l'actif circulant", "debit": "", "credit": "41 000"}, {"account": "1511", "label": "Provisions pour litiges", "debit": "70 000", "credit": ""}, {"account": "7195", "label": "Reprises sur provisions pour risques et charges", "debit": "", "credit": "70 000"}]}, {"date": "31/12/N+2", "description": "Dotations et reprises année N+2", "lines": [{"account": "2951", "label": "Provisions pour dépréciation des titres de participation", "debit": "100 000", "credit": ""}, {"account": "7194", "label": "Reprises sur provisions pour dépréciation des immobilisations financières", "debit": "", "credit": "100 000"}, {"account": "6196", "label": "DEP pour dépréciation de l'actif circulant", "debit": "190 000", "credit": ""}, {"account": "3942", "label": "Provisions pour dépréciation des clients", "debit": "", "credit": "190 000"}, {"account": "6195", "label": "DEP pour risques et charges", "debit": "162 000", "credit": ""}, {"account": "1511", "label": "Provisions pour litiges", "debit": "", "credit": "162 000"}]}]}, {"nom": "Journal de Banque", "code": "BQ", "ecritures": [{"date": "31/12/N+1", "description": "Règlements", "lines": [{"account": "5141", "label": "Banques", "debit": "60 000", "credit": ""}, {"account": "3421", "label": "Clients douteux ou litigieux", "debit": "", "credit": "60 000"}, {"account": "6188", "label": "Autres charges externes", "debit": "80 000", "credit": ""}, {"account": "5141", "label": "Banques", "debit": "", "credit": "80 000"}]}]}], "tableau_suivi": "Nature          |    N     |   N+1    |   N+2    |\n----------------|----------|----------|----------|\nTitres          |     0     | 200 000  | 100 000  |\nCréances        | 175 000   | 134 000  | 190 000  |\nLitiges         |  70 000   |    0     | 162 000  |\n----------------|----------|----------|----------|\nTotal provisions| 245 000   | 334 000  | 452 000  |", "analyse": "L'entreprise montre une dégradation progressive de sa situation:\n1. Les titres se redressent partiellement en N+2\n2. La situation du client douteux s'aggrave jusqu'à la perte totale\n3. Les litiges se succèdent avec des montants croissants\nLe montant total des provisions augmente chaque année, ce qui nécessite une attention particulière de la direction."}}, {"title": "Exercice 10 - Provisions et Opérations en Devises", "description": "La société IMPORT-EXPORT présente au 31/12/N les opérations suivantes:\n\n1. Créances en USD:\n- Client A: 50 000 USD (cours initial 9,8, cours clôture 10,2)\n- Client B: 30 000 USD (cours initial 10,0, cours clôture 9,7)\n\n2. Dettes en EUR:\n- Fournisseur X: 40 000 EUR (cours initial 10,5, cours clôture 10,8)\n- Fournisseur Y: 25 000 EUR (cours initial 10,7, cours clôture 10,4)\n\n3. Stock de marchandises importées:\n- Lot 1: Coût 300 000 DH, valeur actuelle 250 000 DH\n- Lot 2: Coût 200 000 DH, valeur actuelle 220 000 DH\n\nAu 31/12/N+1:\n- Client A règle sa dette (cours 10,1)\n- Fournisseur X est payé (cours 10,9)\n- La valeur du stock Lot 1 remonte à 280 000 DH", "difficulty": "Expert", "question": "1. <PERSON><PERSON>z tous les écarts de change et provisions nécessaires au 31/12/N\n2. Comptabilisez toutes les écritures au 31/12/N\n3. Comptabilisez les opérations de N+1\n4. Présentez un tableau de synthèse des impacts sur le résultat", "solution": {"calcul": "Écarts de change au 31/12/N:\n1. Client A: (50 000 × 10,2) - (50 000 × 9,8) = 20 000 DH (gain latent)\n2. Client B: (30 000 × 9,7) - (30 000 × 10,0) = -9 000 DH (perte latente)\n3. Fournisseur X: (40 000 × 10,8) - (40 000 × 10,5) = 12 000 DH (perte latente)\n4. Fournisseur Y: (25 000 × 10,4) - (25 000 × 10,7) = -7 500 DH (gain latent)\n\nProvisions stocks:\n1. Lot 1: 300 000 - 250 000 = 50 000 DH\n2. Lot 2: Aucune provision (plus-value latente)\n\nOpérations N+1:\n1. Client A: Perte réalisée = (50 000 × 10,1) - (50 000 × 10,2) = -5 000 DH\n2. Fournisseur X: Perte réalisée = (40 000 × 10,9) - (40 000 × 10,8) = 4 000 DH\n3. Reprise stock: 50 000 - (300 000 - 280 000) = 30 000 DH", "journaux": [{"nom": "Journal des Opérations Diverses", "code": "OD", "ecritures": [{"date": "31/12/N", "description": "Écarts de conversion et provisions", "lines": [{"account": "3721", "label": "Écarts de conversion Actif", "debit": "9 000", "credit": ""}, {"account": "4741", "label": "Augmentation des créances circulantes", "debit": "", "credit": "20 000"}, {"account": "4741", "label": "Diminution des dettes circulantes", "debit": "", "credit": "7 500"}, {"account": "3722", "label": "Écarts de conversion Passif", "debit": "", "credit": "12 000"}, {"account": "6196", "label": "DEP pour dépréciation de l'actif circulant", "debit": "50 000", "credit": ""}, {"account": "3915", "label": "Provisions pour dépréciation des stocks", "debit": "", "credit": "50 000"}]}, {"date": "31/12/N+1", "description": "Reprise sur provision stock", "lines": [{"account": "3915", "label": "Provisions pour dépréciation des stocks", "debit": "30 000", "credit": ""}, {"account": "7196", "label": "Reprises sur provisions pour dépréciation de l'actif circulant", "debit": "", "credit": "30 000"}]}]}, {"nom": "Journal de Banque", "code": "BQ", "ecritures": [{"date": "31/12/N+1", "description": "Règlement client A", "lines": [{"account": "5141", "label": "Banques", "debit": "505 000", "credit": ""}, {"account": "4411", "label": "Clients - ventes de biens ou services", "debit": "", "credit": "510 000"}, {"account": "6385", "label": "Pertes de change", "debit": "5 000", "credit": ""}]}, {"date": "31/12/N+1", "description": "Règlement fournisseur X", "lines": [{"account": "4411", "label": "Fournisseurs", "debit": "432 000", "credit": ""}, {"account": "5141", "label": "Banques", "debit": "", "credit": "436 000"}, {"account": "6385", "label": "Pertes de change", "debit": "4 000", "credit": ""}]}]}], "tableau_synthese": "Élément                |  Impact N  | Impact N+1 |   Total   |\n----------------------|------------|------------|-----------||\nÉcarts change latents |    6 500   |   -6 500   |     0     |\nÉcarts change réalisés|      0     |   -9 000   |  -9 000   |\nProvisions stocks     |  -50 000   |   30 000   | -20 000   |\n----------------------|------------|------------|-----------||\nImpact total résultat |  -43 500   |   14 500   | -29 000   |"}}]}