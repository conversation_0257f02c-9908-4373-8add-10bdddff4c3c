import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:moroccanaccounting/services/offline_cache_service.dart';

// Generate mocks
@GenerateMocks([
  Connectivity,
  Directory,
  File,
])
import 'offline_cache_service_test.mocks.dart';

void main() {
  group('OfflineCacheService', () {
    late OfflineCacheService cacheService;
    late MockConnectivity mockConnectivity;
    late MockDirectory mockCacheDirectory;
    late MockFile mockFile;
    late Directory tempDir;

    setUpAll(() async {
      // Initialize Hive for testing
      await Hive.initFlutter();
      
      // Register adapters
      if (!Hive.isAdapterRegistered(121)) {
        Hive.registerAdapter(CacheEntryAdapter());
      }
    });

    setUp(() async {
      // Create temporary directory for testing
      tempDir = await Directory.systemTemp.createTemp('cache_test_');
      
      // Initialize mocks
      mockConnectivity = MockConnectivity();
      mockCacheDirectory = MockDirectory();
      mockFile = MockFile();
      
      // Setup mock behaviors
      when(mockCacheDirectory.path).thenReturn(tempDir.path);
      when(mockCacheDirectory.exists()).thenAnswer((_) async => true);
      when(mockCacheDirectory.create(recursive: true)).thenAnswer((_) async => mockCacheDirectory);
      
      // Initialize cache service
      cacheService = OfflineCacheService();
    });

    tearDown(() async {
      // Clean up
      await cacheService.dispose();
      await tempDir.delete(recursive: true);
      
      // Close any open boxes
      try {
        await Hive.box<CacheEntry>('offline_cache_metadata').close();
      } catch (_) {}
      try {
        await Hive.box('cache_statistics').close();
      } catch (_) {}
      
      // Delete boxes
      await Hive.deleteBoxFromDisk('offline_cache_metadata');
      await Hive.deleteBoxFromDisk('cache_statistics');
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        await cacheService.initialize();
        
        // Verify cache directory exists
        expect(await Directory('${tempDir.path}/offline_cache').exists(), isTrue);
      });

      test('should handle initialization errors gracefully', () async {
        // Create a scenario where initialization might fail
        final invalidDir = Directory('/invalid/path/that/does/not/exist');
        
        // This should not throw but handle the error
        expect(() async => await cacheService.initialize(), returnsNormally);
      });

      test('should create cache directory if it does not exist', () async {
        final cacheDir = Directory('${tempDir.path}/offline_cache');
        if (await cacheDir.exists()) {
          await cacheDir.delete(recursive: true);
        }
        
        await cacheService.initialize();
        
        expect(await cacheDir.exists(), isTrue);
      });
    });

    group('Content Caching', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should cache guide content successfully', () async {
        // Mock asset loading
        const mockContent = '{"title": "Test Guide", "content": "Test content"}';
        
        // Mock the rootBundle.loadString call
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        final result = await cacheService.cacheGuideContent('test_guide');
        
        expect(result, isTrue);
        expect(cacheService.isCacheValid('guide_test_guide'), isTrue);
      });

      test('should cache quiz data successfully', () async {
        final result = await cacheService.cacheQuizData();
        
        expect(result, isTrue);
        expect(cacheService.isCacheValid('quiz_data'), isTrue);
      });

      test('should handle caching errors gracefully', () async {
        // Test with invalid guide ID
        final result = await cacheService.cacheGuideContent('invalid_guide');
        
        // Should handle error without throwing
        expect(result, isFalse);
      });

      test('should compress content when caching', () async {
        final mockContent = '{"title": "Test Guide", "content": "${'x' * 1000}"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        await cacheService.cacheGuideContent('large_guide');
        
        final stats = await cacheService.getCacheStatistics();
        expect(stats.compressedEntries, greaterThan(0));
        expect(stats.compressionRatio, lessThan(1.0));
      });
    });

    group('Cache Retrieval', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should retrieve cached content successfully', () async {
        const mockContent = '{"title": "Test Guide", "content": "Test content"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        await cacheService.cacheGuideContent('test_guide');
        
        final retrievedContent = await cacheService.getCachedContent('guide_test_guide');
        
        expect(retrievedContent, equals(mockContent));
      });

      test('should return null for non-existent content', () async {
        final content = await cacheService.getCachedContent('non_existent_key');
        
        expect(content, isNull);
      });

      test('should return null for expired content', () async {
        // Create an expired cache entry manually
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        final expiredEntry = CacheEntry(
          key: 'expired_content',
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          lastAccessed: DateTime.now().subtract(const Duration(days: 10)),
          expiresAt: DateTime.now().subtract(const Duration(days: 1)),
          sizeBytes: 100,
          contentType: 'application/json',
        );
        
        await box.put('expired_content', expiredEntry);
        
        final content = await cacheService.getCachedContent('expired_content');
        
        expect(content, isNull);
      });

      test('should update last accessed time when retrieving content', () async {
        const mockContent = '{"title": "Test Guide"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        await cacheService.cacheGuideContent('test_guide');
        
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        final originalEntry = box.get('guide_test_guide');
        final originalAccessTime = originalEntry!.lastAccessed;
        
        // Wait a bit to ensure time difference
        await Future.delayed(const Duration(milliseconds: 10));
        
        await cacheService.getCachedContent('guide_test_guide');
        
        final updatedEntry = box.get('guide_test_guide');
        expect(updatedEntry!.lastAccessed.isAfter(originalAccessTime), isTrue);
      });
    });

    group('Cache Validation', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should validate cache entries correctly', () async {
        const mockContent = '{"title": "Test Guide"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        await cacheService.cacheGuideContent('test_guide');
        
        expect(cacheService.isCacheValid('guide_test_guide'), isTrue);
        expect(cacheService.isCacheValid('non_existent_key'), isFalse);
      });

      test('should identify expired entries as invalid', () async {
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        final expiredEntry = CacheEntry(
          key: 'expired_content',
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          lastAccessed: DateTime.now().subtract(const Duration(days: 10)),
          expiresAt: DateTime.now().subtract(const Duration(days: 1)),
          sizeBytes: 100,
          contentType: 'application/json',
        );
        
        await box.put('expired_content', expiredEntry);
        
        expect(cacheService.isCacheValid('expired_content'), isFalse);
      });
    });

    group('Cache Expiration and Cleanup', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should clear expired cache entries', () async {
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        
        // Add expired entry
        final expiredEntry = CacheEntry(
          key: 'expired_content',
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          lastAccessed: DateTime.now().subtract(const Duration(days: 10)),
          expiresAt: DateTime.now().subtract(const Duration(days: 1)),
          sizeBytes: 100,
          contentType: 'application/json',
        );
        
        // Add valid entry
        final validEntry = CacheEntry(
          key: 'valid_content',
          createdAt: DateTime.now(),
          lastAccessed: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 7)),
          sizeBytes: 100,
          contentType: 'application/json',
        );
        
        await box.put('expired_content', expiredEntry);
        await box.put('valid_content', validEntry);
        
        final removedCount = await cacheService.clearExpiredCache();
        
        expect(removedCount, equals(1));
        expect(box.containsKey('expired_content'), isFalse);
        expect(box.containsKey('valid_content'), isTrue);
      });

      test('should clear all cache when requested', () async {
        const mockContent = '{"title": "Test Guide"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        await cacheService.cacheGuideContent('test_guide');
        
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        expect(box.length, greaterThan(0));
        
        await cacheService.clearCache();
        
        expect(box.length, equals(0));
      });
    });

    group('Cache Size Management', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should calculate cache size correctly', () async {
        const mockContent = '{"title": "Test Guide", "content": "Test content"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        await cacheService.cacheGuideContent('test_guide');
        
        final cacheSize = await cacheService.getCacheSize();
        
        expect(cacheSize, greaterThan(0));
      });

      test('should optimize cache when size limit exceeded', () async {
        // Create multiple cache entries to simulate large cache
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        
        for (int i = 0; i < 10; i++) {
          final entry = CacheEntry(
            key: 'content_$i',
            createdAt: DateTime.now().subtract(Duration(days: i)),
            lastAccessed: DateTime.now().subtract(Duration(days: i)),
            expiresAt: DateTime.now().add(const Duration(days: 7)),
            sizeBytes: 50 * 1024 * 1024, // 50MB each
            contentType: 'application/json',
          );
          
          await box.put('content_$i', entry);
        }
        
        final initialSize = await cacheService.getCacheSize();
        
        await cacheService.optimizeCache();
        
        final optimizedSize = await cacheService.getCacheSize();
        
        expect(optimizedSize, lessThan(initialSize));
      });

      test('should preserve essential content during optimization', () async {
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        
        // Add essential content
        final essentialEntry = CacheEntry(
          key: 'guide_ir',
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          lastAccessed: DateTime.now().subtract(const Duration(days: 10)),
          expiresAt: DateTime.now().add(const Duration(days: 7)),
          sizeBytes: 100 * 1024 * 1024, // 100MB
          contentType: 'application/json',
        );
        
        // Add non-essential content
        final nonEssentialEntry = CacheEntry(
          key: 'other_content',
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          lastAccessed: DateTime.now().subtract(const Duration(days: 10)),
          expiresAt: DateTime.now().add(const Duration(days: 7)),
          sizeBytes: 100 * 1024 * 1024, // 100MB
          contentType: 'application/json',
        );
        
        await box.put('guide_ir', essentialEntry);
        await box.put('other_content', nonEssentialEntry);
        
        await cacheService.optimizeCache();
        
        expect(box.containsKey('guide_ir'), isTrue);
      });
    });

    group('Cache Statistics', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should provide accurate cache statistics', () async {
        const mockContent = '{"title": "Test Guide"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        await cacheService.cacheGuideContent('test_guide');
        
        final stats = await cacheService.getCacheStatistics();
        
        expect(stats.totalEntries, greaterThan(0));
        expect(stats.totalSizeBytes, greaterThan(0));
        expect(stats.contentTypeCounts, isNotEmpty);
        expect(stats.formattedSize, isNotEmpty);
      });

      test('should track hit rate correctly', () async {
        const mockContent = '{"title": "Test Guide"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        await cacheService.cacheGuideContent('test_guide');
        
        // Generate cache hits
        await cacheService.getCachedContent('guide_test_guide');
        await cacheService.getCachedContent('guide_test_guide');
        
        // Generate cache miss
        await cacheService.getCachedContent('non_existent_key');
        
        final stats = await cacheService.getCacheStatistics();
        
        expect(stats.hitRate, greaterThan(0.0));
        expect(stats.hitRate, lessThanOrEqualTo(1.0));
      });

      test('should format cache size correctly', () async {
        final stats = CacheStatistics(
          totalEntries: 1,
          totalSizeBytes: 1024,
          expiredEntries: 0,
          compressedEntries: 0,
          hitRate: 0.0,
          compressionRatio: 1.0,
          contentTypeCounts: {},
          lastOptimization: DateTime.now(),
        );
        
        expect(stats.formattedSize, equals('1.0 KB'));
        
        final largeSizeStats = CacheStatistics(
          totalEntries: 1,
          totalSizeBytes: 1024 * 1024,
          expiredEntries: 0,
          compressedEntries: 0,
          hitRate: 0.0,
          compressionRatio: 1.0,
          contentTypeCounts: {},
          lastOptimization: DateTime.now(),
        );
        
        expect(largeSizeStats.formattedSize, equals('1.0 MB'));
      });
    });

    group('Essential Content Preloading', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should preload essential content', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return '{"title": "Essential Content"}';
            }
            return null;
          },
        );

        await cacheService.preloadEssentialContent();
        
        // Verify that essential content is cached
        final stats = await cacheService.getCacheStatistics();
        expect(stats.totalEntries, greaterThan(0));
      });

      test('should handle preloading errors gracefully', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            throw PlatformException(code: 'ASSET_NOT_FOUND', message: 'Asset not found');
          },
        );

        // Should not throw exception
        expect(() async => await cacheService.preloadEssentialContent(), returnsNormally);
      });
    });

    group('Network Connectivity', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should detect online status correctly', () async {
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.wifi);
        
        // Note: This test would need dependency injection to work properly
        // For now, we test that the method doesn't throw
        expect(() async => await cacheService.isOnline(), returnsNormally);
      });

      test('should detect offline status correctly', () async {
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => ConnectivityResult.none);
        
        // Note: This test would need dependency injection to work properly
        // For now, we test that the method doesn't throw
        expect(() async => await cacheService.isOnline(), returnsNormally);
      });
    });

    group('Error Handling', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should handle file system errors gracefully', () async {
        // Test with invalid file path
        final content = await cacheService.getCachedContent('invalid/path/key');
        
        expect(content, isNull);
      });

      test('should handle Hive box errors gracefully', () async {
        // Close the box to simulate error
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        await box.close();
        
        // Operations should handle the error gracefully
        expect(() async => await cacheService.getCacheSize(), returnsNormally);
      });

      test('should handle compression errors gracefully', () async {
        // Test with malformed data
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            return null; // Return null to simulate error
          },
        );

        final result = await cacheService.cacheGuideContent('invalid_guide');
        
        expect(result, isFalse);
      });
    });

    group('Performance Tests', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should handle large cache operations efficiently', () async {
        final stopwatch = Stopwatch()..start();
        
        // Create multiple cache entries
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        
        for (int i = 0; i < 100; i++) {
          final entry = CacheEntry(
            key: 'content_$i',
            createdAt: DateTime.now(),
            lastAccessed: DateTime.now(),
            expiresAt: DateTime.now().add(const Duration(days: 7)),
            sizeBytes: 1024,
            contentType: 'application/json',
          );
          
          await box.put('content_$i', entry);
        }
        
        stopwatch.stop();
        
        // Should complete within reasonable time (adjust threshold as needed)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });

      test('should handle concurrent cache operations', () async {
        const mockContent = '{"title": "Test Guide"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        // Perform concurrent operations
        final futures = <Future>[];
        
        for (int i = 0; i < 10; i++) {
          futures.add(cacheService.cacheGuideContent('guide_$i'));
        }
        
        final results = await Future.wait(futures);
        
        // All operations should complete successfully
        expect(results.every((result) => result == true), isTrue);
      });

      test('should optimize cache efficiently', () async {
        // Create large cache
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        
        for (int i = 0; i < 50; i++) {
          final entry = CacheEntry(
            key: 'content_$i',
            createdAt: DateTime.now().subtract(Duration(days: i)),
            lastAccessed: DateTime.now().subtract(Duration(days: i)),
            expiresAt: DateTime.now().add(const Duration(days: 7)),
            sizeBytes: 10 * 1024 * 1024, // 10MB each
            contentType: 'application/json',
          );
          
          await box.put('content_$i', entry);
        }
        
        final stopwatch = Stopwatch()..start();
        
        await cacheService.optimizeCache();
        
        stopwatch.stop();
        
        // Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
      });
    });

    group('Cache Integrity', () {
      setUp(() async {
        await cacheService.initialize();
      });

      test('should maintain data consistency across operations', () async {
        const mockContent = '{"title": "Test Guide", "content": "Test content"}';
        
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return mockContent;
            }
            return null;
          },
        );

        // Cache content
        await cacheService.cacheGuideContent('test_guide');
        
        // Retrieve content
        final retrievedContent = await cacheService.getCachedContent('guide_test_guide');
        
        // Content should match exactly
        expect(retrievedContent, equals(mockContent));
        
        // Perform optimization
        await cacheService.optimizeCache();
        
        // Content should still be retrievable and match
        final contentAfterOptimization = await cacheService.getCachedContent('guide_test_guide');
        expect(contentAfterOptimization, equals(mockContent));
      });

      test('should handle cache corruption gracefully', () async {
        final box = Hive.box<CacheEntry>('offline_cache_metadata');
        
        // Create a cache entry with invalid metadata
        final corruptEntry = CacheEntry(
          key: 'corrupt_content',
          createdAt: DateTime.now(),
          lastAccessed: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 7)),
          sizeBytes: -1, // Invalid size
          contentType: 'application/json',
        );
        
        await box.put('corrupt_content', corruptEntry);
        
        // Operations should handle corruption gracefully
        expect(() async => await cacheService.getCacheStatistics(), returnsNormally);
        expect(() async => await cacheService.getCacheSize(), returnsNormally);
      });
    });

    group('Cache Entry Model', () {
      test('should create cache entry with correct properties', () {
        final now = DateTime.now();
        final entry = CacheEntry(
          key: 'test_key',
          createdAt: now,
          lastAccessed: now,
          expiresAt: now.add(const Duration(days: 7)),
          sizeBytes: 1024,
          contentType: 'application/json',
          isCompressed: true,
          etag: 'test_etag',
          metadata: {'test': 'value'},
        );
        
        expect(entry.key, equals('test_key'));
        expect(entry.sizeBytes, equals(1024));
        expect(entry.contentType, equals('application/json'));
        expect(entry.isCompressed, isTrue);
        expect(entry.etag, equals('test_etag'));
        expect(entry.metadata?['test'], equals('value'));
      });

      test('should correctly identify expired entries', () {
        final expiredEntry = CacheEntry(
          key: 'expired',
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          lastAccessed: DateTime.now().subtract(const Duration(days: 10)),
          expiresAt: DateTime.now().subtract(const Duration(days: 1)),
          sizeBytes: 1024,
          contentType: 'application/json',
        );
        
        expect(expiredEntry.isExpired, isTrue);
        expect(expiredEntry.isValid, isFalse);
      });

      test('should correctly identify valid entries', () {
        final validEntry = CacheEntry(
          key: 'valid',
          createdAt: DateTime.now(),
          lastAccessed: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 7)),
          sizeBytes: 1024,
          contentType: 'application/json',
        );
        
        expect(validEntry.isExpired, isFalse);
        expect(validEntry.isValid, isTrue);
      });

      test('should create copy with updated properties', () {
        final original = CacheEntry(
          key: 'test',
          createdAt: DateTime.now(),
          lastAccessed: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 7)),
          sizeBytes: 1024,
          contentType: 'application/json',
        );
        
        final newAccessTime = DateTime.now().add(const Duration(hours: 1));
        final copy = original.copyWith(lastAccessed: newAccessTime);
        
        expect(copy.key, equals(original.key));
        expect(copy.lastAccessed, equals(newAccessTime));
        expect(copy.sizeBytes, equals(original.sizeBytes));
      });
    });

    group('Disposal and Cleanup', () {
      test('should dispose resources properly', () async {
        await cacheService.initialize();
        
        // Should not throw when disposing
        expect(() async => await cacheService.dispose(), returnsNormally);
      });

      test('should handle multiple dispose calls gracefully', () async {
        await cacheService.initialize();
        
        await cacheService.dispose();
        
        // Second dispose should not throw
        expect(() async => await cacheService.dispose(), returnsNormally);
      });
    });
  });
}

// Adapter for CacheEntry (would normally be generated)
class CacheEntryAdapter extends TypeAdapter<CacheEntry> {
  @override
  final int typeId = 121;

  @override
  CacheEntry read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CacheEntry(
      key: fields[0] as String,
      createdAt: fields[1] as DateTime,
      lastAccessed: fields[2] as DateTime,
      expiresAt: fields[3] as DateTime,
      sizeBytes: fields[4] as int,
      contentType: fields[5] as String,
      isCompressed: fields[6] as bool? ?? false,
      etag: fields[7] as String?,
      metadata: fields[8] as Map<String, dynamic>?,
    );
  }

  @override
  void write(BinaryWriter writer, CacheEntry obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.key)
      ..writeByte(1)
      ..write(obj.createdAt)
      ..writeByte(2)
      ..write(obj.lastAccessed)
      ..writeByte(3)
      ..write(obj.expiresAt)
      ..writeByte(4)
      ..write(obj.sizeBytes)
      ..writeByte(5)
      ..write(obj.contentType)
      ..writeByte(6)
      ..write(obj.isCompressed)
      ..writeByte(7)
      ..write(obj.etag)
      ..writeByte(8)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CacheEntryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}