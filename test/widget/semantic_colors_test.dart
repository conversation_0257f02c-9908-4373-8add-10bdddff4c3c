import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/theme/semantic_colors.dart';

void main() {
  group('SemanticColors Tests', () {
    test('Light semantic colors have correct values', () {
      const colors = SemanticColors.light;
      
      // Success colors
      expect(colors.success, const Color(0xFF059669));
      expect(colors.successContainer, const Color(0xFFD1FAE5));
      expect(colors.onSuccess, const Color(0xFFFFFFFF));
      expect(colors.onSuccessContainer, const Color(0xFF064E3B));
      
      // Warning colors
      expect(colors.warning, const Color(0xFFD97706));
      expect(colors.warningContainer, const Color(0xFFFEF3C7));
      expect(colors.onWarning, const Color(0xFFFFFFFF));
      expect(colors.onWarningContainer, const Color(0xFF92400E));
      
      // Info colors
      expect(colors.info, const Color(0xFF0284C7));
      expect(colors.infoContainer, const Color(0xFFE0F2FE));
      expect(colors.onInfo, const Color(0xFFFFFFFF));
      expect(colors.onInfoContainer, const Color(0xFF0C4A6E));
      
      // Error variant colors
      expect(colors.errorVariant, const Color(0xFFDC2626));
      expect(colors.errorContainer, const Color(0xFFFEE2E2));
      expect(colors.onErrorVariant, const Color(0xFFFFFFFF));
      expect(colors.onErrorContainer, const Color(0xFF991B1B));
      
      // Financial colors
      expect(colors.positive, const Color(0xFF059669));
      expect(colors.negative, const Color(0xFFDC2626));
      expect(colors.neutral, const Color(0xFF6B7280));
    });

    test('Dark semantic colors have correct values', () {
      const colors = SemanticColors.dark;
      
      // Success colors
      expect(colors.success, const Color(0xFF34D399));
      expect(colors.successContainer, const Color(0xFF064E3B));
      expect(colors.onSuccess, const Color(0xFF000000));
      expect(colors.onSuccessContainer, const Color(0xFFD1FAE5));
      
      // Warning colors
      expect(colors.warning, const Color(0xFFFBBF24));
      expect(colors.warningContainer, const Color(0xFF92400E));
      expect(colors.onWarning, const Color(0xFF000000));
      expect(colors.onWarningContainer, const Color(0xFFFEF3C7));
      
      // Info colors
      expect(colors.info, const Color(0xFF38BDF8));
      expect(colors.infoContainer, const Color(0xFF0C4A6E));
      expect(colors.onInfo, const Color(0xFF000000));
      expect(colors.onInfoContainer, const Color(0xFFE0F2FE));
      
      // Error variant colors
      expect(colors.errorVariant, const Color(0xFFF87171));
      expect(colors.errorContainer, const Color(0xFF991B1B));
      expect(colors.onErrorVariant, const Color(0xFF000000));
      expect(colors.onErrorContainer, const Color(0xFFFEE2E2));
      
      // Financial colors
      expect(colors.positive, const Color(0xFF34D399));
      expect(colors.negative, const Color(0xFFF87171));
      expect(colors.neutral, const Color(0xFF9CA3AF));
    });

    test('Semantic colors copyWith works correctly', () {
      const original = SemanticColors.light;
      final modified = original.copyWith(
        success: const Color(0xFF00FF00),
        warning: const Color(0xFFFF8800),
      );
      
      expect(modified.success, const Color(0xFF00FF00));
      expect(modified.warning, const Color(0xFFFF8800));
      // Other colors should remain unchanged
      expect(modified.info, original.info);
      expect(modified.errorVariant, original.errorVariant);
    });

    test('Semantic colors lerp works correctly', () {
      const light = SemanticColors.light;
      const dark = SemanticColors.dark;
      
      final interpolated = light.lerp(dark, 0.5);
      
      // Should be halfway between light and dark colors
      expect(interpolated.success, Color.lerp(light.success, dark.success, 0.5));
      expect(interpolated.warning, Color.lerp(light.warning, dark.warning, 0.5));
      expect(interpolated.info, Color.lerp(light.info, dark.info, 0.5));
      expect(interpolated.errorVariant, Color.lerp(light.errorVariant, dark.errorVariant, 0.5));
    });

    test('Semantic colors lerp with non-SemanticColors returns original', () {
      const colors = SemanticColors.light;
      final result = colors.lerp(null, 0.5);
      
      expect(result, colors);
    });

    testWidgets('SemanticColors extension works in context', (WidgetTester tester) async {
      late SemanticColors capturedColors;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: const [SemanticColors.light],
          ),
          home: Builder(
            builder: (context) {
              capturedColors = context.semanticColors;
              return const Scaffold(
                body: Text('Test'),
              );
            },
          ),
        ),
      );
      
      expect(capturedColors.success, const Color(0xFF059669));
      expect(capturedColors.warning, const Color(0xFFD97706));
      expect(capturedColors.info, const Color(0xFF0284C7));
      expect(capturedColors.errorVariant, const Color(0xFFDC2626));
    });

    group('WCAG Contrast Tests', () {
      test('Success colors meet contrast requirements', () {
        const colors = SemanticColors.light;
        
        // Test contrast between success and onSuccess
        final successContrast = _calculateContrast(colors.success, colors.onSuccess);
        expect(successContrast, greaterThan(4.5)); // WCAG AA requirement
        
        // Test contrast between successContainer and onSuccessContainer
        final containerContrast = _calculateContrast(
          colors.successContainer, 
          colors.onSuccessContainer,
        );
        expect(containerContrast, greaterThan(4.5));
      });

      test('Warning colors meet contrast requirements', () {
        const colors = SemanticColors.light;
        
        final warningContrast = _calculateContrast(colors.warning, colors.onWarning);
        expect(warningContrast, greaterThan(4.5));
        
        final containerContrast = _calculateContrast(
          colors.warningContainer, 
          colors.onWarningContainer,
        );
        expect(containerContrast, greaterThan(4.5));
      });

      test('Info colors meet contrast requirements', () {
        const colors = SemanticColors.light;
        
        final infoContrast = _calculateContrast(colors.info, colors.onInfo);
        expect(infoContrast, greaterThan(4.5));
        
        final containerContrast = _calculateContrast(
          colors.infoContainer, 
          colors.onInfoContainer,
        );
        expect(containerContrast, greaterThan(4.5));
      });

      test('Error colors meet contrast requirements', () {
        const colors = SemanticColors.light;
        
        final errorContrast = _calculateContrast(colors.errorVariant, colors.onErrorVariant);
        expect(errorContrast, greaterThan(4.5));
        
        final containerContrast = _calculateContrast(
          colors.errorContainer, 
          colors.onErrorContainer,
        );
        expect(containerContrast, greaterThan(4.5));
      });

      test('Financial colors meet contrast requirements', () {
        const colors = SemanticColors.light;
        
        final positiveContrast = _calculateContrast(colors.positive, colors.onPositive);
        expect(positiveContrast, greaterThan(4.5));
        
        final negativeContrast = _calculateContrast(colors.negative, colors.onNegative);
        expect(negativeContrast, greaterThan(4.5));
        
        final neutralContrast = _calculateContrast(colors.neutral, colors.onNeutral);
        expect(neutralContrast, greaterThan(4.5));
      });
    });
  });
}

/// Calculate contrast ratio between two colors
/// Based on WCAG 2.1 guidelines
double _calculateContrast(Color color1, Color color2) {
  final luminance1 = _calculateLuminance(color1);
  final luminance2 = _calculateLuminance(color2);
  
  final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
  final darker = luminance1 > luminance2 ? luminance2 : luminance1;
  
  return (lighter + 0.05) / (darker + 0.05);
}

/// Calculate relative luminance of a color
double _calculateLuminance(Color color) {
  final r = _linearizeColorComponent(color.red / 255.0);
  final g = _linearizeColorComponent(color.green / 255.0);
  final b = _linearizeColorComponent(color.blue / 255.0);
  
  return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}

/// Linearize color component for luminance calculation
double _linearizeColorComponent(double component) {
  if (component <= 0.03928) {
    return component / 12.92;
  } else {
    return math.pow((component + 0.055) / 1.055, 2.4).toDouble();
  }
}
