import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:moroccanaccounting/services/theme_service.dart';
import 'package:moroccanaccounting/widgets/financial_statements/bilan_widget.dart';
import 'package:moroccanaccounting/widgets/financial_statements/cpc_widget.dart';
import 'package:moroccanaccounting/widgets/financial_statements/balance_widget.dart';
import 'package:moroccanaccounting/widgets/financial_statements/fusion_widget.dart';

void main() {
  group('BilanWidget Tests', () {
    testWidgets('renders correctly with sample data', (WidgetTester tester) async {
      final actif = {
        'Immobilisations': [
          {'rubrique': 'Immobilisations corporelles', 'montant': 1000000.0},
          {'rubrique': 'Immobilisations incorporelles', 'montant': 500000.0},
        ],
      };
      final passif = {
        'Capitaux Propres': [
          {'rubrique': 'Capital social', 'montant': 1200000.0},
          {'rubrique': 'Réserves', 'montant': 300000.0},
        ],
      };

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => ThemeService(),
            child: BilanWidget(
              actif: actif,
              passif: passif,
              title: 'Bilan Test',
              date: DateTime.now(),
            ),
          ),
        ),
      );

      expect(find.text('Bilan Test'), findsOneWidget);
      expect(find.text('Immobilisations corporelles'), findsOneWidget);
      expect(find.text('Capital social'), findsOneWidget);
    });
  });

  group('CPCWidget Tests', () {
    testWidgets('renders correctly with sample data', (WidgetTester tester) async {
      final produits = [
        {'rubrique': 'Ventes de marchandises', 'montant': 800000.0},
        {'rubrique': 'Prestations de services', 'montant': 400000.0},
      ];
      final charges = [
        {'rubrique': 'Achats de marchandises', 'montant': -600000.0},
        {'rubrique': 'Charges de personnel', 'montant': -300000.0},
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => ThemeService(),
            child: CPCWidget(
              produits: produits,
              charges: charges,
              title: 'CPC Test',
              date: DateTime.now(),
            ),
          ),
        ),
      );

      expect(find.text('CPC Test'), findsOneWidget);
      expect(find.text('PRODUITS'), findsOneWidget);
      expect(find.text('CHARGES'), findsOneWidget);
      expect(find.text('Ventes de marchandises'), findsOneWidget);
      expect(find.text('Achats de marchandises'), findsOneWidget);
    });
  });

  group('BalanceWidget Tests', () {
    testWidgets('renders correctly with sample data', (WidgetTester tester) async {
      final comptes = [
        {
          'numero': '5141',
          'intitule': 'Banque',
          'mouvements_debit': 1000000.0,
          'mouvements_credit': 800000.0,
          'solde_debit': 200000.0,
          'solde_credit': 0.0,
        },
        {
          'numero': '4411',
          'intitule': 'Fournisseurs',
          'mouvements_debit': 500000.0,
          'mouvements_credit': 700000.0,
          'solde_debit': 0.0,
          'solde_credit': 200000.0,
        },
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => ThemeService(),
            child: BalanceWidget(
              comptes: comptes,
              title: 'Balance Test',
              date: DateTime.now(),
            ),
          ),
        ),
      );

      expect(find.text('Balance Test'), findsOneWidget);
      expect(find.text('Banque'), findsOneWidget);
      expect(find.text('Fournisseurs'), findsOneWidget);
      expect(find.text('5141'), findsOneWidget);
      expect(find.text('4411'), findsOneWidget);
    });
  });

  group('FusionWidget Tests', () {
    testWidgets('renders correctly with sample data', (WidgetTester tester) async {
      final societeA = {
        'capital': 1000000.0,
        'reserves': 500000.0,
        'plus_value': 200000.0,
        'actions': 1000,
      };
      final societeB = {
        'capital': 800000.0,
        'reserves': 300000.0,
        'plus_value': 100000.0,
        'actions': 800,
      };

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => ThemeService(),
            child: FusionWidget(
              societeA: societeA,
              societeB: societeB,
              type: 'absorption',
              date: DateTime.now(),
            ),
          ),
        ),
      );

      expect(find.text('Fusion ABSORPTION'), findsOneWidget);
      expect(find.text('Société A'), findsOneWidget);
      expect(find.text('Société B'), findsOneWidget);
      expect(find.text('Capital'), findsAtLeast(1));
      expect(find.text('Réserves'), findsAtLeast(1));
    });
  });
}