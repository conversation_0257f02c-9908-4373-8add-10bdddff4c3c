import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/theme/design_tokens.dart';
import 'package:moroccanaccounting/theme/responsive_breakpoints.dart';

void main() {
  group('DesignTokens Tests', () {
    test('Typography scale follows logical progression', () {
      // Font sizes should increase in a logical progression
      expect(DesignTokens.fontSizeXs, 12.0);
      expect(DesignTokens.fontSizeSm, 14.0);
      expect(DesignTokens.fontSizeBase, 16.0);
      expect(DesignTokens.fontSizeLg, 18.0);
      expect(DesignTokens.fontSizeXl, 20.0);
      expect(DesignTokens.fontSize2xl, 24.0);
      expect(DesignTokens.fontSize3xl, 28.0);
      expect(DesignTokens.fontSize4xl, 32.0);
      expect(DesignTokens.fontSize5xl, 36.0);
      expect(DesignTokens.fontSize6xl, 48.0);
      
      // Verify progression is logical
      expect(DesignTokens.fontSizeSm > DesignTokens.fontSizeXs, true);
      expect(DesignTokens.fontSizeBase > DesignTokens.fontSizeSm, true);
      expect(DesignTokens.fontSizeLg > DesignTokens.fontSizeBase, true);
      expect(DesignTokens.fontSizeXl > DesignTokens.fontSizeLg, true);
    });

    test('Spacing scale follows 4px base unit', () {
      // All spacing values should be multiples of 4
      expect(DesignTokens.space1 % 4, 0);
      expect(DesignTokens.space2 % 4, 0);
      expect(DesignTokens.space3 % 4, 0);
      expect(DesignTokens.space4 % 4, 0);
      expect(DesignTokens.space5 % 4, 0);
      expect(DesignTokens.space6 % 4, 0);
      expect(DesignTokens.space8 % 4, 0);
      expect(DesignTokens.space10 % 4, 0);
      expect(DesignTokens.space12 % 4, 0);
      expect(DesignTokens.space16 % 4, 0);
      
      // Verify progression
      expect(DesignTokens.space2 > DesignTokens.space1, true);
      expect(DesignTokens.space3 > DesignTokens.space2, true);
      expect(DesignTokens.space4 > DesignTokens.space3, true);
    });

    test('Icon sizes are appropriate', () {
      // Icon sizes should be reasonable for UI elements
      expect(DesignTokens.iconXs, 16.0);
      expect(DesignTokens.iconSm, 20.0);
      expect(DesignTokens.iconBase, 24.0);
      expect(DesignTokens.iconLg, 28.0);
      expect(DesignTokens.iconXl, 32.0);
      
      // Verify progression
      expect(DesignTokens.iconSm > DesignTokens.iconXs, true);
      expect(DesignTokens.iconBase > DesignTokens.iconSm, true);
      expect(DesignTokens.iconLg > DesignTokens.iconBase, true);
      expect(DesignTokens.iconXl > DesignTokens.iconLg, true);
    });

    test('Border radius values are consistent', () {
      // Border radius should follow a logical progression
      expect(DesignTokens.radiusXs, 4.0);
      expect(DesignTokens.radiusSm, 8.0);
      expect(DesignTokens.radiusBase, 12.0);
      expect(DesignTokens.radiusLg, 16.0);
      expect(DesignTokens.radiusXl, 20.0);
      expect(DesignTokens.radius2xl, 24.0);
      
      // All should be multiples of 4
      expect(DesignTokens.radiusXs % 4, 0);
      expect(DesignTokens.radiusSm % 4, 0);
      expect(DesignTokens.radiusBase % 4, 0);
      expect(DesignTokens.radiusLg % 4, 0);
      expect(DesignTokens.radiusXl % 4, 0);
      expect(DesignTokens.radius2xl % 4, 0);
    });

    test('Elevation levels follow Material Design', () {
      // Elevation should follow Material Design guidelines
      expect(DesignTokens.elevation0, 0.0);
      expect(DesignTokens.elevation1, 1.0);
      expect(DesignTokens.elevation2, 2.0);
      expect(DesignTokens.elevation4, 4.0);
      expect(DesignTokens.elevation6, 6.0);
      expect(DesignTokens.elevation8, 8.0);
      expect(DesignTokens.elevation12, 12.0);
      expect(DesignTokens.elevation16, 16.0);
      expect(DesignTokens.elevation24, 24.0);
      
      // Verify progression
      expect(DesignTokens.elevation1 > DesignTokens.elevation0, true);
      expect(DesignTokens.elevation2 > DesignTokens.elevation1, true);
      expect(DesignTokens.elevation4 > DesignTokens.elevation2, true);
    });

    test('Animation durations are reasonable', () {
      // Animation durations should be in reasonable ranges
      expect(DesignTokens.durationFast.inMilliseconds, 150);
      expect(DesignTokens.durationNormal.inMilliseconds, 200);
      expect(DesignTokens.durationSlow.inMilliseconds, 300);
      expect(DesignTokens.durationSlower.inMilliseconds, 500);
      
      // Verify progression
      expect(DesignTokens.durationNormal > DesignTokens.durationFast, true);
      expect(DesignTokens.durationSlow > DesignTokens.durationNormal, true);
      expect(DesignTokens.durationSlower > DesignTokens.durationSlow, true);
    });

    test('Breakpoints are appropriate for responsive design', () {
      expect(DesignTokens.breakpointMobile, 600.0);
      expect(DesignTokens.breakpointTablet, 1024.0);
      
      // Mobile breakpoint should be less than tablet
      expect(DesignTokens.breakpointMobile < DesignTokens.breakpointTablet, true);
    });

    test('Touch targets meet accessibility requirements', () {
      // Touch targets should meet minimum accessibility requirements
      expect(DesignTokens.touchTargetMin, 44.0);
      expect(DesignTokens.touchTargetComfortable, 48.0);
      
      // Comfortable should be larger than minimum
      expect(DesignTokens.touchTargetComfortable > DesignTokens.touchTargetMin, true);
      
      // Both should meet WCAG guidelines (minimum 44px)
      expect(DesignTokens.touchTargetMin >= 44.0, true);
      expect(DesignTokens.touchTargetComfortable >= 44.0, true);
    });

    test('Line heights are appropriate for readability', () {
      expect(DesignTokens.lineHeightTight, 1.2);
      expect(DesignTokens.lineHeightNormal, 1.4);
      expect(DesignTokens.lineHeightRelaxed, 1.6);
      expect(DesignTokens.lineHeightLoose, 1.8);
      
      // Verify progression
      expect(DesignTokens.lineHeightNormal > DesignTokens.lineHeightTight, true);
      expect(DesignTokens.lineHeightRelaxed > DesignTokens.lineHeightNormal, true);
      expect(DesignTokens.lineHeightLoose > DesignTokens.lineHeightRelaxed, true);
    });

    test('Letter spacing values are reasonable', () {
      expect(DesignTokens.letterSpacingTight, -0.5);
      expect(DesignTokens.letterSpacingNormal, 0.0);
      expect(DesignTokens.letterSpacingWide, 0.5);
      expect(DesignTokens.letterSpacingWider, 1.0);
      
      // Verify progression
      expect(DesignTokens.letterSpacingNormal > DesignTokens.letterSpacingTight, true);
      expect(DesignTokens.letterSpacingWide > DesignTokens.letterSpacingNormal, true);
      expect(DesignTokens.letterSpacingWider > DesignTokens.letterSpacingWide, true);
    });

    test('Container constraints are reasonable', () {
      expect(DesignTokens.maxContentWidth, 1200.0);
      expect(DesignTokens.minContentWidth, 320.0);
      
      // Max should be greater than min
      expect(DesignTokens.maxContentWidth > DesignTokens.minContentWidth, true);
      
      // Min width should accommodate mobile devices
      expect(DesignTokens.minContentWidth >= 320.0, true);
    });

    test('Grid system is consistent', () {
      expect(DesignTokens.gridColumns, 12);
      expect(DesignTokens.gridGutter, DesignTokens.space4);
      
      // Grid columns should be a reasonable number
      expect(DesignTokens.gridColumns >= 12, true);
      
      // Grid gutter should use spacing tokens
      expect(DesignTokens.gridGutter, 16.0);
    });

    test('Z-index layers are properly ordered', () {
      expect(DesignTokens.zIndexDropdown, 1000);
      expect(DesignTokens.zIndexSticky, 1020);
      expect(DesignTokens.zIndexFixed, 1030);
      expect(DesignTokens.zIndexModal, 1040);
      expect(DesignTokens.zIndexPopover, 1050);
      expect(DesignTokens.zIndexTooltip, 1060);
      
      // Verify proper ordering
      expect(DesignTokens.zIndexSticky > DesignTokens.zIndexDropdown, true);
      expect(DesignTokens.zIndexFixed > DesignTokens.zIndexSticky, true);
      expect(DesignTokens.zIndexModal > DesignTokens.zIndexFixed, true);
      expect(DesignTokens.zIndexPopover > DesignTokens.zIndexModal, true);
      expect(DesignTokens.zIndexTooltip > DesignTokens.zIndexPopover, true);
    });

    testWidgets('DesignTokens extension methods work correctly', (WidgetTester tester) async {
      late double capturedSpacing;
      late double capturedResponsiveSpacing;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              capturedSpacing = context.spacing(2.0);
              capturedResponsiveSpacing = DesignTokensExtension(context).responsiveSpacing(8.0, 12.0, 16.0);
              return const Scaffold(
                body: Text('Test'),
              );
            },
          ),
        ),
      );
      
      // Test spacing multiplier
      expect(capturedSpacing, DesignTokens.space4 * 2.0);
      
      // Test responsive spacing (should return mobile value for default test size)
      expect(capturedResponsiveSpacing, 8.0);
    });
  });

  group('ResponsiveBreakpoints Tests', () {
    testWidgets('Device type detection works correctly', (WidgetTester tester) async {
      // Test mobile detection
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(ResponsiveBreakpoints.getDeviceType(context),
                     DeviceType.mobile);
              expect(context.isMobile, true);
              expect(context.isTablet, false);
              expect(context.isDesktop, false);
              return const Scaffold(body: Text('Mobile'));
            },
          ),
        ),
      );

      // Test tablet detection
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(ResponsiveBreakpoints.getDeviceType(context),
                     DeviceType.tablet);
              expect(context.isMobile, false);
              expect(context.isTablet, true);
              expect(context.isDesktop, false);
              return const Scaffold(body: Text('Tablet'));
            },
          ),
        ),
      );

      // Test desktop detection
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(ResponsiveBreakpoints.getDeviceType(context),
                     DeviceType.desktop);
              expect(context.isMobile, false);
              expect(context.isTablet, false);
              expect(context.isDesktop, true);
              return const Scaffold(body: Text('Desktop'));
            },
          ),
        ),
      );
    });

    testWidgets('Responsive values work correctly', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(400, 800)); // Mobile
      
      late int capturedGridColumns;
      late double capturedMaxWidth;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              capturedGridColumns = context.responsiveGridColumns;
              capturedMaxWidth = context.responsiveMaxWidth;
              return const Scaffold(body: Text('Test'));
            },
          ),
        ),
      );
      
      expect(capturedGridColumns, 1); // Mobile should have 1 column
      expect(capturedMaxWidth, double.infinity); // Mobile should have no max width
    });
  });
}
