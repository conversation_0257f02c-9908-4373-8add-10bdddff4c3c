// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in moroccanaccounting/test/accessibility/accessibility_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i14;
import 'dart:ui' as _i13;

import 'package:device_info_plus/device_info_plus.dart' as _i16;
import 'package:device_info_plus/src/model/android_device_info.dart' as _i3;
import 'package:device_info_plus/src/model/ios_device_info.dart' as _i4;
import 'package:device_info_plus/src/model/linux_device_info.dart' as _i5;
import 'package:device_info_plus/src/model/macos_device_info.dart' as _i7;
import 'package:device_info_plus/src/model/web_browser_info.dart' as _i6;
import 'package:device_info_plus/src/model/windows_device_info.dart' as _i8;
import 'package:device_info_plus_platform_interface/device_info_plus_platform_interface.dart'
    as _i9;
import 'package:flutter/foundation.dart' as _i12;
import 'package:flutter/material.dart' as _i11;
import 'package:flutter/services.dart' as _i15;
import 'package:flutter_tts/flutter_tts.dart' as _i2;
import 'package:hive/hive.dart' as _i19;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i18;
import 'package:moroccanaccounting/models/accessibility/accessibility_preferences.dart'
    as _i10;
import 'package:moroccanaccounting/services/theme_service.dart' as _i17;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSpeechRateValidRange_0 extends _i1.SmartFake
    implements _i2.SpeechRateValidRange {
  _FakeSpeechRateValidRange_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAndroidDeviceInfo_1 extends _i1.SmartFake
    implements _i3.AndroidDeviceInfo {
  _FakeAndroidDeviceInfo_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeIosDeviceInfo_2 extends _i1.SmartFake implements _i4.IosDeviceInfo {
  _FakeIosDeviceInfo_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLinuxDeviceInfo_3 extends _i1.SmartFake
    implements _i5.LinuxDeviceInfo {
  _FakeLinuxDeviceInfo_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWebBrowserInfo_4 extends _i1.SmartFake
    implements _i6.WebBrowserInfo {
  _FakeWebBrowserInfo_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMacOsDeviceInfo_5 extends _i1.SmartFake
    implements _i7.MacOsDeviceInfo {
  _FakeMacOsDeviceInfo_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWindowsDeviceInfo_6 extends _i1.SmartFake
    implements _i8.WindowsDeviceInfo {
  _FakeWindowsDeviceInfo_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaseDeviceInfo_7 extends _i1.SmartFake
    implements _i9.BaseDeviceInfo {
  _FakeBaseDeviceInfo_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAccessibilityPreferences_8 extends _i1.SmartFake
    implements _i10.AccessibilityPreferences {
  _FakeAccessibilityPreferences_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeThemeData_9 extends _i1.SmartFake implements _i11.ThemeData {
  _FakeThemeData_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );

  @override
  String toString(
          {_i12.DiagnosticLevel? minLevel = _i12.DiagnosticLevel.info}) =>
      super.toString();
}

/// A class which mocks [FlutterTts].
///
/// See the documentation for Mockito's code generation for more information.
class MockFlutterTts extends _i1.Mock implements _i2.FlutterTts {
  MockFlutterTts() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set startHandler(_i13.VoidCallback? _startHandler) => super.noSuchMethod(
        Invocation.setter(
          #startHandler,
          _startHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set initHandler(_i13.VoidCallback? _initHandler) => super.noSuchMethod(
        Invocation.setter(
          #initHandler,
          _initHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set completionHandler(_i13.VoidCallback? _completionHandler) =>
      super.noSuchMethod(
        Invocation.setter(
          #completionHandler,
          _completionHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set pauseHandler(_i13.VoidCallback? _pauseHandler) => super.noSuchMethod(
        Invocation.setter(
          #pauseHandler,
          _pauseHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set continueHandler(_i13.VoidCallback? _continueHandler) =>
      super.noSuchMethod(
        Invocation.setter(
          #continueHandler,
          _continueHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set cancelHandler(_i13.VoidCallback? _cancelHandler) => super.noSuchMethod(
        Invocation.setter(
          #cancelHandler,
          _cancelHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set progressHandler(_i2.ProgressHandler? _progressHandler) =>
      super.noSuchMethod(
        Invocation.setter(
          #progressHandler,
          _progressHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set errorHandler(_i2.ErrorHandler? _errorHandler) => super.noSuchMethod(
        Invocation.setter(
          #errorHandler,
          _errorHandler,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i14.Future<int?> get getMaxSpeechInputLength => (super.noSuchMethod(
        Invocation.getter(#getMaxSpeechInputLength),
        returnValue: _i14.Future<int?>.value(),
      ) as _i14.Future<int?>);

  @override
  _i14.Future<dynamic> get getLanguages => (super.noSuchMethod(
        Invocation.getter(#getLanguages),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> get getEngines => (super.noSuchMethod(
        Invocation.getter(#getEngines),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> get getDefaultEngine => (super.noSuchMethod(
        Invocation.getter(#getDefaultEngine),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> get getDefaultVoice => (super.noSuchMethod(
        Invocation.getter(#getDefaultVoice),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> get getVoices => (super.noSuchMethod(
        Invocation.getter(#getVoices),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<_i2.SpeechRateValidRange> get getSpeechRateValidRange =>
      (super.noSuchMethod(
        Invocation.getter(#getSpeechRateValidRange),
        returnValue: _i14.Future<_i2.SpeechRateValidRange>.value(
            _FakeSpeechRateValidRange_0(
          this,
          Invocation.getter(#getSpeechRateValidRange),
        )),
      ) as _i14.Future<_i2.SpeechRateValidRange>);

  @override
  _i14.Future<dynamic> awaitSpeakCompletion(bool? awaitCompletion) =>
      (super.noSuchMethod(
        Invocation.method(
          #awaitSpeakCompletion,
          [awaitCompletion],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> awaitSynthCompletion(bool? awaitCompletion) =>
      (super.noSuchMethod(
        Invocation.method(
          #awaitSynthCompletion,
          [awaitCompletion],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> speak(String? text) => (super.noSuchMethod(
        Invocation.method(
          #speak,
          [text],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> pause() => (super.noSuchMethod(
        Invocation.method(
          #pause,
          [],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> synthesizeToFile(
    String? text,
    String? fileName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #synthesizeToFile,
          [
            text,
            fileName,
          ],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setLanguage(String? language) => (super.noSuchMethod(
        Invocation.method(
          #setLanguage,
          [language],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setSpeechRate(double? rate) => (super.noSuchMethod(
        Invocation.method(
          #setSpeechRate,
          [rate],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setVolume(double? volume) => (super.noSuchMethod(
        Invocation.method(
          #setVolume,
          [volume],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setSharedInstance(bool? sharedSession) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSharedInstance,
          [sharedSession],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> autoStopSharedSession(bool? autoStop) =>
      (super.noSuchMethod(
        Invocation.method(
          #autoStopSharedSession,
          [autoStop],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setIosAudioCategory(
    _i2.IosTextToSpeechAudioCategory? category,
    List<_i2.IosTextToSpeechAudioCategoryOptions>? options, [
    _i2.IosTextToSpeechAudioMode? mode =
        _i2.IosTextToSpeechAudioMode.defaultMode,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #setIosAudioCategory,
          [
            category,
            options,
            mode,
          ],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setEngine(String? engine) => (super.noSuchMethod(
        Invocation.method(
          #setEngine,
          [engine],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setPitch(double? pitch) => (super.noSuchMethod(
        Invocation.method(
          #setPitch,
          [pitch],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setVoice(Map<String, String>? voice) =>
      (super.noSuchMethod(
        Invocation.method(
          #setVoice,
          [voice],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> stop() => (super.noSuchMethod(
        Invocation.method(
          #stop,
          [],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> isLanguageAvailable(String? language) =>
      (super.noSuchMethod(
        Invocation.method(
          #isLanguageAvailable,
          [language],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> isLanguageInstalled(String? language) =>
      (super.noSuchMethod(
        Invocation.method(
          #isLanguageInstalled,
          [language],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> areLanguagesInstalled(List<String>? languages) =>
      (super.noSuchMethod(
        Invocation.method(
          #areLanguagesInstalled,
          [languages],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setSilence(int? timems) => (super.noSuchMethod(
        Invocation.method(
          #setSilence,
          [timems],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  _i14.Future<dynamic> setQueueMode(int? queueMode) => (super.noSuchMethod(
        Invocation.method(
          #setQueueMode,
          [queueMode],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);

  @override
  void setStartHandler(_i13.VoidCallback? callback) => super.noSuchMethod(
        Invocation.method(
          #setStartHandler,
          [callback],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setInitHandler(_i13.VoidCallback? callback) => super.noSuchMethod(
        Invocation.method(
          #setInitHandler,
          [callback],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setCompletionHandler(_i13.VoidCallback? callback) => super.noSuchMethod(
        Invocation.method(
          #setCompletionHandler,
          [callback],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setContinueHandler(_i13.VoidCallback? callback) => super.noSuchMethod(
        Invocation.method(
          #setContinueHandler,
          [callback],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setPauseHandler(_i13.VoidCallback? callback) => super.noSuchMethod(
        Invocation.method(
          #setPauseHandler,
          [callback],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setCancelHandler(_i13.VoidCallback? callback) => super.noSuchMethod(
        Invocation.method(
          #setCancelHandler,
          [callback],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setProgressHandler(_i2.ProgressHandler? callback) => super.noSuchMethod(
        Invocation.method(
          #setProgressHandler,
          [callback],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setErrorHandler(_i2.ErrorHandler? handler) => super.noSuchMethod(
        Invocation.method(
          #setErrorHandler,
          [handler],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i14.Future<dynamic> platformCallHandler(_i15.MethodCall? call) =>
      (super.noSuchMethod(
        Invocation.method(
          #platformCallHandler,
          [call],
        ),
        returnValue: _i14.Future<dynamic>.value(),
      ) as _i14.Future<dynamic>);
}

/// A class which mocks [DeviceInfoPlugin].
///
/// See the documentation for Mockito's code generation for more information.
class MockDeviceInfoPlugin extends _i1.Mock implements _i16.DeviceInfoPlugin {
  MockDeviceInfoPlugin() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i14.Future<_i3.AndroidDeviceInfo> get androidInfo => (super.noSuchMethod(
        Invocation.getter(#androidInfo),
        returnValue:
            _i14.Future<_i3.AndroidDeviceInfo>.value(_FakeAndroidDeviceInfo_1(
          this,
          Invocation.getter(#androidInfo),
        )),
      ) as _i14.Future<_i3.AndroidDeviceInfo>);

  @override
  _i14.Future<_i4.IosDeviceInfo> get iosInfo => (super.noSuchMethod(
        Invocation.getter(#iosInfo),
        returnValue: _i14.Future<_i4.IosDeviceInfo>.value(_FakeIosDeviceInfo_2(
          this,
          Invocation.getter(#iosInfo),
        )),
      ) as _i14.Future<_i4.IosDeviceInfo>);

  @override
  _i14.Future<_i5.LinuxDeviceInfo> get linuxInfo => (super.noSuchMethod(
        Invocation.getter(#linuxInfo),
        returnValue:
            _i14.Future<_i5.LinuxDeviceInfo>.value(_FakeLinuxDeviceInfo_3(
          this,
          Invocation.getter(#linuxInfo),
        )),
      ) as _i14.Future<_i5.LinuxDeviceInfo>);

  @override
  _i14.Future<_i6.WebBrowserInfo> get webBrowserInfo => (super.noSuchMethod(
        Invocation.getter(#webBrowserInfo),
        returnValue:
            _i14.Future<_i6.WebBrowserInfo>.value(_FakeWebBrowserInfo_4(
          this,
          Invocation.getter(#webBrowserInfo),
        )),
      ) as _i14.Future<_i6.WebBrowserInfo>);

  @override
  _i14.Future<_i7.MacOsDeviceInfo> get macOsInfo => (super.noSuchMethod(
        Invocation.getter(#macOsInfo),
        returnValue:
            _i14.Future<_i7.MacOsDeviceInfo>.value(_FakeMacOsDeviceInfo_5(
          this,
          Invocation.getter(#macOsInfo),
        )),
      ) as _i14.Future<_i7.MacOsDeviceInfo>);

  @override
  _i14.Future<_i8.WindowsDeviceInfo> get windowsInfo => (super.noSuchMethod(
        Invocation.getter(#windowsInfo),
        returnValue:
            _i14.Future<_i8.WindowsDeviceInfo>.value(_FakeWindowsDeviceInfo_6(
          this,
          Invocation.getter(#windowsInfo),
        )),
      ) as _i14.Future<_i8.WindowsDeviceInfo>);

  @override
  _i14.Future<_i9.BaseDeviceInfo> get deviceInfo => (super.noSuchMethod(
        Invocation.getter(#deviceInfo),
        returnValue:
            _i14.Future<_i9.BaseDeviceInfo>.value(_FakeBaseDeviceInfo_7(
          this,
          Invocation.getter(#deviceInfo),
        )),
      ) as _i14.Future<_i9.BaseDeviceInfo>);
}

/// A class which mocks [ThemeService].
///
/// See the documentation for Mockito's code generation for more information.
class MockThemeService extends _i1.Mock implements _i17.ThemeService {
  MockThemeService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i17.ThemeType get currentTheme => (super.noSuchMethod(
        Invocation.getter(#currentTheme),
        returnValue: _i17.ThemeType.light,
      ) as _i17.ThemeType);

  @override
  bool get isDarkMode => (super.noSuchMethod(
        Invocation.getter(#isDarkMode),
        returnValue: false,
      ) as bool);

  @override
  _i10.AccessibilityPreferences get accessibilityPreferences =>
      (super.noSuchMethod(
        Invocation.getter(#accessibilityPreferences),
        returnValue: _FakeAccessibilityPreferences_8(
          this,
          Invocation.getter(#accessibilityPreferences),
        ),
      ) as _i10.AccessibilityPreferences);

  @override
  bool get isHighContrastEnabled => (super.noSuchMethod(
        Invocation.getter(#isHighContrastEnabled),
        returnValue: false,
      ) as bool);

  @override
  double get fontScale => (super.noSuchMethod(
        Invocation.getter(#fontScale),
        returnValue: 0.0,
      ) as double);

  @override
  bool get isReducedMotionEnabled => (super.noSuchMethod(
        Invocation.getter(#isReducedMotionEnabled),
        returnValue: false,
      ) as bool);

  @override
  _i11.ThemeData get theme => (super.noSuchMethod(
        Invocation.getter(#theme),
        returnValue: _FakeThemeData_9(
          this,
          Invocation.getter(#theme),
        ),
      ) as _i11.ThemeData);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i14.Future<void> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  void setTheme(_i17.ThemeType? theme) => super.noSuchMethod(
        Invocation.method(
          #setTheme,
          [theme],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setAccessibilityPreferences(
          _i10.AccessibilityPreferences? preferences) =>
      super.noSuchMethod(
        Invocation.method(
          #setAccessibilityPreferences,
          [preferences],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i14.Future<void> updateFontScale(double? scale) => (super.noSuchMethod(
        Invocation.method(
          #updateFontScale,
          [scale],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> toggleHighContrast() => (super.noSuchMethod(
        Invocation.method(
          #toggleHighContrast,
          [],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> updateReducedMotion(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #updateReducedMotion,
          [enabled],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  void setAccessibilityChangeCallback(
          dynamic Function(_i10.AccessibilityPreferences)? callback) =>
      super.noSuchMethod(
        Invocation.method(
          #setAccessibilityChangeCallback,
          [callback],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void cycleTheme() => super.noSuchMethod(
        Invocation.method(
          #cycleTheme,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  String getThemeName(_i17.ThemeType? type) => (super.noSuchMethod(
        Invocation.method(
          #getThemeName,
          [type],
        ),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.method(
            #getThemeName,
            [type],
          ),
        ),
      ) as String);

  @override
  String getAccessibleThemeName() => (super.noSuchMethod(
        Invocation.method(
          #getAccessibleThemeName,
          [],
        ),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.method(
            #getAccessibleThemeName,
            [],
          ),
        ),
      ) as String);

  @override
  bool isCurrentThemeAccessible() => (super.noSuchMethod(
        Invocation.method(
          #isCurrentThemeAccessible,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  Map<String, dynamic> getAccessibilitySummary() => (super.noSuchMethod(
        Invocation.method(
          #getAccessibilitySummary,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i14.Future<void> resetAccessibilityToDefaults() => (super.noSuchMethod(
        Invocation.method(
          #resetAccessibilityToDefaults,
          [],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> applyAccessibilityPreset(String? presetName) =>
      (super.noSuchMethod(
        Invocation.method(
          #applyAccessibilityPreset,
          [presetName],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  List<Map<String, String>> getAccessibilityPresets() => (super.noSuchMethod(
        Invocation.method(
          #getAccessibilityPresets,
          [],
        ),
        returnValue: <Map<String, String>>[],
      ) as List<Map<String, String>>);

  @override
  _i11.ThemeData getAccessibleTheme() => (super.noSuchMethod(
        Invocation.method(
          #getAccessibleTheme,
          [],
        ),
        returnValue: _FakeThemeData_9(
          this,
          Invocation.method(
            #getAccessibleTheme,
            [],
          ),
        ),
      ) as _i11.ThemeData);

  @override
  void addListener(_i13.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i13.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [Box].
///
/// See the documentation for Mockito's code generation for more information.
class MockBox<E> extends _i1.Mock implements _i19.Box<E> {
  MockBox() {
    _i1.throwOnMissingStub(this);
  }

  @override
  Iterable<E> get values => (super.noSuchMethod(
        Invocation.getter(#values),
        returnValue: <E>[],
      ) as Iterable<E>);

  @override
  String get name => (super.noSuchMethod(
        Invocation.getter(#name),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.getter(#name),
        ),
      ) as String);

  @override
  bool get isOpen => (super.noSuchMethod(
        Invocation.getter(#isOpen),
        returnValue: false,
      ) as bool);

  @override
  bool get lazy => (super.noSuchMethod(
        Invocation.getter(#lazy),
        returnValue: false,
      ) as bool);

  @override
  Iterable<dynamic> get keys => (super.noSuchMethod(
        Invocation.getter(#keys),
        returnValue: <dynamic>[],
      ) as Iterable<dynamic>);

  @override
  int get length => (super.noSuchMethod(
        Invocation.getter(#length),
        returnValue: 0,
      ) as int);

  @override
  bool get isEmpty => (super.noSuchMethod(
        Invocation.getter(#isEmpty),
        returnValue: false,
      ) as bool);

  @override
  bool get isNotEmpty => (super.noSuchMethod(
        Invocation.getter(#isNotEmpty),
        returnValue: false,
      ) as bool);

  @override
  Iterable<E> valuesBetween({
    dynamic startKey,
    dynamic endKey,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #valuesBetween,
          [],
          {
            #startKey: startKey,
            #endKey: endKey,
          },
        ),
        returnValue: <E>[],
      ) as Iterable<E>);

  @override
  E? getAt(int? index) => (super.noSuchMethod(Invocation.method(
        #getAt,
        [index],
      )) as E?);

  @override
  Map<dynamic, E> toMap() => (super.noSuchMethod(
        Invocation.method(
          #toMap,
          [],
        ),
        returnValue: <dynamic, E>{},
      ) as Map<dynamic, E>);

  @override
  dynamic keyAt(int? index) => super.noSuchMethod(Invocation.method(
        #keyAt,
        [index],
      ));

  @override
  _i14.Stream<_i19.BoxEvent> watch({dynamic key}) => (super.noSuchMethod(
        Invocation.method(
          #watch,
          [],
          {#key: key},
        ),
        returnValue: _i14.Stream<_i19.BoxEvent>.empty(),
      ) as _i14.Stream<_i19.BoxEvent>);

  @override
  bool containsKey(dynamic key) => (super.noSuchMethod(
        Invocation.method(
          #containsKey,
          [key],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i14.Future<void> put(
    dynamic key,
    E? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #put,
          [
            key,
            value,
          ],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> putAt(
    int? index,
    E? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #putAt,
          [
            index,
            value,
          ],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> putAll(Map<dynamic, E>? entries) => (super.noSuchMethod(
        Invocation.method(
          #putAll,
          [entries],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<int> add(E? value) => (super.noSuchMethod(
        Invocation.method(
          #add,
          [value],
        ),
        returnValue: _i14.Future<int>.value(0),
      ) as _i14.Future<int>);

  @override
  _i14.Future<Iterable<int>> addAll(Iterable<E>? values) => (super.noSuchMethod(
        Invocation.method(
          #addAll,
          [values],
        ),
        returnValue: _i14.Future<Iterable<int>>.value(<int>[]),
      ) as _i14.Future<Iterable<int>>);

  @override
  _i14.Future<void> delete(dynamic key) => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [key],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> deleteAt(int? index) => (super.noSuchMethod(
        Invocation.method(
          #deleteAt,
          [index],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> deleteAll(Iterable<dynamic>? keys) => (super.noSuchMethod(
        Invocation.method(
          #deleteAll,
          [keys],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> compact() => (super.noSuchMethod(
        Invocation.method(
          #compact,
          [],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<int> clear() => (super.noSuchMethod(
        Invocation.method(
          #clear,
          [],
        ),
        returnValue: _i14.Future<int>.value(0),
      ) as _i14.Future<int>);

  @override
  _i14.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> deleteFromDisk() => (super.noSuchMethod(
        Invocation.method(
          #deleteFromDisk,
          [],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);

  @override
  _i14.Future<void> flush() => (super.noSuchMethod(
        Invocation.method(
          #flush,
          [],
        ),
        returnValue: _i14.Future<void>.value(),
        returnValueForMissingStub: _i14.Future<void>.value(),
      ) as _i14.Future<void>);
}
