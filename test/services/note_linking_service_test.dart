import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:moroccanaccounting/models/guide/personal_note_data.dart';
import 'package:moroccanaccounting/services/note_linking_service.dart';
import 'dart:io';

void main() {
  group('NoteLinkingService', () {
    late NoteLinkingService service;
    late Directory tempDir;

    setUpAll(() async {
      // Create a temporary directory for testing
      tempDir = await Directory.systemTemp.createTemp('hive_test_');
      Hive.init(tempDir.path);

      // Register adapters - these would normally be generated
      // For testing, we'll skip the actual Hive operations
    });

    setUp(() async {
      service = NoteLinkingService();
      // Skip initialization for unit tests
    });

    tearDown(() async {
      // Clean up any test data
    });

    tearDownAll(() async {
      await Hive.close();
      if (tempDir.existsSync()) {
        tempDir.deleteSync(recursive: true);
      }
    });

    group('Link Management', () {
      test('should create a link between two notes', () async {
        final link = await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
          description: 'Test link',
        );

        expect(link.sourceNoteId, equals('note1'));
        expect(link.targetNoteId, equals('note2'));
        expect(link.linkType, equals(NoteLinkType.reference));
        expect(link.description, equals('Test link'));
      });

      test('should get outgoing links for a note', () async {
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note3',
          linkType: NoteLinkType.related,
        );

        final outgoingLinks = await service.getOutgoingLinks('note1');
        expect(outgoingLinks.length, equals(2));
        expect(outgoingLinks.every((link) => link.sourceNoteId == 'note1'), isTrue);
      });

      test('should get incoming links for a note', () async {
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );
        await service.createLink(
          sourceNoteId: 'note3',
          targetNoteId: 'note2',
          linkType: NoteLinkType.related,
        );

        final incomingLinks = await service.getIncomingLinks('note2');
        expect(incomingLinks.length, equals(2));
        expect(incomingLinks.every((link) => link.targetNoteId == 'note2'), isTrue);
      });

      test('should check if two notes are connected', () async {
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );

        final areConnected = await service.areNotesConnected('note1', 'note2');
        expect(areConnected, isTrue);

        final areNotConnected = await service.areNotesConnected('note1', 'note3');
        expect(areNotConnected, isFalse);
      });

      test('should find notes within degrees of separation', () async {
        // Create a chain: note1 -> note2 -> note3 -> note4
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );
        await service.createLink(
          sourceNoteId: 'note2',
          targetNoteId: 'note3',
          linkType: NoteLinkType.reference,
        );
        await service.createLink(
          sourceNoteId: 'note3',
          targetNoteId: 'note4',
          linkType: NoteLinkType.reference,
        );

        final notesWithin2Degrees = await service.getNotesWithinDegrees('note1', 2);
        expect(notesWithin2Degrees.contains('note2'), isTrue);
        expect(notesWithin2Degrees.contains('note3'), isTrue);
        expect(notesWithin2Degrees.contains('note4'), isFalse);

        final notesWithin3Degrees = await service.getNotesWithinDegrees('note1', 3);
        expect(notesWithin3Degrees.contains('note4'), isTrue);
      });

      test('should remove a link', () async {
        final link = await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );

        await service.removeLink(link.id);

        final links = await service.getLinksForNote('note1');
        expect(links.isEmpty, isTrue);
      });

      test('should get links by type', () async {
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );
        await service.createLink(
          sourceNoteId: 'note2',
          targetNoteId: 'note3',
          linkType: NoteLinkType.related,
        );
        await service.createLink(
          sourceNoteId: 'note3',
          targetNoteId: 'note4',
          linkType: NoteLinkType.reference,
        );

        final referenceLinks = await service.getLinksByType(NoteLinkType.reference);
        expect(referenceLinks.length, equals(2));
        expect(referenceLinks.every((link) => link.linkType == NoteLinkType.reference), isTrue);

        final relatedLinks = await service.getLinksByType(NoteLinkType.related);
        expect(relatedLinks.length, equals(1));
        expect(relatedLinks.first.linkType, equals(NoteLinkType.related));
      });
    });

    group('Share Management', () {
      test('should create share info for a note', () async {
        final shareInfo = await service.createShareInfo(
          noteId: 'note1',
          shareToken: 'test_token_123',
          isPublic: true,
          allowComments: true,
          allowEditing: false,
        );

        expect(shareInfo.noteId, equals('note1'));
        expect(shareInfo.shareToken, equals('test_token_123'));
        expect(shareInfo.isPublic, isTrue);
        expect(shareInfo.allowComments, isTrue);
        expect(shareInfo.allowEditing, isFalse);
      });

      test('should get share info for a note', () async {
        await service.createShareInfo(
          noteId: 'note1',
          shareToken: 'test_token_123',
          isPublic: true,
        );

        final shareInfo = await service.getShareInfoForNote('note1');
        expect(shareInfo, isNotNull);
        expect(shareInfo!.noteId, equals('note1'));
        expect(shareInfo.shareToken, equals('test_token_123'));
      });

      test('should get share info by token', () async {
        await service.createShareInfo(
          noteId: 'note1',
          shareToken: 'test_token_123',
          isPublic: true,
        );

        final shareInfo = await service.getShareInfoByToken('test_token_123');
        expect(shareInfo, isNotNull);
        expect(shareInfo!.noteId, equals('note1'));
        expect(shareInfo.shareToken, equals('test_token_123'));
      });

      test('should remove share info', () async {
        final shareInfo = await service.createShareInfo(
          noteId: 'note1',
          shareToken: 'test_token_123',
          isPublic: true,
        );

        await service.removeShareInfo(shareInfo.id);

        final retrievedShareInfo = await service.getShareInfoForNote('note1');
        expect(retrievedShareInfo, isNull);
      });

      test('should generate unique share tokens', () async {
        final token1 = service.generateShareToken();
        final token2 = service.generateShareToken();

        expect(token1, isNot(equals(token2)));
        expect(token1.startsWith('share_'), isTrue);
        expect(token2.startsWith('share_'), isTrue);
      });

      test('should validate share token format', () async {
        expect(service.isValidShareToken('share_1234567890_123'), isTrue);
        expect(service.isValidShareToken('invalid_token'), isFalse);
        expect(service.isValidShareToken('share_123'), isFalse);
      });

      test('should clean up expired shares', () async {
        final expiredDate = DateTime.now().subtract(const Duration(days: 1));
        await service.createShareInfo(
          noteId: 'note1',
          shareToken: 'expired_token',
          isPublic: true,
          expiresAt: expiredDate,
        );

        await service.createShareInfo(
          noteId: 'note2',
          shareToken: 'valid_token',
          isPublic: true,
          expiresAt: DateTime.now().add(const Duration(days: 1)),
        );

        await service.cleanupExpiredShares();

        final expiredShare = await service.getShareInfoByToken('expired_token');
        final validShare = await service.getShareInfoByToken('valid_token');

        expect(expiredShare, isNull);
        expect(validShare, isNotNull);
      });
    });

    group('Statistics', () {
      test('should calculate link statistics', () async {
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );
        await service.createLink(
          sourceNoteId: 'note2',
          targetNoteId: 'note3',
          linkType: NoteLinkType.related,
        );
        await service.createShareInfo(
          noteId: 'note1',
          shareToken: 'token1',
          isPublic: true,
        );
        await service.createShareInfo(
          noteId: 'note2',
          shareToken: 'token2',
          isPublic: false,
        );

        final statistics = await service.getLinkStatistics();

        expect(statistics.totalLinks, equals(2));
        expect(statistics.linksByType[NoteLinkType.reference], equals(1));
        expect(statistics.linksByType[NoteLinkType.related], equals(1));
        expect(statistics.totalSharedNotes, equals(2));
        expect(statistics.publicShares, equals(1));
        expect(statistics.privateShares, equals(1));
      });

      test('should find orphaned notes', () async {
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );

        final allNoteIds = ['note1', 'note2', 'note3', 'note4'];
        final orphanedNotes = await service.findOrphanedNotes(allNoteIds);

        expect(orphanedNotes.length, equals(2));
        expect(orphanedNotes.contains('note3'), isTrue);
        expect(orphanedNotes.contains('note4'), isTrue);
      });

      test('should find hub notes', () async {
        // Create note1 as a hub with multiple connections
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note3',
          linkType: NoteLinkType.reference,
        );
        await service.createLink(
          sourceNoteId: 'note4',
          targetNoteId: 'note1',
          linkType: NoteLinkType.reference,
        );

        final hubNotes = await service.findHubNotes(minConnections: 3);

        expect(hubNotes.length, equals(1));
        expect(hubNotes.first.key, equals('note1'));
        expect(hubNotes.first.value, equals(3));
      });
    });

    group('Import/Export', () {
      test('should export links to JSON', () async {
        await service.createLink(
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: NoteLinkType.reference,
        );
        await service.createShareInfo(
          noteId: 'note1',
          shareToken: 'token1',
          isPublic: true,
        );

        final exportData = await service.exportLinks();

        expect(exportData['version'], equals('1.0'));
        expect(exportData['links'], isA<List>());
        expect(exportData['shareInfo'], isA<List>());
        expect((exportData['links'] as List).length, equals(1));
        expect((exportData['shareInfo'] as List).length, equals(1));
      });

      test('should import links from JSON', () async {
        final importData = {
          'version': '1.0',
          'links': [
            {
              'id': 'link1',
              'sourceNoteId': 'note1',
              'targetNoteId': 'note2',
              'linkType': 'reference',
              'description': 'Test link',
              'createdAt': DateTime.now().toIso8601String(),
            }
          ],
          'shareInfo': [
            {
              'id': 'share1',
              'noteId': 'note1',
              'shareToken': 'token1',
              'isPublic': true,
              'allowComments': false,
              'allowEditing': false,
              'createdAt': DateTime.now().toIso8601String(),
              'lastAccessed': DateTime.now().toIso8601String(),
            }
          ],
        };

        await service.importLinks(importData);

        final links = await service.getAllLinks();
        final shareInfo = await service.getAllShareInfo();

        expect(links.length, equals(1));
        expect(shareInfo.length, equals(1));
        expect(links.first.sourceNoteId, equals('note1'));
        expect(shareInfo.first.shareToken, equals('token1'));
      });
    });
  });
}
