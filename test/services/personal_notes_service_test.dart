import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/models/guide/personal_note_data.dart';
import 'package:moroccanaccounting/services/personal_notes_service.dart';

void main() {
  group('PersonalNotesService Tests', () {
    late PersonalNotesService service;

    setUp(() {
      service = PersonalNotesService();
    });

    tearDown(() {
      service.dispose();
    });

    test('should create service instance successfully', () {
      expect(service, isNotNull);
    });
    
    test('should create note data model correctly', () {
      final note = PersonalNoteData(
        id: 'test_note_1',
        guideId: 'test_guide',
        sectionId: 'test_section',
        title: 'Test Note',
        content: 'This is a test note content',
        noteType: NoteType.text,
        tags: ['test', 'sample'],
      );

      expect(note.id, equals('test_note_1'));
      expect(note.title, equals('Test Note'));
      expect(note.content, equals('This is a test note content'));
      expect(note.noteType, equals(NoteType.text));
      expect(note.tags, contains('test'));
      expect(note.tags, contains('sample'));
    });
    
    test('should validate note data correctly', () {
      final validNote = PersonalNoteData(
        id: 'note_1',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: 'Valid Note',
        content: 'This is valid content',
        noteType: NoteType.text,
      );

      expect(validNote.isValid(), isTrue);

      final invalidNote = PersonalNoteData(
        id: '',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: '',
        content: 'Content without title',
        noteType: NoteType.text,
      );

      expect(invalidNote.isValid(), isFalse);
    });
    
    test('should handle note types correctly', () {
      final textNote = PersonalNoteData(
        id: 'note_1',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: 'Text Note',
        content: 'Content',
        noteType: NoteType.text,
      );

      final checklistNote = PersonalNoteData(
        id: 'note_2',
        guideId: 'guide_1',
        sectionId: 'section_2',
        title: 'Checklist Note',
        content: 'Content',
        noteType: NoteType.checklist,
      );

      final reminderNote = PersonalNoteData(
        id: 'note_3',
        guideId: 'guide_1',
        sectionId: 'section_3',
        title: 'Reminder Note',
        content: 'Content',
        noteType: NoteType.reminder,
      );

      expect(textNote.getNoteTypeLabel(), equals('Texte'));
      expect(checklistNote.getNoteTypeLabel(), equals('Liste de contrôle'));
      expect(reminderNote.getNoteTypeLabel(), equals('Rappel'));
    });
    
    test('should handle note privacy correctly', () {
      final privateNote = PersonalNoteData(
        id: 'note_1',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: 'Private Note',
        content: 'This is private',
        noteType: NoteType.text,
        isPrivate: true,
      );

      final publicNote = PersonalNoteData(
        id: 'note_2',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: 'Public Note',
        content: 'This is public',
        noteType: NoteType.text,
        isPrivate: false,
      );

      expect(privateNote.isPrivate, isTrue);
      expect(publicNote.isPrivate, isFalse);
    });

    test('should handle reminder dates correctly', () {
      final reminderDate = DateTime.now().add(Duration(days: 1));
      final overdueDate = DateTime.now().subtract(Duration(days: 1));

      final futureReminder = PersonalNoteData(
        id: 'note_1',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: 'Future Reminder',
        content: 'Content',
        noteType: NoteType.reminder,
        reminderDate: reminderDate,
      );

      final overdueReminder = PersonalNoteData(
        id: 'note_2',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: 'Overdue Reminder',
        content: 'Content',
        noteType: NoteType.reminder,
        reminderDate: overdueDate,
      );

      expect(futureReminder.hasOverdueReminder(), isFalse);
      expect(overdueReminder.hasOverdueReminder(), isTrue);
    });

    test('should generate content preview correctly', () {
      final shortNote = PersonalNoteData(
        id: 'note_1',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: 'Short Note',
        content: 'Short content',
        noteType: NoteType.text,
      );

      final longNote = PersonalNoteData(
        id: 'note_2',
        guideId: 'guide_1',
        sectionId: 'section_1',
        title: 'Long Note',
        content: 'This is a very long note content that should be truncated when generating a preview because it exceeds the maximum length allowed for previews',
        noteType: NoteType.text,
      );

      expect(shortNote.getContentPreview(), equals('Short content'));
      expect(longNote.getContentPreview().length, equals(100));
      expect(longNote.getContentPreview().endsWith('...'), isTrue);
    });
  });
}
