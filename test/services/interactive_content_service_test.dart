import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/services/interactive_content_service.dart';

void main() {
  group('InteractiveContentService Tests', () {
    late InteractiveContentService service;

    setUp(() {
      service = InteractiveContentService.instance;
    });

    tearDown(() {
      service.clearCache();
      service.dispose();
    });
    
    test('should create service instance successfully', () {
      expect(service, isNotNull);
    });

    test('should handle content types correctly', () {
      final types = InteractiveContentType.values;
      expect(types.length, equals(5));
      expect(types, contains(InteractiveContentType.walkthrough));
      expect(types, contains(InteractiveContentType.simulation));
      expect(types, contains(InteractiveContentType.diagram));
      expect(types, contains(InteractiveContentType.quiz));
      expect(types, contains(InteractiveContentType.calculator));
    });

    test('should provide correct content type labels', () {
      expect(InteractiveContentType.walkthrough.label, equals('Parcours guidé'));
      expect(InteractiveContentType.simulation.label, equals('Simulation'));
      expect(InteractiveContentType.diagram.label, equals('Diagramme interactif'));
      expect(InteractiveContentType.quiz.label, equals('Quiz'));
      expect(InteractiveContentType.calculator.label, equals('Calculatrice'));
    });
    
    test('should create InteractiveProgress correctly', () {
      final progressData = {
        'guideId': 'test_guide',
        'contentId': 'test_content',
        'steps': {'step1': {'score': 0.8, 'attempts': 1}},
        'totalSteps': 1,
        'completedSteps': 1,
        'progressPercentage': 100.0,
        'averageScore': 0.8,
        'lastUpdated': DateTime.now().toIso8601String(),
        'isCompleted': true,
      };

      final progress = InteractiveProgress.fromMap(progressData);
      expect(progress.guideId, equals('test_guide'));
      expect(progress.contentId, equals('test_content'));
      expect(progress.totalSteps, equals(1));
      expect(progress.completedSteps, equals(1));
      expect(progress.averageScore, equals(0.8));
      expect(progress.isCompleted, isTrue);
    });
    
    test('should create UserInteractionState correctly', () {
      final stateData = {
        'guideId': 'test_guide',
        'contentId': 'test_content',
        'stepId': 'step_1',
        'state': {'currentInput': 'test', 'attempts': 2},
        'savedAt': DateTime.now().toIso8601String(),
      };

      final userState = UserInteractionState.fromMap(stateData);
      expect(userState.guideId, equals('test_guide'));
      expect(userState.contentId, equals('test_content'));
      expect(userState.stepId, equals('step_1'));
      expect(userState.state['currentInput'], equals('test'));
      expect(userState.state['attempts'], equals(2));
    });
    
    test('should calculate ContentStatistics correctly', () {
      const stats1 = ContentStatistics(
        totalSteps: 5,
        completedSteps: 5,
        progressPercentage: 100.0,
        averageScore: 0.95,
        totalAttempts: 5,
        timeSpent: Duration(minutes: 10),
        isCompleted: true,
      );

      expect(stats1.completionRate, equals(100.0));
      expect(stats1.performanceGrade, equals('Excellent'));
      expect(stats1.estimatedDifficulty, equals('Facile'));

      const stats2 = ContentStatistics(
        totalSteps: 5,
        completedSteps: 2,
        progressPercentage: 40.0,
        averageScore: 0.45,
        totalAttempts: 15,
        timeSpent: Duration(minutes: 30),
        isCompleted: false,
      );

      expect(stats2.completionRate, equals(40.0));
      expect(stats2.performanceGrade, equals('À améliorer'));
      expect(stats2.estimatedDifficulty, equals('Difficile'));
    });
    
    test('should handle progress calculations correctly', () {
      final progress = InteractiveProgress(
        guideId: 'test_guide',
        contentId: 'test_content',
        steps: {
          'step1': {'attempts': 2},
          'step2': {'attempts': 1},
          'step3': {'attempts': 3},
        },
        totalSteps: 3,
        completedSteps: 2,
        progressPercentage: 66.67,
        averageScore: 0.75,
        lastUpdated: DateTime.now(),
        isCompleted: false,
      );

      expect(progress.totalAttempts, equals(6));
      expect(progress.timeSpent, equals(Duration(minutes: 12))); // 6 attempts * 2 minutes
    });

    test('should convert data models to and from maps correctly', () {
      final originalProgress = InteractiveProgress(
        guideId: 'test_guide',
        contentId: 'test_content',
        steps: {'step1': {'score': 0.8}},
        totalSteps: 1,
        completedSteps: 1,
        progressPercentage: 100.0,
        averageScore: 0.8,
        lastUpdated: DateTime.now(),
        isCompleted: true,
      );

      final map = originalProgress.toMap();
      final reconstructed = InteractiveProgress.fromMap(map);

      expect(reconstructed.guideId, equals(originalProgress.guideId));
      expect(reconstructed.contentId, equals(originalProgress.contentId));
      expect(reconstructed.totalSteps, equals(originalProgress.totalSteps));
      expect(reconstructed.averageScore, equals(originalProgress.averageScore));
      expect(reconstructed.isCompleted, equals(originalProgress.isCompleted));
    });
  });
}
