import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/models/guide/guide_progress_data.dart';

void main() {
  group('GuideProgressData Tests', () {
    test('creates new section progress correctly', () {
      const guideId = 'test_guide';
      const sectionId = 'section_1';

      final progress = GuideProgressData.newSection(
        guideId: guideId,
        sectionId: sectionId,
      );

      expect(progress.guideId, equals(guideId));
      expect(progress.sectionId, equals(sectionId));
      expect(progress.isCompleted, isFalse);
      expect(progress.timeSpent, equals(0));
      expect(progress.scrollProgress, equals(0.0));
      expect(progress.visitCount, equals(1));
    });

    test('creates completed progress correctly', () {
      const guideId = 'test_guide';
      const sectionId = 'section_1';
      const timeSpent = 300;

      final progress = GuideProgressData.completed(
        guideId: guideId,
        sectionId: sectionId,
        timeSpent: timeSpent,
      );

      expect(progress.guideId, equals(guideId));
      expect(progress.sectionId, equals(sectionId));
      expect(progress.isCompleted, isTrue);
      expect(progress.timeSpent, equals(timeSpent));
      expect(progress.scrollProgress, equals(1.0));
      expect(progress.completionTimestamp, isNotNull);
    });

    test('updates visit correctly', () {
      final progress = GuideProgressData.newSection(
        guideId: 'test_guide',
        sectionId: 'section_1',
      );

      final updatedProgress = progress.updateVisit(
        additionalTimeSpent: 120,
        newScrollProgress: 0.5,
      );

      expect(updatedProgress.timeSpent, equals(120));
      expect(updatedProgress.scrollProgress, equals(0.5));
      expect(updatedProgress.visitCount, equals(2));
      expect(updatedProgress.isCompleted, isFalse);
    });

    test('auto-completes when scroll progress reaches 90%', () {
      final progress = GuideProgressData.newSection(
        guideId: 'test_guide',
        sectionId: 'section_1',
      );

      final updatedProgress = progress.updateVisit(
        newScrollProgress: 0.9,
        markCompleted: true,
      );

      expect(updatedProgress.isCompleted, isTrue);
      expect(updatedProgress.scrollProgress, equals(0.9));
      expect(updatedProgress.completionTimestamp, isNotNull);
    });

    test('generates correct progress key', () {
      final progress = GuideProgressData.newSection(
        guideId: 'test_guide',
        sectionId: 'section_1',
      );

      expect(progress.progressKey, equals('test_guide_section_1'));
    });

    test('calculates completion percentage correctly', () {
      final incompleteProgress = GuideProgressData.newSection(
        guideId: 'test_guide',
        sectionId: 'section_1',
      ).updateVisit(newScrollProgress: 0.7);

      final completeProgress = GuideProgressData.completed(
        guideId: 'test_guide',
        sectionId: 'section_2',
        timeSpent: 300,
      );

      expect(incompleteProgress.completionPercentage, equals(0.7));
      expect(completeProgress.completionPercentage, equals(1.0));
    });

    test('formats time spent correctly', () {
      final progress1 = GuideProgressData.newSection(
        guideId: 'test_guide',
        sectionId: 'section_1',
      ).updateVisit(additionalTimeSpent: 45);

      final progress2 = GuideProgressData.newSection(
        guideId: 'test_guide',
        sectionId: 'section_2',
      ).updateVisit(additionalTimeSpent: 150);

      final progress3 = GuideProgressData.newSection(
        guideId: 'test_guide',
        sectionId: 'section_3',
      ).updateVisit(additionalTimeSpent: 3900);

      expect(progress1.formattedTimeSpent, equals('45s'));
      expect(progress2.formattedTimeSpent, equals('2m 30s'));
      expect(progress3.formattedTimeSpent, equals('1h 5m'));
    });

    test('detects recently visited sections', () {
      final now = DateTime.now();
      final recentProgress = GuideProgressData(
        guideId: 'test_guide',
        sectionId: 'section_1',
        isCompleted: false,
        timeSpent: 100,
        lastVisited: now.subtract(const Duration(hours: 2)),
      );

      final oldProgress = GuideProgressData(
        guideId: 'test_guide',
        sectionId: 'section_2',
        isCompleted: false,
        timeSpent: 100,
        lastVisited: now.subtract(const Duration(days: 2)),
      );

      expect(recentProgress.isRecentlyVisited, isTrue);
      expect(oldProgress.isRecentlyVisited, isFalse);
    });
  });
}
