import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/models/guide/personal_note_data.dart';
import 'package:moroccanaccounting/services/note_sharing_service.dart';

void main() {
  group('NoteSharingService', () {
    late NoteSharingService service;
    late PersonalNoteData testNote;

    setUp(() async {
      service = NoteSharingService();
      await service.initialize();
      
      testNote = PersonalNoteData(
        id: 'test_note_1',
        guideId: 'test_guide',
        sectionId: 'test_section',
        title: 'Test Note Title',
        content: 'This is a test note content with some **markdown** formatting.',
        noteType: NoteType.text,
        tags: ['test', 'example'],
        isPrivate: false,
        reminderDate: DateTime.now().add(const Duration(days: 7)),
      );
    });

    group('Single Note Export', () {
      test('should export note as JSON', () async {
        final jsonContent = await service.exportNote(
          testNote,
          ExportFormat.json,
        );

        expect(jsonContent, contains('"id": "test_note_1"'));
        expect(jsonContent, contains('"title": "Test Note Title"'));
        expect(jsonContent, contains('"content": "This is a test note content'));
        expect(jsonContent, contains('"tags": ["test", "example"]'));
      });

      test('should export note as Markdown', () async {
        final markdownContent = await service.exportNote(
          testNote,
          ExportFormat.markdown,
        );

        expect(markdownContent, contains('# Test Note Title'));
        expect(markdownContent, contains('This is a test note content'));
        expect(markdownContent, contains('**Type:** Texte'));
        expect(markdownContent, contains('**Tags:** test, example'));
      });

      test('should export note as HTML', () async {
        final htmlContent = await service.exportNote(
          testNote,
          ExportFormat.html,
        );

        expect(htmlContent, contains('<!DOCTYPE html>'));
        expect(htmlContent, contains('<title>Test Note Title</title>'));
        expect(htmlContent, contains('<h1>Test Note Title</h1>'));
        expect(htmlContent, contains('This is a test note content'));
        expect(htmlContent, contains('<strong>Type:</strong> Texte'));
      });

      test('should export note as plain text', () async {
        final textContent = await service.exportNote(
          testNote,
          ExportFormat.txt,
        );

        expect(textContent, contains('Test Note Title'));
        expect(textContent, contains('================='));
        expect(textContent, contains('This is a test note content'));
        expect(textContent, contains('Type: Texte'));
        expect(textContent, contains('Tags: test, example'));
      });

      test('should export note as CSV', () async {
        final csvContent = await service.exportNote(
          testNote,
          ExportFormat.csv,
        );

        expect(csvContent, contains('ID,Title,Content,Type,Created,Modified'));
        expect(csvContent, contains('"test_note_1"'));
        expect(csvContent, contains('"Test Note Title"'));
        expect(csvContent, contains('"Texte"'));
      });

      test('should export note without metadata when specified', () async {
        final jsonContent = await service.exportNote(
          testNote,
          ExportFormat.json,
          includeMetadata: false,
        );

        expect(jsonContent, isNot(contains('"createdAt"')));
        expect(jsonContent, isNot(contains('"lastModified"')));
        expect(jsonContent, isNot(contains('"viewCount"')));
      });

      test('should export note without links when specified', () async {
        final markdownContent = await service.exportNote(
          testNote,
          ExportFormat.markdown,
          includeLinks: false,
        );

        expect(markdownContent, isNot(contains('## Liaisons')));
      });
    });

    group('Multiple Notes Export', () {
      late List<PersonalNoteData> testNotes;

      setUp(() {
        testNotes = [
          testNote,
          PersonalNoteData(
            id: 'test_note_2',
            guideId: 'test_guide',
            sectionId: 'test_section',
            title: 'Second Test Note',
            content: 'Content of the second note.',
            noteType: NoteType.checklist,
            tags: ['checklist'],
            isPrivate: true,
          ),
          PersonalNoteData(
            id: 'test_note_3',
            guideId: 'test_guide',
            sectionId: 'test_section',
            title: 'Third Test Note',
            content: 'Content of the third note.',
            noteType: NoteType.reminder,
            tags: ['reminder'],
            isPrivate: false,
          ),
        ];
      });

      test('should export multiple notes as JSON', () async {
        final jsonContent = await service.exportNotes(
          testNotes,
          ExportFormat.json,
        );

        expect(jsonContent, contains('"noteCount": 3'));
        expect(jsonContent, contains('"Test Note Title"'));
        expect(jsonContent, contains('"Second Test Note"'));
        expect(jsonContent, contains('"Third Test Note"'));
      });

      test('should export multiple notes as Markdown', () async {
        final markdownContent = await service.exportNotes(
          testNotes,
          ExportFormat.markdown,
        );

        expect(markdownContent, contains('# Collection de Notes'));
        expect(markdownContent, contains('Nombre de notes: 3'));
        expect(markdownContent, contains('# Test Note Title'));
        expect(markdownContent, contains('# Second Test Note'));
        expect(markdownContent, contains('---')); // Separator between notes
      });

      test('should exclude private notes when specified', () async {
        final jsonContent = await service.exportNotes(
          testNotes,
          ExportFormat.json,
          includePrivate: false,
        );

        expect(jsonContent, contains('"noteCount": 2'));
        expect(jsonContent, contains('"Test Note Title"'));
        expect(jsonContent, isNot(contains('"Second Test Note"'))); // Private note
        expect(jsonContent, contains('"Third Test Note"'));
      });

      test('should export multiple notes as HTML', () async {
        final htmlContent = await service.exportNotes(
          testNotes,
          ExportFormat.html,
        );

        expect(htmlContent, contains('<!DOCTYPE html>'));
        expect(htmlContent, contains('<h1>Collection de Notes</h1>'));
        expect(htmlContent, contains('Nombre de notes: 3'));
        expect(htmlContent, contains('<div class="note">'));
      });

      test('should export multiple notes as CSV', () async {
        final csvContent = await service.exportNotes(
          testNotes,
          ExportFormat.csv,
        );

        final lines = csvContent.split('\n');
        expect(lines.length, greaterThan(3)); // Header + 3 notes + empty line
        expect(lines[0], contains('ID,Title,Content,Type'));
        expect(csvContent, contains('"test_note_1"'));
        expect(csvContent, contains('"test_note_2"'));
        expect(csvContent, contains('"test_note_3"'));
      });
    });

    group('Sharing Functionality', () {
      test('should create shareable link', () async {
        final shareLink = await service.createShareableLink(
          testNote,
          isPublic: true,
          allowComments: true,
          allowEditing: false,
        );

        expect(shareLink, startsWith('https://app.example.com/shared/'));
        expect(shareLink, contains('share_'));
      });

      test('should create shareable link with expiration', () async {
        final expirationDate = DateTime.now().add(const Duration(days: 30));
        final shareLink = await service.createShareableLink(
          testNote,
          isPublic: true,
          expiresAt: expirationDate,
        );

        expect(shareLink, isNotNull);
        expect(shareLink, startsWith('https://app.example.com/shared/'));
      });
    });

    group('File Operations', () {
      test('should sanitize file names', () async {
        final noteWithSpecialChars = PersonalNoteData(
          id: 'test_note_special',
          guideId: 'test_guide',
          sectionId: 'test_section',
          title: 'Note with Special/Characters: <Test>',
          content: 'Test content',
        );

        // This would be tested by checking the internal _sanitizeFileName method
        // In a real implementation, you might make this method public for testing
        expect(true, isTrue); // Placeholder assertion
      });

      test('should get correct file extension for format', () async {
        // This would test the internal _getFileExtension method
        // In a real implementation, you might make this method public for testing
        expect(true, isTrue); // Placeholder assertion
      });
    });

    group('Content Formatting', () {
      test('should escape HTML content properly', () async {
        final noteWithHtml = PersonalNoteData(
          id: 'test_note_html',
          guideId: 'test_guide',
          sectionId: 'test_section',
          title: 'Note with <HTML> & "quotes"',
          content: 'Content with <script>alert("xss")</script> & special chars',
        );

        final htmlContent = await service.exportNote(
          noteWithHtml,
          ExportFormat.html,
        );

        expect(htmlContent, contains('&lt;HTML&gt;'));
        expect(htmlContent, contains('&quot;quotes&quot;'));
        expect(htmlContent, contains('&lt;script&gt;'));
        expect(htmlContent, contains('&amp;'));
      });

      test('should escape CSV content properly', () async {
        final noteWithCsv = PersonalNoteData(
          id: 'test_note_csv',
          guideId: 'test_guide',
          sectionId: 'test_section',
          title: 'Note with "quotes" and, commas',
          content: 'Content with "double quotes" and, commas',
        );

        final csvContent = await service.exportNote(
          noteWithCsv,
          ExportFormat.csv,
        );

        expect(csvContent, contains('""quotes""'));
        expect(csvContent, contains('""double quotes""'));
      });

      test('should format dates consistently', () async {
        final jsonContent = await service.exportNote(
          testNote,
          ExportFormat.json,
        );

        final markdownContent = await service.exportNote(
          testNote,
          ExportFormat.markdown,
        );

        // Both should contain formatted dates
        expect(jsonContent, contains('createdAt'));
        expect(markdownContent, contains('Créé le:'));
      });
    });

    group('Network Export', () {
      test('should export note network', () async {
        final allNotes = [
          testNote,
          PersonalNoteData(
            id: 'connected_note_1',
            guideId: 'test_guide',
            sectionId: 'test_section',
            title: 'Connected Note 1',
            content: 'This note is connected to the test note',
          ),
          PersonalNoteData(
            id: 'connected_note_2',
            guideId: 'test_guide',
            sectionId: 'test_section',
            title: 'Connected Note 2',
            content: 'This note is also connected',
          ),
        ];

        final networkExport = await service.exportNoteNetwork(
          testNote,
          allNotes,
          format: ExportFormat.json,
          maxDepth: 2,
        );

        expect(networkExport, contains('"centerNote"'));
        expect(networkExport, contains('"connectedNotes"'));
        expect(networkExport, contains('"links"'));
        expect(networkExport, contains('"maxDepth": 2'));
      });
    });

    group('Error Handling', () {
      test('should handle empty note list gracefully', () async {
        final result = await service.exportNotes(
          [],
          ExportFormat.json,
        );

        expect(result, contains('"noteCount": 0'));
        expect(result, contains('"notes": []'));
      });

      test('should handle note with null values gracefully', () async {
        final noteWithNulls = PersonalNoteData(
          id: 'test_note_nulls',
          guideId: 'test_guide',
          sectionId: 'test_section',
          title: 'Note with nulls',
          content: 'Content',
          tags: [],
          reminderDate: null,
        );

        final jsonContent = await service.exportNote(
          noteWithNulls,
          ExportFormat.json,
        );

        expect(jsonContent, contains('"reminderDate": null'));
        expect(jsonContent, contains('"tags": []'));
      });
    });
  });
}
