import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:moroccanaccounting/models/guide/note_collaboration_data.dart';
import 'package:moroccanaccounting/services/note_collaboration_service.dart';

void main() {
  group('NoteCollaborationService', () {
    late NoteCollaborationService service;
    
    setUpAll(() async {
      await Hive.initFlutter();
      // Register adapters for collaboration models
      // Note: In a real implementation, these would be generated
    });

    setUp(() async {
      service = NoteCollaborationService();
      await service.initialize();
    });

    tearDown(() async {
      await Hive.deleteBoxFromDisk('note_comments');
      await Hive.deleteBoxFromDisk('collaboration_activities');
      await Hive.deleteBoxFromDisk('editing_sessions');
    });

    group('Comment Management', () {
      test('should add a comment to a note', () async {
        final comment = await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: '<PERSON>',
          content: 'This is a test comment',
          commentType: CommentType.general,
        );

        expect(comment.noteId, equals('note1'));
        expect(comment.authorId, equals('user1'));
        expect(comment.authorName, equals('John Doe'));
        expect(comment.content, equals('This is a test comment'));
        expect(comment.commentType, equals(CommentType.general));
        expect(comment.isResolved, isFalse);
      });

      test('should get comments for a specific note', () async {
        await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'First comment',
        );
        await service.addComment(
          noteId: 'note1',
          authorId: 'user2',
          authorName: 'Jane Smith',
          content: 'Second comment',
        );
        await service.addComment(
          noteId: 'note2',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Comment on different note',
        );

        final commentsForNote1 = await service.getCommentsForNote('note1');
        expect(commentsForNote1.length, equals(2));
        expect(commentsForNote1.every((c) => c.noteId == 'note1'), isTrue);
      });

      test('should create threaded comments', () async {
        final parentComment = await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Parent comment',
        );

        final replyComment = await service.addComment(
          noteId: 'note1',
          authorId: 'user2',
          authorName: 'Jane Smith',
          content: 'Reply to parent',
          parentCommentId: parentComment.id,
        );

        expect(replyComment.parentCommentId, equals(parentComment.id));
        expect(replyComment.isThreaded, isTrue);

        final threadedComments = await service.getThreadedComments(parentComment.id);
        expect(threadedComments.length, equals(1));
        expect(threadedComments.first.id, equals(replyComment.id));
      });

      test('should resolve a comment', () async {
        final comment = await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'This needs to be resolved',
        );

        await service.resolveComment(comment.id, 'user2', 'Jane Smith');

        final comments = await service.getCommentsForNote('note1');
        final resolvedComment = comments.firstWhere((c) => c.id == comment.id);
        expect(resolvedComment.isResolved, isTrue);
      });

      test('should get unresolved comments', () async {
        final comment1 = await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Unresolved comment',
        );

        final comment2 = await service.addComment(
          noteId: 'note1',
          authorId: 'user2',
          authorName: 'Jane Smith',
          content: 'Another comment',
        );

        await service.resolveComment(comment2.id, 'user1', 'John Doe');

        final unresolvedComments = await service.getUnresolvedComments('note1');
        expect(unresolvedComments.length, equals(1));
        expect(unresolvedComments.first.id, equals(comment1.id));
      });

      test('should delete a comment', () async {
        final comment = await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Comment to be deleted',
        );

        await service.deleteComment(comment.id);

        final comments = await service.getCommentsForNote('note1');
        expect(comments.isEmpty, isTrue);
      });

      test('should create comments with different types', () async {
        final suggestionComment = await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'I suggest changing this',
          commentType: CommentType.suggestion,
        );

        final questionComment = await service.addComment(
          noteId: 'note1',
          authorId: 'user2',
          authorName: 'Jane Smith',
          content: 'What does this mean?',
          commentType: CommentType.question,
        );

        expect(suggestionComment.commentType, equals(CommentType.suggestion));
        expect(questionComment.commentType, equals(CommentType.question));
        expect(suggestionComment.getCommentTypeLabel(), equals('Suggestion'));
        expect(questionComment.getCommentTypeLabel(), equals('Question'));
      });
    });

    group('Activity Management', () {
      test('should log activities when comments are added', () async {
        await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Test comment',
        );

        final activities = await service.getActivitiesForNote('note1');
        expect(activities.length, equals(1));
        expect(activities.first.activityType, equals(ActivityType.commentAdded));
        expect(activities.first.userId, equals('user1'));
        expect(activities.first.userName, equals('John Doe'));
      });

      test('should log activities when comments are resolved', () async {
        final comment = await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Test comment',
        );

        await service.resolveComment(comment.id, 'user2', 'Jane Smith');

        final activities = await service.getActivitiesForNote('note1');
        expect(activities.length, equals(2)); // One for add, one for resolve
        
        final resolveActivity = activities.firstWhere(
          (a) => a.activityType == ActivityType.commentResolved,
        );
        expect(resolveActivity.userId, equals('user2'));
        expect(resolveActivity.userName, equals('Jane Smith'));
      });

      test('should get recent activities across all notes', () async {
        await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Comment on note 1',
        );

        await service.addComment(
          noteId: 'note2',
          authorId: 'user2',
          authorName: 'Jane Smith',
          content: 'Comment on note 2',
        );

        final recentActivities = await service.getRecentActivities(limit: 10);
        expect(recentActivities.length, equals(2));
        
        // Should be sorted by timestamp (most recent first)
        expect(recentActivities.first.timestamp.isAfter(recentActivities.last.timestamp) ||
               recentActivities.first.timestamp.isAtSameMomentAs(recentActivities.last.timestamp), 
               isTrue);
      });
    });

    group('Editing Session Management', () {
      test('should start an editing session', () async {
        final session = await service.startEditingSession(
          noteId: 'note1',
          userId: 'user1',
          userName: 'John Doe',
        );

        expect(session.noteId, equals('note1'));
        expect(session.userId, equals('user1'));
        expect(session.userName, equals('John Doe'));
        expect(session.isActive, isTrue);
      });

      test('should end an editing session', () async {
        final session = await service.startEditingSession(
          noteId: 'note1',
          userId: 'user1',
          userName: 'John Doe',
        );

        await service.endEditingSession(session.id, 'user1', 'John Doe');

        final activeSessions = await service.getActiveSessionsForNote('note1');
        expect(activeSessions.isEmpty, isTrue);
      });

      test('should get active sessions for a note', () async {
        await service.startEditingSession(
          noteId: 'note1',
          userId: 'user1',
          userName: 'John Doe',
        );

        await service.startEditingSession(
          noteId: 'note1',
          userId: 'user2',
          userName: 'Jane Smith',
        );

        await service.startEditingSession(
          noteId: 'note2',
          userId: 'user1',
          userName: 'John Doe',
        );

        final activeSessionsNote1 = await service.getActiveSessionsForNote('note1');
        expect(activeSessionsNote1.length, equals(2));
        expect(activeSessionsNote1.every((s) => s.noteId == 'note1'), isTrue);
      });

      test('should check if note is being edited by others', () async {
        await service.startEditingSession(
          noteId: 'note1',
          userId: 'user1',
          userName: 'John Doe',
        );

        final isBeingEditedByOthers = await service.isNoteBeingEdited('note1', 'user2');
        expect(isBeingEditedByOthers, isTrue);

        final isBeingEditedBySelf = await service.isNoteBeingEdited('note1', 'user1');
        expect(isBeingEditedBySelf, isFalse);
      });

      test('should get active editors for a note', () async {
        await service.startEditingSession(
          noteId: 'note1',
          userId: 'user1',
          userName: 'John Doe',
        );

        await service.startEditingSession(
          noteId: 'note1',
          userId: 'user2',
          userName: 'Jane Smith',
        );

        final activeEditors = await service.getActiveEditorsForNote('note1');
        expect(activeEditors.length, equals(2));
        expect(activeEditors.contains('John Doe'), isTrue);
        expect(activeEditors.contains('Jane Smith'), isTrue);
      });
    });

    group('Collaboration Statistics', () {
      test('should calculate collaboration statistics', () async {
        // Add comments
        final comment1 = await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'First comment',
        );

        await service.addComment(
          noteId: 'note1',
          authorId: 'user2',
          authorName: 'Jane Smith',
          content: 'Second comment',
        );

        // Resolve one comment
        await service.resolveComment(comment1.id, 'user1', 'John Doe');

        // Start editing session
        await service.startEditingSession(
          noteId: 'note1',
          userId: 'user3',
          userName: 'Bob Wilson',
        );

        final statistics = await service.getCollaborationStatistics('note1');

        expect(statistics.totalComments, equals(2));
        expect(statistics.unresolvedComments, equals(1));
        expect(statistics.resolvedComments, equals(1));
        expect(statistics.uniqueCollaborators, equals(3)); // user1, user2, user3
        expect(statistics.activeEditors, equals(1));
        expect(statistics.isActivelyCollaborated, isTrue);
      });

      test('should calculate collaboration score', () async {
        // Add multiple comments and collaborators
        await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Comment 1',
        );

        final comment2 = await service.addComment(
          noteId: 'note1',
          authorId: 'user2',
          authorName: 'Jane Smith',
          content: 'Comment 2',
        );

        await service.resolveComment(comment2.id, 'user1', 'John Doe');

        await service.startEditingSession(
          noteId: 'note1',
          userId: 'user3',
          userName: 'Bob Wilson',
        );

        final statistics = await service.getCollaborationStatistics('note1');
        final score = statistics.collaborationScore;

        expect(score, greaterThan(0));
        expect(score, lessThanOrEqualTo(100));
      });
    });

    group('Export Collaboration Data', () {
      test('should export collaboration data for a note', () async {
        await service.addComment(
          noteId: 'note1',
          authorId: 'user1',
          authorName: 'John Doe',
          content: 'Test comment',
        );

        await service.startEditingSession(
          noteId: 'note1',
          userId: 'user1',
          userName: 'John Doe',
        );

        final exportData = await service.exportCollaborationData('note1');

        expect(exportData['version'], equals('1.0'));
        expect(exportData['noteId'], equals('note1'));
        expect(exportData['statistics'], isA<Map<String, dynamic>>());
        expect(exportData['comments'], isA<List>());
        expect(exportData['activities'], isA<List>());
        expect((exportData['comments'] as List).length, equals(1));
        expect((exportData['activities'] as List).length, greaterThan(0));
      });
    });
  });
}
