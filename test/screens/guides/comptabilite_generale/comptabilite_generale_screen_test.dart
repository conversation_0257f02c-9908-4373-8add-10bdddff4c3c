import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_generale/comptabilite_generale_screen.dart';
import 'package:moroccanaccounting/theme/app_theme.dart';

void main() {
  testWidgets('ComptabiliteGeneraleScreen uses theme colors correctly',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        theme: AppTheme.getThemeData(AppTheme.getLightColorScheme()),
        home: const ComptabiliteGeneraleScreen(),
      ),
    );

    // Verify header uses theme colors
    final headerFinder = find.text('Principes Fondamentaux');
    expect(headerFinder, findsOneWidget);
    
    final headerText = tester.widget<Text>(headerFinder);
    expect(headerText.style?.color, equals(AppTheme.getLightColorScheme().onPrimary));

    // Verify module cards use theme colors
    final modulesFinder = find.text('Sections détaillées');
    expect(modulesFinder, findsOneWidget);

    final moduleText = tester.widget<Text>(modulesFinder);
    expect(moduleText.style?.color, equals(AppTheme.getLightColorScheme().onSurface));

    // Verify all module cards are present
    expect(find.text('Introduction'), findsOneWidget);
    expect(find.text('Documents Comptables'), findsOneWidget);
    expect(find.text('Plan Comptable'), findsOneWidget);
    expect(find.text('Opérations Courantes'), findsOneWidget);
    expect(find.text('Gestion des Stocks'), findsOneWidget);

    // Verify module icons use theme colors
    final moduleColors = AppTheme.getModuleColors(AppTheme.getLightColorScheme());
    final icons = tester.widgetList<Icon>(find.byType(Icon));
    expect(icons.length, greaterThan(0));
    
    // First icon should use first module color
    final firstIcon = icons.first;
    expect(firstIcon.color, equals(moduleColors[0]));
  });

  testWidgets('ComptabiliteGeneraleScreen handles dark theme correctly',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        theme: AppTheme.getThemeData(AppTheme.getDarkColorScheme()),
        home: const ComptabiliteGeneraleScreen(),
      ),
    );

    // Verify header uses dark theme colors
    final headerFinder = find.text('Principes Fondamentaux');
    expect(headerFinder, findsOneWidget);
    
    final headerText = tester.widget<Text>(headerFinder);
    expect(headerText.style?.color, equals(AppTheme.getDarkColorScheme().onPrimary));

    // Verify module cards use dark theme colors
    final modulesFinder = find.text('Sections détaillées');
    expect(modulesFinder, findsOneWidget);

    final moduleText = tester.widget<Text>(modulesFinder);
    expect(moduleText.style?.color, equals(AppTheme.getDarkColorScheme().onSurface));
  });
}
