import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/models/guide/personal_note_data.dart';
import 'package:moroccanaccounting/models/guide/note_collaboration_data.dart';
import 'package:moroccanaccounting/services/note_sharing_service.dart';

void main() {
  group('Note Linking Integration Tests', () {
    test('PersonalNoteData should support linking fields', () {
      final note = PersonalNoteData(
        id: 'test_note_1',
        guideId: 'test_guide',
        sectionId: 'test_section',
        title: 'Test Note',
        content: 'Test content',
        linkedNoteIds: ['note2', 'note3'],
        backlinkedNoteIds: ['note4'],
        isShared: true,
        shareToken: 'test_token',
        viewCount: 5,
        metadata: {'custom': 'data'},
      );

      expect(note.linkedNoteIds, equals(['note2', 'note3']));
      expect(note.backlinkedNoteIds, equals(['note4']));
      expect(note.isShared, isTrue);
      expect(note.shareToken, equals('test_token'));
      expect(note.viewCount, equals(5));
      expect(note.metadata['custom'], equals('data'));
    });

    test('PersonalNoteData should support link management methods', () {
      final note = PersonalNoteData(
        id: 'test_note_1',
        guideId: 'test_guide',
        sectionId: 'test_section',
        title: 'Test Note',
        content: 'Test content',
      );

      // Test connection methods
      expect(note.allConnectedNoteIds, isEmpty);
      expect(note.connectionCount, equals(0));
      expect(note.hasConnections, isFalse);
      expect(note.isLinkedTo('note2'), isFalse);
      expect(note.hasBacklinkFrom('note3'), isFalse);
      expect(note.isConnectedTo('note4'), isFalse);
    });

    test('PersonalNoteData should support sharing methods', () {
      final note = PersonalNoteData(
        id: 'test_note_1',
        guideId: 'test_guide',
        sectionId: 'test_section',
        title: 'Test Note',
        content: 'Test content',
        isPrivate: false,
      );

      expect(note.canBeShared, isTrue);
      expect(note.sharingStatus, equals('Non partagé'));

      final privateNote = PersonalNoteData(
        id: 'test_note_2',
        guideId: 'test_guide',
        sectionId: 'test_section',
        title: 'Private Note',
        content: 'Private content',
        isPrivate: true,
      );

      expect(privateNote.canBeShared, isFalse);
    });

    test('NoteLink should have correct properties', () {
      final link = NoteLink(
        id: 'link1',
        sourceNoteId: 'note1',
        targetNoteId: 'note2',
        linkType: NoteLinkType.reference,
        description: 'Test link',
      );

      expect(link.id, equals('link1'));
      expect(link.sourceNoteId, equals('note1'));
      expect(link.targetNoteId, equals('note2'));
      expect(link.linkType, equals(NoteLinkType.reference));
      expect(link.description, equals('Test link'));
      expect(link.getLinkTypeLabel(), equals('Référence'));
    });

    test('NoteLink should support different link types', () {
      final linkTypes = [
        NoteLinkType.reference,
        NoteLinkType.related,
        NoteLinkType.followUp,
        NoteLinkType.prerequisite,
        NoteLinkType.child,
        NoteLinkType.parent,
      ];

      final expectedLabels = [
        'Référence',
        'Lié',
        'Suivi',
        'Prérequis',
        'Sous-note',
        'Note parent',
      ];

      for (int i = 0; i < linkTypes.length; i++) {
        final link = NoteLink(
          id: 'link$i',
          sourceNoteId: 'note1',
          targetNoteId: 'note2',
          linkType: linkTypes[i],
        );

        expect(link.getLinkTypeLabel(), equals(expectedLabels[i]));
      }
    });

    test('NoteShareInfo should validate correctly', () {
      final validShare = NoteShareInfo(
        id: 'share1',
        noteId: 'note1',
        shareToken: 'token123',
        isPublic: true,
        expiresAt: DateTime.now().add(const Duration(days: 30)),
      );

      expect(validShare.isValid, isTrue);

      final expiredShare = NoteShareInfo(
        id: 'share2',
        noteId: 'note1',
        shareToken: 'token456',
        isPublic: true,
        expiresAt: DateTime.now().subtract(const Duration(days: 1)),
      );

      expect(expiredShare.isValid, isFalse);

      final neverExpiresShare = NoteShareInfo(
        id: 'share3',
        noteId: 'note1',
        shareToken: 'token789',
        isPublic: true,
        expiresAt: null,
      );

      expect(neverExpiresShare.isValid, isTrue);
    });

    test('NoteComment should have correct properties', () {
      final comment = NoteComment(
        id: 'comment1',
        noteId: 'note1',
        authorId: 'user1',
        authorName: 'John Doe',
        content: 'This is a test comment',
        commentType: CommentType.suggestion,
        isResolved: false,
      );

      expect(comment.id, equals('comment1'));
      expect(comment.noteId, equals('note1'));
      expect(comment.authorId, equals('user1'));
      expect(comment.authorName, equals('John Doe'));
      expect(comment.content, equals('This is a test comment'));
      expect(comment.commentType, equals(CommentType.suggestion));
      expect(comment.isResolved, isFalse);
      expect(comment.getCommentTypeLabel(), equals('Suggestion'));
      expect(comment.isThreaded, isFalse);
      expect(comment.isLineSpecific, isFalse);
      expect(comment.isTextSelection, isFalse);
    });

    test('NoteComment should support threading', () {
      final parentComment = NoteComment(
        id: 'comment1',
        noteId: 'note1',
        authorId: 'user1',
        authorName: 'John Doe',
        content: 'Parent comment',
      );

      final childComment = NoteComment(
        id: 'comment2',
        noteId: 'note1',
        authorId: 'user2',
        authorName: 'Jane Smith',
        content: 'Reply comment',
        parentCommentId: 'comment1',
      );

      expect(parentComment.isThreaded, isFalse);
      expect(childComment.isThreaded, isTrue);
      expect(childComment.parentCommentId, equals('comment1'));
    });

    test('CollaborationActivity should have correct properties', () {
      final activity = CollaborationActivity(
        id: 'activity1',
        noteId: 'note1',
        userId: 'user1',
        userName: 'John Doe',
        activityType: ActivityType.commentAdded,
        description: 'Added a comment',
      );

      expect(activity.id, equals('activity1'));
      expect(activity.noteId, equals('note1'));
      expect(activity.userId, equals('user1'));
      expect(activity.userName, equals('John Doe'));
      expect(activity.activityType, equals(ActivityType.commentAdded));
      expect(activity.description, equals('Added a comment'));
      expect(activity.getActivityTypeLabel(), equals('Commentaire ajouté'));
    });

    test('EditingSession should track session state', () {
      final session = EditingSession(
        id: 'session1',
        noteId: 'note1',
        userId: 'user1',
        userName: 'John Doe',
      );

      expect(session.id, equals('session1'));
      expect(session.noteId, equals('note1'));
      expect(session.userId, equals('user1'));
      expect(session.userName, equals('John Doe'));
      expect(session.isActive, isTrue);
      expect(session.duration.inSeconds, greaterThanOrEqualTo(0));
    });

    test('ExportFormat enum should be accessible', () {
      expect(ExportFormat.json, isNotNull);
      expect(ExportFormat.markdown, isNotNull);
      expect(ExportFormat.html, isNotNull);
      expect(ExportFormat.txt, isNotNull);
      expect(ExportFormat.csv, isNotNull);
    });
  });
}
