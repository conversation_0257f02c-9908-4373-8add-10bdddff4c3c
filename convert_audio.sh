#!/bin/bash
# <PERSON>ript to convert MP3 files to WebM and OGG formats for web compatibility
# Requires ffmpeg to be installed

# Check if ffmpeg is installed
if ! command -v ffmpeg &> /dev/null; then
    echo "Error: ffmpeg is not installed. Please install it first."
    echo "On Windows: Use chocolatey with 'choco install ffmpeg'"
    echo "On macOS: Use homebrew with 'brew install ffmpeg'"
    echo "On Linux: Use apt with 'sudo apt install ffmpeg'"
    exit 1
fi

# Directory containing MP3 files
SOUND_DIR="assets/sounds"

# Check if directory exists
if [ ! -d "$SOUND_DIR" ]; then
    echo "Error: Directory $SOUND_DIR does not exist."
    exit 1
fi

echo "Converting audio files in $SOUND_DIR to web-friendly formats..."

# Process each MP3 file
for mp3_file in "$SOUND_DIR"/*.mp3; do
    if [ -f "$mp3_file" ]; then
        filename=$(basename -- "$mp3_file")
        basename="${filename%.*}"
        
        echo "Converting $filename to WebM format..."
        ffmpeg -i "$mp3_file" -c:a libopus -b:a 128k "$SOUND_DIR/$basename.webm" -y
        
        echo "Converting $filename to OGG format..."
        ffmpeg -i "$mp3_file" -c:a libvorbis -q:a 4 "$SOUND_DIR/$basename.ogg" -y
        
        echo "Conversion complete for $filename"
    fi
done

echo "All conversions completed successfully!"
echo "Remember to add these files to your pubspec.yaml if you haven't already." 