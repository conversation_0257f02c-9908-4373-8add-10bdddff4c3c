# Note Linking and Sharing Features

This document describes the comprehensive note linking and sharing capabilities added to the Moroccan Accounting application.

## Overview

The note linking and sharing system provides users with powerful tools to:
- Create connections between related notes
- Visualize note relationships through interactive graphs
- Share notes with others through various formats and methods
- Collaborate on notes with comments and real-time editing
- Export notes in multiple formats for external use

## Features

### 1. Note Linking System

#### Link Types
- **Reference**: Simple reference to another note
- **Related**: Notes with related content
- **Follow-up**: Sequential relationship (this note follows another)
- **Prerequisite**: Dependency relationship (this note requires another)
- **Child**: Hierarchical relationship (sub-note)
- **Parent**: Hierarchical relationship (parent note)

#### Capabilities
- Create bidirectional links between notes
- Navigate through connected notes
- Find notes within N degrees of separation
- Identify hub notes (highly connected)
- Detect orphaned notes (no connections)

### 2. Note Sharing

#### Export Formats
- **JSON**: Structured data with full metadata
- **Markdown**: Human-readable format with formatting
- **HTML**: Web-ready format with styling
- **Plain Text**: Simple text format
- **CSV**: Spreadsheet-compatible format

#### Sharing Options
- Generate shareable links with access controls
- Export individual notes or collections
- Share note networks (connected notes)
- Copy to clipboard functionality
- Direct platform sharing integration

### 3. Collaborative Features

#### Comments System
- Add comments to notes with different types:
  - General comments
  - Suggestions
  - Questions
  - Approvals
  - Corrections
- Threaded comments (replies)
- Line-specific and text-selection comments
- Comment resolution workflow
- Mention other users in comments

#### Real-time Collaboration
- Track active editing sessions
- Show who is currently editing a note
- Activity logging for all collaborative actions
- Collaboration statistics and scoring

### 4. Visual Graph Representation

#### Interactive Graph
- Visual representation of note connections
- Pan and zoom functionality
- Node sizing based on connection count
- Color coding by note type
- Click to navigate between notes

#### Graph Features
- Filter by note type, tags, or privacy
- Highlight specific notes
- Show/hide orphaned notes
- Export graph visualizations

## Technical Implementation

### Data Models

#### Enhanced PersonalNoteData
```dart
class PersonalNoteData {
  // Existing fields...
  List<String> linkedNoteIds;      // Outgoing links
  List<String> backlinkedNoteIds;  // Incoming links
  String? shareToken;              // Sharing token
  bool isShared;                   // Sharing status
  DateTime? lastSharedAt;          // Last share date
  int viewCount;                   // View statistics
  Map<String, dynamic> metadata;   // Extensible metadata
}
```

#### NoteLink
```dart
class NoteLink {
  String id;
  String sourceNoteId;
  String targetNoteId;
  NoteLinkType linkType;
  String? description;
  DateTime createdAt;
}
```

#### NoteComment
```dart
class NoteComment {
  String id;
  String noteId;
  String authorId;
  String authorName;
  String content;
  CommentType commentType;
  String? parentCommentId;  // For threading
  int? lineNumber;          // For line-specific comments
  bool isResolved;
  List<String> mentions;
}
```

### Services

#### NoteLinkingService
- Manages note links and relationships
- Provides graph traversal algorithms
- Handles sharing metadata
- Generates statistics

#### NoteSharingService
- Exports notes in various formats
- Manages sharing permissions
- Handles file operations
- Provides network export functionality

#### NoteCollaborationService
- Manages comments and discussions
- Tracks editing sessions
- Logs collaborative activities
- Provides collaboration analytics

### UI Components

#### NoteLinksWidget
- Displays note connections
- Allows adding/removing links
- Shows link types and descriptions
- Provides navigation between linked notes

#### NoteGraphWidget
- Interactive graph visualization
- Customizable node and edge styling
- Pan/zoom controls
- Node selection and highlighting

#### NoteSharingWidget
- Sharing controls and options
- Export format selection
- Access permission management
- Share link generation

#### NoteCommentsWidget
- Comment display and management
- Threaded conversation view
- Comment type indicators
- Resolution workflow

## Usage Examples

### Creating Note Links

```dart
final linkingService = NoteLinkingService();
await linkingService.initialize();

// Create a reference link
final link = await linkingService.createLink(
  sourceNoteId: 'note1',
  targetNoteId: 'note2',
  linkType: NoteLinkType.reference,
  description: 'Related concept',
);

// Find connected notes
final connectedNotes = await linkingService.getConnectedNoteIds('note1');

// Find notes within 2 degrees
final nearbyNotes = await linkingService.getNotesWithinDegrees('note1', 2);
```

### Sharing Notes

```dart
final sharingService = NoteSharingService();
await sharingService.initialize();

// Export as Markdown
final markdown = await sharingService.exportNote(
  note,
  NoteSharingService.ExportFormat.markdown,
  includeMetadata: true,
  includeLinks: true,
);

// Create shareable link
final shareLink = await sharingService.createShareableLink(
  note,
  isPublic: true,
  allowComments: true,
  expiresAt: DateTime.now().add(Duration(days: 30)),
);

// Share via platform
await sharingService.shareNote(note, format: ExportFormat.markdown);
```

### Adding Comments

```dart
final collaborationService = NoteCollaborationService();
await collaborationService.initialize();

// Add a comment
final comment = await collaborationService.addComment(
  noteId: 'note1',
  authorId: 'user1',
  authorName: 'John Doe',
  content: 'This section needs clarification',
  commentType: CommentType.question,
);

// Reply to a comment
final reply = await collaborationService.addComment(
  noteId: 'note1',
  authorId: 'user2',
  authorName: 'Jane Smith',
  content: 'I agree, let me explain...',
  parentCommentId: comment.id,
);

// Resolve a comment
await collaborationService.resolveComment(
  comment.id,
  'user1',
  'John Doe',
);
```

## Configuration

### Hive Setup
Register the new adapters in your app initialization:

```dart
void main() async {
  await Hive.initFlutter();
  
  // Register existing adapters
  Hive.registerAdapter(PersonalNoteDataAdapter());
  Hive.registerAdapter(NoteTypeAdapter());
  
  // Register new adapters
  Hive.registerAdapter(NoteLinkAdapter());
  Hive.registerAdapter(NoteLinkTypeAdapter());
  Hive.registerAdapter(NoteShareInfoAdapter());
  Hive.registerAdapter(NoteCommentAdapter());
  Hive.registerAdapter(CommentTypeAdapter());
  Hive.registerAdapter(CollaborationActivityAdapter());
  Hive.registerAdapter(EditingSessionAdapter());
  
  runApp(MyApp());
}
```

### Service Initialization
Initialize services in your app:

```dart
class NotesManager {
  final NoteLinkingService _linkingService = NoteLinkingService();
  final NoteSharingService _sharingService = NoteSharingService();
  final NoteCollaborationService _collaborationService = NoteCollaborationService();
  
  Future<void> initialize() async {
    await _linkingService.initialize();
    await _sharingService.initialize();
    await _collaborationService.initialize();
  }
}
```

## Testing

Comprehensive test suites are provided for all services:

- `test/services/note_linking_service_test.dart`
- `test/services/note_sharing_service_test.dart`
- `test/services/note_collaboration_service_test.dart`

Run tests with:
```bash
flutter test test/services/
```

## Performance Considerations

### Optimization Tips
1. **Lazy Loading**: Load links and comments only when needed
2. **Caching**: Cache frequently accessed note relationships
3. **Pagination**: Implement pagination for large comment threads
4. **Indexing**: Consider indexing for faster note searches
5. **Cleanup**: Regularly clean up expired shares and old activities

### Memory Management
- Dispose of services when no longer needed
- Close streams and controllers properly
- Limit the number of active editing sessions

## Security Considerations

### Data Protection
- Private notes are excluded from exports by default
- Share tokens are generated securely
- Access permissions are enforced at the service level
- Expired shares are automatically cleaned up

### User Privacy
- User IDs and names are stored separately from content
- Comments can be anonymized if needed
- Activity logs can be purged after a certain period

## Future Enhancements

### Planned Features
1. **Real-time Synchronization**: Live updates across devices
2. **Advanced Graph Analytics**: Centrality measures, clustering
3. **AI-Powered Suggestions**: Automatic link recommendations
4. **Version Control**: Track note changes over time
5. **Advanced Permissions**: Fine-grained access control
6. **Integration APIs**: Connect with external services
7. **Mobile Optimizations**: Touch-friendly graph interactions
8. **Offline Support**: Sync when connection is restored

### Extensibility
The system is designed to be extensible:
- Custom link types can be added
- New export formats can be implemented
- Additional comment types are supported
- Metadata system allows for custom fields

## Troubleshooting

### Common Issues
1. **Links not appearing**: Check if both notes exist and services are initialized
2. **Export failures**: Verify note data integrity and format support
3. **Comments not loading**: Ensure collaboration service is properly initialized
4. **Graph performance**: Limit the number of nodes displayed simultaneously

### Debug Tools
- Enable debug logging in services
- Use Hive browser to inspect stored data
- Check service initialization status
- Monitor memory usage during graph operations

## Contributing

When contributing to these features:
1. Follow the existing code patterns
2. Add comprehensive tests for new functionality
3. Update documentation for API changes
4. Consider performance implications
5. Maintain backward compatibility when possible

## License

These features are part of the Moroccan Accounting application and follow the same licensing terms as the main project.
