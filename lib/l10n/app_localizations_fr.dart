// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'Comptabilité Marocaine';

  @override
  String get welcomeMessage =>
      'Bienvenue dans votre guide de comptabilité marocaine';

  @override
  String get planComptable => 'Plan Comptable Marocain';

  @override
  String get terminology => 'Terminologie Comptable';

  @override
  String get exercises => 'Exercices Pratiques';

  @override
  String get references => 'Références';

  @override
  String get quizTitle => 'Quiz';

  @override
  String get quizCategory1 => 'Catégorie 1';

  @override
  String get quizCategory2 => 'Catégorie 2';

  @override
  String get quizCategory3 => 'Catégorie 3';

  @override
  String quizWelcomeMessage(Object category) {
    return 'Bienvenue dans $category';
  }

  @override
  String get quizStartButton => 'Commencer le Quiz';

  @override
  String get liasseFiscaleTitle => 'Liasse Fiscale';

  @override
  String get liasseFiscaleDescription =>
      'Documents et obligations fiscales annuelles';

  @override
  String get liasseFiscaleGuideTab => 'Guide';

  @override
  String get liasseFiscaleChecklistTab => 'Check-list';

  @override
  String get liasseFiscaleObligationsTab => 'Obligations';

  @override
  String get liasseFiscaleOverviewTitle => 'Vue d\'ensemble';

  @override
  String get liasseFiscaleFinancialStatementsTitle => 'États financiers';

  @override
  String get liasseFiscaleTaxFormsTitle => 'Formulaires fiscaux';

  @override
  String get liasseFiscalePreparationTitle => 'Processus de préparation';

  @override
  String get liasseFiscaleElectronicFilingTitle => 'Télédéclaration';

  @override
  String get liasseFiscaleCommonErrorsTitle => 'Erreurs courantes';

  @override
  String get liasseFiscaleBestPracticesTitle => 'Bonnes pratiques';

  @override
  String get liasseFiscaleDocumentCategoriesTitle => 'Catégories de documents';

  @override
  String get liasseFiscaleProgressTitle => 'Progression';

  @override
  String get liasseFiscaleMandatoryDocuments => 'Documents obligatoires';

  @override
  String get liasseFiscaleSupportingDocuments => 'Documents justificatifs';

  @override
  String get liasseFiscaleIndustrySpecificDocuments =>
      'Documents spécifiques au secteur';

  @override
  String get liasseFiscaleValidationWarning =>
      'Attention : documents obligatoires manquants';

  @override
  String get liasseFiscaleExportChecklist => 'Exporter la check-list';

  @override
  String get liasseFiscaleSaveProgress => 'Sauvegarder la progression';

  @override
  String get liasseFiscaleSearchDocuments => 'Rechercher des documents';

  @override
  String get liasseFiscaleFilterDocuments => 'Filtrer les documents';

  @override
  String get liasseFiscaleAnnualCalendarTitle => 'Calendrier annuel';

  @override
  String get liasseFiscalePenaltyStructureTitle => 'Structure des pénalités';

  @override
  String get liasseFiscaleComplianceRequirementsTitle =>
      'Exigences de conformité';

  @override
  String get liasseFiscaleRecordKeepingTitle => 'Conservation des documents';

  @override
  String get liasseFiscaleAmendmentProceduresTitle =>
      'Procédures de rectification';

  @override
  String get liasseFiscaleSpecialSituationsTitle => 'Situations particulières';

  @override
  String get liasseFiscaleProfessionalSupportTitle => 'Support professionnel';

  @override
  String get tvaRemboursementTab => 'Remboursement';

  @override
  String get tvaRemboursementTitle => 'Remboursement TVA';

  @override
  String get tvaRemboursementEligibilityTitle => 'Critères d\'éligibilité';

  @override
  String get tvaRemboursementDocumentsTitle => 'Documents requis';

  @override
  String get tvaRemboursementProcessTitle => 'Processus étape par étape';

  @override
  String get tvaRemboursementTemplatesTitle => 'Modèles de documents';

  @override
  String get tvaRemboursementErrorsTitle => 'Erreurs courantes';

  @override
  String get tvaRemboursementTimelineTitle => 'Délais de traitement';

  @override
  String get tvaRemboursementTroubleshootingTitle => 'Résolution de problèmes';

  @override
  String get tvaRemboursementSpecialScenariosTitle => 'Scénarios particuliers';

  @override
  String get tvaRemboursementEligibilityDescription =>
      'Qui peut demander un remboursement';

  @override
  String get tvaRemboursementMinimumAmount => 'Montant minimum';

  @override
  String get tvaRemboursementTimeLimit => 'Délai limite';

  @override
  String get tvaRemboursementQualifyingGoods => 'Biens éligibles';

  @override
  String get tvaRemboursementDocumentChecklist =>
      'Liste de contrôle des documents';

  @override
  String get tvaRemboursementOriginalInvoices => 'Factures originales';

  @override
  String get tvaRemboursementTaxFreeForm => 'Formulaire Tax Free';

  @override
  String get tvaRemboursementPassportRequirement => 'Exigences de passeport';

  @override
  String get tvaRemboursementBankDetails => 'Coordonnées bancaires';

  @override
  String get tvaRemboursementProcessingTime => 'Délai de traitement';

  @override
  String get tvaRemboursementTrackingProcedure => 'Procédure de suivi';

  @override
  String get tvaRemboursementDeniedRefund => 'Remboursement refusé';

  @override
  String get tvaRemboursementDelayedRefund => 'Remboursement retardé';

  @override
  String get tvaRemboursementExportScenarios => 'Scénarios d\'exportation';

  @override
  String get tvaRemboursementBusinessVsTourist => 'Entreprise vs Touriste';

  @override
  String get tvaRemboursementBulkPurchases => 'Achats en gros';

  @override
  String get agriculturePratiquesTitle => 'Pratiques Agricoles';

  @override
  String get agricultureExercicesTitle => 'Exercices Agricoles';

  @override
  String get agricultureCalculateursTitle => 'Calculateurs Agricoles';

  @override
  String get agricultureIrTitle => 'IR Agricole';

  @override
  String get agricultureIsTitle => 'IS Agricole';

  @override
  String get agricultureTvaTitle => 'TVA Agricole';

  @override
  String get agricultureCooperativesTitle => 'Coopératives';

  @override
  String get agricultureComptabiliteTitle => 'Comptabilité Agricole';

  @override
  String get agriculturePracticalScenariosTitle => 'Scénarios pratiques';

  @override
  String get agricultureStepByStepGuidesTitle => 'Guides étape par étape';

  @override
  String get agricultureErrorPreventionTitle => 'Prévention des erreurs';

  @override
  String get agricultureSeasonalConsiderationsTitle =>
      'Considérations saisonnières';

  @override
  String get agricultureSmallFarmGuidanceTitle =>
      'Guidance pour petites exploitations';

  @override
  String get agricultureCrossReferenceTitle => 'Références croisées';

  @override
  String get agricultureProgressiveDifficultyTitle => 'Difficulté progressive';

  @override
  String get agricultureScenarioBasedQuestionsTitle =>
      'Questions basées sur des scénarios';

  @override
  String get agricultureCalculationExercisesTitle => 'Exercices de calcul';

  @override
  String get agricultureMultipleChoiceTitle => 'Questions à choix multiples';

  @override
  String get agricultureCaseStudyAnalysisTitle => 'Analyse d\'études de cas';

  @override
  String get agricultureSelfAssessmentTitle => 'Auto-évaluation';

  @override
  String get agricultureIrCalculatorTitle => 'Calculateur IR Agricole';

  @override
  String get agricultureIsCalculatorTitle => 'Calculateur IS Agricole';

  @override
  String get agricultureTvaCalculatorTitle => 'Calculateur TVA Agricole';

  @override
  String get agricultureAmortizationCalculatorTitle =>
      'Calculateur d\'amortissement';

  @override
  String get agricultureSubsidyCalculatorTitle =>
      'Calculateur d\'impact des subventions';

  @override
  String get agricultureCooperativeCalculatorTitle => 'Calculateur coopérative';

  @override
  String get agricultureRevenueTitle => 'Détermination du revenu';

  @override
  String get agricultureDeductibleExpensesTitle => 'Charges déductibles';

  @override
  String get agricultureTaxExemptionsTitle => 'Exonérations fiscales';

  @override
  String get agricultureProgressiveScaleTitle => 'Barème progressif';

  @override
  String get agricultureDeclarationObligationsTitle =>
      'Obligations déclaratives';

  @override
  String get agricultureSpecialCasesTitle => 'Cas particuliers';

  @override
  String get agricultureFormationRequirementsTitle => 'Exigences de formation';

  @override
  String get agricultureSpecialTaxRegimesTitle => 'Régimes fiscaux spéciaux';

  @override
  String get agricultureMemberTransactionsTitle => 'Transactions des membres';

  @override
  String get agricultureAccountingObligationsTitle => 'Obligations comptables';

  @override
  String get agricultureComplianceCalendarTitle => 'Calendrier de conformité';

  @override
  String get agricultureChartOfAccountsTitle => 'Plan comptable';

  @override
  String get agricultureBiologicalAssetsTitle => 'Actifs biologiques';

  @override
  String get agricultureProductionCyclesTitle => 'Cycles de production';

  @override
  String get agricultureInventoryValuationTitle => 'Évaluation des stocks';

  @override
  String get agricultureSubsidyAccountingTitle =>
      'Comptabilisation des subventions';

  @override
  String get agricultureFinancialStatementsTitle => 'États financiers';

  @override
  String get agriculturePracticalExamplesTitle => 'Exemples pratiques';

  @override
  String get checkboxCompleted => 'Terminé';

  @override
  String get checkboxPending => 'En attente';

  @override
  String progressIndicatorLabel(Object percentage) {
    return 'Progression : $percentage%';
  }

  @override
  String get validationMessageRequired => 'Ce champ est obligatoire';

  @override
  String get validationMessageInvalid => 'Valeur invalide';

  @override
  String get validationMessageMissing => 'Documents manquants';

  @override
  String get helpTextTooltip => 'Aide';

  @override
  String get expandSectionButton => 'Développer';

  @override
  String get collapseSectionButton => 'Réduire';

  @override
  String get saveButton => 'Sauvegarder';

  @override
  String get exportButton => 'Exporter';

  @override
  String get calculateButton => 'Calculer';

  @override
  String get resetButton => 'Réinitialiser';

  @override
  String get nextStepButton => 'Étape suivante';

  @override
  String get previousStepButton => 'Étape précédente';

  @override
  String get finishButton => 'Terminer';

  @override
  String get searchPlaceholder => 'Rechercher...';

  @override
  String get filterPlaceholder => 'Filtrer par...';

  @override
  String get selectAllButton => 'Tout sélectionner';

  @override
  String get deselectAllButton => 'Tout désélectionner';

  @override
  String get loadingMessage => 'Chargement en cours...';

  @override
  String get noDataMessage => 'Aucune donnée disponible';

  @override
  String get errorLoadingData => 'Erreur lors du chargement des données';

  @override
  String get retryButton => 'Réessayer';

  @override
  String get errorNetworkConnection => 'Erreur de connexion réseau';

  @override
  String get errorFileNotFound => 'Fichier non trouvé';

  @override
  String get errorInvalidData => 'Données invalides';

  @override
  String get errorCalculationFailed => 'Échec du calcul';

  @override
  String get errorSaveFailed => 'Échec de la sauvegarde';

  @override
  String get errorExportFailed => 'Échec de l\'exportation';

  @override
  String get helpTextCalculator =>
      'Saisissez les valeurs requises pour effectuer le calcul';

  @override
  String get helpTextChecklist =>
      'Cochez les éléments au fur et à mesure de leur completion';

  @override
  String get helpTextDocuments =>
      'Assurez-vous d\'avoir tous les documents requis';

  @override
  String get helpTextDeadlines =>
      'Respectez les délais pour éviter les pénalités';

  @override
  String get helpTextValidation => 'Vérifiez vos données avant de soumettre';

  @override
  String get helpTextBackup => 'Sauvegardez régulièrement votre progression';

  @override
  String get warningUnsavedChanges =>
      'Vous avez des modifications non sauvegardées';

  @override
  String get warningMissingDocuments => 'Documents obligatoires manquants';

  @override
  String get warningApproachingDeadline => 'Délai approchant';

  @override
  String get warningInvalidInput => 'Saisie invalide détectée';

  @override
  String get confirmationSaved => 'Données sauvegardées avec succès';

  @override
  String get confirmationExported => 'Export réalisé avec succès';

  @override
  String get confirmationCalculated => 'Calcul effectué avec succès';

  @override
  String get confirmationCompleted => 'Processus terminé avec succès';
}
