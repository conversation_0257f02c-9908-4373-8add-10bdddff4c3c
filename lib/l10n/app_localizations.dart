import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('fr')];

  /// No description provided for @appTitle.
  ///
  /// In fr, this message translates to:
  /// **'Comptabilité Marocaine'**
  String get appTitle;

  /// No description provided for @welcomeMessage.
  ///
  /// In fr, this message translates to:
  /// **'Bienvenue dans votre guide de comptabilité marocaine'**
  String get welcomeMessage;

  /// No description provided for @planComptable.
  ///
  /// In fr, this message translates to:
  /// **'Plan Comptable Marocain'**
  String get planComptable;

  /// No description provided for @terminology.
  ///
  /// In fr, this message translates to:
  /// **'Terminologie Comptable'**
  String get terminology;

  /// No description provided for @exercises.
  ///
  /// In fr, this message translates to:
  /// **'Exercices Pratiques'**
  String get exercises;

  /// No description provided for @references.
  ///
  /// In fr, this message translates to:
  /// **'Références'**
  String get references;

  /// No description provided for @quizTitle.
  ///
  /// In fr, this message translates to:
  /// **'Quiz'**
  String get quizTitle;

  /// No description provided for @quizCategory1.
  ///
  /// In fr, this message translates to:
  /// **'Catégorie 1'**
  String get quizCategory1;

  /// No description provided for @quizCategory2.
  ///
  /// In fr, this message translates to:
  /// **'Catégorie 2'**
  String get quizCategory2;

  /// No description provided for @quizCategory3.
  ///
  /// In fr, this message translates to:
  /// **'Catégorie 3'**
  String get quizCategory3;

  /// No description provided for @quizWelcomeMessage.
  ///
  /// In fr, this message translates to:
  /// **'Bienvenue dans {category}'**
  String quizWelcomeMessage(Object category);

  /// No description provided for @quizStartButton.
  ///
  /// In fr, this message translates to:
  /// **'Commencer le Quiz'**
  String get quizStartButton;

  /// No description provided for @liasseFiscaleTitle.
  ///
  /// In fr, this message translates to:
  /// **'Liasse Fiscale'**
  String get liasseFiscaleTitle;

  /// No description provided for @liasseFiscaleDescription.
  ///
  /// In fr, this message translates to:
  /// **'Documents et obligations fiscales annuelles'**
  String get liasseFiscaleDescription;

  /// No description provided for @liasseFiscaleGuideTab.
  ///
  /// In fr, this message translates to:
  /// **'Guide'**
  String get liasseFiscaleGuideTab;

  /// No description provided for @liasseFiscaleChecklistTab.
  ///
  /// In fr, this message translates to:
  /// **'Check-list'**
  String get liasseFiscaleChecklistTab;

  /// No description provided for @liasseFiscaleObligationsTab.
  ///
  /// In fr, this message translates to:
  /// **'Obligations'**
  String get liasseFiscaleObligationsTab;

  /// No description provided for @liasseFiscaleOverviewTitle.
  ///
  /// In fr, this message translates to:
  /// **'Vue d\'ensemble'**
  String get liasseFiscaleOverviewTitle;

  /// No description provided for @liasseFiscaleFinancialStatementsTitle.
  ///
  /// In fr, this message translates to:
  /// **'États financiers'**
  String get liasseFiscaleFinancialStatementsTitle;

  /// No description provided for @liasseFiscaleTaxFormsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Formulaires fiscaux'**
  String get liasseFiscaleTaxFormsTitle;

  /// No description provided for @liasseFiscalePreparationTitle.
  ///
  /// In fr, this message translates to:
  /// **'Processus de préparation'**
  String get liasseFiscalePreparationTitle;

  /// No description provided for @liasseFiscaleElectronicFilingTitle.
  ///
  /// In fr, this message translates to:
  /// **'Télédéclaration'**
  String get liasseFiscaleElectronicFilingTitle;

  /// No description provided for @liasseFiscaleCommonErrorsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Erreurs courantes'**
  String get liasseFiscaleCommonErrorsTitle;

  /// No description provided for @liasseFiscaleBestPracticesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Bonnes pratiques'**
  String get liasseFiscaleBestPracticesTitle;

  /// No description provided for @liasseFiscaleDocumentCategoriesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Catégories de documents'**
  String get liasseFiscaleDocumentCategoriesTitle;

  /// No description provided for @liasseFiscaleProgressTitle.
  ///
  /// In fr, this message translates to:
  /// **'Progression'**
  String get liasseFiscaleProgressTitle;

  /// No description provided for @liasseFiscaleMandatoryDocuments.
  ///
  /// In fr, this message translates to:
  /// **'Documents obligatoires'**
  String get liasseFiscaleMandatoryDocuments;

  /// No description provided for @liasseFiscaleSupportingDocuments.
  ///
  /// In fr, this message translates to:
  /// **'Documents justificatifs'**
  String get liasseFiscaleSupportingDocuments;

  /// No description provided for @liasseFiscaleIndustrySpecificDocuments.
  ///
  /// In fr, this message translates to:
  /// **'Documents spécifiques au secteur'**
  String get liasseFiscaleIndustrySpecificDocuments;

  /// No description provided for @liasseFiscaleValidationWarning.
  ///
  /// In fr, this message translates to:
  /// **'Attention : documents obligatoires manquants'**
  String get liasseFiscaleValidationWarning;

  /// No description provided for @liasseFiscaleExportChecklist.
  ///
  /// In fr, this message translates to:
  /// **'Exporter la check-list'**
  String get liasseFiscaleExportChecklist;

  /// No description provided for @liasseFiscaleSaveProgress.
  ///
  /// In fr, this message translates to:
  /// **'Sauvegarder la progression'**
  String get liasseFiscaleSaveProgress;

  /// No description provided for @liasseFiscaleSearchDocuments.
  ///
  /// In fr, this message translates to:
  /// **'Rechercher des documents'**
  String get liasseFiscaleSearchDocuments;

  /// No description provided for @liasseFiscaleFilterDocuments.
  ///
  /// In fr, this message translates to:
  /// **'Filtrer les documents'**
  String get liasseFiscaleFilterDocuments;

  /// No description provided for @liasseFiscaleAnnualCalendarTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calendrier annuel'**
  String get liasseFiscaleAnnualCalendarTitle;

  /// No description provided for @liasseFiscalePenaltyStructureTitle.
  ///
  /// In fr, this message translates to:
  /// **'Structure des pénalités'**
  String get liasseFiscalePenaltyStructureTitle;

  /// No description provided for @liasseFiscaleComplianceRequirementsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Exigences de conformité'**
  String get liasseFiscaleComplianceRequirementsTitle;

  /// No description provided for @liasseFiscaleRecordKeepingTitle.
  ///
  /// In fr, this message translates to:
  /// **'Conservation des documents'**
  String get liasseFiscaleRecordKeepingTitle;

  /// No description provided for @liasseFiscaleAmendmentProceduresTitle.
  ///
  /// In fr, this message translates to:
  /// **'Procédures de rectification'**
  String get liasseFiscaleAmendmentProceduresTitle;

  /// No description provided for @liasseFiscaleSpecialSituationsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Situations particulières'**
  String get liasseFiscaleSpecialSituationsTitle;

  /// No description provided for @liasseFiscaleProfessionalSupportTitle.
  ///
  /// In fr, this message translates to:
  /// **'Support professionnel'**
  String get liasseFiscaleProfessionalSupportTitle;

  /// No description provided for @tvaRemboursementTab.
  ///
  /// In fr, this message translates to:
  /// **'Remboursement'**
  String get tvaRemboursementTab;

  /// No description provided for @tvaRemboursementTitle.
  ///
  /// In fr, this message translates to:
  /// **'Remboursement TVA'**
  String get tvaRemboursementTitle;

  /// No description provided for @tvaRemboursementEligibilityTitle.
  ///
  /// In fr, this message translates to:
  /// **'Critères d\'éligibilité'**
  String get tvaRemboursementEligibilityTitle;

  /// No description provided for @tvaRemboursementDocumentsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Documents requis'**
  String get tvaRemboursementDocumentsTitle;

  /// No description provided for @tvaRemboursementProcessTitle.
  ///
  /// In fr, this message translates to:
  /// **'Processus étape par étape'**
  String get tvaRemboursementProcessTitle;

  /// No description provided for @tvaRemboursementTemplatesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Modèles de documents'**
  String get tvaRemboursementTemplatesTitle;

  /// No description provided for @tvaRemboursementErrorsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Erreurs courantes'**
  String get tvaRemboursementErrorsTitle;

  /// No description provided for @tvaRemboursementTimelineTitle.
  ///
  /// In fr, this message translates to:
  /// **'Délais de traitement'**
  String get tvaRemboursementTimelineTitle;

  /// No description provided for @tvaRemboursementTroubleshootingTitle.
  ///
  /// In fr, this message translates to:
  /// **'Résolution de problèmes'**
  String get tvaRemboursementTroubleshootingTitle;

  /// No description provided for @tvaRemboursementSpecialScenariosTitle.
  ///
  /// In fr, this message translates to:
  /// **'Scénarios particuliers'**
  String get tvaRemboursementSpecialScenariosTitle;

  /// No description provided for @tvaRemboursementEligibilityDescription.
  ///
  /// In fr, this message translates to:
  /// **'Qui peut demander un remboursement'**
  String get tvaRemboursementEligibilityDescription;

  /// No description provided for @tvaRemboursementMinimumAmount.
  ///
  /// In fr, this message translates to:
  /// **'Montant minimum'**
  String get tvaRemboursementMinimumAmount;

  /// No description provided for @tvaRemboursementTimeLimit.
  ///
  /// In fr, this message translates to:
  /// **'Délai limite'**
  String get tvaRemboursementTimeLimit;

  /// No description provided for @tvaRemboursementQualifyingGoods.
  ///
  /// In fr, this message translates to:
  /// **'Biens éligibles'**
  String get tvaRemboursementQualifyingGoods;

  /// No description provided for @tvaRemboursementDocumentChecklist.
  ///
  /// In fr, this message translates to:
  /// **'Liste de contrôle des documents'**
  String get tvaRemboursementDocumentChecklist;

  /// No description provided for @tvaRemboursementOriginalInvoices.
  ///
  /// In fr, this message translates to:
  /// **'Factures originales'**
  String get tvaRemboursementOriginalInvoices;

  /// No description provided for @tvaRemboursementTaxFreeForm.
  ///
  /// In fr, this message translates to:
  /// **'Formulaire Tax Free'**
  String get tvaRemboursementTaxFreeForm;

  /// No description provided for @tvaRemboursementPassportRequirement.
  ///
  /// In fr, this message translates to:
  /// **'Exigences de passeport'**
  String get tvaRemboursementPassportRequirement;

  /// No description provided for @tvaRemboursementBankDetails.
  ///
  /// In fr, this message translates to:
  /// **'Coordonnées bancaires'**
  String get tvaRemboursementBankDetails;

  /// No description provided for @tvaRemboursementProcessingTime.
  ///
  /// In fr, this message translates to:
  /// **'Délai de traitement'**
  String get tvaRemboursementProcessingTime;

  /// No description provided for @tvaRemboursementTrackingProcedure.
  ///
  /// In fr, this message translates to:
  /// **'Procédure de suivi'**
  String get tvaRemboursementTrackingProcedure;

  /// No description provided for @tvaRemboursementDeniedRefund.
  ///
  /// In fr, this message translates to:
  /// **'Remboursement refusé'**
  String get tvaRemboursementDeniedRefund;

  /// No description provided for @tvaRemboursementDelayedRefund.
  ///
  /// In fr, this message translates to:
  /// **'Remboursement retardé'**
  String get tvaRemboursementDelayedRefund;

  /// No description provided for @tvaRemboursementExportScenarios.
  ///
  /// In fr, this message translates to:
  /// **'Scénarios d\'exportation'**
  String get tvaRemboursementExportScenarios;

  /// No description provided for @tvaRemboursementBusinessVsTourist.
  ///
  /// In fr, this message translates to:
  /// **'Entreprise vs Touriste'**
  String get tvaRemboursementBusinessVsTourist;

  /// No description provided for @tvaRemboursementBulkPurchases.
  ///
  /// In fr, this message translates to:
  /// **'Achats en gros'**
  String get tvaRemboursementBulkPurchases;

  /// No description provided for @agriculturePratiquesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Pratiques Agricoles'**
  String get agriculturePratiquesTitle;

  /// No description provided for @agricultureExercicesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Exercices Agricoles'**
  String get agricultureExercicesTitle;

  /// No description provided for @agricultureCalculateursTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calculateurs Agricoles'**
  String get agricultureCalculateursTitle;

  /// No description provided for @agricultureIrTitle.
  ///
  /// In fr, this message translates to:
  /// **'IR Agricole'**
  String get agricultureIrTitle;

  /// No description provided for @agricultureIsTitle.
  ///
  /// In fr, this message translates to:
  /// **'IS Agricole'**
  String get agricultureIsTitle;

  /// No description provided for @agricultureTvaTitle.
  ///
  /// In fr, this message translates to:
  /// **'TVA Agricole'**
  String get agricultureTvaTitle;

  /// No description provided for @agricultureCooperativesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Coopératives'**
  String get agricultureCooperativesTitle;

  /// No description provided for @agricultureComptabiliteTitle.
  ///
  /// In fr, this message translates to:
  /// **'Comptabilité Agricole'**
  String get agricultureComptabiliteTitle;

  /// No description provided for @agriculturePracticalScenariosTitle.
  ///
  /// In fr, this message translates to:
  /// **'Scénarios pratiques'**
  String get agriculturePracticalScenariosTitle;

  /// No description provided for @agricultureStepByStepGuidesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Guides étape par étape'**
  String get agricultureStepByStepGuidesTitle;

  /// No description provided for @agricultureErrorPreventionTitle.
  ///
  /// In fr, this message translates to:
  /// **'Prévention des erreurs'**
  String get agricultureErrorPreventionTitle;

  /// No description provided for @agricultureSeasonalConsiderationsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Considérations saisonnières'**
  String get agricultureSeasonalConsiderationsTitle;

  /// No description provided for @agricultureSmallFarmGuidanceTitle.
  ///
  /// In fr, this message translates to:
  /// **'Guidance pour petites exploitations'**
  String get agricultureSmallFarmGuidanceTitle;

  /// No description provided for @agricultureCrossReferenceTitle.
  ///
  /// In fr, this message translates to:
  /// **'Références croisées'**
  String get agricultureCrossReferenceTitle;

  /// No description provided for @agricultureProgressiveDifficultyTitle.
  ///
  /// In fr, this message translates to:
  /// **'Difficulté progressive'**
  String get agricultureProgressiveDifficultyTitle;

  /// No description provided for @agricultureScenarioBasedQuestionsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Questions basées sur des scénarios'**
  String get agricultureScenarioBasedQuestionsTitle;

  /// No description provided for @agricultureCalculationExercisesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Exercices de calcul'**
  String get agricultureCalculationExercisesTitle;

  /// No description provided for @agricultureMultipleChoiceTitle.
  ///
  /// In fr, this message translates to:
  /// **'Questions à choix multiples'**
  String get agricultureMultipleChoiceTitle;

  /// No description provided for @agricultureCaseStudyAnalysisTitle.
  ///
  /// In fr, this message translates to:
  /// **'Analyse d\'études de cas'**
  String get agricultureCaseStudyAnalysisTitle;

  /// No description provided for @agricultureSelfAssessmentTitle.
  ///
  /// In fr, this message translates to:
  /// **'Auto-évaluation'**
  String get agricultureSelfAssessmentTitle;

  /// No description provided for @agricultureIrCalculatorTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calculateur IR Agricole'**
  String get agricultureIrCalculatorTitle;

  /// No description provided for @agricultureIsCalculatorTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calculateur IS Agricole'**
  String get agricultureIsCalculatorTitle;

  /// No description provided for @agricultureTvaCalculatorTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calculateur TVA Agricole'**
  String get agricultureTvaCalculatorTitle;

  /// No description provided for @agricultureAmortizationCalculatorTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calculateur d\'amortissement'**
  String get agricultureAmortizationCalculatorTitle;

  /// No description provided for @agricultureSubsidyCalculatorTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calculateur d\'impact des subventions'**
  String get agricultureSubsidyCalculatorTitle;

  /// No description provided for @agricultureCooperativeCalculatorTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calculateur coopérative'**
  String get agricultureCooperativeCalculatorTitle;

  /// No description provided for @agricultureRevenueTitle.
  ///
  /// In fr, this message translates to:
  /// **'Détermination du revenu'**
  String get agricultureRevenueTitle;

  /// No description provided for @agricultureDeductibleExpensesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Charges déductibles'**
  String get agricultureDeductibleExpensesTitle;

  /// No description provided for @agricultureTaxExemptionsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Exonérations fiscales'**
  String get agricultureTaxExemptionsTitle;

  /// No description provided for @agricultureProgressiveScaleTitle.
  ///
  /// In fr, this message translates to:
  /// **'Barème progressif'**
  String get agricultureProgressiveScaleTitle;

  /// No description provided for @agricultureDeclarationObligationsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Obligations déclaratives'**
  String get agricultureDeclarationObligationsTitle;

  /// No description provided for @agricultureSpecialCasesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Cas particuliers'**
  String get agricultureSpecialCasesTitle;

  /// No description provided for @agricultureFormationRequirementsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Exigences de formation'**
  String get agricultureFormationRequirementsTitle;

  /// No description provided for @agricultureSpecialTaxRegimesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Régimes fiscaux spéciaux'**
  String get agricultureSpecialTaxRegimesTitle;

  /// No description provided for @agricultureMemberTransactionsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Transactions des membres'**
  String get agricultureMemberTransactionsTitle;

  /// No description provided for @agricultureAccountingObligationsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Obligations comptables'**
  String get agricultureAccountingObligationsTitle;

  /// No description provided for @agricultureComplianceCalendarTitle.
  ///
  /// In fr, this message translates to:
  /// **'Calendrier de conformité'**
  String get agricultureComplianceCalendarTitle;

  /// No description provided for @agricultureChartOfAccountsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Plan comptable'**
  String get agricultureChartOfAccountsTitle;

  /// No description provided for @agricultureBiologicalAssetsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Actifs biologiques'**
  String get agricultureBiologicalAssetsTitle;

  /// No description provided for @agricultureProductionCyclesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Cycles de production'**
  String get agricultureProductionCyclesTitle;

  /// No description provided for @agricultureInventoryValuationTitle.
  ///
  /// In fr, this message translates to:
  /// **'Évaluation des stocks'**
  String get agricultureInventoryValuationTitle;

  /// No description provided for @agricultureSubsidyAccountingTitle.
  ///
  /// In fr, this message translates to:
  /// **'Comptabilisation des subventions'**
  String get agricultureSubsidyAccountingTitle;

  /// No description provided for @agricultureFinancialStatementsTitle.
  ///
  /// In fr, this message translates to:
  /// **'États financiers'**
  String get agricultureFinancialStatementsTitle;

  /// No description provided for @agriculturePracticalExamplesTitle.
  ///
  /// In fr, this message translates to:
  /// **'Exemples pratiques'**
  String get agriculturePracticalExamplesTitle;

  /// No description provided for @checkboxCompleted.
  ///
  /// In fr, this message translates to:
  /// **'Terminé'**
  String get checkboxCompleted;

  /// No description provided for @checkboxPending.
  ///
  /// In fr, this message translates to:
  /// **'En attente'**
  String get checkboxPending;

  /// No description provided for @progressIndicatorLabel.
  ///
  /// In fr, this message translates to:
  /// **'Progression : {percentage}%'**
  String progressIndicatorLabel(Object percentage);

  /// No description provided for @validationMessageRequired.
  ///
  /// In fr, this message translates to:
  /// **'Ce champ est obligatoire'**
  String get validationMessageRequired;

  /// No description provided for @validationMessageInvalid.
  ///
  /// In fr, this message translates to:
  /// **'Valeur invalide'**
  String get validationMessageInvalid;

  /// No description provided for @validationMessageMissing.
  ///
  /// In fr, this message translates to:
  /// **'Documents manquants'**
  String get validationMessageMissing;

  /// No description provided for @helpTextTooltip.
  ///
  /// In fr, this message translates to:
  /// **'Aide'**
  String get helpTextTooltip;

  /// No description provided for @expandSectionButton.
  ///
  /// In fr, this message translates to:
  /// **'Développer'**
  String get expandSectionButton;

  /// No description provided for @collapseSectionButton.
  ///
  /// In fr, this message translates to:
  /// **'Réduire'**
  String get collapseSectionButton;

  /// No description provided for @saveButton.
  ///
  /// In fr, this message translates to:
  /// **'Sauvegarder'**
  String get saveButton;

  /// No description provided for @exportButton.
  ///
  /// In fr, this message translates to:
  /// **'Exporter'**
  String get exportButton;

  /// No description provided for @calculateButton.
  ///
  /// In fr, this message translates to:
  /// **'Calculer'**
  String get calculateButton;

  /// No description provided for @resetButton.
  ///
  /// In fr, this message translates to:
  /// **'Réinitialiser'**
  String get resetButton;

  /// No description provided for @nextStepButton.
  ///
  /// In fr, this message translates to:
  /// **'Étape suivante'**
  String get nextStepButton;

  /// No description provided for @previousStepButton.
  ///
  /// In fr, this message translates to:
  /// **'Étape précédente'**
  String get previousStepButton;

  /// No description provided for @finishButton.
  ///
  /// In fr, this message translates to:
  /// **'Terminer'**
  String get finishButton;

  /// No description provided for @searchPlaceholder.
  ///
  /// In fr, this message translates to:
  /// **'Rechercher...'**
  String get searchPlaceholder;

  /// No description provided for @filterPlaceholder.
  ///
  /// In fr, this message translates to:
  /// **'Filtrer par...'**
  String get filterPlaceholder;

  /// No description provided for @selectAllButton.
  ///
  /// In fr, this message translates to:
  /// **'Tout sélectionner'**
  String get selectAllButton;

  /// No description provided for @deselectAllButton.
  ///
  /// In fr, this message translates to:
  /// **'Tout désélectionner'**
  String get deselectAllButton;

  /// No description provided for @loadingMessage.
  ///
  /// In fr, this message translates to:
  /// **'Chargement en cours...'**
  String get loadingMessage;

  /// No description provided for @noDataMessage.
  ///
  /// In fr, this message translates to:
  /// **'Aucune donnée disponible'**
  String get noDataMessage;

  /// No description provided for @errorLoadingData.
  ///
  /// In fr, this message translates to:
  /// **'Erreur lors du chargement des données'**
  String get errorLoadingData;

  /// No description provided for @retryButton.
  ///
  /// In fr, this message translates to:
  /// **'Réessayer'**
  String get retryButton;

  /// No description provided for @errorNetworkConnection.
  ///
  /// In fr, this message translates to:
  /// **'Erreur de connexion réseau'**
  String get errorNetworkConnection;

  /// No description provided for @errorFileNotFound.
  ///
  /// In fr, this message translates to:
  /// **'Fichier non trouvé'**
  String get errorFileNotFound;

  /// No description provided for @errorInvalidData.
  ///
  /// In fr, this message translates to:
  /// **'Données invalides'**
  String get errorInvalidData;

  /// No description provided for @errorCalculationFailed.
  ///
  /// In fr, this message translates to:
  /// **'Échec du calcul'**
  String get errorCalculationFailed;

  /// No description provided for @errorSaveFailed.
  ///
  /// In fr, this message translates to:
  /// **'Échec de la sauvegarde'**
  String get errorSaveFailed;

  /// No description provided for @errorExportFailed.
  ///
  /// In fr, this message translates to:
  /// **'Échec de l\'exportation'**
  String get errorExportFailed;

  /// No description provided for @helpTextCalculator.
  ///
  /// In fr, this message translates to:
  /// **'Saisissez les valeurs requises pour effectuer le calcul'**
  String get helpTextCalculator;

  /// No description provided for @helpTextChecklist.
  ///
  /// In fr, this message translates to:
  /// **'Cochez les éléments au fur et à mesure de leur completion'**
  String get helpTextChecklist;

  /// No description provided for @helpTextDocuments.
  ///
  /// In fr, this message translates to:
  /// **'Assurez-vous d\'avoir tous les documents requis'**
  String get helpTextDocuments;

  /// No description provided for @helpTextDeadlines.
  ///
  /// In fr, this message translates to:
  /// **'Respectez les délais pour éviter les pénalités'**
  String get helpTextDeadlines;

  /// No description provided for @helpTextValidation.
  ///
  /// In fr, this message translates to:
  /// **'Vérifiez vos données avant de soumettre'**
  String get helpTextValidation;

  /// No description provided for @helpTextBackup.
  ///
  /// In fr, this message translates to:
  /// **'Sauvegardez régulièrement votre progression'**
  String get helpTextBackup;

  /// No description provided for @warningUnsavedChanges.
  ///
  /// In fr, this message translates to:
  /// **'Vous avez des modifications non sauvegardées'**
  String get warningUnsavedChanges;

  /// No description provided for @warningMissingDocuments.
  ///
  /// In fr, this message translates to:
  /// **'Documents obligatoires manquants'**
  String get warningMissingDocuments;

  /// No description provided for @warningApproachingDeadline.
  ///
  /// In fr, this message translates to:
  /// **'Délai approchant'**
  String get warningApproachingDeadline;

  /// No description provided for @warningInvalidInput.
  ///
  /// In fr, this message translates to:
  /// **'Saisie invalide détectée'**
  String get warningInvalidInput;

  /// No description provided for @confirmationSaved.
  ///
  /// In fr, this message translates to:
  /// **'Données sauvegardées avec succès'**
  String get confirmationSaved;

  /// No description provided for @confirmationExported.
  ///
  /// In fr, this message translates to:
  /// **'Export réalisé avec succès'**
  String get confirmationExported;

  /// No description provided for @confirmationCalculated.
  ///
  /// In fr, this message translates to:
  /// **'Calcul effectué avec succès'**
  String get confirmationCalculated;

  /// No description provided for @confirmationCompleted.
  ///
  /// In fr, this message translates to:
  /// **'Processus terminé avec succès'**
  String get confirmationCompleted;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'fr':
      return AppLocalizationsFr();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
