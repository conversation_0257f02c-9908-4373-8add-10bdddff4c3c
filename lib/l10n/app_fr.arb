{"appTitle": "Comptabilité <PERSON>", "welcomeMessage": "Bienvenue dans votre guide de comptabilité marocaine", "planComptable": "Plan Comptable Mar<PERSON>ain", "terminology": "Terminologie Comptable", "exercises": "Exercices Pratiques", "references": "Références", "quizTitle": "Quiz", "quizCategory1": "Catégorie 1", "quizCategory2": "Catégorie 2", "quizCategory3": "Catégorie 3", "quizWelcomeMessage": "Bienvenue dans {category}", "quizStartButton": "Commence<PERSON> le Quiz", "@_comment_liasse_fiscale": "Liasse Fiscale module localization", "liasseFiscaleTitle": "Liasse Fiscale", "liasseFiscaleDescription": "Documents et obligations fiscales annuelles", "liasseFiscaleGuideTab": "Guide", "liasseFiscaleChecklistTab": "Check-list", "liasseFiscaleObligationsTab": "Obligations", "liasseFiscaleOverviewTitle": "Vue d'ensemble", "liasseFiscaleFinancialStatementsTitle": "États financiers", "liasseFiscaleTaxFormsTitle": "Formulaires fiscaux", "liasseFiscalePreparationTitle": "Processus de préparation", "liasseFiscaleElectronicFilingTitle": "Télédéclaration", "liasseFiscaleCommonErrorsTitle": "Erreurs courantes", "liasseFiscaleBestPracticesTitle": "Bonnes pratiques", "liasseFiscaleDocumentCategoriesTitle": "Catégories de documents", "liasseFiscaleProgressTitle": "Progression", "liasseFiscaleMandatoryDocuments": "Documents obligatoires", "liasseFiscaleSupportingDocuments": "Documents justificatifs", "liasseFiscaleIndustrySpecificDocuments": "Documents spécifiques au secteur", "liasseFiscaleValidationWarning": "Attention : documents obligatoires manquants", "liasseFiscaleExportChecklist": "Exporter la check-list", "liasseFiscaleSaveProgress": "Sauvegarder la progression", "liasseFiscaleSearchDocuments": "Rechercher des documents", "liasseFiscaleFilterDocuments": "Filtrer les documents", "liasseFiscaleAnnualCalendarTitle": "<PERSON><PERSON><PERSON> annuel", "liasseFiscalePenaltyStructureTitle": "Structure des pénalités", "liasseFiscaleComplianceRequirementsTitle": "Exigences de conformité", "liasseFiscaleRecordKeepingTitle": "Conservation des documents", "liasseFiscaleAmendmentProceduresTitle": "Procédures de rectification", "liasseFiscaleSpecialSituationsTitle": "Situations particulières", "liasseFiscaleProfessionalSupportTitle": "Support professionnel", "@_comment_tva_remboursement": "VAT Remboursement localization", "tvaRemboursementTab": "Remboursement", "tvaRemboursementTitle": "Remboursement TVA", "tvaRemboursementEligibilityTitle": "Critères d'éligibilité", "tvaRemboursementDocumentsTitle": "Documents requis", "tvaRemboursementProcessTitle": "Processus étape par étape", "tvaRemboursementTemplatesTitle": "Modèles de documents", "tvaRemboursementErrorsTitle": "Erreurs courantes", "tvaRemboursementTimelineTitle": "<PERSON><PERSON><PERSON><PERSON> de traitement", "tvaRemboursementTroubleshootingTitle": "Résolution de problèmes", "tvaRemboursementSpecialScenariosTitle": "Scénarios particuliers", "tvaRemboursementEligibilityDescription": "Qui peut demander un remboursement", "tvaRemboursementMinimumAmount": "Montant minimum", "tvaRemboursementTimeLimit": "<PERSON><PERSON><PERSON>", "tvaRemboursementQualifyingGoods": "Biens éligibles", "tvaRemboursementDocumentChecklist": "Liste de contrôle des documents", "tvaRemboursementOriginalInvoices": "Factures originales", "tvaRemboursementTaxFreeForm": "Formulaire Tax Free", "tvaRemboursementPassportRequirement": "Exigences de passeport", "tvaRemboursementBankDetails": "Coordonnées bancaires", "tvaRemboursementProcessingTime": "<PERSON><PERSON><PERSON>", "tvaRemboursementTrackingProcedure": "Procédure de suivi", "tvaRemboursementDeniedRefund": "Remboursement refusé", "tvaRemboursementDelayedRefund": "Remboursement retardé", "tvaRemboursementExportScenarios": "Scénarios d'exportation", "tvaRemboursementBusinessVsTourist": "Entreprise vs Touriste", "tvaRemboursementBulkPurchases": "Achats en gros", "@_comment_agriculture_enhancements": "Agriculture module enhancements", "agriculturePratiquesTitle": "Pratiques Agricoles", "agricultureExercicesTitle": "Exercices Agricoles", "agricultureCalculateursTitle": "Calculateurs Agricoles", "agricultureIrTitle": "IR Agricole", "agricultureIsTitle": "IS Agricole", "agricultureTvaTitle": "TVA Agricole", "agricultureCooperativesTitle": "Coopératives", "agricultureComptabiliteTitle": "Comptabilité Agricole", "agriculturePracticalScenariosTitle": "Scénarios pratiques", "agricultureStepByStepGuidesTitle": "Guides étape par étape", "agricultureErrorPreventionTitle": "Prévention des erreurs", "agricultureSeasonalConsiderationsTitle": "Considérations saisonnières", "agricultureSmallFarmGuidanceTitle": "Guidance pour petites exploitations", "agricultureCrossReferenceTitle": "Références croisées", "agricultureProgressiveDifficultyTitle": "Difficulté progressive", "agricultureScenarioBasedQuestionsTitle": "Questions basées sur des scénarios", "agricultureCalculationExercisesTitle": "Exercices de calcul", "agricultureMultipleChoiceTitle": "Questions à choix multiples", "agricultureCaseStudyAnalysisTitle": "Analyse d'études de cas", "agricultureSelfAssessmentTitle": "Auto-évaluation", "agricultureIrCalculatorTitle": "Calculateur IR Agricole", "agricultureIsCalculatorTitle": "Calculateur IS Agricole", "agricultureTvaCalculatorTitle": "Calculateur TVA Agricole", "agricultureAmortizationCalculatorTitle": "Calculateur d'amortissement", "agricultureSubsidyCalculatorTitle": "Calculateur d'impact des subventions", "agricultureCooperativeCalculatorTitle": "Calculateur coopérative", "agricultureRevenueTitle": "Détermination du revenu", "agricultureDeductibleExpensesTitle": "Charges déductibles", "agricultureTaxExemptionsTitle": "Exonérations fiscales", "agricultureProgressiveScaleTitle": "Barème progressif", "agricultureDeclarationObligationsTitle": "Obligations déclaratives", "agricultureSpecialCasesTitle": "Cas particuliers", "agricultureFormationRequirementsTitle": "Exigences de formation", "agricultureSpecialTaxRegimesTitle": "Régi<PERSON> fiscaux spéciaux", "agricultureMemberTransactionsTitle": "Transactions des membres", "agricultureAccountingObligationsTitle": "Obligations comptables", "agricultureComplianceCalendarTitle": "Calendrier de conformité", "agricultureChartOfAccountsTitle": "Plan comptable", "agricultureBiologicalAssetsTitle": "Actifs biologiques", "agricultureProductionCyclesTitle": "Cycles de production", "agricultureInventoryValuationTitle": "Évaluation des stocks", "agricultureSubsidyAccountingTitle": "Comptabilisation des subventions", "agricultureFinancialStatementsTitle": "États financiers", "agriculturePracticalExamplesTitle": "Exemples pratiques", "@_comment_interactive_elements": "Interactive elements localization", "checkboxCompleted": "<PERSON><PERSON><PERSON><PERSON>", "checkboxPending": "En attente", "progressIndicatorLabel": "Progression : {percentage}%", "validationMessageRequired": "Ce champ est obligatoire", "validationMessageInvalid": "<PERSON><PERSON> invalide", "validationMessageMissing": "Documents manquants", "helpTextTooltip": "Aide", "expandSectionButton": "Développer", "collapseSectionButton": "<PERSON><PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON><PERSON><PERSON>", "exportButton": "Exporter", "calculateButton": "Calculer", "resetButton": "Réinitialiser", "nextStepButton": "Étape suiva<PERSON>", "previousStepButton": "Étape <PERSON>", "finishButton": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Rechercher...", "filterPlaceholder": "Filtrer par...", "selectAllButton": "<PERSON><PERSON>", "deselectAllButton": "<PERSON><PERSON>", "loadingMessage": "Chargement en cours...", "noDataMessage": "<PERSON><PERSON><PERSON> donnée disponible", "errorLoadingData": "Erreur lors du chargement des données", "retryButton": "<PERSON><PERSON><PERSON><PERSON>", "@_comment_error_messages": "Error messages and help text", "errorNetworkConnection": "Erreur de connexion réseau", "errorFileNotFound": "Fichier non trouvé", "errorInvalidData": "Données invalides", "errorCalculationFailed": "Échec du calcul", "errorSaveFailed": "Échec de la sauvegarde", "errorExportFailed": "Échec de l'exportation", "helpTextCalculator": "Saisissez les valeurs requises pour effectuer le calcul", "helpTextChecklist": "Cochez les éléments au fur et à mesure de leur completion", "helpTextDocuments": "Assurez-vous d'avoir tous les documents requis", "helpTextDeadlines": "Respectez les délais pour éviter les pénalités", "helpTextValidation": "Vérifiez vos données avant de soumettre", "helpTextBackup": "Sauvegardez régulièrement votre progression", "warningUnsavedChanges": "Vous avez des modifications non sauvegardées", "warningMissingDocuments": "Documents obligatoires manquants", "warningApproachingDeadline": "<PERSON><PERSON><PERSON> approchant", "warningInvalidInput": "<PERSON><PERSON> invalide d<PERSON>e", "confirmationSaved": "Données sauvegardées avec succès", "confirmationExported": "Export réalisé avec succès", "confirmationCalculated": "Calcul effectué avec succès", "confirmationCompleted": "Processus terminé avec succès"}