import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import '../../models/immobilisations/asset_preset.dart';
import '../../services/asset_preset_service.dart';

/// Enhanced asset selection widget with autocomplete, filtering, and preview capabilities
/// 
/// This widget provides a comprehensive interface for selecting asset presets with:
/// - Fuzzy search autocomplete functionality
/// - Category-based filtering with chips
/// - Recent selections memory
/// - Detailed preset preview cards
/// - Accessibility support and keyboard navigation
class AssetPresetSelector extends StatefulWidget {
  /// Currently selected preset
  final AssetPreset? selectedPreset;
  
  /// Callback when a preset is selected
  final ValueChanged<AssetPreset?> onPresetSelected;
  
  /// Whether to allow custom/manual entry mode
  final bool allowCustom;
  
  /// List of recently used preset IDs for quick access
  final List<String>? recentPresets;
  
  /// Initial category filter to apply
  final String? initialCategory;
  
  /// Whether the selector is enabled
  final bool enabled;
  
  /// Hint text for the search field
  final String? hintText;
  
  /// Whether to show the preset preview card
  final bool showPreview;

  const AssetPresetSelector({
    super.key,
    this.selectedPreset,
    required this.onPresetSelected,
    this.allowCustom = true,
    this.recentPresets,
    this.initialCategory,
    this.enabled = true,
    this.hintText,
    this.showPreview = true,
  });

  @override
  State<AssetPresetSelector> createState() => _AssetPresetSelectorState();
}

class _AssetPresetSelectorState extends State<AssetPresetSelector>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final AssetPresetService _presetService = AssetPresetService.instance;
  
  List<AssetPreset> _allPresets = [];
  List<AssetPreset> _filteredPresets = [];
  List<String> _categories = [];
  String? _selectedCategory;
  bool _isLoading = true;
  bool _hasError = false;
  bool _showPreviewCard = false;
  
  late AnimationController _previewAnimationController;
  late Animation<double> _previewAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadPresets();
    _setupInitialState();
  }

  void _initializeAnimations() {
    _previewAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _previewAnimation = CurvedAnimation(
      parent: _previewAnimationController,
      curve: Curves.easeInOut,
    );
  }

  void _setupInitialState() {
    if (widget.selectedPreset != null) {
      _searchController.text = widget.selectedPreset!.name;
      _showPreviewCard = widget.showPreview;
      if (_showPreviewCard) {
        _previewAnimationController.forward();
      }
    }
    
    if (widget.initialCategory != null) {
      _selectedCategory = widget.initialCategory;
    }
  }

  Future<void> _loadPresets() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      final presets = await _presetService.loadPresets();
      final categories = _presetService.getCategories();

      setState(() {
        _allPresets = presets;
        _categories = categories;
        _isLoading = false;
        _filterPresets();
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  void _filterPresets() {
    List<AssetPreset> filtered = _allPresets;

    // Apply category filter
    if (_selectedCategory != null) {
      filtered = filtered.where((preset) => preset.category == _selectedCategory).toList();
    }

    // Apply search filter
    final searchQuery = _searchController.text.toLowerCase().trim();
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((preset) {
        return preset.name.toLowerCase().contains(searchQuery) ||
               preset.description.toLowerCase().contains(searchQuery) ||
               preset.category.toLowerCase().contains(searchQuery) ||
               preset.examples.any((example) => example.toLowerCase().contains(searchQuery));
      }).toList();
    }

    // Sort by relevance (exact matches first, then partial matches)
    if (searchQuery.isNotEmpty) {
      filtered.sort((a, b) {
        final aExact = a.name.toLowerCase() == searchQuery ? 0 : 1;
        final bExact = b.name.toLowerCase() == searchQuery ? 0 : 1;
        if (aExact != bExact) return aExact.compareTo(bExact);
        
        final aStarts = a.name.toLowerCase().startsWith(searchQuery) ? 0 : 1;
        final bStarts = b.name.toLowerCase().startsWith(searchQuery) ? 0 : 1;
        if (aStarts != bStarts) return aStarts.compareTo(bStarts);
        
        return a.name.compareTo(b.name);
      });
    }

    setState(() {
      _filteredPresets = filtered;
    });
  }

  void _onPresetSelected(AssetPreset preset) {
    setState(() {
      _searchController.text = preset.name;
      _showPreviewCard = widget.showPreview;
    });
    
    if (_showPreviewCard) {
      _previewAnimationController.forward();
    }
    
    widget.onPresetSelected(preset);
    _searchFocusNode.unfocus();
    
    // Announce selection for accessibility
    _announceSelection(preset);
  }

  void _announceSelection(AssetPreset preset) {
    final message = 'Actif sélectionné: ${preset.name}, catégorie ${preset.category}, '
        'taux ${preset.formattedRate}, durée ${preset.defaultDuration} ans';
    
    SemanticsService.announce(message, TextDirection.ltr);
  }

  void _onCategorySelected(String? category) {
    setState(() {
      _selectedCategory = category;
      _filterPresets();
    });
  }

  void _clearSelection() {
    setState(() {
      _searchController.clear();
      _showPreviewCard = false;
    });
    _previewAnimationController.reverse();
    widget.onPresetSelected(null);
  }

  void _resetToPreset() {
    if (widget.selectedPreset != null) {
      _onPresetSelected(widget.selectedPreset!);
    }
  }

  List<AssetPreset> _getRecentPresets() {
    if (widget.recentPresets == null || widget.recentPresets!.isEmpty) {
      return [];
    }
    
    return widget.recentPresets!
        .map((id) => _presetService.findPresetById(id))
        .where((preset) => preset != null)
        .cast<AssetPreset>()
        .take(5)
        .toList();
  }

  List<AssetPreset> _getCommonPresets() {
    // Return most commonly used presets based on category popularity
    final categoryCount = <String, int>{};
    for (final preset in _allPresets) {
      categoryCount[preset.category] = (categoryCount[preset.category] ?? 0) + 1;
    }
    
    final popularCategories = categoryCount.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value));
    
    final commonPresets = <AssetPreset>[];
    for (final entry in popularCategories.take(3)) {
      final categoryPresets = _allPresets
          .where((p) => p.category == entry.key)
          .take(2)
          .toList();
      commonPresets.addAll(categoryPresets);
    }
    
    return commonPresets.take(6).toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _previewAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search field with autocomplete
        _buildSearchField(theme, colorScheme),
        
        const SizedBox(height: 12),
        
        // Category filter chips
        if (_categories.isNotEmpty) _buildCategoryChips(theme, colorScheme),
        
        const SizedBox(height: 8),
        
        // Quick access buttons for recent/common presets
        _buildQuickAccessSection(theme, colorScheme),
        
        // Preview card
        if (_showPreviewCard && widget.selectedPreset != null)
          _buildPreviewCard(theme, colorScheme),
      ],
    );
  }

  Widget _buildSearchField(ThemeData theme, ColorScheme colorScheme) {
    return Autocomplete<AssetPreset>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text.isEmpty) {
          return const Iterable<AssetPreset>.empty();
        }
        return _filteredPresets.take(10);
      },
      displayStringForOption: (AssetPreset preset) => preset.name,
      onSelected: _onPresetSelected,
      fieldViewBuilder: (context, controller, focusNode, onFieldSubmitted) {
        // Sync with our controller
        if (controller != _searchController) {
          controller.text = _searchController.text;
          controller.selection = _searchController.selection;
        }
        
        return TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          enabled: widget.enabled,
          onChanged: (value) {
            controller.text = value;
            controller.selection = TextSelection.fromPosition(
              TextPosition(offset: value.length),
            );
            _filterPresets();
          },
          onSubmitted: (value) => onFieldSubmitted(),
          decoration: InputDecoration(
            labelText: 'Type d\'actif',
            hintText: widget.hintText ?? 'Rechercher un type d\'actif...',
            prefixIcon: _isLoading
                ? const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  )
                : const Icon(Icons.search),
            suffixIcon: _searchController.text.isNotEmpty
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.selectedPreset != null && widget.allowCustom)
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: _resetToPreset,
                          tooltip: 'Réinitialiser au preset',
                          visualDensity: VisualDensity.compact,
                        ),
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: _clearSelection,
                        tooltip: 'Effacer',
                        visualDensity: VisualDensity.compact,
                      ),
                    ],
                  )
                : null,
            border: const OutlineInputBorder(),
            errorText: _hasError ? 'Erreur de chargement des presets' : null,
          ),
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return _buildOptionsView(context, onSelected, options, theme, colorScheme);
      },
    );
  }

  Widget _buildOptionsView(
    BuildContext context,
    AutocompleteOnSelected<AssetPreset> onSelected,
    Iterable<AssetPreset> options,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Align(
      alignment: Alignment.topLeft,
      child: Material(
        elevation: 4.0,
        borderRadius: BorderRadius.circular(8.0),
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxHeight: 300, maxWidth: 400),
          child: options.isEmpty
              ? _buildNoResultsView(theme, colorScheme)
              : ListView.builder(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: options.length,
                  itemBuilder: (context, index) {
                    final preset = options.elementAt(index);
                    return _buildOptionTile(preset, onSelected, theme, colorScheme);
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildOptionTile(
    AssetPreset preset,
    AutocompleteOnSelected<AssetPreset> onSelected,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final searchQuery = _searchController.text.toLowerCase();
    
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: colorScheme.primaryContainer,
        child: Icon(
          preset.icon,
          color: colorScheme.onPrimaryContainer,
          size: 20,
        ),
      ),
      title: _buildHighlightedText(preset.name, searchQuery, theme),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Chip(
                label: Text(
                  preset.category,
                  style: theme.textTheme.labelSmall,
                ),
                backgroundColor: colorScheme.secondaryContainer,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              ),
              const SizedBox(width: 8),
              Text(
                '${preset.formattedRate} • ${preset.defaultDuration} ans',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          if (preset.description.isNotEmpty)
            Text(
              preset.description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
      onTap: () => onSelected(preset),
      dense: true,
    );
  }

  Widget _buildHighlightedText(String text, String query, ThemeData theme) {
    if (query.isEmpty) {
      return Text(text);
    }
    
    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();
    final index = lowerText.indexOf(lowerQuery);
    
    if (index == -1) {
      return Text(text);
    }
    
    return RichText(
      text: TextSpan(
        style: theme.textTheme.bodyMedium,
        children: [
          if (index > 0)
            TextSpan(text: text.substring(0, index)),
          TextSpan(
            text: text.substring(index, index + query.length),
            style: TextStyle(
              backgroundColor: theme.colorScheme.primaryContainer,
              color: theme.colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (index + query.length < text.length)
            TextSpan(text: text.substring(index + query.length)),
        ],
      ),
    );
  }

  Widget _buildNoResultsView(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.search_off,
            size: 48,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 8),
          Text(
            'Aucun résultat trouvé',
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Essayez un autre terme de recherche',
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          if (widget.allowCustom) ...[
            const SizedBox(height: 12),
            TextButton.icon(
              onPressed: () {
                widget.onPresetSelected(null);
                _searchFocusNode.unfocus();
              },
              icon: const Icon(Icons.edit),
              label: const Text('Saisie manuelle'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCategoryChips(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Catégories',
          style: theme.textTheme.labelMedium?.copyWith(
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: [
            FilterChip(
              label: const Text('Toutes'),
              selected: _selectedCategory == null,
              onSelected: (_) => _onCategorySelected(null),
            ),
            ..._categories.map((category) {
              final count = _allPresets.where((p) => p.category == category).length;
              return FilterChip(
                label: Text('$category ($count)'),
                selected: _selectedCategory == category,
                onSelected: (_) => _onCategorySelected(category),
              );
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickAccessSection(ThemeData theme, ColorScheme colorScheme) {
    final recentPresets = _getRecentPresets();
    final commonPresets = _getCommonPresets();
    
    if (recentPresets.isEmpty && commonPresets.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (recentPresets.isNotEmpty) ...[
          Text(
            'Récemment utilisés',
            style: theme.textTheme.labelMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          _buildQuickAccessChips(recentPresets, theme, colorScheme),
          const SizedBox(height: 12),
        ],
        if (commonPresets.isNotEmpty) ...[
          Text(
            'Actifs courants',
            style: theme.textTheme.labelMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          _buildQuickAccessChips(commonPresets, theme, colorScheme),
        ],
      ],
    );
  }

  Widget _buildQuickAccessChips(
    List<AssetPreset> presets,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: presets.map((preset) {
        return ActionChip(
          avatar: Icon(
            preset.icon,
            size: 16,
            color: colorScheme.onSurfaceVariant,
          ),
          label: Text(
            preset.name,
            style: theme.textTheme.labelSmall,
          ),
          onPressed: () => _onPresetSelected(preset),
          backgroundColor: colorScheme.surfaceContainerHighest,
        );
      }).toList(),
    );
  }

  Widget _buildPreviewCard(ThemeData theme, ColorScheme colorScheme) {
    final preset = widget.selectedPreset!;
    
    return AnimatedBuilder(
      animation: _previewAnimation,
      builder: (context, child) {
        return SizeTransition(
          sizeFactor: _previewAnimation,
          child: Container(
            margin: const EdgeInsets.only(top: 16),
            child: Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: colorScheme.primaryContainer,
                          child: Icon(
                            preset.icon,
                            color: colorScheme.onPrimaryContainer,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                preset.name,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                preset.category,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            setState(() {
                              _showPreviewCard = false;
                            });
                            _previewAnimationController.reverse();
                          },
                          tooltip: 'Fermer l\'aperçu',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Key metrics
                    Row(
                      children: [
                        _buildMetricChip(
                          'Taux',
                          preset.formattedRate,
                          Icons.percent,
                          theme,
                          colorScheme,
                        ),
                        const SizedBox(width: 8),
                        _buildMetricChip(
                          'Durée',
                          '${preset.defaultDuration} ans',
                          Icons.schedule,
                          theme,
                          colorScheme,
                        ),
                        const SizedBox(width: 8),
                        _buildMetricChip(
                          'Mode',
                          preset.defaultMode == 'linear' ? 'Linéaire' : 'Dégressif',
                          preset.defaultMode == 'linear' ? Icons.trending_up : Icons.trending_down,
                          theme,
                          colorScheme,
                        ),
                      ],
                    ),
                    
                    if (preset.description.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Text(
                        preset.description,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                    
                    if (preset.examples.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Text(
                        'Exemples:',
                        style: theme.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        preset.examples.join(', '),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                    
                    if (preset.complianceNote.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 16,
                              color: colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                preset.complianceNote,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    
                    const SizedBox(height: 16),
                    
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: FilledButton.icon(
                            onPressed: () {
                              // Apply preset action - this would typically trigger
                              // auto-population of form fields
                              widget.onPresetSelected(preset);
                              HapticFeedback.lightImpact();
                            },
                            icon: const Icon(Icons.check),
                            label: const Text('Appliquer le preset'),
                          ),
                        ),
                        if (widget.allowCustom) ...[
                          const SizedBox(width: 8),
                          OutlinedButton.icon(
                            onPressed: () {
                              // Customize action - this would allow manual override
                              // while keeping the preset as a starting point
                              widget.onPresetSelected(preset);
                              HapticFeedback.lightImpact();
                            },
                            icon: const Icon(Icons.edit),
                            label: const Text('Personnaliser'),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricChip(
    String label,
    String value,
    IconData icon,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: colorScheme.onSecondaryContainer,
          ),
          const SizedBox(width: 4),
          Text(
            '$label: $value',
            style: theme.textTheme.labelSmall?.copyWith(
              color: colorScheme.onSecondaryContainer,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}