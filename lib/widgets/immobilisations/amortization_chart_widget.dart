import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../models/immobilisations/amortization_row.dart';

enum ChartType {
  annualAnnuityBar,
  cumulativeLine,
  netBookValueLine,
  combined,
  comparison,
}

class AmortizationChartWidget extends StatefulWidget {
  final List<AmortizationRow> data;
  final List<AmortizationRow>? comparisonData;
  final ChartType chartType;
  final bool showComparison;
  final String title;
  final ValueChanged<ChartType>? onChartTypeChanged;

  const AmortizationChartWidget({
    super.key,
    required this.data,
    this.comparisonData,
    this.chartType = ChartType.annualAnnuityBar,
    this.showComparison = false,
    this.title = 'Graphique d\'Amortissement',
    this.onChartTypeChanged,
  });

  @override
  State<AmortizationChartWidget> createState() => _AmortizationChartWidgetState();
}

class _AmortizationChartWidgetState extends State<AmortizationChartWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  Timer? _debounceTimer;
  final GlobalKey _chartKey = GlobalKey();
  bool _isExporting = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void didUpdateWidget(AmortizationChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.chartType != widget.chartType ||
        oldWidget.data != widget.data ||
        oldWidget.comparisonData != widget.comparisonData) {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(const Duration(milliseconds: 300), () {
        _animationController.reset();
        _animationController.forward();
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  String _formatCurrency(double value) {
    return '${NumberFormat('#,##0', 'fr_FR').format(value)} DH';
  }

  String _formatYear(double value) {
    final index = value.toInt();
    if (index >= 0 && index < widget.data.length) {
      final year = widget.data[index].year;
      return year.toString();
    }
    return '';
  }

  List<FlSpot> _getAnnuitySpots() {
    return widget.data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.annuity);
    }).toList();
  }

  List<FlSpot> _getCumulativeSpots() {
    return widget.data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.cumulativeAnnuity);
    }).toList();
  }

  List<FlSpot> _getNetBookValueSpots() {
    return widget.data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.netBookValue);
    }).toList();
  }

  List<FlSpot> _getComparisonAnnuitySpots() {
    if (widget.comparisonData == null) return [];
    return widget.comparisonData!.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.annuity);
    }).toList();
  }

  List<FlSpot> _getComparisonCumulativeSpots() {
    if (widget.comparisonData == null) return [];
    return widget.comparisonData!.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.cumulativeAnnuity);
    }).toList();
  }

  List<FlSpot> _getComparisonNetBookValueSpots() {
    if (widget.comparisonData == null) return [];
    return widget.comparisonData!.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.netBookValue);
    }).toList();
  }

  List<BarChartGroupData> _getBarChartGroups(ColorScheme colorScheme) {
    return widget.data.asMap().entries.map((entry) {
      final index = entry.key;
      final row = entry.value;
      
      List<BarChartRodData> rods = [
        BarChartRodData(
          toY: row.annuity,
          color: colorScheme.primary,
          width: widget.showComparison && widget.comparisonData != null ? 8 : 16,
          borderRadius: BorderRadius.circular(4),
        ),
      ];

      if (widget.showComparison && 
          widget.comparisonData != null && 
          index < widget.comparisonData!.length) {
        rods.add(
          BarChartRodData(
            toY: widget.comparisonData![index].annuity,
            color: colorScheme.secondary,
            width: 8,
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }

      return BarChartGroupData(
        x: index,
        barRods: rods,
        barsSpace: 4,
      );
    }).toList();
  }

  Widget _buildBarChart(ColorScheme colorScheme) {
    final maxY = widget.data.map((e) => e.annuity).reduce((a, b) => a > b ? a : b);
    final comparisonMaxY = widget.comparisonData?.map((e) => e.annuity).reduce((a, b) => a > b ? a : b) ?? 0;
    final chartMaxY = maxY > comparisonMaxY ? maxY : comparisonMaxY;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return BarChart(
          BarChartData(
            maxY: chartMaxY * 1.1,
            barGroups: _getBarChartGroups(colorScheme)
                .map((group) => BarChartGroupData(
                      x: group.x,
                      barRods: group.barRods
                          .map((rod) => BarChartRodData(
                                toY: rod.toY * _animation.value,
                                color: rod.color,
                                width: rod.width,
                                borderRadius: rod.borderRadius,
                              ))
                          .toList(),
                      barsSpace: group.barsSpace,
                    ))
                .toList(),
            gridData: FlGridData(
              show: true,
              drawVerticalLine: false,
              horizontalInterval: chartMaxY / 5,
              getDrawingHorizontalLine: (value) {
                return FlLine(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                  strokeWidth: 1,
                );
              },
            ),
            titlesData: FlTitlesData(
              leftTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 80,
                  interval: chartMaxY / 5,
                  getTitlesWidget: (value, meta) {
                    return Text(
                      _formatCurrency(value),
                      style: TextStyle(
                        color: colorScheme.onSurface,
                        fontSize: 10,
                      ),
                    );
                  },
                ),
              ),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 30,
                  getTitlesWidget: (value, meta) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        _formatYear(value),
                        style: TextStyle(
                          color: colorScheme.onSurface,
                          fontSize: 10,
                        ),
                      ),
                    );
                  },
                ),
              ),
              rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
              topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            ),
            borderData: FlBorderData(
              show: true,
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            barTouchData: BarTouchData(
              enabled: true,
              touchTooltipData: BarTouchTooltipData(
                getTooltipColor: (group) => colorScheme.inverseSurface,
                getTooltipItem: (group, groupIndex, rod, rodIndex) {
                  final row = widget.data[groupIndex];
                  String label = 'Année ${row.year}\n';
                  
                  if (widget.showComparison && widget.comparisonData != null) {
                    if (rodIndex == 0) {
                      label += 'Principal: ${_formatCurrency(rod.toY)}';
                    } else {
                      label += 'Comparaison: ${_formatCurrency(rod.toY)}';
                    }
                  } else {
                    label += 'Annuité: ${_formatCurrency(rod.toY)}';
                  }
                  
                  return BarTooltipItem(
                    label,
                    TextStyle(
                      color: colorScheme.onInverseSurface,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLineChart(ColorScheme colorScheme) {
    List<LineChartBarData> lineBarsData = [];

    switch (widget.chartType) {
      case ChartType.cumulativeLine:
        lineBarsData.add(
          LineChartBarData(
            spots: _getCumulativeSpots(),
            isCurved: true,
            color: colorScheme.primary,
            barWidth: 3,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: colorScheme.primary.withValues(alpha: 0.1),
            ),
          ),
        );
        if (widget.showComparison && widget.comparisonData != null) {
          lineBarsData.add(
            LineChartBarData(
              spots: _getComparisonCumulativeSpots(),
              isCurved: true,
              color: colorScheme.secondary,
              barWidth: 3,
              dotData: FlDotData(show: false),
              dashArray: [5, 5],
            ),
          );
        }
        break;
      case ChartType.netBookValueLine:
        lineBarsData.add(
          LineChartBarData(
            spots: _getNetBookValueSpots(),
            isCurved: true,
            color: colorScheme.tertiary,
            barWidth: 3,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: colorScheme.tertiary.withValues(alpha: 0.1),
            ),
          ),
        );
        if (widget.showComparison && widget.comparisonData != null) {
          lineBarsData.add(
            LineChartBarData(
              spots: _getComparisonNetBookValueSpots(),
              isCurved: true,
              color: colorScheme.error,
              barWidth: 3,
              dotData: FlDotData(show: false),
              dashArray: [5, 5],
            ),
          );
        }
        break;
      case ChartType.combined:
        lineBarsData.addAll([
          LineChartBarData(
            spots: _getCumulativeSpots(),
            isCurved: true,
            color: colorScheme.primary,
            barWidth: 3,
            dotData: FlDotData(show: false),
          ),
          LineChartBarData(
            spots: _getNetBookValueSpots(),
            isCurved: true,
            color: colorScheme.tertiary,
            barWidth: 3,
            dotData: FlDotData(show: false),
          ),
        ]);
        break;
      case ChartType.comparison:
        if (widget.comparisonData != null) {
          lineBarsData.addAll([
            LineChartBarData(
              spots: _getAnnuitySpots(),
              isCurved: true,
              color: colorScheme.primary,
              barWidth: 3,
              dotData: FlDotData(show: false),
            ),
            LineChartBarData(
              spots: _getComparisonAnnuitySpots(),
              isCurved: true,
              color: colorScheme.secondary,
              barWidth: 3,
              dotData: FlDotData(show: false),
              dashArray: [5, 5],
            ),
          ]);
        }
        break;
      default:
        break;
    }

    final maxY = _getMaxYValue();

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return LineChart(
          LineChartData(
            maxY: maxY * 1.1,
            lineBarsData: lineBarsData
                .map((lineBar) => LineChartBarData(
                      spots: lineBar.spots
                          .map((spot) => FlSpot(spot.x, spot.y * _animation.value))
                          .toList(),
                      isCurved: lineBar.isCurved,
                      color: lineBar.color,
                      barWidth: lineBar.barWidth,
                      dotData: lineBar.dotData,
                      belowBarData: lineBar.belowBarData != null
                          ? BarAreaData(
                              show: true,
                              color: lineBar.belowBarData.color,
                            )
                          : BarAreaData(show: false),
                      dashArray: lineBar.dashArray,
                    ))
                .toList(),
            gridData: FlGridData(
              show: true,
              drawVerticalLine: false,
              horizontalInterval: maxY / 5,
              getDrawingHorizontalLine: (value) {
                return FlLine(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                  strokeWidth: 1,
                );
              },
            ),
            titlesData: FlTitlesData(
              leftTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 80,
                  interval: maxY / 5,
                  getTitlesWidget: (value, meta) {
                    return Text(
                      _formatCurrency(value),
                      style: TextStyle(
                        color: colorScheme.onSurface,
                        fontSize: 10,
                      ),
                    );
                  },
                ),
              ),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 30,
                  getTitlesWidget: (value, meta) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        _formatYear(value),
                        style: TextStyle(
                          color: colorScheme.onSurface,
                          fontSize: 10,
                        ),
                      ),
                    );
                  },
                ),
              ),
              rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
              topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            ),
            borderData: FlBorderData(
              show: true,
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            lineTouchData: LineTouchData(
              enabled: true,
              touchTooltipData: LineTouchTooltipData(
                getTooltipColor: (touchedSpot) => colorScheme.inverseSurface,
                getTooltipItems: (touchedSpots) {
                  return touchedSpots.map((touchedSpot) {
                    final index = touchedSpot.x.toInt();
                    if (index >= 0 && index < widget.data.length) {
                      final row = widget.data[index];
                      String label = 'Année ${row.year}\n';
                      
                      switch (widget.chartType) {
                        case ChartType.cumulativeLine:
                          label += 'Cumulé: ${_formatCurrency(touchedSpot.y)}';
                          break;
                        case ChartType.netBookValueLine:
                          label += 'VNC: ${_formatCurrency(touchedSpot.y)}';
                          break;
                        case ChartType.combined:
                          if (touchedSpot.barIndex == 0) {
                            label += 'Cumulé: ${_formatCurrency(touchedSpot.y)}';
                          } else {
                            label += 'VNC: ${_formatCurrency(touchedSpot.y)}';
                          }
                          break;
                        case ChartType.comparison:
                          if (touchedSpot.barIndex == 0) {
                            label += 'Principal: ${_formatCurrency(touchedSpot.y)}';
                          } else {
                            label += 'Comparaison: ${_formatCurrency(touchedSpot.y)}';
                          }
                          break;
                        default:
                          label += 'Valeur: ${_formatCurrency(touchedSpot.y)}';
                      }
                      
                      return LineTooltipItem(
                        label,
                        TextStyle(
                          color: colorScheme.onInverseSurface,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    }
                    return null;
                  }).toList();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  double _getMaxYValue() {
    double maxY = 0;
    
    switch (widget.chartType) {
      case ChartType.cumulativeLine:
        maxY = widget.data.map((e) => e.cumulativeAnnuity).reduce((a, b) => a > b ? a : b);
        if (widget.showComparison && widget.comparisonData != null) {
          final comparisonMax = widget.comparisonData!.map((e) => e.cumulativeAnnuity).reduce((a, b) => a > b ? a : b);
          maxY = maxY > comparisonMax ? maxY : comparisonMax;
        }
        break;
      case ChartType.netBookValueLine:
        maxY = widget.data.map((e) => e.netBookValue).reduce((a, b) => a > b ? a : b);
        if (widget.showComparison && widget.comparisonData != null) {
          final comparisonMax = widget.comparisonData!.map((e) => e.netBookValue).reduce((a, b) => a > b ? a : b);
          maxY = maxY > comparisonMax ? maxY : comparisonMax;
        }
        break;
      case ChartType.combined:
        final cumulativeMax = widget.data.map((e) => e.cumulativeAnnuity).reduce((a, b) => a > b ? a : b);
        final netBookMax = widget.data.map((e) => e.netBookValue).reduce((a, b) => a > b ? a : b);
        maxY = cumulativeMax > netBookMax ? cumulativeMax : netBookMax;
        break;
      case ChartType.comparison:
        maxY = widget.data.map((e) => e.annuity).reduce((a, b) => a > b ? a : b);
        if (widget.comparisonData != null) {
          final comparisonMax = widget.comparisonData!.map((e) => e.annuity).reduce((a, b) => a > b ? a : b);
          maxY = maxY > comparisonMax ? maxY : comparisonMax;
        }
        break;
      default:
        maxY = widget.data.map((e) => e.annuity).reduce((a, b) => a > b ? a : b);
    }
    
    return maxY;
  }

  Widget _buildLegend(ColorScheme colorScheme, TextTheme textTheme) {
    List<Widget> legendItems = [];

    switch (widget.chartType) {
      case ChartType.annualAnnuityBar:
        legendItems.add(_buildLegendItem(
          'Annuités Annuelles',
          colorScheme.primary,
          colorScheme,
          textTheme,
        ));
        if (widget.showComparison && widget.comparisonData != null) {
          legendItems.add(_buildLegendItem(
            'Comparaison',
            colorScheme.secondary,
            colorScheme,
            textTheme,
          ));
        }
        break;
      case ChartType.cumulativeLine:
        legendItems.add(_buildLegendItem(
          'Amortissement Cumulé',
          colorScheme.primary,
          colorScheme,
          textTheme,
        ));
        if (widget.showComparison && widget.comparisonData != null) {
          legendItems.add(_buildLegendItem(
            'Comparaison Cumulée',
            colorScheme.secondary,
            colorScheme,
            textTheme,
            isDashed: true,
          ));
        }
        break;
      case ChartType.netBookValueLine:
        legendItems.add(_buildLegendItem(
          'Valeur Nette Comptable',
          colorScheme.tertiary,
          colorScheme,
          textTheme,
        ));
        if (widget.showComparison && widget.comparisonData != null) {
          legendItems.add(_buildLegendItem(
            'VNC Comparaison',
            colorScheme.error,
            colorScheme,
            textTheme,
            isDashed: true,
          ));
        }
        break;
      case ChartType.combined:
        legendItems.addAll([
          _buildLegendItem(
            'Amortissement Cumulé',
            colorScheme.primary,
            colorScheme,
            textTheme,
          ),
          _buildLegendItem(
            'Valeur Nette Comptable',
            colorScheme.tertiary,
            colorScheme,
            textTheme,
          ),
        ]);
        break;
      case ChartType.comparison:
        legendItems.addAll([
          _buildLegendItem(
            'Principal',
            colorScheme.primary,
            colorScheme,
            textTheme,
          ),
          _buildLegendItem(
            'Comparaison',
            colorScheme.secondary,
            colorScheme,
            textTheme,
            isDashed: true,
          ),
        ]);
        break;
    }

    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: legendItems,
    );
  }

  Widget _buildLegendItem(
    String label,
    Color color,
    ColorScheme colorScheme,
    TextTheme textTheme, {
    bool isDashed = false,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 20,
          height: 3,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(1.5),
          ),
          child: isDashed
              ? CustomPaint(
                  painter: DashedLinePainter(color: color),
                )
              : null,
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface,
            fontSize: 11,
          ),
        ),
      ],
    );
  }

  Widget _buildChartTypeSelector(ColorScheme colorScheme, TextTheme textTheme) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: ChartType.values.map((type) {
          final isSelected = widget.chartType == type;
          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: FilterChip(
              label: Text(_getChartTypeLabel(type)),
              selected: isSelected,
              onSelected: widget.onChartTypeChanged != null
                  ? (selected) {
                      if (selected) {
                        widget.onChartTypeChanged!(type);
                      }
                    }
                  : null,
              backgroundColor: colorScheme.surface,
              selectedColor: colorScheme.primaryContainer,
              checkmarkColor: colorScheme.primary,
              labelStyle: textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? colorScheme.onPrimaryContainer
                    : colorScheme.onSurface,
                fontSize: 11,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  String _getChartTypeLabel(ChartType type) {
    switch (type) {
      case ChartType.annualAnnuityBar:
        return 'Barres Annuelles';
      case ChartType.cumulativeLine:
        return 'Cumulé';
      case ChartType.netBookValueLine:
        return 'VNC';
      case ChartType.combined:
        return 'Combiné';
      case ChartType.comparison:
        return 'Comparaison';
    }
  }

  Future<void> _exportChart() async {
    if (_isExporting) return;
    
    setState(() {
      _isExporting = true;
    });

    try {
      final RenderRepaintBoundary boundary =
          _chartKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/amortization_chart_${DateTime.now().millisecondsSinceEpoch}.png');
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Graphique d\'amortissement - ${widget.title}',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'export: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    if (widget.data.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          height: 300,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                colorScheme.surface,
                colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.bar_chart,
                  size: 48,
                  color: colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'Aucune donnée à afficher',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primaryContainer,
                    colorScheme.primary.withValues(alpha: 0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: textTheme.titleLarge?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (!_isExporting)
                    IconButton(
                      onPressed: _exportChart,
                      icon: Icon(
                        Icons.share,
                        color: colorScheme.primary,
                      ),
                      tooltip: 'Exporter le graphique',
                    )
                  else
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
                      ),
                    ),
                ],
              ),
            ),
            
            // Chart Type Selector
            if (widget.onChartTypeChanged != null)
              Padding(
                padding: const EdgeInsets.all(16),
                child: _buildChartTypeSelector(colorScheme, textTheme),
              ),

            // Chart
            RepaintBoundary(
              key: _chartKey,
              child: Container(
                height: isTablet ? 400 : 300,
                padding: const EdgeInsets.all(16),
                child: widget.chartType == ChartType.annualAnnuityBar
                    ? _buildBarChart(colorScheme)
                    : _buildLineChart(colorScheme),
              ),
            ),

            // Legend
            Padding(
              padding: const EdgeInsets.all(16),
              child: _buildLegend(colorScheme, textTheme),
            ),
          ],
        ),
      ),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final Color color;

  DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    const dashWidth = 3.0;
    const dashSpace = 2.0;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}