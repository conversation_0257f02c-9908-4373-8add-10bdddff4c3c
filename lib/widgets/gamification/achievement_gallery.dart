// AchievementGallery widget for Moroccan Accounting App
// Displays all achievements in a grid with filtering, sorting, and sharing.
// References: achievement_badge.dart

import 'package:flutter/material.dart';
import '../../models/gamification/achievement_definitions.dart';
import 'achievement_badge.dart';

class AchievementGallery extends StatefulWidget {
  final List<AchievementDefinition> achievements;
  final Set<String> unlockedIds;
  final void Function(AchievementDefinition)? onShare;

  const AchievementGallery({
    super.key,
    required this.achievements,
    required this.unlockedIds,
    this.onShare,
  });

  @override
  State<AchievementGallery> createState() => _AchievementGalleryState();
}

class _AchievementGalleryState extends State<AchievementGallery> {
  AchievementCategory? selectedCategory;
  AchievementRarity? selectedRarity;
  String searchQuery = '';
  bool showUnlockedOnly = false;
  bool gridView = true;

  List<AchievementDefinition> get filteredAchievements {
    return widget.achievements.where((a) {
      if (selectedCategory != null && a.category != selectedCategory) return false;
      if (selectedRarity != null && a.rarity != selectedRarity) return false;
      if (showUnlockedOnly && !widget.unlockedIds.contains(a.id)) return false;
      if (searchQuery.isNotEmpty &&
          !a.title.toLowerCase().contains(searchQuery.toLowerCase()) &&
          !a.description.toLowerCase().contains(searchQuery.toLowerCase())) {
        return false;
      }
      return true;
    }).toList();
  }

  void showAchievementDetails(AchievementDefinition achievement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(achievement.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AchievementBadge(
              achievement: achievement,
              unlocked: widget.unlockedIds.contains(achievement.id),
              points: achievement.points,
            ),
            SizedBox(height: 8),
            Text(achievement.description),
            SizedBox(height: 8),
            Text('Category: ${achievement.category.name}'),
            Text('Rarity: ${achievement.rarity.name}'),
            Text('Points: ${achievement.points}'),
            SizedBox(height: 8),
            if (widget.onShare != null && widget.unlockedIds.contains(achievement.id))
              ElevatedButton.icon(
                icon: Icon(Icons.share),
                label: Text('Share'),
                onPressed: () {
                  Navigator.of(context).pop();
                  widget.onShare!(achievement);
                },
              ),
          ],
        ),
        actions: [
          TextButton(
            child: Text('Close'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Filters and search
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Row(
            children: [
              DropdownButton<AchievementCategory>(
                hint: Text('Category'),
                value: selectedCategory,
                items: AchievementCategory.values
                    .map((c) => DropdownMenuItem(
                          value: c,
                          child: Text(c.name),
                        ))
                    .toList(),
                onChanged: (c) => setState(() => selectedCategory = c),
              ),
              SizedBox(width: 8),
              DropdownButton<AchievementRarity>(
                hint: Text('Rarity'),
                value: selectedRarity,
                items: AchievementRarity.values
                    .map((r) => DropdownMenuItem(
                          value: r,
                          child: Text(r.name),
                        ))
                    .toList(),
                onChanged: (r) => setState(() => selectedRarity = r),
              ),
              SizedBox(width: 8),
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search achievements',
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                  onChanged: (q) => setState(() => searchQuery = q),
                ),
              ),
              SizedBox(width: 8),
              IconButton(
                icon: Icon(gridView ? Icons.grid_view : Icons.list),
                onPressed: () => setState(() => gridView = !gridView),
              ),
              Checkbox(
                value: showUnlockedOnly,
                onChanged: (v) => setState(() => showUnlockedOnly = v ?? false),
              ),
              Text('Unlocked only'),
            ],
          ),
        ),
        // Achievement grid/list
        Expanded(
          child: gridView
              ? GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: filteredAchievements.length,
                  itemBuilder: (context, i) {
                    final achievement = filteredAchievements[i];
                    final unlocked = widget.unlockedIds.contains(achievement.id);
                    return GestureDetector(
                      onTap: () => showAchievementDetails(achievement),
                      child: AchievementBadge(
                        achievement: achievement,
                        unlocked: unlocked,
                        points: achievement.points,
                      ),
                    );
                  },
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredAchievements.length,
                  itemBuilder: (context, i) {
                    final achievement = filteredAchievements[i];
                    final unlocked = widget.unlockedIds.contains(achievement.id);
                    return ListTile(
                      leading: AchievementBadge(
                        achievement: achievement,
                        unlocked: unlocked,
                        points: achievement.points,
                      ),
                      title: Text(achievement.title),
                      subtitle: Text(achievement.description),
                      trailing: unlocked && widget.onShare != null
                          ? IconButton(
                              icon: Icon(Icons.share),
                              onPressed: () => widget.onShare!(achievement),
                            )
                          : null,
                      onTap: () => showAchievementDetails(achievement),
                    );
                  },
                ),
        ),
        // Statistics
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Text('Total: ${widget.achievements.length}'),
              Text('Unlocked: ${widget.unlockedIds.length}'),
              Text('Points: ${widget.achievements.where((a) => widget.unlockedIds.contains(a.id)).fold(0, (sum, a) => sum + a.points)}'),
            ],
          ),
        ),
      ],
    );
  }
}

// End of achievement_gallery.dart
