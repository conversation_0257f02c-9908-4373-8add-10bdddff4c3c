// LearningStreakWidget for Moroccan Accounting App
// Displays learning streaks, milestones, flame animations, and sharing.
// References: semantic_colors.dart

import 'package:flutter/material.dart';
import '../../models/gamification/learning_streak_data.dart';
import '../../theme/semantic_colors.dart';

class LearningStreakWidget extends StatelessWidget {
  final LearningStreakData streak;
  final VoidCallback? onShare;
  final VoidCallback? onFreeze;
  final VoidCallback? onRecover;

  const LearningStreakWidget({
    super.key,
    required this.streak,
    this.onShare,
    this.onFreeze,
    this.onRecover,
  });

  Color getStreakColor(BuildContext context) {
    // Use epic color for streaks
    return context.semanticColors.badgeEpic;
  }

  @override
  Widget build(BuildContext context) {
    final milestones = streak.getStreakMilestones();
    final isMilestone = milestones.contains(streak.currentStreak);
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Streak flame animation (placeholder)
            Icon(Icons.local_fire_department, color: getStreakColor(context), size: 48),
            SizedBox(height: 8),
            Text('Current Streak: ${streak.currentStreak} days', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            Text('Best Streak: ${streak.bestStreak} days', style: TextStyle(fontSize: 16)),
            SizedBox(height: 8),
            // Milestone celebration
            if (isMilestone)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.celebration, color: Colors.amber, size: 32),
                  SizedBox(width: 8),
                  Text('Milestone reached: ${streak.currentStreak} days!', style: TextStyle(color: Colors.amber, fontWeight: FontWeight.bold)),
                ],
              ),
            SizedBox(height: 8),
            // Streak history visualization
            SizedBox(
              height: 40,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                itemCount: streak.streakHistory.length,
                separatorBuilder: (_, __) => SizedBox(width: 4),
                itemBuilder: (context, i) {
                  final period = streak.streakHistory[i];
                  return Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: getStreakColor(context).withOpacity(0.7),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text('${period.length}', style: TextStyle(color: Colors.white, fontSize: 12)),
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 8),
            // Streak recovery/freeze options
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (onFreeze != null)
                  ElevatedButton.icon(
                    icon: Icon(Icons.ac_unit),
                    label: Text('Freeze'),
                    onPressed: onFreeze,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.blueGrey),
                  ),
                SizedBox(width: 8),
                if (onRecover != null)
                  ElevatedButton.icon(
                    icon: Icon(Icons.refresh),
                    label: Text('Recover'),
                    onPressed: onRecover,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                  ),
              ],
            ),
            SizedBox(height: 8),
            // Motivational message
            Text(
              streak.currentStreak > 0
                  ? 'Keep your streak alive! Consistency is key.'
                  : 'Start your streak today and unlock achievements!',
              style: TextStyle(fontSize: 16, color: getStreakColor(context)),
            ),
            SizedBox(height: 8),
            // Share and leaderboard
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (onShare != null)
                  ElevatedButton.icon(
                    icon: Icon(Icons.share),
                    label: Text('Share Streak'),
                    onPressed: onShare,
                  ),
                SizedBox(width: 8),
                Icon(Icons.leaderboard, color: Colors.purple),
                SizedBox(width: 4),
                Text('Leaderboard'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// End of learning_streak_widget.dart
