// PersonalizedRecommendations widget for Moroccan Accounting App
// Displays personalized learning recommendations based on user progress and preferences.

import 'package:flutter/material.dart';
import '../../models/gamification/personalized_learning_path.dart';
import '../../theme/design_tokens.dart';

class PersonalizedRecommendations extends StatelessWidget {
  final PersonalizedLearningPath? learningPath;
  final void Function(String)? onSectionTap;
  final void Function(String)? onQuizTap;
  final VoidCallback? onRefresh;

  const PersonalizedRecommendations({
    super.key,
    this.learningPath,
    this.onSectionTap,
    this.onQuizTap,
    this.onRefresh,
  });

  IconData getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Icons.trending_down;
      case 'medium':
        return Icons.trending_flat;
      case 'hard':
        return Icons.trending_up;
      default:
        return Icons.help_outline;
    }
  }

  Color getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return DesignTokens.successColor;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (learningPath == null) {
      return Card(
        margin: const EdgeInsets.all(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.lightbulb_outline, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text(
                'No recommendations available',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              Text('Complete more activities to get personalized recommendations.'),
              SizedBox(height: 8),
              if (onRefresh != null)
                ElevatedButton.icon(
                  icon: Icon(Icons.refresh),
                  label: Text('Refresh'),
                  onPressed: onRefresh,
                ),
            ],
          ),
        ),
      );
    }

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.psychology, color: DesignTokens.primaryColor, size: 32),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Personalized for You',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        learningPath!.reasonForRecommendation,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
                if (onRefresh != null)
                  IconButton(
                    icon: Icon(Icons.refresh),
                    onPressed: onRefresh,
                  ),
              ],
            ),
            SizedBox(height: 16),

            // Difficulty and time info
            Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: getDifficultyColor(learningPath!.difficultyLevel).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: getDifficultyColor(learningPath!.difficultyLevel)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        getDifficultyIcon(learningPath!.difficultyLevel),
                        size: 16,
                        color: getDifficultyColor(learningPath!.difficultyLevel),
                      ),
                      SizedBox(width: 4),
                      Text(
                        learningPath!.difficultyLevel.toUpperCase(),
                        style: TextStyle(
                          color: getDifficultyColor(learningPath!.difficultyLevel),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12),
                Row(
                  children: [
                    Icon(Icons.schedule, size: 16, color: Colors.grey),
                    SizedBox(width: 4),
                    Text('${learningPath!.estimatedTime} min'),
                  ],
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.priority_high, size: 16, color: Colors.orange),
                      SizedBox(width: 4),
                      Text('Priority ${learningPath!.priority}'),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Recommended sections
            if (learningPath!.recommendedSections.isNotEmpty) ...[
              Text(
                'Recommended Sections',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              ...learningPath!.recommendedSections.map((sectionId) {
                return Card(
                  margin: EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: Icon(Icons.menu_book, color: DesignTokens.primaryColor),
                    title: Text('Section: $sectionId'),
                    subtitle: Text('Tap to study this section'),
                    trailing: Icon(Icons.arrow_forward_ios),
                    onTap: () => onSectionTap?.call(sectionId),
                  ),
                );
              }),
            ],

            // Recommended quizzes
            if (learningPath!.recommendedQuizzes.isNotEmpty) ...[
              SizedBox(height: 16),
              Text(
                'Recommended Quizzes',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              ...learningPath!.recommendedQuizzes.map((quizId) {
                return Card(
                  margin: EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: Icon(Icons.quiz, color: Colors.orange),
                    title: Text('Quiz: $quizId'),
                    subtitle: Text('Test your knowledge'),
                    trailing: Icon(Icons.arrow_forward_ios),
                    onTap: () => onQuizTap?.call(quizId),
                  ),
                );
              }),
            ],

            SizedBox(height: 16),

            // Next recommendation button
            Center(
              child: ElevatedButton.icon(
                icon: Icon(Icons.play_arrow),
                label: Text('Start Next Recommendation'),
                onPressed: () {
                  final nextRec = learningPath!.getNextRecommendation();
                  if (nextRec != null) {
                    if (learningPath!.recommendedSections.contains(nextRec)) {
                      onSectionTap?.call(nextRec);
                    } else if (learningPath!.recommendedQuizzes.contains(nextRec)) {
                      onQuizTap?.call(nextRec);
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: DesignTokens.primaryColor,
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// End of personalized_recommendations.dart
