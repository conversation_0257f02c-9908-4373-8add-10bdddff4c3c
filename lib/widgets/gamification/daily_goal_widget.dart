// DailyGoalWidget for Moroccan Accounting App
// Displays daily goal tracking, progress, streaks, and motivational features.
// References: design_tokens.dart

import 'package:flutter/material.dart';
import '../../models/gamification/daily_goal_data.dart';
import '../../theme/design_tokens.dart';

class DailyGoalWidget extends StatelessWidget {
  final DailyGoalData goal;
  final VoidCallback? onSetGoal;
  final VoidCallback? onCelebrate;

  const DailyGoalWidget({
    super.key,
    required this.goal,
    this.onSetGoal,
    this.onCelebrate,
  });

  IconData getGoalIcon(DailyGoalType type) {
    switch (type) {
      case DailyGoalType.timeBased:
        return Icons.timer;
      case DailyGoalType.sectionBased:
        return Icons.menu_book;
      case DailyGoalType.quizBased:
        return Icons.quiz;
    }
  }

  String getGoalTypeLabel(DailyGoalType type) {
    switch (type) {
      case DailyGoalType.timeBased:
        return 'Minutes';
      case DailyGoalType.sectionBased:
        return 'Sections';
      case DailyGoalType.quizBased:
        return 'Quizzes';
    }
  }

  @override
  Widget build(BuildContext context) {
    final progress = goal.getProgressPercentage();
    final completed = goal.isCompleted;
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Goal type and icon
            Row(
              children: [
                Icon(getGoalIcon(goal.goalType), size: 32, color: DesignTokens.primaryColor),
                SizedBox(width: 12),
                Text(
                  'Daily Goal: ${goal.targetValue} ${getGoalTypeLabel(goal.goalType)}',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Spacer(),
                if (onSetGoal != null)
                  IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: onSetGoal,
                  ),
              ],
            ),
            SizedBox(height: 16),
            // Circular progress indicator
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 100,
                  height: 100,
                  child: CircularProgressIndicator(
                    value: progress,
                    strokeWidth: 8,
                    backgroundColor: Colors.grey.shade300,
                    color: completed ? DesignTokens.successColor : DesignTokens.primaryColor,
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('${(progress * 100).toInt()}%', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                    Text('${goal.currentProgress}/${goal.targetValue}', style: TextStyle(fontSize: 16)),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16),
            // Streak tracking
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.local_fire_department, color: DesignTokens.streakColor),
                SizedBox(width: 4),
                Text('Streak: ${goal.streakCount} days', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(width: 16),
                Icon(Icons.star, color: Colors.amber),
                SizedBox(width: 4),
                Text('Best: ${goal.bestStreak} days'),
              ],
            ),
            SizedBox(height: 16),
            // Motivational message
            Text(
              completed
                  ? 'Goal completed! 🎉'
                  : progress > 0.7
                      ? 'Almost there, keep going!'
                      : 'Stay motivated and reach your goal!',
              style: TextStyle(fontSize: 16, color: completed ? DesignTokens.successColor : DesignTokens.primaryColor),
            ),
            SizedBox(height: 8),
            // Completion celebration
            if (completed && onCelebrate != null)
              ElevatedButton.icon(
                icon: Icon(Icons.celebration),
                label: Text('Celebrate'),
                onPressed: onCelebrate,
                style: ElevatedButton.styleFrom(backgroundColor: DesignTokens.successColor),
              ),
            // Goal history and statistics
            SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.history, color: Colors.grey),
                SizedBox(width: 4),
                Text('Last reset: ${goal.lastResetDate.toLocal().toString().split(' ')[0]}'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// End of daily_goal_widget.dart
