// GamificationDashboard widget for Moroccan Accounting App
// Combines all gamification features in a comprehensive dashboard view.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/achievement_provider.dart';
import '../../providers/daily_goal_provider.dart';
import '../../providers/learning_streak_provider.dart';
import '../../providers/personalized_learning_provider.dart';
import 'achievement_badge.dart';
import 'daily_goal_widget.dart';
import 'learning_streak_widget.dart';
import 'personalized_recommendations.dart';
import '../../theme/design_tokens.dart';

class GamificationDashboard extends ConsumerWidget {
  final VoidCallback? onViewAllAchievements;
  final VoidCallback? onSetDailyGoal;
  final VoidCallback? onShareStreak;
  final void Function(String)? onSectionTap;
  final void Function(String)? onQuizTap;

  const GamificationDashboard({
    super.key,
    this.onViewAllAchievements,
    this.onSetDailyGoal,
    this.onShareStreak,
    this.onSectionTap,
    this.onQuizTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final achievements = ref.watch(recentAchievementsProvider);
    final achievementPoints = ref.watch(achievementPointsProvider);
    final currentGoal = ref.watch(currentDailyGoalProvider);
    final currentStreak = ref.watch(currentStreakProvider);
    final learningPath = ref.watch(learningPathProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with total points
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(Icons.emoji_events, color: DesignTokens.primaryColor, size: 48),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your Progress',
                          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '$achievementPoints total points',
                          style: TextStyle(fontSize: 18, color: DesignTokens.primaryColor),
                        ),
                        Text(
                          '${achievements.length} achievements unlocked',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: DesignTokens.primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      '${achievements.length}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: DesignTokens.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 20),

          // Recent achievements section
          Row(
            children: [
              Text(
                'Recent Achievements',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Spacer(),
              TextButton.icon(
                icon: Icon(Icons.arrow_forward),
                label: Text('View All'),
                onPressed: onViewAllAchievements,
              ),
            ],
          ),
          SizedBox(height: 8),
          
          if (achievements.isEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(Icons.emoji_events_outlined, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text('No achievements yet'),
                    Text('Complete activities to unlock your first achievement!'),
                  ],
                ),
              ),
            )
          else
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: achievements.length,
                itemBuilder: (context, index) {
                  final achievement = achievements[index];
                  return Container(
                    width: 100,
                    margin: EdgeInsets.only(right: 8),
                    child: AchievementBadge(
                      achievement: achievement,
                      unlocked: true,
                      points: achievement.points,
                    ),
                  );
                },
              ),
            ),
          
          SizedBox(height: 20),

          // Daily goal section
          if (currentGoal != null) 
            DailyGoalWidget(
              goal: currentGoal,
              onSetGoal: onSetDailyGoal,
            )
          else
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(Icons.flag, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text('Set Your Daily Goal', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    Text('Stay motivated with daily learning targets'),
                    SizedBox(height: 8),
                    ElevatedButton.icon(
                      icon: Icon(Icons.add),
                      label: Text('Set Goal'),
                      onPressed: onSetDailyGoal,
                    ),
                  ],
                ),
              ),
            ),

          SizedBox(height: 20),

          // Learning streak section
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.local_fire_department, color: DesignTokens.streakColor, size: 32),
                      SizedBox(width: 12),
                      Text(
                        'Learning Streak',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      Spacer(),
                      if (onShareStreak != null)
                        IconButton(
                          icon: Icon(Icons.share),
                          onPressed: onShareStreak,
                        ),
                    ],
                  ),
                  Text(
                    '$currentStreak days',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: DesignTokens.streakColor,
                    ),
                  ),
                  Text(
                    currentStreak > 0 
                        ? 'Keep it going! 🔥'
                        : 'Start your streak today!',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 20),

          // Personalized recommendations
          PersonalizedRecommendations(
            learningPath: learningPath,
            onSectionTap: onSectionTap,
            onQuizTap: onQuizTap,
            onRefresh: () {
              // Trigger recommendations refresh
              ref.invalidate(learningPathProvider);
            },
          ),

          SizedBox(height: 20),

          // Quick stats
          Row(
            children: [
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(Icons.quiz, color: Colors.orange, size: 32),
                        SizedBox(height: 8),
                        Text('Quizzes', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('Completed: 0'), // TODO: Add actual stats
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(Icons.menu_book, color: Colors.blue, size: 32),
                        SizedBox(height: 8),
                        Text('Sections', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('Read: 0'), // TODO: Add actual stats
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(Icons.schedule, color: Colors.green, size: 32),
                        SizedBox(height: 8),
                        Text('Time', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('0 min'), // TODO: Add actual stats
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// End of gamification_dashboard.dart
