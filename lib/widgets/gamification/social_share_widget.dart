// SocialShareWidget for Moroccan Accounting App
// Displays achievement sharing options, previews, privacy, and feedback.
// References: social_share_data.dart

import 'package:flutter/material.dart';
import '../../models/gamification/social_share_data.dart';

class SocialShareWidget extends StatelessWidget {
  final SocialShareData shareData;
  final void Function(SocialPlatform)? onPlatformSelected;
  final VoidCallback? onShare;
  final VoidCallback? onPrivacyToggle;

  const SocialShareWidget({
    super.key,
    required this.shareData,
    this.onPlatformSelected,
    this.onShare,
    this.onPrivacyToggle,
  });

  IconData getPlatformIcon(SocialPlatform platform) {
    switch (platform) {
      case SocialPlatform.facebook:
        return Icons.facebook;
      case SocialPlatform.twitter:
        return Icons.alternate_email;
      case SocialPlatform.instagram:
        return Icons.camera_alt;
      case SocialPlatform.whatsapp:
        return Icons.chat; // Placeholder for WhatsApp
      case SocialPlatform.other:
        return Icons.share;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Platform selection
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: SocialPlatform.values.map((platform) {
                return IconButton(
                  icon: Icon(getPlatformIcon(platform), color: platform == shareData.platform ? Colors.blue : Colors.grey),
                  onPressed: () => onPlatformSelected?.call(platform),
                  tooltip: platform.name,
                );
              }).toList(),
            ),
            SizedBox(height: 8),
            // Share preview
            Text('Share Preview:', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 4),
            Text(shareData.shareText, style: TextStyle(fontSize: 16)),
            SizedBox(height: 8),
            // Badge image preview (placeholder)
            if (shareData.shareImage != null)
              Image.asset(shareData.shareImage!, width: 64, height: 64),
            SizedBox(height: 8),
            // Privacy toggle
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Switch(
                  value: !shareData.isPrivate,
                  onChanged: (_) => onPrivacyToggle?.call(),
                  activeColor: Colors.green,
                ),
                Text(shareData.isPrivate ? 'Private' : 'Public'),
              ],
            ),
            SizedBox(height: 8),
            // Share count and feedback
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.send, color: Colors.blue),
                SizedBox(width: 4),
                Text('Shared ${shareData.shareCount} times'),
              ],
            ),
            SizedBox(height: 8),
            // Share button
            ElevatedButton.icon(
              icon: Icon(Icons.share),
              label: Text('Share Achievement'),
              onPressed: onShare,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            ),
            SizedBox(height: 8),
            // User preference
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Checkbox(
                  value: shareData.userPrefersSharing,
                  onChanged: (_) => onPrivacyToggle?.call(),
                ),
                Text('I want to share achievements'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// End of social_share_widget.dart
