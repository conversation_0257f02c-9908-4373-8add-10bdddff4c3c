// AchievementBadge widget for Moroccan Accounting App
// Displays achievement badges with unlock animations, rarity styling, and accessibility support.

import 'package:flutter/material.dart';
import '../../models/gamification/achievement_definitions.dart';
import '../../theme/semantic_colors.dart';

class AchievementBadge extends StatelessWidget {
  final AchievementDefinition achievement;
  final bool unlocked;
  final DateTime? unlockDate;
  final int? points;
  final VoidCallback? onTap;

  const AchievementBadge({
    super.key,
    required this.achievement,
    this.unlocked = false,
    this.unlockDate,
    this.points,
    this.onTap,
  });

// Returns the color for the achievement rarity using context.semanticColors
Color getRarityColor(BuildContext context, AchievementRarity rarity) {
  final colors = context.semanticColors;
  switch (rarity) {
    case AchievementRarity.common:
      return colors.badgeCommon;
    case AchievementRarity.rare:
      return colors.badgeRare;
    case AchievementRarity.epic:
      return colors.badgeEpic;
    case AchievementRarity.legendary:
      return colors.badgeLegendary;
  }
}

@override
Widget build(BuildContext context) {
  return Semantics(
    label: achievement.title,
    hint: unlocked ? 'Unlocked achievement badge' : 'Locked achievement badge',
    child: GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeOut,
        decoration: BoxDecoration(
          color: getRarityColor(context, achievement.rarity),
          borderRadius: BorderRadius.circular(16),
          boxShadow: unlocked
              ? [
                  BoxShadow(
                    color: getRarityColor(context, achievement.rarity).withOpacity(0.5),
                    blurRadius: 12,
                    spreadRadius: 2,
                  ),
                ]
              : [],
          border: Border.all(
            color: unlocked ? Colors.amberAccent : Colors.grey,
            width: unlocked ? 3 : 1,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Badge icon
            Icon(
              Icons.emoji_events,
              color: unlocked ? Colors.white : Colors.grey.shade400,
              size: 48,
              semanticLabel: achievement.title,
            ),
            // Glow animation for unlocked
            if (unlocked)
              Positioned.fill(
                child: AnimatedOpacity(
                  opacity: unlocked ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 600),
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: getRarityColor(context, achievement.rarity).withOpacity(0.7),
                          blurRadius: 24,
                          spreadRadius: 8,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            // Confetti effect (placeholder)
            if (unlocked)
              Positioned(
                top: 0,
                right: 0,
                child: Icon(Icons.celebration, color: Colors.amberAccent, size: 24),
              ),
            // Locked overlay
            if (!unlocked)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.4),
                  child: Center(
                    child: Icon(Icons.lock, color: Colors.white, size: 32),
                  ),
                ),
              ),
            // Details overlay
            Positioned(
              bottom: 8,
              child: Column(
                children: [
                  Text(
                    achievement.title,
                    style: TextStyle(
                      color: unlocked ? Colors.white : Colors.grey.shade300,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (unlocked && unlockDate != null)
                    Text(
                      'Unlocked: ${unlockDate!.toLocal().toString().split(' ')[0]}',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  if (points != null)
                    Text(
                      '+$points pts',
                      style: TextStyle(
                        color: Colors.amberAccent,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

}

// End of achievement_badge.dart
