// AchievementNotification widget for Moroccan Accounting App
// Displays animated achievement unlock notifications with confetti and celebration effects.

import 'package:flutter/material.dart';
import '../../models/gamification/achievement_definitions.dart';
import '../../theme/semantic_colors.dart';

class AchievementNotification extends StatefulWidget {
  final AchievementDefinition achievement;
  final VoidCallback? onDismiss;
  final VoidCallback? onShare;

  const AchievementNotification({
    super.key,
    required this.achievement,
    this.onDismiss,
    this.onShare,
  });

  @override
  State<AchievementNotification> createState() => _AchievementNotificationState();
}

class _AchievementNotificationState extends State<AchievementNotification>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.bounceOut,
    ));

    _slideController.forward();
    _scaleController.forward();

    // Auto-dismiss after 5 seconds
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  void _dismiss() {
    _slideController.reverse().then((_) {
      widget.onDismiss?.call();
    });
  }

  Color getRarityColor() {
    final colors = context.semanticColors;
    switch (widget.achievement.rarity) {
      case AchievementRarity.common:
        return colors.badgeCommon;
      case AchievementRarity.rare:
        return colors.badgeRare;
      case AchievementRarity.epic:
        return colors.badgeEpic;
      case AchievementRarity.legendary:
        return colors.badgeLegendary;
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: getRarityColor(),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: getRarityColor().withOpacity(0.5),
                blurRadius: 12,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Celebration icons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.celebration, color: Colors.amber, size: 32),
                  SizedBox(width: 8),
                  Text('Achievement Unlocked!', 
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 8),
                  Icon(Icons.celebration, color: Colors.amber, size: 32),
                ],
              ),
              SizedBox(height: 12),
              
              // Achievement badge
              Icon(Icons.emoji_events, color: Colors.white, size: 48),
              SizedBox(height: 8),
              
              // Achievement details
              Text(
                widget.achievement.title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4),
              Text(
                widget.achievement.description,
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              
              // Points and rarity
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.star, color: Colors.amber, size: 20),
                  SizedBox(width: 4),
                  Text(
                    '+${widget.achievement.points} points',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 16),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      widget.achievement.rarity.name.toUpperCase(),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton.icon(
                    icon: Icon(Icons.close, color: Colors.white),
                    label: Text('Dismiss', style: TextStyle(color: Colors.white)),
                    onPressed: _dismiss,
                  ),
                  if (widget.onShare != null)
                    TextButton.icon(
                      icon: Icon(Icons.share, color: Colors.white),
                      label: Text('Share', style: TextStyle(color: Colors.white)),
                      onPressed: widget.onShare,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// End of achievement_notification.dart
