import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/theme_service.dart';
import 'package:intl/intl.dart';
import '../../theme/semantic_colors.dart';
import '../../theme/design_tokens.dart';

class BilanWidget extends StatelessWidget {
  final Map<String, List<Map<String, dynamic>>> actif;
  final Map<String, List<Map<String, dynamic>>> passif;
  final String title;
  final DateTime date;

  const BilanWidget({
    super.key,
    required this.actif,
    required this.passif,
    required this.title,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;
    final semanticColors = context.semanticColors;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? colorScheme.surface.withValues(alpha: 0.8) : colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: isDark ? 0.1 : 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(DesignTokens.space3),
            decoration: BoxDecoration(
              color: semanticColors.neutral.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(DesignTokens.radiusSm),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.account_balance,
                  color: semanticColors.neutral,
                  size: DesignTokens.iconSm,
                ),
                const SizedBox(width: DesignTokens.space4),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: semanticColors.neutral,
                        ),
                      ),
                      Text(
                        'Au ${DateFormat('dd/MM/yyyy').format(date)}',
                        style: textTheme.bodySmall?.copyWith(
                          color: semanticColors.neutral.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: EdgeInsets.all(DesignTokens.space4),
              child: IntrinsicWidth(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(context, 'ACTIF', actif),
                    const SizedBox(width: DesignTokens.space6),
                    _buildSection(context, 'PASSIF', passif),
                  ],
                ),
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: DesignTokens.space4, vertical: DesignTokens.space3),
            decoration: BoxDecoration(
              color: semanticColors.neutral.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                bottom: Radius.circular(DesignTokens.radiusSm),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: semanticColors.neutral,
                  ),
                ),
                Text(
                  '${_calculateTotal(actif).toStringAsFixed(2)} DH',
                  style: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: semanticColors.neutral,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    Map<String, List<Map<String, dynamic>>> data,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;
    final semanticColors = context.semanticColors;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: DesignTokens.space3, horizontal: DesignTokens.space4),
          decoration: BoxDecoration(
            color: semanticColors.neutral.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
            border: Border.all(
              color: semanticColors.neutral.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                title == 'ACTIF' ? Icons.account_balance_wallet : Icons.account_balance,
                color: semanticColors.neutral,
                size: DesignTokens.iconSm,
              ),
              const SizedBox(width: DesignTokens.space4),
              Text(
                title,
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: semanticColors.neutral,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: DesignTokens.space6),
        ...data.entries.map((category) {
          return Container(
            margin: EdgeInsets.only(bottom: DesignTokens.space6),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(vertical: DesignTokens.space3, horizontal: DesignTokens.space4),
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.3 : 0.5),
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(DesignTokens.radiusXl)),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(DesignTokens.space2),
                        decoration: BoxDecoration(
                          color: semanticColors.neutral.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(DesignTokens.radiusSm),
                        ),
                        child: Text(
                          category.key.substring(0, 1),
                          style: textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: semanticColors.neutral,
                          ),
                        ),
                      ),
                      const SizedBox(width: DesignTokens.space4),
                      Text(
                        category.key,
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
                Table(
                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                  columnWidths: const {
                    0: FixedColumnWidth(240),
                    1: FixedColumnWidth(160),
                  },
                  children: [
                    TableRow(
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.2 : 0.3),
                        border: Border(
                          bottom: BorderSide(
                            color: colorScheme.outline.withValues(alpha: 0.1),
                            width: 1,
                          ),
                        ),
                      ),
                      children: [
                        _buildHeaderCell(context, 'Rubrique'),
                        _buildHeaderCell(context, 'Montant', isNumeric: true),
                      ],
                    ),
                    ...category.value.map((item) {
                      return TableRow(
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: colorScheme.outline.withValues(alpha: 0.05),
                              width: 1,
                            ),
                          ),
                        ),
                        children: [
                          _buildCell(context, item['rubrique']),
                          _buildCell(
                            context,
                            '${NumberFormat('#,##0.00', 'fr_FR').format(item['montant'])} DH',
                            isNumeric: true,
                          ),
                        ],
                      );
                    }),
                    TableRow(
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.1 : 0.2),
                      ),
                      children: [
                        _buildCell(
                          context,
                          'Total ${category.key}',
                          isBold: true,
                        ),
                        _buildCell(
                          context,
                          '${NumberFormat('#,##0.00', 'fr_FR').format(_calculateCategoryTotal(category.value))} DH',
                          isNumeric: true,
                          isBold: true,
                          highlight: true,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text,
      {bool isNumeric = false}) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.symmetric(vertical: DesignTokens.space3, horizontal: DesignTokens.space4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.semanticColors.neutral,
              letterSpacing: 0.5,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  Widget _buildCell(BuildContext context, String text,
      {bool isNumeric = false, bool isBold = false, bool highlight = false}) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final semanticColors = context.semanticColors;
    
    return Container(
      padding: EdgeInsets.symmetric(vertical: DesignTokens.space3, horizontal: DesignTokens.space4),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(
          color: highlight ? semanticColors.positive : semanticColors.neutral,
          fontWeight: isBold || highlight ? FontWeight.bold : null,
          letterSpacing: highlight ? 0.5 : null,
        ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  double _calculateCategoryTotal(List<Map<String, dynamic>> items) {
    return items.fold(0.0, (sum, item) => sum + (item['montant'] as double));
  }

  double _calculateTotal(Map<String, List<Map<String, dynamic>>> data) {
    return data.values.fold(
        0.0,
        (sum, category) =>
            sum + category.fold(0.0, (sum, item) => sum + item['montant']));
  }
}
