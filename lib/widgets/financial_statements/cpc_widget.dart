import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/theme_service.dart';
import 'package:intl/intl.dart';
import '../../theme/semantic_colors.dart';
import '../../theme/design_tokens.dart';

class CPCWidget extends StatelessWidget {
  final List<Map<String, dynamic>> produits;
  final List<Map<String, dynamic>> charges;
  final String title;
  final DateTime date;

  const CPCWidget({
    super.key,
    required this.produits,
    required this.charges,
    required this.title,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;
    final semanticColors = context.semanticColors;

    return SingleChildScrollView(
      child: Container(
        decoration: BoxDecoration(
          color: isDark ? colorScheme.surface.withValues(alpha: 0.8) : colorScheme.surface,
          borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: isDark ? 0.1 : 0.2),
          ),
        ),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(DesignTokens.space3),
            decoration: BoxDecoration(
              color: semanticColors.neutral.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(DesignTokens.radiusSm),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: semanticColors.neutral,
                  size: DesignTokens.iconSm,
                ),
                const SizedBox(width: DesignTokens.space2),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: semanticColors.neutral,
                        ),
                      ),
                      Text(
                        'Au ${DateFormat('dd/MM/yyyy').format(date)}',
                        style: textTheme.bodySmall?.copyWith(
                          color: semanticColors.neutral.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: EdgeInsets.all(DesignTokens.space4),
              child: IntrinsicWidth(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(context, 'PRODUITS', produits),
                    const SizedBox(height: DesignTokens.space6),
                    _buildSection(context, 'CHARGES', charges),
                    const SizedBox(height: DesignTokens.space6),
                    _buildResultatNet(context),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    )
    )
    ;
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    List<Map<String, dynamic>> items,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;
    final semanticColors = context.semanticColors;

    return Container(
      margin: EdgeInsets.only(bottom: DesignTokens.space6),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: DesignTokens.space3, horizontal: DesignTokens.space4),
            decoration: BoxDecoration(
              color: title == 'PRODUITS' 
                ? semanticColors.positive.withValues(alpha: 0.1)
                : semanticColors.negative.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(DesignTokens.radiusXl)),
            ),
            child: Row(
              children: [
                Icon(
                  title == 'PRODUITS' 
                    ? Icons.trending_up_rounded
                    : Icons.trending_down_rounded,
                  color: title == 'PRODUITS' 
                    ? semanticColors.positive
                    : semanticColors.negative,
                  size: DesignTokens.iconBase,
                ),
                const SizedBox(width: DesignTokens.space3),
                Text(
                  title,
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: title == 'PRODUITS' 
                      ? semanticColors.positive
                      : semanticColors.negative,
                    letterSpacing: 0.5,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: DesignTokens.space3, vertical: DesignTokens.space2),
                  decoration: BoxDecoration(
                    color: (title == 'PRODUITS' 
                      ? semanticColors.positive
                      : semanticColors.negative).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(DesignTokens.radiusXl),
                    border: Border.all(
                      color: (title == 'PRODUITS' 
                        ? semanticColors.positive
                        : semanticColors.negative).withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '${NumberFormat('#,##0.00', 'fr_FR').format(_calculateTotal(items))} DH',
                    style: textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: title == 'PRODUITS' 
                        ? semanticColors.positive
                        : semanticColors.negative,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Table(
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            columnWidths: const {
              0: FixedColumnWidth(240),
              1: FixedColumnWidth(160),
            },
            children: [
              TableRow(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.2 : 0.3),
                  border: Border(
                    bottom: BorderSide(
                      color: colorScheme.outline.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                ),
                children: [
                  _buildHeaderCell(context, 'Rubrique'),
                  _buildHeaderCell(context, 'Montant', isNumeric: true),
                ],
              ),
              ...items.map((item) {
                final isPositive = item['montant'] >= 0;
                return TableRow(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: colorScheme.outline.withValues(alpha: 0.05),
                        width: 1,
                      ),
                    ),
                  ),
                  children: [
                    _buildCell(context, item['rubrique']),
                    _buildCell(
                      context,
                      '${NumberFormat('#,##0.00', 'fr_FR').format(item['montant'].abs())} DH',
                      isNumeric: true,
                      isPositive: isPositive,
                    ),
                  ],
                );
              }),
              TableRow(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.1 : 0.2),
                ),
                children: [
                  _buildCell(
                    context,
                    'Total $title',
                    isBold: true,
                  ),
                  _buildCell(
                    context,
                    '${NumberFormat('#,##0.00', 'fr_FR').format(_calculateTotal(items))} DH',
                    isNumeric: true,
                    isBold: true,
                    highlight: true,
                    isPositive: title == 'PRODUITS',
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultatNet(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;
    final semanticColors = context.semanticColors;

    final totalProduits = _calculateTotal(produits);
    final totalCharges = _calculateTotal(charges);
    final resultatNet = totalProduits - totalCharges;

    return Container(
      padding: EdgeInsets.symmetric(vertical: DesignTokens.space3, horizontal: DesignTokens.space4),
      decoration: BoxDecoration(
        color: semanticColors.neutral.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Résultat Net',
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: semanticColors.neutral,
            ),
          ),
          Text(
            '${resultatNet.toStringAsFixed(2)} DH',
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: resultatNet >= 0 ? semanticColors.positive : semanticColors.negative,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text,
      {bool isNumeric = false}) {
    final colorScheme = Theme.of(context).colorScheme;
    final semanticColors = context.semanticColors;
    return Container(
      padding: EdgeInsets.symmetric(vertical: DesignTokens.space3, horizontal: DesignTokens.space4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: semanticColors.neutral,
              letterSpacing: 0.5,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  Widget _buildCell(BuildContext context, String text,
      {bool isNumeric = false, bool isBold = false, bool highlight = false, bool isPositive = true}) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final semanticColors = context.semanticColors;
    
    Color textColor = semanticColors.neutral;
    if (highlight) {
      textColor = isPositive ? semanticColors.positive : semanticColors.negative;
    } else if (isNumeric) {
      textColor = isPositive ? semanticColors.neutral : semanticColors.negative;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(vertical: DesignTokens.space3, horizontal: DesignTokens.space4),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(
          color: textColor,
          fontWeight: isBold || highlight ? FontWeight.bold : null,
          letterSpacing: highlight ? 0.5 : null,
        ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  double _calculateTotal(List<Map<String, dynamic>> items) {
    return items.fold(0.0, (sum, item) => sum + (item['montant'] as double));
  }
}
