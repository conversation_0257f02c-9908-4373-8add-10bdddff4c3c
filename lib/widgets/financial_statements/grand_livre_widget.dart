import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/theme_service.dart';
import 'package:intl/intl.dart';

class GrandLivreEntry {
  final String? date;
  final String? pieceNumber;
  final String description;
  final double debit;
  final double credit;

  const GrandLivreEntry({
    this.date,
    this.pieceNumber,
    required this.description,
    required this.debit,
    required this.credit,
  });
}

class GrandLivreWidget extends StatelessWidget {
  final Map<String, List<GrandLivreEntry>> entries;
  final String title;
  final DateTime date;

  const GrandLivreWidget({
    super.key,
    required this.entries,
    required this.title,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      decoration: BoxDecoration(
        color:
            isDark ? colorScheme.surface.withValues(alpha: 0.8) : colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: isDark ? 0.1 : 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(11),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.book,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                      Text(
                        'Au ${DateFormat('dd/MM/yyyy').format(date)}',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.primary.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: entries.entries.map((account) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        decoration: BoxDecoration(
                          color: colorScheme.surfaceContainerHighest
                              .withValues(alpha: isDark ? 0.3 : 0.5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Compte ${account.key}',
                          style: textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.middle,
                        columnWidths: const {
                          0: FixedColumnWidth(100), // Date
                          1: FixedColumnWidth(100), // N° Pièce
                          2: FixedColumnWidth(250), // Libellé
                          3: FixedColumnWidth(120), // Débit
                          4: FixedColumnWidth(120), // Crédit
                          5: FixedColumnWidth(120), // Solde
                        },
                        children: [
                          TableRow(
                            decoration: BoxDecoration(
                              color: colorScheme.surfaceContainerHighest
                                  .withValues(alpha: isDark ? 0.2 : 0.4),
                            ),
                            children: [
                              _buildHeaderCell(context, 'Date'),
                              _buildHeaderCell(context, 'N° Pièce'),
                              _buildHeaderCell(context, 'Libellé'),
                              _buildHeaderCell(context, 'Débit',
                                  isNumeric: true),
                              _buildHeaderCell(context, 'Crédit',
                                  isNumeric: true),
                              _buildHeaderCell(context, 'Solde',
                                  isNumeric: true),
                            ],
                          ),
                          ...account.value.map((entry) {
                            final solde = _calculateSolde(
                                account.value, account.value.indexOf(entry));
                            return TableRow(
                              children: [
                                _buildCell(context, entry.date ?? ''),
                                _buildCell(context, entry.pieceNumber ?? ''),
                                _buildCell(context, entry.description),
                                _buildCell(
                                  context,
                                  entry.debit > 0
                                      ? '${entry.debit.toStringAsFixed(2)} DH'
                                      : '',
                                  isNumeric: true,
                                ),
                                _buildCell(
                                  context,
                                  entry.credit > 0
                                      ? '${entry.credit.toStringAsFixed(2)} DH'
                                      : '',
                                  isNumeric: true,
                                ),
                                _buildCell(
                                  context,
                                  '${solde.abs().toStringAsFixed(2)} DH',
                                  isNumeric: true,
                                  isBold: true,
                                  color: solde >= 0
                                      ? colorScheme.primary
                                      : colorScheme.error,
                                ),
                              ],
                            );
                          }),
                          TableRow(
                            decoration: BoxDecoration(
                              color: colorScheme.surfaceContainerHighest
                                  .withValues(alpha: isDark ? 0.1 : 0.2),
                            ),
                            children: [
                              _buildCell(context, '', isBold: true),
                              _buildCell(context, '', isBold: true),
                              _buildCell(context, 'Totaux', isBold: true),
                              _buildCell(
                                context,
                                '${_calculateTotalDebit(account.value).toStringAsFixed(2)} DH',
                                isNumeric: true,
                                isBold: true,
                              ),
                              _buildCell(
                                context,
                                '${_calculateTotalCredit(account.value).toStringAsFixed(2)} DH',
                                isNumeric: true,
                                isBold: true,
                              ),
                              _buildCell(
                                context,
                                '${_calculateFinalSolde(account.value).abs().toStringAsFixed(2)} DH',
                                isNumeric: true,
                                isBold: true,
                                color: _calculateFinalSolde(account.value) >= 0
                                    ? colorScheme.primary
                                    : colorScheme.error,
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text,
      {bool isNumeric = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  Widget _buildCell(
    BuildContext context,
    String text, {
    bool isNumeric = false,
    bool isBold = false,
    Color? color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color ?? Theme.of(context).colorScheme.onSurface,
              fontWeight: isBold ? FontWeight.bold : null,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  double _calculateSolde(List<GrandLivreEntry> entries, int upToIndex) {
    double solde = 0;
    for (var i = 0; i <= upToIndex; i++) {
      solde += entries[i].debit - entries[i].credit;
    }
    return solde;
  }

  double _calculateTotalDebit(List<GrandLivreEntry> entries) {
    return entries.fold(0.0, (sum, entry) => sum + entry.debit);
  }

  double _calculateTotalCredit(List<GrandLivreEntry> entries) {
    return entries.fold(0.0, (sum, entry) => sum + entry.credit);
  }

  double _calculateFinalSolde(List<GrandLivreEntry> entries) {
    return _calculateTotalDebit(entries) - _calculateTotalCredit(entries);
  }
}
