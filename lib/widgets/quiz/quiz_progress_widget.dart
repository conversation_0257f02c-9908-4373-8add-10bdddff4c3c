import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../services/quiz_progress_service.dart';

class QuizProgressWidget extends StatefulWidget {
  final VoidCallback onTapQuiz;

  const QuizProgressWidget({
    super.key,
    required this.onTapQuiz,
  });

  @override
  State<QuizProgressWidget> createState() => _QuizProgressWidgetState();
}

class _QuizProgressWidgetState extends State<QuizProgressWidget> {
  final QuizProgressService _progressService = QuizProgressService();
  bool _isLoading = true;
  QuizStats? _stats;
  Map<String, Map<String, QuizResult>>? _quizResults;
  
  // Store the most recent quiz info
  QuizResult? _mostRecentQuiz;
  String _mostRecentCategory = '';
  String _mostRecentLevel = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final stats = await _progressService.getQuizStats();
      final results = await _progressService.getAllQuizResults();

      // Find the most recent quiz
      QuizResult? mostRecentQuiz;
      String mostRecentCategory = '';
      String mostRecentLevel = '';
      
      results.forEach((category, levels) {
        levels.forEach((level, result) {
          if (mostRecentQuiz == null || 
              result.lastCompletedAt.isAfter(mostRecentQuiz!.lastCompletedAt)) {
            mostRecentQuiz = result;
            mostRecentCategory = category;
            mostRecentLevel = level;
          }
        });
      });

      setState(() {
        _stats = stats;
        _quizResults = results;
        _mostRecentQuiz = mostRecentQuiz;
        _mostRecentCategory = mostRecentCategory;
        _mostRecentLevel = mostRecentLevel;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading quiz progress: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    if (_isLoading) {
      return _buildLoadingCard(colorScheme);
    }

    if (_stats == null || 
        _stats!.totalQuizzesCompleted == 0 || 
        _quizResults == null || 
        _quizResults!.isEmpty) {
      return _buildEmptyCard(colorScheme, textTheme);
    }

    return _buildProgressCard(colorScheme, textTheme);
  }

  Widget _buildLoadingCard(ColorScheme colorScheme) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: EdgeInsets.all(isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),
        height: isVerySmallScreen ? 120 : (isSmallScreen ? 140 : 180),
        child: Center(
          child: CircularProgressIndicator(
            color: colorScheme.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyCard(ColorScheme colorScheme, TextTheme textTheme) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: widget.onTapQuiz,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: EdgeInsets.all(isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),
          height: isVerySmallScreen ? 120 : (isSmallScreen ? 140 : 180),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.quiz,
                size: isVerySmallScreen ? 28 : (isSmallScreen ? 36 : 48),
                color: colorScheme.primary.withValues(alpha: 0.7),
              ),
              SizedBox(height: isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),
              Text(
                'Commencez votre premier quiz!',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                  fontSize: isVerySmallScreen ? 12 : (isSmallScreen ? 14 : null),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 8)),
              Text(
                'Testez vos connaissances et suivez votre progression',
                style: textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                  fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : null),
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressCard(ColorScheme colorScheme, TextTheme textTheme) {
    final stats = _stats!;
    final results = _quizResults!;
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;
    
    // Calculate total categories and levels completed
    final int totalCategories = results.length;
    int totalLevelsCompleted = 0;
    results.forEach((_, levels) {
      totalLevelsCompleted += levels.length;
    });

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: widget.onTapQuiz,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: EdgeInsets.all(isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isVerySmallScreen ? 6 : 8),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.quiz,
                      color: colorScheme.primary,
                      size: isVerySmallScreen ? 16 : (isSmallScreen ? 20 : 24),
                    ),
                  ),
                  SizedBox(width: isVerySmallScreen ? 8 : 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Progression Quiz',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                            fontSize: isVerySmallScreen ? 12 : (isSmallScreen ? 14 : null),
                          ),
                        ),
                        Text(
                          '${stats.totalQuizzesCompleted} quiz complétés',
                          style: textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                            fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : null),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isVerySmallScreen ? 8 : 12, 
                      vertical: isVerySmallScreen ? 4 : 6
                    ),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${stats.correctAnswerPercentage.toStringAsFixed(0)}%',
                      style: textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onPrimaryContainer,
                        fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : null),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatColumn(
                    context,
                    'Points',
                    stats.totalPointsEarned.toString(),
                    Icons.stars,
                    colorScheme.primary,
                  ),
                  _buildStatColumn(
                    context,
                    'Catégories',
                    totalCategories.toString(),
                    Icons.category,
                    colorScheme.secondary,
                  ),
                  _buildStatColumn(
                    context,
                    'Niveaux',
                    totalLevelsCompleted.toString(),
                    Icons.trending_up,
                    colorScheme.tertiary,
                  ),
                ],
              ),
              if (_mostRecentQuiz != null && !(isSmallScreen && isVerySmallScreen)) ...[
                SizedBox(height: isVerySmallScreen ? 12 : 16),
                const Divider(),
                SizedBox(height: isVerySmallScreen ? 6 : 8),
                Text(
                  'Dernier Quiz Complété',
                  style: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                    fontSize: isVerySmallScreen ? 10 : (isSmallScreen ? 12 : null),
                  ),
                ),
                SizedBox(height: isVerySmallScreen ? 6 : 8),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '$_mostRecentCategory: $_mostRecentLevel',
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface,
                              fontSize: isVerySmallScreen ? 10 : (isSmallScreen ? 12 : null),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Score: ${_mostRecentQuiz!.lastScore}/${_mostRecentQuiz!.totalQuestions}',
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                              fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : null),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: isVerySmallScreen ? 40 : 60,
                      height: isVerySmallScreen ? 40 : 60,
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            CircularProgressIndicator(
                              value: _mostRecentQuiz!.lastScorePercentage / 100,
                              backgroundColor: colorScheme.surfaceContainerHighest,
                              color: _getScoreColor(_mostRecentQuiz!.lastScorePercentage, colorScheme),
                              strokeWidth: isVerySmallScreen ? 4 : 6,
                            ),
                            Text(
                              '${_mostRecentQuiz!.lastScorePercentage.toStringAsFixed(0)}%',
                              style: textTheme.labelSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: colorScheme.onSurfaceVariant,
                                fontSize: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : null),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildStatColumn(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.all(isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 10)),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: isVerySmallScreen ? 12 : (isSmallScreen ? 14 : 20),
          ),
        ),
        SizedBox(height: isVerySmallScreen ? 1 : (isSmallScreen ? 2 : 8)),
        Text(
          value,
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
            fontSize: isVerySmallScreen ? 10 : (isSmallScreen ? 12 : null),
          ),
        ),
        Text(
          label,
          style: textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurfaceVariant,
            fontSize: isVerySmallScreen ? 7 : (isSmallScreen ? 9 : null),
          ),
        ),
      ],
    );
  }

  Color _getScoreColor(double percentage, ColorScheme colorScheme) {
    if (percentage >= 80) {
      return Colors.green;
    } else if (percentage >= 60) {
      return Colors.amber;
    } else {
      return Colors.redAccent;
    }
  }
} 