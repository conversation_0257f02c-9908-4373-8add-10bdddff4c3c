// Custom Quiz Builder Widget
// Integrates with CustomQuizConfig and quiz services/providers

import 'package:flutter/material.dart';
import '../../models/quiz/custom_quiz_config.dart';
// import '../../services/question_selection_service.dart';
// Quiz generation logic should be handled externally.

/// Widget for building custom quizzes with configurable options.
class CustomQuizBuilderWidget extends StatefulWidget {
  final CustomQuizConfig? initialConfig;
  final Function(CustomQuizConfig) onConfigChanged;

  const CustomQuizBuilderWidget({
    super.key,
    this.initialConfig,
    required this.onConfigChanged,
  });

  @override
  State<CustomQuizBuilderWidget> createState() => _CustomQuizBuilderWidgetState();
}

class _CustomQuizBuilderWidgetState extends State<CustomQuizBuilderWidget> {
  late CustomQuizConfig _config;

  @override
  void initState() {
    super.initState();
    _config = widget.initialConfig ?? const CustomQuizConfig();
  }

  void _updateConfig(CustomQuizConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigChanged(_config);
  }

  @override
  Widget build(BuildContext context) {
    // Example UI for quiz config options
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Custom Quiz Builder', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            // Question count
            Row(
              children: [
                const Text('Nombre de questions:'),
                const SizedBox(width: 8),
                DropdownButton<int>(
                  value: _config.questionCount,
                  items: List.generate(20, (i) => (i + 1) * 5)
                      .map((n) => DropdownMenuItem(value: n, child: Text('$n')))
                      .toList(),
                  onChanged: (val) {
                    if (val != null) {
                      _updateConfig(_config.copyWith(questionCount: val));
                    }
                  },
                ),
              ],
            ),
            // Difficulty range
            Row(
              children: [
                const Text('Difficulté min:'),
                const SizedBox(width: 8),
                DropdownButton<int>(
                  value: _config.minDifficulty,
                  items: List.generate(5, (i) => i + 1)
                      .map((d) => DropdownMenuItem(value: d, child: Text('$d')))
                      .toList(),
                  onChanged: (val) {
                    if (val != null) {
                      _updateConfig(_config.copyWith(minDifficulty: val));
                    }
                  },
                ),
                const SizedBox(width: 16),
                const Text('max:'),
                const SizedBox(width: 8),
                DropdownButton<int>(
                  value: _config.maxDifficulty,
                  items: List.generate(5, (i) => i + 1)
                      .map((d) => DropdownMenuItem(value: d, child: Text('$d')))
                      .toList(),
                  onChanged: (val) {
                    if (val != null) {
                      _updateConfig(_config.copyWith(maxDifficulty: val));
                    }
                  },
                ),
              ],
            ),
            // Time per question
            Row(
              children: [
                const Text('Temps/question (sec):'),
                const SizedBox(width: 8),
                DropdownButton<int>(
                  value: _config.timePerQuestion,
                  items: [10, 20, 30, 45, 60, 90, 120, 180, 300]
                      .map((t) => DropdownMenuItem(value: t, child: Text('$t')))
                      .toList(),
                  onChanged: (val) {
                    if (val != null) {
                      _updateConfig(_config.copyWith(timePerQuestion: val));
                    }
                  },
                ),
              ],
            ),
            // Spaced repetition toggle
            SwitchListTile(
              title: const Text('Inclure révisions programmées'),
              value: _config.includeSpacedRepetition,
              onChanged: (val) {
                _updateConfig(_config.copyWith(includeSpacedRepetition: val));
              },
            ),
            // Adaptive difficulty toggle
            SwitchListTile(
              title: const Text('Difficulté adaptive'),
              value: _config.adaptiveDifficulty,
              onChanged: (val) {
                _updateConfig(_config.copyWith(adaptiveDifficulty: val));
              },
            ),
            // Focus on weak areas toggle
            SwitchListTile(
              title: const Text('Focus sur les faiblesses'),
              value: _config.focusOnWeakAreas,
              onChanged: (val) {
                _updateConfig(_config.copyWith(focusOnWeakAreas: val));
              },
            ),
            // Custom title
            TextField(
              decoration: const InputDecoration(labelText: 'Titre personnalisé'),
              controller: TextEditingController(text: _config.customTitle),
              onChanged: (val) {
                _updateConfig(_config.copyWith(customTitle: val));
              },
            ),
            // Topics and categories selection (placeholder, should be replaced with actual data)
            const SizedBox(height: 16),
            Text('Sujets sélectionnés: ${_config.selectedTopics.join(", ")}'),
            Text('Catégories sélectionnées: ${_config.selectedCategories.join(", ")}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Quiz generation logic should be handled externally
                if (_config.isValid()) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Configuration valide: ${_config.getSummary()}')),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Configuration invalide')),
                  );
                }
              },
              child: const Text('Valider la configuration'),
            ),
          ],
        ),
      ),
    );
  }
}

// End of CustomQuizBuilderWidget
