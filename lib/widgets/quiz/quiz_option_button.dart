import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../models/quiz_model.dart';
import '../../theme/semantic_colors.dart';
import '../../theme/design_tokens.dart';
import '../../theme/app_typography.dart';
import '../accessibility/semantic_wrapper.dart';

class QuizOptionButton extends StatefulWidget {
  final QuizQuestion question;
  final int index;
  final bool isSelected;
  final bool showResult;
  final VoidCallback onPressed;
  final Color color;
  final bool enableKeyboardShortcuts;
  final bool enableHapticFeedback;
  final bool enableReducedMotion;
  final FocusNode? focusNode;
  final ValueChanged<bool>? onFocusChange;

  const QuizOptionButton({
    super.key,
    required this.question,
    required this.index,
    required this.isSelected,
    required this.showResult,
    required this.onPressed,
    required this.color,
    this.enableKeyboardShortcuts = true,
    this.enableHapticFeedback = true,
    this.enableReducedMotion = false,
    this.focusNode,
    this.onFocusChange,
  });

  @override
  State<QuizOptionButton> createState() => _QuizOptionButtonState();
}

class _QuizOptionButtonState extends State<QuizOptionButton> {
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _wasSelectedBefore = false;
  bool _wasResultShownBefore = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
    _wasSelectedBefore = widget.isSelected;
    _wasResultShownBefore = widget.showResult;
  }

  @override
  void didUpdateWidget(QuizOptionButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Handle focus node changes
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_handleFocusChange);
      _focusNode = widget.focusNode ?? FocusNode();
      _focusNode.addListener(_handleFocusChange);
    }

    // Announce state changes for screen readers
    _announceStateChanges(oldWidget);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_handleFocusChange);
    }
    super.dispose();
  }

  void _handleFocusChange() {
    final isFocused = _focusNode.hasFocus;
    if (_isFocused != isFocused) {
      setState(() {
        _isFocused = isFocused;
      });
      widget.onFocusChange?.call(isFocused);
    }
  }

  void _announceStateChanges(QuizOptionButton oldWidget) {
    // Announce selection changes
    if (widget.isSelected != _wasSelectedBefore) {
      final optionLetter = String.fromCharCode(65 + widget.index);
      final message = widget.isSelected 
          ? 'Option $optionLetter selected: ${widget.question.options[widget.index]}'
          : 'Option $optionLetter unselected';
      SemanticsService.announce(message, TextDirection.ltr);
      _wasSelectedBefore = widget.isSelected;
    }

    // Announce result reveals
    if (widget.showResult && !_wasResultShownBefore) {
      final optionLetter = String.fromCharCode(65 + widget.index);
      final isCorrect = widget.index == widget.question.correct;
      final message = isCorrect 
          ? 'Option $optionLetter is correct'
          : 'Option $optionLetter is incorrect';
      SemanticsService.announce(message, TextDirection.ltr);
      _wasResultShownBefore = widget.showResult;
    }
  }

  void _handleSelection() {
    // Provide haptic feedback
    if (widget.enableHapticFeedback) {
      HapticFeedback.selectionClick();
    }
    
    widget.onPressed();
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      // Handle keyboard shortcuts for option selection
      if (widget.enableKeyboardShortcuts) {
        final optionKey = LogicalKeyboardKey.keyA.keyId + widget.index;
        if (event.logicalKey.keyId == optionKey) {
          _handleSelection();
          return KeyEventResult.handled;
        }
      }

      // Handle standard activation keys
      if (event.logicalKey == LogicalKeyboardKey.enter ||
          event.logicalKey == LogicalKeyboardKey.space) {
        _handleSelection();
        return KeyEventResult.handled;
      }
    }

    return KeyEventResult.ignored;
  }

  String _generateSemanticLabel() {
    final optionLetter = String.fromCharCode(65 + widget.index);
    final optionText = widget.question.options[widget.index];
    final isCorrect = widget.index == widget.question.correct;
    
    String label = 'Option $optionLetter: $optionText';
    
    if (widget.showResult) {
      if (isCorrect) {
        label += ', correct answer';
      } else if (widget.isSelected && !isCorrect) {
        label += ', incorrect answer, selected';
      } else if (!isCorrect) {
        label += ', incorrect answer';
      }
    } else if (widget.isSelected) {
      label += ', selected';
    }
    
    return label;
  }

  String _generateSemanticHint() {
    if (widget.showResult) {
      return 'Quiz results are displayed';
    }
    
    final optionLetter = String.fromCharCode(65 + widget.index);
    String hint = 'Tap to select this option';
    
    if (widget.enableKeyboardShortcuts) {
      hint += ', or press $optionLetter key';
    }
    
    return hint;
  }

  @override
  Widget build(BuildContext context) {
    final option = widget.question.options[widget.index];
    final isCorrect = widget.index == widget.question.correct;
    final colorScheme = Theme.of(context).colorScheme;
    final semanticColors = context.semanticColors;

    // Determine button color based on state using semantic colors
    Color backgroundColor;
    Color borderColor;
    Color textColor;
    Color focusColor;

    if (widget.showResult) {
      if (isCorrect) {
        backgroundColor = semanticColors.successContainer;
        borderColor = semanticColors.success;
        textColor = semanticColors.onSuccessContainer;
        focusColor = semanticColors.success;
      } else if (widget.isSelected && !isCorrect) {
        backgroundColor = semanticColors.errorContainer;
        borderColor = semanticColors.errorVariant;
        textColor = semanticColors.onErrorContainer;
        focusColor = semanticColors.errorVariant;
      } else {
        backgroundColor = colorScheme.surfaceContainerHighest;
        borderColor = colorScheme.outline;
        textColor = colorScheme.onSurfaceVariant;
        focusColor = colorScheme.primary;
      }
    } else {
      if (widget.isSelected) {
        backgroundColor = widget.color.withValues(alpha: 0.2);
        borderColor = widget.color;
        textColor = widget.color.withValues(alpha: 0.9);
        focusColor = widget.color;
      } else {
        backgroundColor = colorScheme.surfaceContainerHighest;
        borderColor = colorScheme.outline;
        textColor = colorScheme.onSurfaceVariant;
        focusColor = colorScheme.primary;
      }
    }

    // Enhanced focus indicator for accessibility
    Widget buildFocusIndicator({required Widget child}) {
      if (!_isFocused) return child;
      
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
          border: Border.all(
            color: focusColor,
            width: 3.0, // Enhanced visibility
          ),
          boxShadow: [
            BoxShadow(
              color: focusColor.withValues(alpha: 0.3),
              blurRadius: 6.0,
              spreadRadius: 2.0,
            ),
          ],
        ),
        child: child,
      );
    }

    Widget buttonContent = Material(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
      elevation: widget.isSelected ? DesignTokens.elevation2 : DesignTokens.elevation0,
      child: InkWell(
        onTap: _handleSelection,
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        child: Container(
          // Ensure minimum touch target size (44x44 dp)
          constraints: const BoxConstraints(
            minHeight: DesignTokens.touchTargetMin,
          ),
          padding: EdgeInsets.all(DesignTokens.space4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
            border: Border.all(
              color: borderColor,
              width: widget.isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: DesignTokens.iconXl + DesignTokens.space1,
                height: DesignTokens.iconXl + DesignTokens.space1,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.isSelected
                      ? widget.color.withValues(alpha: 0.2)
                      : colorScheme.surface,
                  border: Border.all(
                    color: widget.isSelected ? widget.color : colorScheme.outline,
                    width: 1.5,
                  ),
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + widget.index), // A, B, C, D...
                    style: AppTypography.labelLarge().copyWith(
                      fontWeight: FontWeight.bold,
                      color: widget.isSelected ? widget.color : colorScheme.onSurfaceVariant,
                    ),
                    semanticsLabel: 'Option ${String.fromCharCode(65 + widget.index)}',
                  ),
                ),
              ),
              SizedBox(width: DesignTokens.space4),
              Expanded(
                child: Text(
                  option,
                  style: AppTypography.bodyLarge().copyWith(
                    color: textColor,
                    fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                  softWrap: true,
                  overflow: TextOverflow.visible,
                ),
              ),
              if (widget.showResult) ...[
                SizedBox(width: DesignTokens.space2),
                Semantics(
                  label: isCorrect ? 'Correct answer' : 'Incorrect answer',
                  child: Icon(
                    isCorrect ? Icons.check_circle : Icons.cancel,
                    color: isCorrect
                        ? semanticColors.success
                        : semanticColors.errorVariant,
                    size: DesignTokens.iconBase,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );

    // Wrap with semantic information and focus management
    Widget accessibleButton = SemanticWrapper(
      semanticLabel: _generateSemanticLabel(),
      semanticHint: _generateSemanticHint(),
      semanticRole: SemanticRole.radio, // Quiz options act like radio buttons
      isButton: true,
      isSelected: widget.isSelected,
      isEnabled: !widget.showResult, // Disable interaction when showing results
      focusNode: _focusNode,
      onFocusChange: (bool focused) => _handleFocusChange(),
      onTap: widget.showResult ? null : _handleSelection,
      onKeyEvent: (KeyEvent event) => _handleKeyEvent(_focusNode, event),
      enhancedFocusIndicator: false, // We handle focus indicator manually
      announceStateChanges: true,
      context: 'Quiz option',
      child: buildFocusIndicator(child: buttonContent),
    );

    // Apply animations with reduced motion support
    if (widget.enableReducedMotion) {
      return accessibleButton;
    } else {
      return accessibleButton
          .animate()
          .fadeIn(duration: DesignTokens.durationNormal)
          .scale(
            begin: const Offset(0.95, 0.95),
            end: const Offset(1.0, 1.0),
            duration: DesignTokens.durationNormal,
          )
          .move(
            begin: Offset(0, 10),
            end: Offset.zero,
            delay: DesignTokens.durationNormal * widget.index,
            duration: DesignTokens.durationNormal,
          );
    }
  }
}

/// Extension methods for quiz option accessibility
extension QuizOptionAccessibility on QuizOptionButton {
  /// Creates an accessible quiz option button with semantic grouping
  static Widget createAccessibleGroup({
    required List<QuizOptionButton> options,
    required String groupLabel,
    int? selectedIndex,
  }) {
    return Semantics(
      label: groupLabel,
      hint: 'Select one option from ${options.length} choices',
      child: FocusTraversalGroup(
        policy: OrderedTraversalPolicy(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            
            return Padding(
              padding: EdgeInsets.only(
                bottom: index < options.length - 1 ? DesignTokens.space3 : 0,
              ),
              child: FocusTraversalOrder(
                order: NumericFocusOrder(index.toDouble()),
                child: option,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
