import 'package:flutter/material.dart';

class QuizProgressTimer extends StatelessWidget {
  final double progress;
  final int timeRemaining;
  final Color color;

  const QuizProgressTimer({
    super.key,
    required this.progress,
    required this.timeRemaining,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    
    // Determine color based on time remaining
    Color timerColor = color;
    if (timeRemaining < 10) {
      timerColor = Colors.red;
    } else if (timeRemaining < 20) {
      timerColor = Colors.orange;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            color: timerColor,
            size: 18,
          ),
          const SizedBox(width: 6),
          Text(
            '$timeRemaining s',
            style: textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
