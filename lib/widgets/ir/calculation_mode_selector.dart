import 'package:flutter/material.dart';

class CalculationModeSelector extends StatelessWidget {
  final bool isAnnualMode;
  final Function(bool) onModeChanged;
  final bool enabled;

  const CalculationModeSelector({
    super.key,
    required this.isAnnualMode,
    required this.onModeChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Mode de calcul',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final isWide = constraints.maxWidth > 400;
                  
                  return Column(
                    children: [
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: enabled 
                                ? Theme.of(context).primaryColor.withValues(alpha: 0.3)
                                : Colors.grey.shade300,
                          ),
                        ),
                        child: _buildSegmentedButtons(context, isWide),
                      ),
                      const SizedBox(height: 12),
                      _buildModeDescription(context),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSegmentedButtons(BuildContext context, bool isWide) {
    return Row(
      children: [
        Expanded(
          child: _buildModeButton(
            context,
            false,
            'Mensuel',
            Icons.calendar_today,
            'Calcul basé sur le barème mensuel',
            isWide,
          ),
        ),
        Expanded(
          child: _buildModeButton(
            context,
            true,
            'Annuel',
            Icons.date_range,
            'Calcul basé sur le barème annuel',
            isWide,
          ),
        ),
      ],
    );
  }

  Widget _buildModeButton(
    BuildContext context,
    bool modeValue,
    String label,
    IconData icon,
    String tooltip,
    bool isWide,
  ) {
    final isSelected = isAnnualMode == modeValue;
    
    return Tooltip(
      message: enabled ? tooltip : 'Calcul en cours...',
      child: InkWell(
        onTap: enabled ? () => onModeChanged(modeValue) : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          constraints: BoxConstraints(
            minHeight: 48, // Minimum touch target
          ),
          padding: EdgeInsets.symmetric(
            vertical: 12,
            horizontal: isWide ? 16 : 8,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? (enabled 
                    ? Theme.of(context).primaryColor 
                    : Colors.grey.shade400)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected
                    ? Colors.white
                    : (enabled 
                        ? Theme.of(context).primaryColor 
                        : Colors.grey.shade500),
                size: isWide ? 24 : 20,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected
                      ? Colors.white
                      : (enabled 
                          ? Theme.of(context).primaryColor 
                          : Colors.grey.shade500),
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                  fontSize: isWide ? 14 : 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModeDescription(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isAnnualMode 
            ? Colors.orange.shade50 
            : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isAnnualMode 
              ? Colors.orange.shade200 
              : Colors.blue.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isAnnualMode ? Icons.info_outline : Icons.info_outline,
                size: 16,
                color: isAnnualMode 
                    ? Colors.orange.shade600 
                    : Colors.blue.shade600,
              ),
              const SizedBox(width: 6),
              Text(
                isAnnualMode ? 'Mode annuel sélectionné' : 'Mode mensuel sélectionné',
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: isAnnualMode 
                      ? Colors.orange.shade700 
                      : Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            isAnnualMode
                ? '• Utilise le barème IR annuel 2025\n'
                  '• Adapté pour les calculs annuels\n'
                  '• Tranches: 0-40K, 40-60K, 60-80K, 80-100K, 100-180K, >180K'
                : '• Utilise le barème IR mensuel 2025\n'
                  '• Adapté pour les salaires mensuels\n'
                  '• Tranches: 0-3.3K, 3.3-5K, 5-6.7K, 6.7-8.3K, 8.3-15K, >15K',
            style: TextStyle(
              fontSize: 12,
              color: isAnnualMode 
                  ? Colors.orange.shade700 
                  : Colors.blue.shade700,
              height: 1.3,
            ),
          ),
          if (!enabled) ...[
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Le mode ne peut pas être changé pendant le calcul',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}