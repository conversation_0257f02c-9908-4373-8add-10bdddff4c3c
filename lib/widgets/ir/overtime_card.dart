import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OvertimeCard extends StatelessWidget {
  final TextEditingController regularController;
  final TextEditingController holidayController;
  final TextEditingController nightController;
  final bool isExpanded;
  final Function(bool) onExpandedChanged;
  final VoidCallback? onFieldChanged;
  final double? baseSalary;

  const OvertimeCard({
    super.key,
    required this.regularController,
    required this.holidayController,
    required this.nightController,
    required this.isExpanded,
    required this.onExpandedChanged,
    this.onFieldChanged,
    this.baseSalary,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          _buildHeader(context),
          AnimatedCrossFade(
            firstChild: const SizedBox.shrink(),
            secondChild: _buildContent(context),
            crossFadeState: isExpanded 
                ? CrossFadeState.showSecond 
                : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return InkWell(
      onTap: () => onExpandedChanged(!isExpanded),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.indigo.shade600,
                    Colors.indigo.shade400,
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.schedule,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Heures supplémentaires',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Jour (25%), Férié/Nuit (50%)',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            if (_hasOvertimeData())
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.indigo.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_calculateTotalAmount().toStringAsFixed(0)} DH',
                  style: TextStyle(
                    color: Colors.indigo.shade700,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            SizedBox(
              height: 48, // Minimum touch target
              width: 48,
              child: Icon(
                isExpanded ? Icons.expand_less : Icons.expand_more,
                size: 24,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        children: [
          const Divider(),
          const SizedBox(height: 16),
          LayoutBuilder(
            builder: (context, constraints) {
              final isWide = constraints.maxWidth > 900;
              
              if (isWide) {
                return _buildThreeColumnLayout();
              } else {
                return _buildStackedLayout();
              }
            },
          ),
          if (baseSalary != null && baseSalary! > 0) ...[
            const SizedBox(height: 16),
            _buildCalculationInfo(context),
          ],
        ],
      ),
    );
  }

  Widget _buildThreeColumnLayout() {
    return Row(
      children: [
        Expanded(child: _buildRegularHoursField()),
        const SizedBox(width: 16),
        Expanded(child: _buildHolidayHoursField()),
        const SizedBox(width: 16),
        Expanded(child: _buildNightHoursField()),
      ],
    );
  }

  Widget _buildStackedLayout() {
    return Column(
      children: [
        _buildRegularHoursField(),
        const SizedBox(height: 12),
        _buildHolidayHoursField(),
        const SizedBox(height: 12),
        _buildNightHoursField(),
      ],
    );
  }

  Widget _buildRegularHoursField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Heures normales (+25%)',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: TextFormField(
            controller: regularController,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              LengthLimitingTextInputFormatter(6),
            ],
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: '0.00',
              suffixText: 'h',
              prefixIcon: Icon(Icons.wb_sunny, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: (_) => onFieldChanged?.call(),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final hours = double.tryParse(value);
                if (hours == null || hours < 0 || hours > 999) {
                  return 'Invalide (0-999)';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHolidayHoursField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Jours fériés (+50%)',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: TextFormField(
            controller: holidayController,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              LengthLimitingTextInputFormatter(6),
            ],
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: '0.00',
              suffixText: 'h',
              prefixIcon: Icon(Icons.celebration, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: (_) => onFieldChanged?.call(),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final hours = double.tryParse(value);
                if (hours == null || hours < 0 || hours > 999) {
                  return 'Invalide (0-999)';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNightHoursField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Heures de nuit (+50%)',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: TextFormField(
            controller: nightController,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              LengthLimitingTextInputFormatter(6),
            ],
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: '0.00',
              suffixText: 'h',
              prefixIcon: Icon(Icons.nights_stay, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: (_) => onFieldChanged?.call(),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final hours = double.tryParse(value);
                if (hours == null || hours < 0 || hours > 999) {
                  return 'Invalide (0-999)';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCalculationInfo(BuildContext context) {
    final hourlyRate = baseSalary! / 191;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.indigo.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.indigo.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Calcul automatique',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.indigo.shade700,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Taux horaire: ${hourlyRate.toStringAsFixed(2)} DH/h',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 4),
          if (_getRegularHours() > 0)
            Text(
              'Jour: ${_getRegularHours()}h × ${(hourlyRate * 1.25).toStringAsFixed(2)} = ${_calculateRegularAmount().toStringAsFixed(2)} DH',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
          if (_getHolidayHours() > 0)
            Text(
              'Férié: ${_getHolidayHours()}h × ${(hourlyRate * 1.50).toStringAsFixed(2)} = ${_calculateHolidayAmount().toStringAsFixed(2)} DH',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
          if (_getNightHours() > 0)
            Text(
              'Nuit: ${_getNightHours()}h × ${(hourlyRate * 1.50).toStringAsFixed(2)} = ${_calculateNightAmount().toStringAsFixed(2)} DH',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
          if (_calculateTotalAmount() > 0) ...[
            const Divider(height: 16),
            Text(
              'Total: ${_calculateTotalAmount().toStringAsFixed(2)} DH',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.indigo.shade700,
                fontSize: 13,
              ),
            ),
          ],
        ],
      ),
    );
  }

  bool _hasOvertimeData() {
    return _getRegularHours() > 0 || _getHolidayHours() > 0 || _getNightHours() > 0;
  }

  double _getRegularHours() {
    return double.tryParse(regularController.text) ?? 0;
  }

  double _getHolidayHours() {
    return double.tryParse(holidayController.text) ?? 0;
  }

  double _getNightHours() {
    return double.tryParse(nightController.text) ?? 0;
  }

  double _calculateRegularAmount() {
    if (baseSalary == null || baseSalary! <= 0) return 0;
    final hourlyRate = baseSalary! / 191;
    return _getRegularHours() * hourlyRate * 1.25;
  }

  double _calculateHolidayAmount() {
    if (baseSalary == null || baseSalary! <= 0) return 0;
    final hourlyRate = baseSalary! / 191;
    return _getHolidayHours() * hourlyRate * 1.50;
  }

  double _calculateNightAmount() {
    if (baseSalary == null || baseSalary! <= 0) return 0;
    final hourlyRate = baseSalary! / 191;
    return _getNightHours() * hourlyRate * 1.50;
  }

  double _calculateTotalAmount() {
    return _calculateRegularAmount() + _calculateHolidayAmount() + _calculateNightAmount();
  }
}