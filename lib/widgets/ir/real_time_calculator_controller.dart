import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../models/salary/salary_data.dart';
import '../../models/salary/salary_result.dart';
import '../../services/salary_calculator_service.dart';

class RealTimeCalculatorController {
  static const int _debounceDelayMs = 300;
  
  final SalaryCalculatorService _calculatorService = SalaryCalculatorService();
  Timer? _debounceTimer;
  
  final ValueNotifier<SalaryResult?> _resultNotifier = ValueNotifier(null);
  final ValueNotifier<bool> _isCalculatingNotifier = ValueNotifier(false);
  final ValueNotifier<String?> _errorNotifier = ValueNotifier(null);
  
  bool _useAnnualMode = false;

  ValueNotifier<SalaryResult?> get resultNotifier => _resultNotifier;
  ValueNotifier<bool> get isCalculatingNotifier => _isCalculatingNotifier;
  ValueNotifier<String?> get errorNotifier => _errorNotifier;

  bool get useAnnualMode => _useAnnualMode;
  
  set useAnnualMode(bool value) {
    if (_useAnnualMode != value) {
      _useAnnualMode = value;
      // Recalculate with new mode if we have a valid result
      if (_resultNotifier.value != null && _lastValidData != null) {
        _performCalculation(_lastValidData!);
      }
    }
  }

  SalaryData? _lastValidData;

  void triggerCalculation(SalaryData data) {
    // Cancel any existing timer
    _debounceTimer?.cancel();
    
    // Clear previous errors
    _errorNotifier.value = null;
    
    // Validate required fields
    if (!_isDataValid(data)) {
      clearResult();
      return;
    }
    
    // Store valid data for potential recalculation
    _lastValidData = data;
    
    // Start debounce timer
    _debounceTimer = Timer(const Duration(milliseconds: _debounceDelayMs), () {
      _performCalculation(data);
    });
  }

  bool _isDataValid(SalaryData data) {
    // Check if base salary is provided and valid
    if (data.baseSalary <= 0) {
      return false;
    }
    
    // Additional validation can be added here
    // For now, we only require a positive base salary
    return true;
  }

  Future<void> _performCalculation(SalaryData data) async {
    _isCalculatingNotifier.value = true;
    _errorNotifier.value = null;
    
    try {
      final result = await _calculatorService.calculate(
        data, 
        useAnnualMode: _useAnnualMode,
      );
      _resultNotifier.value = result;
    } catch (e) {
      _errorNotifier.value = 'Erreur de calcul: ${e.toString()}';
      _resultNotifier.value = null;
    } finally {
      _isCalculatingNotifier.value = false;
    }
  }

  void clearResult() {
    _debounceTimer?.cancel();
    _resultNotifier.value = null;
    _errorNotifier.value = null;
    _isCalculatingNotifier.value = false;
    _lastValidData = null;
  }

  void dispose() {
    _debounceTimer?.cancel();
    _resultNotifier.dispose();
    _isCalculatingNotifier.dispose();
    _errorNotifier.dispose();
  }
}