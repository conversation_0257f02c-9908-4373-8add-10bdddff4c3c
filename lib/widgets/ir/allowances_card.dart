import 'package:flutter/material.dart';
import '../../utils/input_validators.dart';

class AllowancesCard extends StatelessWidget {
  final TextEditingController transportController;
  final TextEditingController housingController;
  final TextEditingController otherController;
  final VoidCallback? onFieldChanged;
  final bool isVisible;

  const AllowancesCard({
    super.key,
    required this.transportController,
    required this.housingController,
    required this.otherController,
    this.onFieldChanged,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.orange.shade600,
                    Colors.orange.shade400,
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.card_giftcard,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Indemnités',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            LayoutBuilder(
              builder: (context, constraints) {
                final isWide = constraints.maxWidth > 600;
                final isMedium = constraints.maxWidth > 900;
                
                if (isMedium) {
                  return _buildThreeColumnLayout();
                } else if (isWide) {
                  return _buildTwoColumnLayout();
                } else {
                  return _buildSingleColumnLayout();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThreeColumnLayout() {
    return Row(
      children: [
        Expanded(child: _buildTransportField()),
        const SizedBox(width: 16),
        Expanded(child: _buildHousingField()),
        const SizedBox(width: 16),
        Expanded(child: _buildOtherField()),
      ],
    );
  }

  Widget _buildTwoColumnLayout() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildTransportField()),
            const SizedBox(width: 16),
            Expanded(child: _buildHousingField()),
          ],
        ),
        const SizedBox(height: 16),
        _buildOtherField(),
      ],
    );
  }

  Widget _buildSingleColumnLayout() {
    return Column(
      children: [
        _buildTransportField(),
        const SizedBox(height: 12),
        _buildHousingField(),
        const SizedBox(height: 12),
        _buildOtherField(),
      ],
    );
  }

  Widget _buildTransportField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Indemnité de transport (DH)',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: TextFormField(
            controller: transportController,
            keyboardType: TextInputType.number,
            inputFormatters: InputValidators.getMonetaryInputFormatters(),
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: '0.00',
              prefixIcon: Icon(Icons.directions_car, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: (_) => onFieldChanged?.call(),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final amount = double.tryParse(value.replaceAll(' ', ''));
                if (amount == null || amount < 0) {
                  return 'Montant invalide';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHousingField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Indemnité de logement (DH)',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: TextFormField(
            controller: housingController,
            keyboardType: TextInputType.number,
            inputFormatters: InputValidators.getMonetaryInputFormatters(),
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: '0.00',
              prefixIcon: Icon(Icons.home, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: (_) => onFieldChanged?.call(),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final amount = double.tryParse(value.replaceAll(' ', ''));
                if (amount == null || amount < 0) {
                  return 'Montant invalide';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOtherField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Autres indemnités (DH)',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: TextFormField(
            controller: otherController,
            keyboardType: TextInputType.number,
            inputFormatters: InputValidators.getMonetaryInputFormatters(),
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: '0.00',
              prefixIcon: Icon(Icons.attach_money, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: (_) => onFieldChanged?.call(),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final amount = double.tryParse(value.replaceAll(' ', ''));
                if (amount == null || amount < 0) {
                  return 'Montant invalide';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }
}