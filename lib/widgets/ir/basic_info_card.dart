import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/input_validators.dart';

class BasicInfoCard extends StatelessWidget {
  final TextEditingController baseSalaryController;
  final TextEditingController yearsController;
  final String selectedEmployeeType;
  final bool includeCNSS;
  final Function(String) onEmployeeTypeChanged;
  final Function(bool) onCNSSChanged;
  final VoidCallback? onFieldChanged;

  const BasicInfoCard({
    super.key,
    required this.baseSalaryController,
    required this.yearsController,
    required this.selectedEmployeeType,
    required this.includeCNSS,
    required this.onEmployeeTypeChanged,
    required this.onCNSSChanged,
    this.onFieldChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Informations de base',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            LayoutBuilder(
              builder: (context, constraints) {
                final isWide = constraints.maxWidth > 600;
                
                if (isWide) {
                  return _buildWideLayout();
                } else {
                  return _buildNarrowLayout();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWideLayout() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildEmployeeTypeDropdown(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildBaseSalaryField(),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildYearsField(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildCNSSToggle(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNarrowLayout() {
    return Column(
      children: [
        _buildEmployeeTypeDropdown(),
        const SizedBox(height: 12),
        _buildBaseSalaryField(),
        const SizedBox(height: 12),
        _buildYearsField(),
        const SizedBox(height: 12),
        _buildCNSSToggle(),
      ],
    );
  }

  Widget _buildEmployeeTypeDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Type d\'employé',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: DropdownButtonFormField<String>(
            value: selectedEmployeeType,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            items: const [
              DropdownMenuItem(
                value: 'cadre',
                child: Text('Cadre'),
              ),
              DropdownMenuItem(
                value: 'employe',
                child: Text('Employé'),
              ),
              DropdownMenuItem(
                value: 'ouvrier',
                child: Text('Ouvrier'),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                onEmployeeTypeChanged(value);
                onFieldChanged?.call();
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBaseSalaryField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Salaire de base (DH)',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: TextFormField(
            controller: baseSalaryController,
            keyboardType: TextInputType.number,
            inputFormatters: InputValidators.getMonetaryInputFormatters(),
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: '0.00',
              prefixIcon: Icon(Icons.monetization_on, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: (_) => onFieldChanged?.call(),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Requis';
              }
              final amount = double.tryParse(value.replaceAll(' ', ''));
              if (amount == null || amount <= 0) {
                return 'Montant invalide';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildYearsField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Années de service',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // Minimum touch target
          child: TextFormField(
            controller: yearsController,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(2),
            ],
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: '0',
              prefixIcon: Icon(Icons.timeline, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: (_) => onFieldChanged?.call(),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final years = int.tryParse(value);
                if (years == null || years < 0 || years > 50) {
                  return 'Invalide (0-50)';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCNSSToggle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Options',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 48, // Minimum touch target
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.grey.shade400,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.security, size: 20, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Inclure CNSS',
                  style: TextStyle(fontSize: 14),
                ),
              ),
              Transform.scale(
                scale: 1.2, // Make switch larger for better touch target
                child: Switch(
                  value: includeCNSS,
                  onChanged: (value) {
                    onCNSSChanged(value);
                    onFieldChanged?.call();
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}