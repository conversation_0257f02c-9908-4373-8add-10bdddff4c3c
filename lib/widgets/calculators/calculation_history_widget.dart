import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/calculators/calculation_history_item.dart';
import '../../services/calculation_history_service.dart';
import '../../theme/design_tokens.dart';

class CalculationHistoryWidget extends ConsumerStatefulWidget {
  const CalculationHistoryWidget({super.key});

  @override
  ConsumerState<CalculationHistoryWidget> createState() => _CalculationHistoryWidgetState();
}

class _CalculationHistoryWidgetState extends ConsumerState<CalculationHistoryWidget> {
  final _searchController = TextEditingController();
  final _historyService = CalculationHistoryService();
  
  List<CalculationHistoryItem> _allItems = [];
  List<CalculationHistoryItem> _filteredItems = [];
  List<CalculationHistoryItem> _selectedItems = [];
  
  CalculatorType? _selectedType;
  bool _showFavoritesOnly = false;
  bool _isGridView = false;
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _loadHistory();
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Historique des Calculs'),
        backgroundColor: DesignTokens.colorPrimary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          if (_selectedItems.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteSelectedItems,
            ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          if (_isLoading)
            const Expanded(
              child: Center(child: CircularProgressIndicator()),
            )
          else if (_filteredItems.isEmpty)
            _buildEmptyState()
          else
            Expanded(
              child: _isGridView ? _buildGridView() : _buildListView(),
            ),
        ],
      ),
      floatingActionButton: _selectedItems.isNotEmpty 
          ? _buildBulkActionsButton()
          : null,
    );
  }
  
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Rechercher dans l\'historique...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterItems();
                      },
                    )
                  : null,
              border: const OutlineInputBorder(),
            ),
            onChanged: (value) => _filterItems(),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<CalculatorType?>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'Type de calculateur',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    const DropdownMenuItem<CalculatorType?>(
                      value: null,
                      child: Text('Tous les types'),
                    ),
                    ...CalculatorType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.displayName),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value;
                    });
                    _filterItems();
                  },
                ),
              ),
              const SizedBox(width: 12),
              FilterChip(
                label: const Text('Favoris'),
                selected: _showFavoritesOnly,
                onSelected: (selected) {
                  setState(() {
                    _showFavoritesOnly = selected;
                  });
                  _filterItems();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildEmptyState() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _allItems.isEmpty 
                  ? 'Aucun calcul dans l\'historique'
                  : 'Aucun résultat pour ces filtres',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _allItems.isEmpty
                  ? 'Vos calculs apparaîtront ici automatiquement'
                  : 'Essayez de modifier vos critères de recherche',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        final isSelected = _selectedItems.contains(item);
        
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: _buildCalculatorIcon(item.calculatorType),
            title: Text(
              item.displayName,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(_formatDate(item.createdAt)),
                if (item.tags.isNotEmpty)
                  Wrap(
                    spacing: 4,
                    children: item.tags.take(3).map((tag) => 
                      Chip(
                        label: Text(tag),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: VisualDensity.compact,
                      ),
                    ).toList(),
                  ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (item.isFavorite)
                  const Icon(Icons.star, color: Colors.amber),
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: () => _showItemMenu(context, item),
                ),
              ],
            ),
            selected: isSelected,
            onTap: () => _toggleSelection(item),
            onLongPress: () => _toggleSelection(item),
          ),
        );
      },
    );
  }
  
  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        final isSelected = _selectedItems.contains(item);
        
        return Card(
          child: InkWell(
            onTap: () => _toggleSelection(item),
            onLongPress: () => _toggleSelection(item),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: isSelected 
                    ? Border.all(color: DesignTokens.colorPrimary, width: 2)
                    : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildCalculatorIcon(item.calculatorType),
                      const Spacer(),
                      if (item.isFavorite)
                        const Icon(Icons.star, color: Colors.amber, size: 20),
                      IconButton(
                        icon: const Icon(Icons.more_vert, size: 20),
                        onPressed: () => _showItemMenu(context, item),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    item.displayName,
                    style: Theme.of(context).textTheme.titleSmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatDate(item.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  if (item.tags.isNotEmpty)
                    Wrap(
                      spacing: 2,
                      children: item.tags.take(2).map((tag) => 
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            tag,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ),
                      ).toList(),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildCalculatorIcon(CalculatorType type) {
    IconData iconData;
    Color color;
    
    switch (type) {
      case CalculatorType.salary:
        iconData = Icons.account_balance_wallet;
        color = Colors.green;
        break;
      case CalculatorType.depreciation:
        iconData = Icons.trending_down;
        color = Colors.orange;
        break;
      case CalculatorType.financialRatios:
        iconData = Icons.analytics;
        color = Colors.blue;
        break;
      case CalculatorType.taxOptimization:
        iconData = Icons.lightbulb;
        color = Colors.purple;
        break;
      case CalculatorType.termination:
        iconData = Icons.work_off;
        color = Colors.red;
        break;
      case CalculatorType.corporateTax:
        iconData = Icons.business;
        color = Colors.indigo;
        break;
      case CalculatorType.tva:
        iconData = Icons.receipt;
        color = Colors.teal;
        break;
    }
    
    return CircleAvatar(
      backgroundColor: color.withOpacity(0.1),
      child: Icon(iconData, color: color, size: 20),
    );
  }
  
  Widget _buildBulkActionsButton() {
    return FloatingActionButton.extended(
      onPressed: _showBulkActionsMenu,
      backgroundColor: DesignTokens.colorPrimary,
      icon: const Icon(Icons.more_horiz),
      label: Text('${_selectedItems.length} sélectionné(s)'),
    );
  }
  
  void _loadHistory() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final items = await _historyService.getHistory();
      setState(() {
        _allItems = items;
        _isLoading = false;
      });
      _filterItems();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  void _filterItems() {
    setState(() {
      _filteredItems = _allItems.where((item) {
        // Filter by search query
        if (_searchController.text.isNotEmpty && 
            !item.matchesQuery(_searchController.text)) {
          return false;
        }
        
        // Filter by calculator type
        if (_selectedType != null && item.calculatorType != _selectedType) {
          return false;
        }
        
        // Filter by favorites
        if (_showFavoritesOnly && !item.isFavorite) {
          return false;
        }
        
        return true;
      }).toList();
    });
  }
  
  void _toggleSelection(CalculationHistoryItem item) {
    setState(() {
      if (_selectedItems.contains(item)) {
        _selectedItems.remove(item);
      } else {
        _selectedItems.add(item);
      }
    });
  }
  
  void _showItemMenu(BuildContext context, CalculationHistoryItem item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(item.isFavorite ? Icons.star_border : Icons.star),
            title: Text(item.isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'),
            onTap: () {
              Navigator.pop(context);
              _toggleFavorite(item);
            },
          ),
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('Modifier'),
            onTap: () {
              Navigator.pop(context);
              _editItem(item);
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Partager'),
            onTap: () {
              Navigator.pop(context);
              _shareItem(item);
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Supprimer', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              _deleteItem(item);
            },
          ),
        ],
      ),
    );
  }
  
  void _showBulkActionsMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.star),
            title: const Text('Ajouter aux favoris'),
            onTap: () {
              Navigator.pop(context);
              _bulkToggleFavorites(true);
            },
          ),
          ListTile(
            leading: const Icon(Icons.star_border),
            title: const Text('Retirer des favoris'),
            onTap: () {
              Navigator.pop(context);
              _bulkToggleFavorites(false);
            },
          ),
          ListTile(
            leading: const Icon(Icons.file_download),
            title: const Text('Exporter'),
            onTap: () {
              Navigator.pop(context);
              _exportSelectedItems();
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Supprimer', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              _deleteSelectedItems();
            },
          ),
        ],
      ),
    );
  }
  
  void _toggleFavorite(CalculationHistoryItem item) async {
    try {
      await _historyService.toggleFavorite(item.id);
      _loadHistory();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  void _editItem(CalculationHistoryItem item) {
    // TODO: Implement edit functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fonctionnalité à venir')),
    );
  }
  
  void _shareItem(CalculationHistoryItem item) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fonctionnalité à venir')),
    );
  }
  
  void _deleteItem(CalculationHistoryItem item) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text('Voulez-vous vraiment supprimer "${item.displayName}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Supprimer', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        await _historyService.deleteCalculation(item.id);
        _loadHistory();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la suppression: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
  
  void _bulkToggleFavorites(bool favorite) async {
    try {
      for (final item in _selectedItems) {
        if (item.isFavorite != favorite) {
          await _historyService.toggleFavorite(item.id);
        }
      }
      setState(() {
        _selectedItems.clear();
      });
      _loadHistory();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  void _exportSelectedItems() async {
    try {
      final ids = _selectedItems.map((item) => item.id).toList();
      await _historyService.exportHistory(ids, ExportFormat.excel);
      setState(() {
        _selectedItems.clear();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'export: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  void _deleteSelectedItems() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text('Voulez-vous vraiment supprimer ${_selectedItems.length} élément(s) ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Supprimer', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        for (final item in _selectedItems) {
          await _historyService.deleteCalculation(item.id);
        }
        setState(() {
          _selectedItems.clear();
        });
        _loadHistory();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la suppression: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} à ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
