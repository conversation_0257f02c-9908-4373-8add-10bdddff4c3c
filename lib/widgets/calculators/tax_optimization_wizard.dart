import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/calculators/tax_optimization_data.dart';
import '../../services/tax_optimization_service.dart';
import '../../theme/design_tokens.dart';
import '../custom_text_field.dart';

class TaxOptimizationWizard extends ConsumerStatefulWidget {
  const TaxOptimizationWizard({super.key});

  @override
  ConsumerState<TaxOptimizationWizard> createState() => _TaxOptimizationWizardState();
}

class _TaxOptimizationWizardState extends ConsumerState<TaxOptimizationWizard> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 5;
  
  // Form keys for each step
  final List<GlobalKey<FormState>> _formKeys = List.generate(5, (index) => GlobalKey<FormState>());
  
  // Step 1: Company Profile
  final _revenueController = TextEditingController();
  final _employeeCountController = TextEditingController();
  final _yearsInBusinessController = TextEditingController();
  BusinessSector _selectedSector = BusinessSector.services;
  LegalForm _selectedLegalForm = LegalForm.sarl;
  bool _isExporter = false;
  bool _hasRnD = false;
  String _selectedRegion = 'Casablanca';
  
  // Step 2: Current Tax Situation
  final _accountingResultController = TextEditingController();
  final _currentTaxableIncomeController = TextEditingController();
  final _currentISTaxController = TextEditingController();
  final _provisionsAmountController = TextEditingController();
  final _depreciationAmountController = TextEditingController();
  TaxRegime _currentRegime = TaxRegime.normal;
  List<String> _existingDeductions = [];
  
  // Step 3: Optimization Goals
  List<OptimizationObjective> _selectedObjectives = [];
  final _timeHorizonController = TextEditingController(text: '3');
  final _targetSavingsController = TextEditingController();
  bool _prioritizeCashFlow = true;
  bool _prioritizeCompliance = true;
  
  // Step 4: Constraints
  final _maxBudgetController = TextEditingController();
  RiskTolerance _riskTolerance = RiskTolerance.moderate;
  bool _requiresAuditCompliance = false;
  List<String> _excludedStrategies = [];
  
  // Results
  TaxOptimizationResult? _result;
  bool _isCalculating = false;
  
  @override
  void dispose() {
    _pageController.dispose();
    _disposeControllers();
    super.dispose();
  }
  
  void _disposeControllers() {
    _revenueController.dispose();
    _employeeCountController.dispose();
    _yearsInBusinessController.dispose();
    _accountingResultController.dispose();
    _currentTaxableIncomeController.dispose();
    _currentISTaxController.dispose();
    _provisionsAmountController.dispose();
    _depreciationAmountController.dispose();
    _timeHorizonController.dispose();
    _targetSavingsController.dispose();
    _maxBudgetController.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Assistant d\'Optimisation Fiscale'),
        backgroundColor: DesignTokens.colorPrimary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildCompanyProfileStep(),
                _buildCurrentTaxSituationStep(),
                _buildOptimizationGoalsStep(),
                _buildConstraintsStep(),
                _buildResultsStep(),
              ],
            ),
          ),
          _buildNavigationButtons(),
        ],
      ),
    );
  }
  
  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: List.generate(_totalSteps, (index) {
              final isCompleted = index < _currentStep;
              final isCurrent = index == _currentStep;
              
              return Expanded(
                child: Container(
                  height: 4,
                  margin: EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
                  decoration: BoxDecoration(
                    color: isCompleted || isCurrent 
                        ? DesignTokens.colorPrimary 
                        : Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 8),
          Text(
            'Étape ${_currentStep + 1} sur $_totalSteps',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: DesignTokens.colorOnSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCompanyProfileStep() {
    return Form(
      key: _formKeys[0],
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Profil de l\'Entreprise',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: DesignTokens.colorPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _revenueController,
              label: 'Chiffre d\'affaires annuel (DH)',
              keyboardType: TextInputType.number,
              validator: _validatePositiveNumber,
            ),
            const SizedBox(height: 16),
            
            DropdownButtonFormField<BusinessSector>(
              value: _selectedSector,
              decoration: const InputDecoration(
                labelText: 'Secteur d\'activité',
                border: OutlineInputBorder(),
              ),
              items: BusinessSector.values.map((sector) {
                return DropdownMenuItem(
                  value: sector,
                  child: Text(sector.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSector = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            
            DropdownButtonFormField<LegalForm>(
              value: _selectedLegalForm,
              decoration: const InputDecoration(
                labelText: 'Forme juridique',
                border: OutlineInputBorder(),
              ),
              items: LegalForm.values.map((form) {
                return DropdownMenuItem(
                  value: form,
                  child: Text(form.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLegalForm = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _employeeCountController,
              label: 'Nombre d\'employés',
              keyboardType: TextInputType.number,
              validator: _validatePositiveInteger,
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _yearsInBusinessController,
              label: 'Années d\'activité',
              keyboardType: TextInputType.number,
              validator: _validatePositiveInteger,
            ),
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text('Entreprise exportatrice'),
              value: _isExporter,
              onChanged: (value) {
                setState(() {
                  _isExporter = value;
                });
              },
            ),
            
            SwitchListTile(
              title: const Text('Activités de R&D'),
              value: _hasRnD,
              onChanged: (value) {
                setState(() {
                  _hasRnD = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCurrentTaxSituationStep() {
    return Form(
      key: _formKeys[1],
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Situation Fiscale Actuelle',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: DesignTokens.colorPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _accountingResultController,
              label: 'Résultat comptable (DH)',
              keyboardType: TextInputType.number,
              validator: _validateNumber,
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _currentTaxableIncomeController,
              label: 'Base imposable actuelle (DH)',
              keyboardType: TextInputType.number,
              validator: _validateNumber,
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _currentISTaxController,
              label: 'IS payé actuellement (DH)',
              keyboardType: TextInputType.number,
              validator: _validateNumber,
            ),
            const SizedBox(height: 16),
            
            DropdownButtonFormField<TaxRegime>(
              value: _currentRegime,
              decoration: const InputDecoration(
                labelText: 'Régime fiscal actuel',
                border: OutlineInputBorder(),
              ),
              items: TaxRegime.values.map((regime) {
                return DropdownMenuItem(
                  value: regime,
                  child: Text(regime.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _currentRegime = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _provisionsAmountController,
              label: 'Montant des provisions (DH)',
              keyboardType: TextInputType.number,
              validator: _validateNumber,
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _depreciationAmountController,
              label: 'Amortissements annuels (DH)',
              keyboardType: TextInputType.number,
              validator: _validateNumber,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOptimizationGoalsStep() {
    return Form(
      key: _formKeys[2],
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Objectifs d\'Optimisation',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: DesignTokens.colorPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Text(
              'Sélectionnez vos objectifs prioritaires:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            
            ...OptimizationObjective.values.map((objective) {
              return CheckboxListTile(
                title: Text(objective.displayName),
                value: _selectedObjectives.contains(objective),
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      _selectedObjectives.add(objective);
                    } else {
                      _selectedObjectives.remove(objective);
                    }
                  });
                },
              );
            }),
            
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _timeHorizonController,
              label: 'Horizon temporel (années)',
              keyboardType: TextInputType.number,
              validator: _validatePositiveInteger,
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _targetSavingsController,
              label: 'Économies cibles (DH)',
              keyboardType: TextInputType.number,
              validator: _validatePositiveNumber,
            ),
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text('Prioriser la trésorerie'),
              subtitle: const Text('Optimiser les flux de trésorerie'),
              value: _prioritizeCashFlow,
              onChanged: (value) {
                setState(() {
                  _prioritizeCashFlow = value;
                });
              },
            ),
            
            SwitchListTile(
              title: const Text('Prioriser la conformité'),
              subtitle: const Text('Minimiser les risques de contrôle'),
              value: _prioritizeCompliance,
              onChanged: (value) {
                setState(() {
                  _prioritizeCompliance = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildConstraintsStep() {
    return Form(
      key: _formKeys[3],
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contraintes et Préférences',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: DesignTokens.colorPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _maxBudgetController,
              label: 'Budget maximum pour l\'implémentation (DH)',
              keyboardType: TextInputType.number,
              validator: _validatePositiveNumber,
            ),
            const SizedBox(height: 16),
            
            Text(
              'Tolérance au risque:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            
            ...RiskTolerance.values.map((tolerance) {
              return RadioListTile<RiskTolerance>(
                title: Text(tolerance.displayName),
                value: tolerance,
                groupValue: _riskTolerance,
                onChanged: (value) {
                  setState(() {
                    _riskTolerance = value!;
                  });
                },
              );
            }),
            
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text('Conformité audit requise'),
              subtitle: const Text('Stratégies compatibles avec les audits'),
              value: _requiresAuditCompliance,
              onChanged: (value) {
                setState(() {
                  _requiresAuditCompliance = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildResultsStep() {
    if (_result == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Complétez toutes les étapes\npour voir les recommandations',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recommandations d\'Optimisation',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.colorPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildCurrentAnalysisCard(),
          const SizedBox(height: 16),
          
          _buildStrategiesCard(),
          const SizedBox(height: 16),
          
          _buildScenariosCard(),
          const SizedBox(height: 16),
          
          _buildProjectedSavingsCard(),
        ],
      ),
    );
  }
  
  Widget _buildCurrentAnalysisCard() {
    final analysis = _result!.currentAnalysis;
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Analyse de la Situation Actuelle',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            
            _buildAnalysisItem('Taux effectif d\'imposition', '${(analysis.effectiveTaxRate * 100).toStringAsFixed(1)}%'),
            _buildAnalysisItem('Taux marginal', '${(analysis.marginalTaxRate * 100).toStringAsFixed(1)}%'),
            
            if (analysis.identifiedIssues.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Problèmes identifiés:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red[700],
                ),
              ),
              ...analysis.identifiedIssues.map((issue) => 
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4),
                  child: Text('• $issue'),
                ),
              ),
            ],
            
            if (analysis.missedOpportunities.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Opportunités manquées:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[700],
                ),
              ),
              ...analysis.missedOpportunities.map((opportunity) => 
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4),
                  child: Text('• $opportunity'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildStrategiesCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Stratégies Recommandées',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            
            ..._result!.recommendedStrategies.take(3).map((strategy) => 
              _buildStrategyItem(strategy),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStrategyItem(OptimizationStrategy strategy) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            strategy.name,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            strategy.description,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Économie: ${strategy.estimatedSavings.toStringAsFixed(0)} DH',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.green[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Risque: ${strategy.riskLevel.displayName}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _getRiskColor(strategy.riskLevel),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildScenariosCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Scénarios d\'Optimisation',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            
            ..._result!.scenarios.map((scenario) => 
              _buildScenarioItem(scenario),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildScenarioItem(OptimizationScenario scenario) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            scenario.name,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Économies: ${scenario.totalSavings.toStringAsFixed(0)} DH'),
              Text('Coût: ${scenario.totalCost.toStringAsFixed(0)} DH'),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Bénéfice net: ${scenario.netBenefit.toStringAsFixed(0)} DH',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: scenario.netBenefit > 0 ? Colors.green[700] : Colors.red[700],
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildProjectedSavingsCard() {
    final savings = _result!.projectedSavings;
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Projections Financières',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            
            _buildAnalysisItem('Économies totales', '${savings.totalSavings.toStringAsFixed(0)} DH'),
            _buildAnalysisItem('Valeur actuelle nette', '${savings.netPresentValue.toStringAsFixed(0)} DH'),
            _buildAnalysisItem('Retour sur investissement', '${(savings.internalRateOfReturn * 100).toStringAsFixed(1)}%'),
            _buildAnalysisItem('Période de retour', '${savings.paybackPeriodMonths} mois'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAnalysisItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: DesignTokens.colorPrimary,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (_currentStep > 0)
            ElevatedButton(
              onPressed: _previousStep,
              child: const Text('Précédent'),
            )
          else
            const SizedBox.shrink(),
          
          if (_currentStep < _totalSteps - 1)
            ElevatedButton(
              onPressed: _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: DesignTokens.colorPrimary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Suivant'),
            )
          else if (_result == null)
            ElevatedButton(
              onPressed: _isCalculating ? null : _generateRecommendations,
              style: ElevatedButton.styleFrom(
                backgroundColor: DesignTokens.colorPrimary,
                foregroundColor: Colors.white,
              ),
              child: _isCalculating 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Générer les Recommandations'),
            ),
        ],
      ),
    );
  }
  
  Color _getRiskColor(RiskLevel riskLevel) {
    switch (riskLevel) {
      case RiskLevel.low:
        return Colors.green;
      case RiskLevel.medium:
        return Colors.orange;
      case RiskLevel.high:
        return Colors.red;
      case RiskLevel.critical:
        return Colors.red[900]!;
    }
  }
  
  String? _validateNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Ce champ est requis';
    }
    if (double.tryParse(value) == null) {
      return 'Veuillez saisir un nombre valide';
    }
    return null;
  }
  
  String? _validatePositiveNumber(String? value) {
    final error = _validateNumber(value);
    if (error != null) return error;
    
    final number = double.parse(value!);
    if (number <= 0) {
      return 'La valeur doit être positive';
    }
    return null;
  }
  
  String? _validatePositiveInteger(String? value) {
    final error = _validateNumber(value);
    if (error != null) return error;
    
    final number = int.tryParse(value!);
    if (number == null || number <= 0) {
      return 'Veuillez saisir un nombre entier positif';
    }
    return null;
  }
  
  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
  
  void _nextStep() {
    if (_formKeys[_currentStep].currentState?.validate() == true) {
      if (_currentStep < _totalSteps - 1) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }
  
  void _generateRecommendations() async {
    // Validate all previous steps
    bool allValid = true;
    for (int i = 0; i < _totalSteps - 1; i++) {
      if (_formKeys[i].currentState?.validate() != true) {
        allValid = false;
        break;
      }
    }
    
    if (!allValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez compléter toutes les étapes'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    if (_selectedObjectives.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner au moins un objectif'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    setState(() {
      _isCalculating = true;
    });
    
    try {
      final input = TaxOptimizationInput(
        companyProfile: CompanyProfile(
          annualRevenue: double.parse(_revenueController.text),
          sector: _selectedSector,
          employeeCount: int.parse(_employeeCountController.text),
          legalForm: _selectedLegalForm,
          yearsInBusiness: int.parse(_yearsInBusinessController.text),
          isExporter: _isExporter,
          hasRnD: _hasRnD,
          region: _selectedRegion,
        ),
        currentTaxSituation: CurrentTaxSituation(
          accountingResult: double.parse(_accountingResultController.text),
          currentTaxableIncome: double.parse(_currentTaxableIncomeController.text),
          currentISTax: double.parse(_currentISTaxController.text),
          existingDeductions: _existingDeductions,
          currentRegime: _currentRegime,
          provisionsAmount: double.parse(_provisionsAmountController.text.isEmpty ? '0' : _provisionsAmountController.text),
          depreciationAmount: double.parse(_depreciationAmountController.text.isEmpty ? '0' : _depreciationAmountController.text),
        ),
        optimizationGoals: OptimizationGoals(
          objectives: _selectedObjectives,
          timeHorizonYears: int.parse(_timeHorizonController.text),
          targetTaxSavings: double.parse(_targetSavingsController.text),
          prioritizeCashFlow: _prioritizeCashFlow,
          prioritizeCompliance: _prioritizeCompliance,
        ),
        constraints: OptimizationConstraints(
          maxBudget: double.parse(_maxBudgetController.text),
          timingPreferences: [],
          riskTolerance: _riskTolerance,
          requiresAuditCompliance: _requiresAuditCompliance,
          excludedStrategies: _excludedStrategies,
        ),
      );
      
      final service = TaxOptimizationService();
      final result = service.analyzeAndOptimize(input);
      
      setState(() {
        _result = result;
        _isCalculating = false;
      });
      
    } catch (e) {
      setState(() {
        _isCalculating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'analyse: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
