import 'package:flutter/material.dart';
import '../../theme/design_tokens.dart';

class GuidedWizardScaffold extends StatefulWidget {
  final String title;
  final List<WizardStep> steps;
  final VoidCallback? onComplete;
  final VoidCallback? onCancel;
  final bool canGoBack;
  final bool showProgress;
  final Color? primaryColor;

  const GuidedWizardScaffold({
    super.key,
    required this.title,
    required this.steps,
    this.onComplete,
    this.onCancel,
    this.canGoBack = true,
    this.showProgress = true,
    this.primaryColor,
  });

  @override
  State<GuidedWizardScaffold> createState() => _GuidedWizardScaffoldState();
}

class _GuidedWizardScaffoldState extends State<GuidedWizardScaffold> {
  late PageController _pageController;
  int _currentStep = 0;
  final Map<int, dynamic> _stepData = {};
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Color get _primaryColor => widget.primaryColor ?? DesignTokens.colorPrimary;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: _primaryColor,
        foregroundColor: Colors.white,
        leading: widget.canGoBack
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: _currentStep > 0 ? _goToPreviousStep : widget.onCancel,
              )
            : null,
        actions: [
          if (widget.onCancel != null)
            TextButton(
              onPressed: widget.onCancel,
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          if (widget.showProgress) _buildProgressIndicator(),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: widget.steps.length,
              itemBuilder: (context, index) {
                final step = widget.steps[index];
                return _buildStepContent(step, index);
              },
            ),
          ),
          _buildNavigationBar(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: List.generate(widget.steps.length, (index) {
              final isCompleted = index < _currentStep;
              final isCurrent = index == _currentStep;
              final isAccessible = index <= _currentStep;

              return Expanded(
                child: GestureDetector(
                  onTap: isAccessible ? () => _goToStep(index) : null,
                  child: Container(
                    height: 4,
                    margin: EdgeInsets.only(right: index < widget.steps.length - 1 ? 8 : 0),
                    decoration: BoxDecoration(
                      color: isCompleted || isCurrent
                          ? _primaryColor
                          : Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Étape ${_currentStep + 1} sur ${widget.steps.length}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: DesignTokens.colorOnSurfaceVariant,
                ),
              ),
              Text(
                widget.steps[_currentStep].title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(WizardStep step, int index) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (step.icon != null) ...[
            Center(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _primaryColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  step.icon,
                  size: 48,
                  color: _primaryColor,
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],
          
          Text(
            step.title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: _primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          if (step.subtitle != null) ...[
            const SizedBox(height: 8),
            Text(
              step.subtitle!,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: DesignTokens.colorOnSurfaceVariant,
              ),
            ),
          ],
          
          const SizedBox(height: 24),
          
          step.content,
          
          if (step.helpText != null) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[700],
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      step.helpText!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNavigationBar() {
    final currentStep = widget.steps[_currentStep];
    final isLastStep = _currentStep == widget.steps.length - 1;
    final canProceed = currentStep.validator?.call(_stepData[_currentStep]) ?? true;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (_currentStep > 0)
            OutlinedButton(
              onPressed: _isProcessing ? null : _goToPreviousStep,
              child: const Text('Précédent'),
            )
          else
            const SizedBox.shrink(),

          if (isLastStep)
            ElevatedButton(
              onPressed: _isProcessing || !canProceed ? null : _completeWizard,
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
              child: _isProcessing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(currentStep.completeButtonText ?? 'Terminer'),
            )
          else
            ElevatedButton(
              onPressed: _isProcessing || !canProceed ? null : _goToNextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(currentStep.nextButtonText ?? 'Suivant'),
            ),
        ],
      ),
    );
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentStep = index;
    });
  }

  void _goToStep(int index) {
    if (index >= 0 && index < widget.steps.length && index <= _currentStep) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      _goToStep(_currentStep - 1);
    }
  }

  void _goToNextStep() async {
    final currentStep = widget.steps[_currentStep];
    
    // Validate current step
    if (currentStep.validator != null) {
      final isValid = currentStep.validator!(_stepData[_currentStep]);
      if (!isValid) {
        return;
      }
    }

    // Execute step action if provided
    if (currentStep.onNext != null) {
      setState(() {
        _isProcessing = true;
      });

      try {
        final result = await currentStep.onNext!(_stepData[_currentStep]);
        if (result != null) {
          _stepData[_currentStep] = result;
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      setState(() {
        _isProcessing = false;
      });
    }

    // Move to next step
    if (_currentStep < widget.steps.length - 1) {
      _goToStep(_currentStep + 1);
    }
  }

  void _completeWizard() async {
    final currentStep = widget.steps[_currentStep];
    
    // Validate final step
    if (currentStep.validator != null) {
      final isValid = currentStep.validator!(_stepData[_currentStep]);
      if (!isValid) {
        return;
      }
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // Execute final step action if provided
      if (currentStep.onNext != null) {
        final result = await currentStep.onNext!(_stepData[_currentStep]);
        if (result != null) {
          _stepData[_currentStep] = result;
        }
      }

      // Call completion callback
      widget.onComplete?.call();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    setState(() {
      _isProcessing = false;
    });
  }

  void updateStepData(int stepIndex, dynamic data) {
    setState(() {
      _stepData[stepIndex] = data;
    });
  }

  dynamic getStepData(int stepIndex) {
    return _stepData[stepIndex];
  }

  Map<int, dynamic> getAllStepData() {
    return Map.from(_stepData);
  }
}

class WizardStep {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget content;
  final String? helpText;
  final String? nextButtonText;
  final String? completeButtonText;
  final bool Function(dynamic data)? validator;
  final Future<dynamic> Function(dynamic data)? onNext;

  const WizardStep({
    required this.title,
    this.subtitle,
    this.icon,
    required this.content,
    this.helpText,
    this.nextButtonText,
    this.completeButtonText,
    this.validator,
    this.onNext,
  });
}

// Helper widget for form steps
class WizardFormStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final List<Widget> children;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisAlignment mainAxisAlignment;

  const WizardFormStep({
    super.key,
    required this.formKey,
    required this.children,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.start,
  });

  @override
  State<WizardFormStep> createState() => _WizardFormStepState();
}

class _WizardFormStepState extends State<WizardFormStep> {
  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: Column(
        crossAxisAlignment: widget.crossAxisAlignment,
        mainAxisAlignment: widget.mainAxisAlignment,
        children: widget.children,
      ),
    );
  }

  bool validate() {
    return widget.formKey.currentState?.validate() ?? false;
  }
}

// Helper widget for confirmation steps
class WizardConfirmationStep extends StatelessWidget {
  final String title;
  final String message;
  final List<Widget>? additionalContent;
  final IconData? icon;
  final Color? iconColor;

  const WizardConfirmationStep({
    super.key,
    required this.title,
    required this.message,
    this.additionalContent,
    this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: 64,
            color: iconColor ?? DesignTokens.colorPrimary,
          ),
          const SizedBox(height: 24),
        ],
        Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          message,
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        if (additionalContent != null) ...[
          const SizedBox(height: 24),
          ...additionalContent!,
        ],
      ],
    );
  }
}
