// Performance Analytics Dashboard Widget
// Integrates with PerformanceAnalytics model and service

import 'package:flutter/material.dart';
// Performance Analytics Dashboard Widget
// Integrates with PerformanceAnalytics model and service

import 'package:flutter/material.dart';
import '../../models/analytics/performance_analytics.dart';
import '../../services/performance_analytics_service.dart';
import '../../services/adaptive_learning_service.dart';

/// Displays user performance analytics in a dashboard format.
class PerformanceAnalyticsDashboard extends StatelessWidget {
  final dynamic adaptiveLearningService;
  final dynamic userProgressService;
  final dynamic spacedRepetitionService;
  final dynamic performanceAnalyticsService; // Enhancement: injected service
  final String categoryName;
  final DateTimeRange? dateRange;

  const PerformanceAnalyticsDashboard({
    super.key,
    required this.adaptiveLearningService,
    required this.userProgressService,
    required this.spacedRepetitionService,
    required this.performanceAnalyticsService, // Enhancement
    required this.categoryName,
    this.dateRange,
  });

  @override
  Widget build(BuildContext context) {
    // Enhancement: use injected performanceAnalyticsService
    final service = performanceAnalyticsService;
    final range = dateRange != null
        ? DateRange(start: dateRange!.start, end: dateRange!.end)
        : DateRange.lastMonth();

    return FutureBuilder<PerformanceAnalytics>(
      future: service.generatePerformanceReport(categoryName, range),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData) {
          return const Center(child: Text('Erreur lors du chargement des données'));
        }
        final data = snapshot.data!;
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Performance Analytics', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 16),
                Text('Précision globale: ${(data.overallAccuracy * 100).toStringAsFixed(1)}%'),
                Text('Dernière mise à jour: ${data.lastUpdated}'),
                const SizedBox(height: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Performance par sujet:', style: Theme.of(context).textTheme.titleMedium),
                    ...data.topicPerformance!.entries.map((entry) => ListTile(
                          title: Text(entry.key),
                          trailing: Text('${(entry.value * 100).toStringAsFixed(1)}%'),
                        )),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Progression par difficulté:', style: Theme.of(context).textTheme.titleMedium),
                    ...data.difficultyProgression!.entries.map((entry) => ListTile(
                          title: Text('Niveau ${entry.key}'),
                          trailing: Text('${(entry.value * 100).toStringAsFixed(1)}%'),
                        )),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Progression hebdomadaire:', style: Theme.of(context).textTheme.titleMedium),
                    ...data.weeklyProgress!.map((week) => ListTile(
                          title: Text('Semaine du ${week.weekStart.toLocal()}'),
                          subtitle: Text('Questions: ${week.questionsAnswered}, Précision: ${(week.accuracy * 100).toStringAsFixed(1)}%'),
                        )),
                  ],
                ),
                if (data.weakestTopics.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Sujets faibles:', style: Theme.of(context).textTheme.titleMedium),
                      ...data.weakestTopics.map((weak) => ListTile(
                            title: Text(weak.topic),
                            subtitle: Text('Précision: ${(weak.accuracy * 100).toStringAsFixed(1)}%, Tentatives: ${weak.attemptCount}'),
                          )),
                    ],
                  ),
                if (data.improvementAreas.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Axes d\'amélioration:', style: Theme.of(context).textTheme.titleMedium),
                      ...data.improvementAreas.map((area) => ListTile(
                            title: Text(area),
                          )),
                    ],
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// End of PerformanceAnalyticsDashboard

// End of PerformanceAnalyticsDashboard
