// Weakness Analysis Display Widget
// Integrates with PerformanceAnalyticsService and TopicWeakness

import 'package:flutter/material.dart';
import '../../services/performance_analytics_service.dart';
import '../../services/adaptive_learning_service.dart';

/// Displays user's weakest topics and improvement recommendations.
class WeaknessAnalysisDisplay extends StatelessWidget {
  final AdaptiveLearningService adaptiveLearningService;
  final int limit;

  const WeaknessAnalysisDisplay({
    super.key,
    required this.adaptiveLearningService,
    this.limit = 5,
  });

  @override
  Widget build(BuildContext context) {
    final service = PerformanceAnalyticsService(adaptiveLearningService: adaptiveLearningService);
    final weaknesses = service.identifyWeakestTopics(limit);
    final recommendations = service.generatePersonalizedRecommendations();

    if (weaknesses.isEmpty) {
      return const Center(child: Text('Aucune faiblesse détectée.'));
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Analyse des faiblesses', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            ...weaknesses.map((weak) => ListTile(
                  title: Text(weak.topic),
                  subtitle: Text('Précision: ${(weak.accuracy * 100).toStringAsFixed(1)}%, Tentatives: ${weak.attemptCount}'),
                  trailing: Text('Tendance: ${weak.improvementTrend > 0 ? "Amélioration" : "Déclin"}'),
                )),
            const SizedBox(height: 16),
            Text('Recommandations:', style: Theme.of(context).textTheme.titleMedium),
            ...recommendations.map((rec) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text('• $rec'),
                )),
          ],
        ),
      ),
    );
  }
}

// End of WeaknessAnalysisDisplay
