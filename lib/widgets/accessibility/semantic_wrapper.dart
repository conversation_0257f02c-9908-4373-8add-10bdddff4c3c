import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import '../../theme/design_tokens.dart';
import '../../theme/semantic_colors.dart';

/// Enum defining different semantic roles for accessibility
enum SemanticRole {
  button,
  link,
  header,
  text,
  image,
  list,
  listItem,
  tab,
  tabPanel,
  dialog,
  alert,
  navigation,
  search,
  form,
  textField,
  checkbox,
  radio,
  slider,
  progressBar,
  menu,
  menuItem,
  tooltip,
  banner,
  complementary,
  contentInfo,
  main,
  region,
  article,
  section,
}

/// A comprehensive wrapper widget that adds semantic information and accessibility
/// features to any child widget. Provides automatic label generation, focus management,
/// keyboard navigation, and screen reader support.
class SemanticWrapper extends StatefulWidget {
  /// The child widget to wrap with semantic information
  final Widget child;

  /// Custom semantic label for screen readers
  final String? semanticLabel;

  /// Additional hint text for screen readers
  final String? semanticHint;

  /// The semantic role of this widget
  final SemanticRole? semanticRole;

  /// Whether this widget should be treated as a button
  final bool isButton;

  /// Whether this widget should be treated as a header
  final bool isHeader;

  /// Whether this widget is a live region that announces changes
  final bool isLiveRegion;

  /// Custom semantics properties for advanced use cases
  final SemanticsProperties? customSemantics;

  /// Focus node for keyboard navigation
  final FocusNode? focusNode;

  /// Whether this widget should automatically receive focus
  final bool autofocus;

  /// Callback when focus changes
  final ValueChanged<bool>? onFocusChange;

  /// Callback for tap/activation events
  final VoidCallback? onTap;

  /// Callback for keyboard events
  final ValueChanged<KeyEvent>? onKeyEvent;

  /// Whether this widget can receive focus
  final bool canRequestFocus;

  /// Whether to skip this widget in focus traversal
  final bool skipTraversal;

  /// Semantic value for widgets with state (like sliders, progress bars)
  final String? semanticValue;

  /// Whether this widget is currently selected
  final bool? isSelected;

  /// Whether this widget is currently enabled
  final bool isEnabled;

  /// Whether this widget is currently expanded (for expandable items)
  final bool? isExpanded;

  /// Whether this widget is currently checked (for checkboxes, radio buttons)
  final bool? isChecked;

  /// The reading order priority for screen readers
  final int? sortKey;

  /// Whether to exclude this widget from semantics tree
  final bool excludeSemantics;

  /// Custom tooltip text
  final String? tooltip;

  /// Whether to announce state changes to screen readers
  final bool announceStateChanges;

  /// Context information for automatic label generation
  final String? context;

  /// Whether to add enhanced focus indicators
  final bool enhancedFocusIndicator;

  /// Custom focus indicator color
  final Color? focusIndicatorColor;

  const SemanticWrapper({
    super.key,
    required this.child,
    this.semanticLabel,
    this.semanticHint,
    this.semanticRole,
    this.isButton = false,
    this.isHeader = false,
    this.isLiveRegion = false,
    this.customSemantics,
    this.focusNode,
    this.autofocus = false,
    this.onFocusChange,
    this.onTap,
    this.onKeyEvent,
    this.canRequestFocus = true,
    this.skipTraversal = false,
    this.semanticValue,
    this.isSelected,
    this.isEnabled = true,
    this.isExpanded,
    this.isChecked,
    this.sortKey,
    this.excludeSemantics = false,
    this.tooltip,
    this.announceStateChanges = true,
    this.context,
    this.enhancedFocusIndicator = false,
    this.focusIndicatorColor,
  });

  @override
  State<SemanticWrapper> createState() => _SemanticWrapperState();
}

class _SemanticWrapperState extends State<SemanticWrapper> {
  late FocusNode _focusNode;
  bool _isFocused = false;
  String? _previousSemanticValue;
  bool? _previousIsSelected;
  bool? _previousIsExpanded;
  bool? _previousIsChecked;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
    
    // Store initial state for change detection
    _previousSemanticValue = widget.semanticValue;
    _previousIsSelected = widget.isSelected;
    _previousIsExpanded = widget.isExpanded;
    _previousIsChecked = widget.isChecked;
  }

  @override
  void didUpdateWidget(SemanticWrapper oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle focus node changes
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_handleFocusChange);

      // Dispose old focus node if it was created internally
      if (oldWidget.focusNode == null) {
        _focusNode.dispose();
      }

      _focusNode = widget.focusNode ?? FocusNode();
      _focusNode.addListener(_handleFocusChange);
    }

    // Announce state changes if enabled
    if (widget.announceStateChanges) {
      _announceStateChanges(oldWidget);
    }
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_handleFocusChange);
    }
    super.dispose();
  }

  void _handleFocusChange() {
    final isFocused = _focusNode.hasFocus;
    if (_isFocused != isFocused) {
      setState(() {
        _isFocused = isFocused;
      });
      widget.onFocusChange?.call(isFocused);
      
      if (isFocused && widget.semanticLabel != null) {
        _announceToScreenReader('Focused: ${widget.semanticLabel}');
      }
    }
  }

  void _announceStateChanges(SemanticWrapper oldWidget) {
    // Announce value changes
    if (widget.semanticValue != _previousSemanticValue) {
      if (widget.semanticValue != null) {
        _announceToScreenReader('Value changed to ${widget.semanticValue}');
      }
      _previousSemanticValue = widget.semanticValue;
    }

    // Announce selection changes
    if (widget.isSelected != _previousIsSelected) {
      final state = widget.isSelected == true ? 'selected' : 'unselected';
      _announceToScreenReader('Item $state');
      _previousIsSelected = widget.isSelected;
    }

    // Announce expansion changes
    if (widget.isExpanded != _previousIsExpanded) {
      final state = widget.isExpanded == true ? 'expanded' : 'collapsed';
      _announceToScreenReader('Item $state');
      _previousIsExpanded = widget.isExpanded;
    }

    // Announce checked state changes
    if (widget.isChecked != _previousIsChecked) {
      final state = widget.isChecked == true ? 'checked' : 'unchecked';
      _announceToScreenReader('Item $state');
      _previousIsChecked = widget.isChecked;
    }
  }

  void _announceToScreenReader(String message) {
    if (widget.isLiveRegion || widget.announceStateChanges) {
      SemanticsService.announce(message, TextDirection.ltr);
    }
  }

  String _generateAutomaticLabel() {
    if (widget.semanticLabel != null) {
      return widget.semanticLabel!;
    }

    // Generate label based on role and context
    final role = widget.semanticRole;
    final context = widget.context ?? '';
    
    if (widget.isButton) {
      return context.isNotEmpty ? '$context button' : 'Button';
    }
    
    if (widget.isHeader) {
      return context.isNotEmpty ? '$context heading' : 'Heading';
    }

    switch (role) {
      case SemanticRole.button:
        return context.isNotEmpty ? '$context button' : 'Button';
      case SemanticRole.link:
        return context.isNotEmpty ? '$context link' : 'Link';
      case SemanticRole.header:
        return context.isNotEmpty ? '$context heading' : 'Heading';
      case SemanticRole.textField:
        return context.isNotEmpty ? '$context text field' : 'Text field';
      case SemanticRole.checkbox:
        return context.isNotEmpty ? '$context checkbox' : 'Checkbox';
      case SemanticRole.radio:
        return context.isNotEmpty ? '$context radio button' : 'Radio button';
      case SemanticRole.slider:
        return context.isNotEmpty ? '$context slider' : 'Slider';
      case SemanticRole.tab:
        return context.isNotEmpty ? '$context tab' : 'Tab';
      case SemanticRole.menu:
        return context.isNotEmpty ? '$context menu' : 'Menu';
      case SemanticRole.menuItem:
        return context.isNotEmpty ? '$context menu item' : 'Menu item';
      default:
        return context.isNotEmpty ? context : 'Interactive element';
    }
  }

  SemanticsProperties _buildSemantics() {
    final label = _generateAutomaticLabel();
    
    return SemanticsProperties(
      label: label,
      hint: widget.semanticHint,
      value: widget.semanticValue,
      button: widget.isButton || widget.semanticRole == SemanticRole.button,
      header: widget.isHeader || widget.semanticRole == SemanticRole.header,
      link: widget.semanticRole == SemanticRole.link,
      textField: widget.semanticRole == SemanticRole.textField,
      readOnly: !widget.isEnabled,
      enabled: widget.isEnabled,
      selected: widget.isSelected,
      expanded: widget.isExpanded,
      checked: widget.isChecked,
      focusable: widget.canRequestFocus,
      focused: _isFocused,
      liveRegion: widget.isLiveRegion,
      sortKey: widget.sortKey != null ? OrdinalSortKey(widget.sortKey!.toDouble()) : null,
      onTap: widget.onTap,
      onIncrease: widget.semanticRole == SemanticRole.slider ? () {
        _announceToScreenReader('Increased');
      } : null,
      onDecrease: widget.semanticRole == SemanticRole.slider ? () {
        _announceToScreenReader('Decreased');
      } : null,
    );
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (widget.onKeyEvent != null) {
      widget.onKeyEvent!(event);
    }

    if (event is KeyDownEvent) {
      // Handle common keyboard shortcuts
      if (event.logicalKey == LogicalKeyboardKey.enter ||
          event.logicalKey == LogicalKeyboardKey.space) {
        if (widget.onTap != null && widget.isEnabled) {
          widget.onTap!();
          return KeyEventResult.handled;
        }
      }
      
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        _focusNode.unfocus();
        return KeyEventResult.handled;
      }
    }

    return KeyEventResult.ignored;
  }

  Widget _buildFocusIndicator({required Widget child}) {
    if (!widget.enhancedFocusIndicator || !_isFocused) {
      return child;
    }

    final colorScheme = Theme.of(context).colorScheme;
    final focusColor = widget.focusIndicatorColor ?? 
                     colorScheme.primary.withValues(alpha: 0.3);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: widget.focusIndicatorColor ?? colorScheme.primary,
          width: 2.0,
        ),
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
        boxShadow: [
          BoxShadow(
            color: focusColor,
            blurRadius: 4.0,
            spreadRadius: 1.0,
          ),
        ],
      ),
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.excludeSemantics) {
      return ExcludeSemantics(child: widget.child);
    }

    Widget semanticChild = Semantics.fromProperties(
      properties: widget.customSemantics ?? _buildSemantics(),
      child: widget.child,
    );

    // Add tooltip if provided
    if (widget.tooltip != null) {
      semanticChild = Tooltip(
        message: widget.tooltip!,
        child: semanticChild,
      );
    }

    // Add focus management
    if (widget.canRequestFocus) {
      semanticChild = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        skipTraversal: widget.skipTraversal,
        onKeyEvent: _handleKeyEvent,
        child: _buildFocusIndicator(child: semanticChild),
      );
    }

    // Add gesture detection for tap events
    if (widget.onTap != null) {
      semanticChild = GestureDetector(
        onTap: widget.isEnabled ? widget.onTap : null,
        child: semanticChild,
      );
    }

    // Ensure minimum touch target size for interactive elements
    if (widget.isButton || widget.onTap != null) {
      semanticChild = ConstrainedBox(
        constraints: const BoxConstraints(
          minWidth: DesignTokens.touchTargetMin,
          minHeight: DesignTokens.touchTargetMin,
        ),
        child: semanticChild,
      );
    }

    return semanticChild;
  }
}

/// Extension methods for easy semantic wrapper usage
extension SemanticWrapperExtension on Widget {
  /// Wraps this widget with semantic information
  Widget withSemantics({
    String? label,
    String? hint,
    SemanticRole? role,
    bool isButton = false,
    bool isHeader = false,
    VoidCallback? onTap,
    String? context,
  }) {
    return SemanticWrapper(
      semanticLabel: label,
      semanticHint: hint,
      semanticRole: role,
      isButton: isButton,
      isHeader: isHeader,
      onTap: onTap,
      context: context,
      child: this,
    );
  }

  /// Wraps this widget as a button with semantic information
  Widget asSemanticButton({
    String? label,
    String? hint,
    required VoidCallback onTap,
    bool enabled = true,
    String? context,
  }) {
    return SemanticWrapper(
      semanticLabel: label,
      semanticHint: hint,
      semanticRole: SemanticRole.button,
      isButton: true,
      onTap: enabled ? onTap : null,
      isEnabled: enabled,
      context: context,
      enhancedFocusIndicator: true,
      child: this,
    );
  }

  /// Wraps this widget as a header with semantic information
  Widget asSemanticHeader({
    String? label,
    String? hint,
    int? level,
    String? context,
  }) {
    return SemanticWrapper(
      semanticLabel: label,
      semanticHint: hint,
      semanticRole: SemanticRole.header,
      isHeader: true,
      sortKey: level,
      context: context,
      child: this,
    );
  }

  /// Wraps this widget with live region semantics for announcements
  Widget asLiveRegion({
    String? label,
    bool announceChanges = true,
  }) {
    return SemanticWrapper(
      semanticLabel: label,
      isLiveRegion: true,
      announceStateChanges: announceChanges,
      child: this,
    );
  }
}