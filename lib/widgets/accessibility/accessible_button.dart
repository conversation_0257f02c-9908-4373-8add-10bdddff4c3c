import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import '../../theme/design_tokens.dart';

/// Accessible button widget that provides comprehensive accessibility features
/// including semantic labeling, focus management, keyboard navigation,
/// touch target compliance, and high contrast support.
class AccessibleButton extends StatefulWidget {
  /// The text to display on the button
  final String text;

  /// Callback when the button is pressed
  final VoidCallback? onPressed;

  /// Type of button styling
  final AccessibleButtonType type;

  /// Size variant of the button
  final AccessibleButtonSize size;

  /// Custom semantic label for screen readers
  final String? semanticLabel;

  /// Additional semantic hint for context
  final String? semanticHint;

  /// Whether the button is in loading state
  final bool isLoading;

  /// Loading text to announce to screen readers
  final String? loadingText;

  /// Icon to display (for icon buttons)
  final IconData? icon;

  /// Position of icon relative to text
  final IconPosition iconPosition;

  /// Tooltip text for additional context
  final String? tooltip;

  /// Whether to enable haptic feedback
  final bool enableHapticFeedback;

  /// Whether to enable sound feedback
  final bool enableSoundFeedback;

  /// Custom focus node for focus management
  final FocusNode? focusNode;

  /// Whether the button should autofocus
  final bool autofocus;

  /// Custom minimum touch target size
  final double? minTouchTargetSize;

  /// Custom border radius
  final double? borderRadius;

  /// Custom padding
  final EdgeInsetsGeometry? padding;

  /// Whether to expand to fill available width
  final bool expandWidth;

  /// Custom background color
  final Color? backgroundColor;

  /// Custom foreground color
  final Color? foregroundColor;

  /// Custom border color
  final Color? borderColor;

  /// Elevation for elevated buttons
  final double? elevation;

  /// Whether to use high contrast colors
  final bool useHighContrast;

  /// Custom accessibility role
  final SemanticsRole? semanticsRole;

  /// Whether this button is part of a group
  final bool isGrouped;

  /// Position in group (for semantic ordering)
  final int? groupPosition;

  /// Total items in group
  final int? groupSize;

  const AccessibleButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = AccessibleButtonType.primary,
    this.size = AccessibleButtonSize.medium,
    this.semanticLabel,
    this.semanticHint,
    this.isLoading = false,
    this.loadingText,
    this.icon,
    this.iconPosition = IconPosition.leading,
    this.tooltip,
    this.enableHapticFeedback = true,
    this.enableSoundFeedback = false,
    this.focusNode,
    this.autofocus = false,
    this.minTouchTargetSize,
    this.borderRadius,
    this.padding,
    this.expandWidth = false,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.elevation,
    this.useHighContrast = false,
    this.semanticsRole,
    this.isGrouped = false,
    this.groupPosition,
    this.groupSize,
  });

  /// Factory constructor for icon-only button
  factory AccessibleButton.icon({
    Key? key,
    required IconData icon,
    required VoidCallback? onPressed,
    String? semanticLabel,
    String? semanticHint,
    String? tooltip,
    AccessibleButtonType type = AccessibleButtonType.primary,
    AccessibleButtonSize size = AccessibleButtonSize.medium,
    bool isLoading = false,
    String? loadingText,
    bool enableHapticFeedback = true,
    bool enableSoundFeedback = false,
    FocusNode? focusNode,
    bool autofocus = false,
    double? minTouchTargetSize,
    double? borderRadius,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    double? elevation,
    bool useHighContrast = false,
    SemanticsRole? semanticsRole,
  }) {
    return AccessibleButton(
      key: key,
      text: '',
      onPressed: onPressed,
      type: type,
      size: size,
      semanticLabel: semanticLabel ?? 'Button',
      semanticHint: semanticHint,
      isLoading: isLoading,
      loadingText: loadingText,
      icon: icon,
      tooltip: tooltip,
      enableHapticFeedback: enableHapticFeedback,
      enableSoundFeedback: enableSoundFeedback,
      focusNode: focusNode,
      autofocus: autofocus,
      minTouchTargetSize: minTouchTargetSize,
      borderRadius: borderRadius,
      padding: padding,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
      elevation: elevation,
      useHighContrast: useHighContrast,
      semanticsRole: semanticsRole,
    );
  }

  @override
  State<AccessibleButton> createState() => _AccessibleButtonState();
}

class _AccessibleButtonState extends State<AccessibleButton> {
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  void _handlePress() {
    if (widget.onPressed == null || widget.isLoading) return;

    // Haptic feedback
    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }

    // Sound feedback
    if (widget.enableSoundFeedback) {
      SystemSound.play(SystemSoundType.click);
    }

    // Screen reader announcement for loading state
    if (widget.isLoading && widget.loadingText != null) {
      SemanticsService.announce(
        widget.loadingText!,
        TextDirection.ltr,
      );
    }

    widget.onPressed!();
  }

  void _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.enter ||
          event.logicalKey == LogicalKeyboardKey.space) {
        _handlePress();
      }
    }
  }

  String _generateSemanticLabel() {
    if (widget.semanticLabel != null) {
      return widget.semanticLabel!;
    }

    String label = widget.text.isNotEmpty ? widget.text : 'Button';

    if (widget.isLoading) {
      label += ', loading';
    }

    if (widget.onPressed == null) {
      label += ', disabled';
    }

    if (widget.isGrouped && widget.groupPosition != null && widget.groupSize != null) {
      label += ', ${widget.groupPosition} of ${widget.groupSize}';
    }

    return label;
  }

  String? _generateSemanticHint() {
    if (widget.semanticHint != null) {
      return widget.semanticHint;
    }

    if (widget.onPressed == null) {
      return 'Button is disabled';
    }

    if (widget.isLoading) {
      return 'Button is loading, please wait';
    }

    return 'Double tap to activate';
  }

  ButtonStyle _getButtonStyle() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Calculate minimum touch target size
    final minSize = widget.minTouchTargetSize ?? DesignTokens.touchTargetMin;
    
    // Get size-specific dimensions
    final dimensions = _getSizeDimensions();
    
    // Ensure minimum touch target compliance
    final height = dimensions.height < minSize ? minSize : dimensions.height;
    final minWidth = dimensions.width < minSize ? minSize : dimensions.width;

    // High contrast color adjustments
    Color? backgroundColor = widget.backgroundColor;
    Color? foregroundColor = widget.foregroundColor;
    Color? borderColor = widget.borderColor;

    if (widget.useHighContrast) {
      backgroundColor = _getHighContrastBackgroundColor(colorScheme);
      foregroundColor = _getHighContrastForegroundColor(colorScheme);
      borderColor = _getHighContrastBorderColor(colorScheme);
    }

    return ButtonStyle(
      minimumSize: MaterialStateProperty.all(Size(minWidth, height)),
      padding: MaterialStateProperty.all(
        widget.padding ?? dimensions.padding,
      ),
      backgroundColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return backgroundColor?.withOpacity(0.38) ?? 
                 _getDefaultBackgroundColor(colorScheme).withOpacity(0.38);
        }
        if (states.contains(MaterialState.pressed)) {
          return backgroundColor?.withOpacity(0.8) ?? 
                 _getDefaultBackgroundColor(colorScheme).withOpacity(0.8);
        }
        if (states.contains(MaterialState.hovered)) {
          return backgroundColor?.withOpacity(0.9) ?? 
                 _getDefaultBackgroundColor(colorScheme).withOpacity(0.9);
        }
        return backgroundColor ?? _getDefaultBackgroundColor(colorScheme);
      }),
      foregroundColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return foregroundColor?.withOpacity(0.38) ?? 
                 _getDefaultForegroundColor(colorScheme).withOpacity(0.38);
        }
        return foregroundColor ?? _getDefaultForegroundColor(colorScheme);
      }),
      // Removed duplicate side - using enhanced version below
      elevation: MaterialStateProperty.resolveWith((states) {
        if (widget.type == AccessibleButtonType.text) return 0.0;
        if (states.contains(MaterialState.disabled)) return 0.0;
        if (states.contains(MaterialState.pressed)) {
          return (widget.elevation ?? DesignTokens.elevation2) - 1.0;
        }
        return widget.elevation ?? DesignTokens.elevation2;
      }),
      shape: MaterialStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? DesignTokens.radiusBase,
          ),
        ),
      ),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.pressed)) {
          return foregroundColor?.withOpacity(0.1) ?? 
                 _getDefaultForegroundColor(colorScheme).withOpacity(0.1);
        }
        if (states.contains(MaterialState.hovered)) {
          return foregroundColor?.withOpacity(0.05) ?? 
                 _getDefaultForegroundColor(colorScheme).withOpacity(0.05);
        }
        if (states.contains(MaterialState.focused)) {
          return foregroundColor?.withOpacity(0.1) ?? 
                 _getDefaultForegroundColor(colorScheme).withOpacity(0.1);
        }
        return null;
      }),
      // Enhanced focus indicator for accessibility
      side: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.focused)) {
          return BorderSide(
            color: colorScheme.primary,
            width: widget.useHighContrast ? 3.0 : 2.0,
          );
        }
        if (widget.type == AccessibleButtonType.outlined) {
          final color = borderColor ?? _getDefaultBorderColor(colorScheme);
          return BorderSide(
            color: states.contains(MaterialState.disabled) 
                ? color.withOpacity(0.38) 
                : color,
            width: widget.useHighContrast ? 2.0 : 1.0,
          );
        }
        return null;
      }),
    );
  }

  _ButtonDimensions _getSizeDimensions() {
    switch (widget.size) {
      case AccessibleButtonSize.small:
        return _ButtonDimensions(
          height: 32.0,
          width: 64.0,
          padding: EdgeInsets.symmetric(horizontal: DesignTokens.space3, vertical: DesignTokens.space2),
          fontSize: DesignTokens.fontSizeSm,
          iconSize: DesignTokens.iconSm,
        );
      case AccessibleButtonSize.medium:
        return _ButtonDimensions(
          height: 40.0,
          width: 80.0,
          padding: EdgeInsets.symmetric(horizontal: DesignTokens.space4, vertical: DesignTokens.space3),
          fontSize: DesignTokens.fontSizeBase,
          iconSize: DesignTokens.iconBase,
        );
      case AccessibleButtonSize.large:
        return _ButtonDimensions(
          height: 48.0,
          width: 96.0,
          padding: EdgeInsets.symmetric(horizontal: DesignTokens.space6, vertical: DesignTokens.space4),
          fontSize: DesignTokens.fontSizeLg,
          iconSize: DesignTokens.iconLg,
        );
    }
  }

  Color _getDefaultBackgroundColor(ColorScheme colorScheme) {
    switch (widget.type) {
      case AccessibleButtonType.primary:
        return colorScheme.primary;
      case AccessibleButtonType.secondary:
        return colorScheme.secondary;
      case AccessibleButtonType.outlined:
        return Colors.transparent;
      case AccessibleButtonType.text:
        return Colors.transparent;
    }
  }

  Color _getDefaultForegroundColor(ColorScheme colorScheme) {
    switch (widget.type) {
      case AccessibleButtonType.primary:
        return colorScheme.onPrimary;
      case AccessibleButtonType.secondary:
        return colorScheme.onSecondary;
      case AccessibleButtonType.outlined:
        return colorScheme.primary;
      case AccessibleButtonType.text:
        return colorScheme.primary;
    }
  }

  Color _getDefaultBorderColor(ColorScheme colorScheme) {
    return colorScheme.outline;
  }

  Color _getHighContrastBackgroundColor(ColorScheme colorScheme) {
    switch (widget.type) {
      case AccessibleButtonType.primary:
        return colorScheme.brightness == Brightness.light 
            ? const Color(0xFF000000) 
            : const Color(0xFFFFFFFF);
      case AccessibleButtonType.secondary:
        return colorScheme.brightness == Brightness.light 
            ? const Color(0xFF1A1A1A) 
            : const Color(0xFFE6E6E6);
      case AccessibleButtonType.outlined:
        return Colors.transparent;
      case AccessibleButtonType.text:
        return Colors.transparent;
    }
  }

  Color _getHighContrastForegroundColor(ColorScheme colorScheme) {
    switch (widget.type) {
      case AccessibleButtonType.primary:
        return colorScheme.brightness == Brightness.light 
            ? const Color(0xFFFFFFFF) 
            : const Color(0xFF000000);
      case AccessibleButtonType.secondary:
        return colorScheme.brightness == Brightness.light 
            ? const Color(0xFFFFFFFF) 
            : const Color(0xFF000000);
      case AccessibleButtonType.outlined:
        return colorScheme.brightness == Brightness.light 
            ? const Color(0xFF000000) 
            : const Color(0xFFFFFFFF);
      case AccessibleButtonType.text:
        return colorScheme.brightness == Brightness.light 
            ? const Color(0xFF000000) 
            : const Color(0xFFFFFFFF);
    }
  }

  Color _getHighContrastBorderColor(ColorScheme colorScheme) {
    return colorScheme.brightness == Brightness.light 
        ? const Color(0xFF000000) 
        : const Color(0xFFFFFFFF);
  }

  Widget _buildButtonContent() {
    final dimensions = _getSizeDimensions();
    
    if (widget.isLoading) {
      return SizedBox(
        width: dimensions.iconSize,
        height: dimensions.iconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.foregroundColor ?? _getDefaultForegroundColor(Theme.of(context).colorScheme),
          ),
        ),
      );
    }

    // Icon-only button
    if (widget.text.isEmpty && widget.icon != null) {
      return Icon(
        widget.icon,
        size: dimensions.iconSize,
      );
    }

    // Text-only button
    if (widget.icon == null) {
      return Text(
        widget.text,
        style: TextStyle(
          fontSize: dimensions.fontSize,
          fontWeight: FontWeight.w500,
        ),
      );
    }

    // Button with icon and text
    final icon = Icon(
      widget.icon,
      size: dimensions.iconSize,
    );
    
    final text = Text(
      widget.text,
      style: TextStyle(
        fontSize: dimensions.fontSize,
        fontWeight: FontWeight.w500,
      ),
    );

    final spacing = SizedBox(width: DesignTokens.space2);

    switch (widget.iconPosition) {
      case IconPosition.leading:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [icon, spacing, text],
        );
      case IconPosition.trailing:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [text, spacing, icon],
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget button = Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        _handleKeyEvent(event);
        return KeyEventResult.handled;
      },
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        child: GestureDetector(
          onTapDown: (_) => setState(() => _isPressed = true),
          onTapUp: (_) => setState(() => _isPressed = false),
          onTapCancel: () => setState(() => _isPressed = false),
          child: ElevatedButton(
            onPressed: widget.isLoading ? null : _handlePress,
            style: _getButtonStyle(),
            child: _buildButtonContent(),
          ),
        ),
      ),
    );

    // Add tooltip if provided
    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    // Expand width if requested
    if (widget.expandWidth) {
      button = SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    // Wrap with semantics for accessibility
    return Semantics(
      button: true,
      enabled: widget.onPressed != null && !widget.isLoading,
      label: _generateSemanticLabel(),
      hint: _generateSemanticHint(),
      onTap: widget.onPressed != null && !widget.isLoading ? _handlePress : null,
      focusable: true,
      focused: _isFocused,
      child: button,
    );
  }
}

/// Button type variants
enum AccessibleButtonType {
  primary,
  secondary,
  outlined,
  text,
}

/// Button size variants
enum AccessibleButtonSize {
  small,
  medium,
  large,
}

/// Icon position relative to text
enum IconPosition {
  leading,
  trailing,
}

/// Internal class for button dimensions
class _ButtonDimensions {
  final double height;
  final double width;
  final EdgeInsetsGeometry padding;
  final double fontSize;
  final double iconSize;

  const _ButtonDimensions({
    required this.height,
    required this.width,
    required this.padding,
    required this.fontSize,
    required this.iconSize,
  });
}