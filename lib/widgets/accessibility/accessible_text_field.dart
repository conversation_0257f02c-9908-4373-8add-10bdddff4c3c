import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import '../custom_text_field.dart';
import '../../theme/design_tokens.dart';
import '../../theme/semantic_colors.dart';
import '../../theme/app_typography.dart';

/// An accessible text field widget that enhances CustomTextField with comprehensive
/// accessibility features including semantic labeling, screen reader support,
/// keyboard navigation, and assistive technology compatibility.
class AccessibleTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final String? description;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final bool readOnly;
  final bool required;
  final VoidCallback? onTap;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final bool obscureText;
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? style;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final bool enableCharacterCount;
  final int? maxLength;
  final bool enableWordCount;
  final bool enableVoiceInput;
  final List<String>? autocompleteOptions;
  final FocusNode? focusNode;
  final bool autofocus;
  final TextInputAction? textInputAction;
  final void Function()? onEditingComplete;
  final String? semanticLabel;
  final String? semanticHint;
  final bool enableLiveRegion;
  final String? errorText;

  const AccessibleTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.description,
    this.keyboardType,
    this.inputFormatters,
    this.validator,
    this.readOnly = false,
    this.required = false,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.suffixIcon,
    this.prefixIcon,
    this.obscureText = false,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.contentPadding,
    this.style,
    this.labelStyle,
    this.hintStyle,
    this.enableCharacterCount = false,
    this.maxLength,
    this.enableWordCount = false,
    this.enableVoiceInput = false,
    this.autocompleteOptions,
    this.focusNode,
    this.autofocus = false,
    this.textInputAction,
    this.onEditingComplete,
    this.semanticLabel,
    this.semanticHint,
    this.enableLiveRegion = false,
    this.errorText,
  });

  @override
  State<AccessibleTextField> createState() => _AccessibleTextFieldState();
}

class _AccessibleTextFieldState extends State<AccessibleTextField> {
  late FocusNode _focusNode;
  String? _currentError;
  String _lastAnnouncedValue = '';
  bool _hasBeenFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
    widget.controller.addListener(_onTextChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    widget.controller.removeListener(_onTextChange);
    super.dispose();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus && !_hasBeenFocused) {
      _hasBeenFocused = true;
      _announceFieldInfo();
    }
  }

  void _onTextChange() {
    final currentValue = widget.controller.text;
    
    // Announce character/word count changes for screen readers
    if (widget.enableCharacterCount || widget.enableWordCount) {
      _announceCountChanges(currentValue);
    }

    // Handle live region announcements
    if (widget.enableLiveRegion && currentValue != _lastAnnouncedValue) {
      _announceTextChange(currentValue);
      _lastAnnouncedValue = currentValue;
    }

    // Call the original onChanged callback
    widget.onChanged?.call(currentValue);
  }

  void _announceFieldInfo() {
    final StringBuffer announcement = StringBuffer();
    
    // Add field type and label
    announcement.write(_generateFieldTypeLabel());
    announcement.write('. ');
    
    // Add description if available
    if (widget.description != null) {
      announcement.write(widget.description);
      announcement.write('. ');
    }
    
    // Add required indicator
    if (widget.required) {
      announcement.write('Required field. ');
    }
    
    // Add current value if any
    if (widget.controller.text.isNotEmpty) {
      announcement.write('Current value: ${widget.controller.text}. ');
    }
    
    // Add character limit if applicable
    if (widget.maxLength != null) {
      announcement.write('Maximum ${widget.maxLength} characters. ');
    }

    SemanticsService.announce(announcement.toString(), TextDirection.ltr);
  }

  void _announceCountChanges(String currentValue) {
    if (widget.enableCharacterCount && widget.maxLength != null) {
      final remaining = widget.maxLength! - currentValue.length;
      if (remaining <= 10 && remaining >= 0) {
        SemanticsService.announce(
          '$remaining characters remaining',
          TextDirection.ltr,
        );
      }
    }

    if (widget.enableWordCount) {
      final wordCount = _getWordCount(currentValue);
      if (wordCount % 10 == 0 && wordCount > 0) {
        SemanticsService.announce(
          '$wordCount words',
          TextDirection.ltr,
        );
      }
    }
  }

  void _announceTextChange(String newValue) {
    if (newValue.isEmpty) {
      SemanticsService.announce('Field cleared', TextDirection.ltr);
    }
  }

  void _announceError(String? error) {
    if (error != null && error != _currentError) {
      SemanticsService.announce('Error: $error', TextDirection.ltr);
      _currentError = error;
    } else if (error == null && _currentError != null) {
      SemanticsService.announce('Error cleared', TextDirection.ltr);
      _currentError = null;
    }
  }

  String _generateFieldTypeLabel() {
    final keyboardType = widget.keyboardType;
    final isPassword = widget.obscureText;
    
    if (isPassword) return 'Password field';
    
    switch (keyboardType) {
      case TextInputType.emailAddress:
        return 'Email field';
      case TextInputType.phone:
        return 'Phone number field';
      case TextInputType.number:
        return 'Number field';
      case TextInputType.url:
        return 'URL field';
      case TextInputType.multiline:
        return 'Text area';
      default:
        return 'Text field';
    }
  }

  String _generateSemanticLabel() {
    if (widget.semanticLabel != null) {
      return widget.semanticLabel!;
    }

    final StringBuffer label = StringBuffer();
    label.write(widget.label);
    
    if (widget.required) {
      label.write(', required');
    }
    
    if (widget.readOnly) {
      label.write(', read only');
    }

    return label.toString();
  }

  String? _generateSemanticHint() {
    if (widget.semanticHint != null) {
      return widget.semanticHint;
    }

    final StringBuffer hint = StringBuffer();
    
    if (widget.description != null) {
      hint.write(widget.description);
      hint.write('. ');
    }
    
    if (widget.hint != null) {
      hint.write(widget.hint);
      hint.write('. ');
    }
    
    if (widget.maxLength != null) {
      hint.write('Maximum ${widget.maxLength} characters. ');
    }

    return hint.isNotEmpty ? hint.toString().trim() : null;
  }

  int _getWordCount(String text) {
    if (text.trim().isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }

  Widget _buildCharacterCounter() {
    if (!widget.enableCharacterCount || widget.maxLength == null) {
      return const SizedBox.shrink();
    }

    return Semantics(
      liveRegion: true,
      child: Padding(
        padding: const EdgeInsets.only(top: DesignTokens.space1),
        child: Text(
          '${widget.controller.text.length}/${widget.maxLength}',
          style: AppTypography.labelSmall().copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          semanticsLabel: '${widget.controller.text.length} of ${widget.maxLength} characters used',
        ),
      ),
    );
  }

  Widget _buildWordCounter() {
    if (!widget.enableWordCount) {
      return const SizedBox.shrink();
    }

    final wordCount = _getWordCount(widget.controller.text);
    
    return Semantics(
      liveRegion: true,
      child: Padding(
        padding: const EdgeInsets.only(top: DesignTokens.space1),
        child: Text(
          '$wordCount words',
          style: AppTypography.labelSmall().copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          semanticsLabel: '$wordCount words',
        ),
      ),
    );
  }

  Widget _buildDescription() {
    if (widget.description == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: DesignTokens.space2),
      child: Text(
        widget.description!,
        style: AppTypography.bodySmall().copyWith(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  Widget _buildVoiceInputButton() {
    if (!widget.enableVoiceInput || widget.readOnly) {
      return const SizedBox.shrink();
    }

    return Semantics(
      button: true,
      label: 'Voice input',
      hint: 'Tap to start voice input',
      child: IconButton(
        icon: const Icon(Icons.mic),
        onPressed: _startVoiceInput,
        tooltip: 'Voice input',
        constraints: const BoxConstraints(
          minWidth: DesignTokens.touchTargetMin,
          minHeight: DesignTokens.touchTargetMin,
        ),
      ),
    );
  }

  void _startVoiceInput() {
    // Voice input implementation would go here
    // This is a placeholder for the actual voice input functionality
    SemanticsService.announce('Voice input started', TextDirection.ltr);
  }

  Widget _buildAutocompleteField() {
    if (widget.autocompleteOptions == null || widget.autocompleteOptions!.isEmpty) {
      return _buildRegularField();
    }

    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text.isEmpty) {
          return const Iterable<String>.empty();
        }
        return widget.autocompleteOptions!.where((String option) {
          return option.toLowerCase().contains(textEditingValue.text.toLowerCase());
        });
      },
      fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
        // Sync the provided controller with our controller
        textEditingController.text = widget.controller.text;
        textEditingController.addListener(() {
          widget.controller.text = textEditingController.text;
        });

        return _buildTextFormField(
          controller: textEditingController,
          focusNode: focusNode,
          onFieldSubmitted: (value) => onFieldSubmitted(),
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Semantics(
          label: 'Autocomplete suggestions',
          child: Material(
            elevation: DesignTokens.elevation4,
            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
            child: ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: options.length,
              itemBuilder: (context, index) {
                final option = options.elementAt(index);
                return Semantics(
                  button: true,
                  label: option,
                  hint: 'Tap to select this option',
                  child: ListTile(
                    title: Text(option),
                    onTap: () => onSelected(option),
                    dense: true,
                    minTileHeight: DesignTokens.touchTargetMin,
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildRegularField() {
    return _buildTextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      onFieldSubmitted: widget.onSubmitted,
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required FocusNode focusNode,
    void Function(String)? onFieldSubmitted,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final semanticColors = context.semanticColors;
    final currentError = widget.errorText ?? _currentError;

    // Announce errors when they change
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _announceError(currentError);
    });

    return Semantics(
      textField: true,
      label: _generateSemanticLabel(),
      hint: _generateSemanticHint(),
      value: controller.text,
      enabled: !widget.readOnly,
      focusable: true,
      focused: focusNode.hasFocus,
      liveRegion: widget.enableLiveRegion,
      child: TextFormField(
        controller: controller,
        focusNode: focusNode,
        keyboardType: widget.keyboardType,
        inputFormatters: widget.inputFormatters,
        validator: widget.validator,
        readOnly: widget.readOnly,
        onTap: widget.onTap,
        onChanged: (value) {
          // onChanged is handled by our listener
        },
        onFieldSubmitted: onFieldSubmitted,
        onEditingComplete: widget.onEditingComplete,
        obscureText: widget.obscureText,
        maxLines: widget.maxLines,
        minLines: widget.minLines,
        expands: widget.expands,
        autofocus: widget.autofocus,
        textInputAction: widget.textInputAction,
        maxLength: widget.maxLength,
        buildCounter: widget.enableCharacterCount ? null : (context, {required currentLength, required isFocused, maxLength}) => null,
        style: widget.style ?? AppTypography.bodyLarge().copyWith(
          color: colorScheme.onSurface,
        ),
        decoration: InputDecoration(
          labelText: widget.label + (widget.required ? ' *' : ''),
          hintText: widget.hint,
          errorText: currentError,
          labelStyle: widget.labelStyle ?? AppTypography.labelLarge().copyWith(
            color: colorScheme.primary,
          ),
          hintStyle: widget.hintStyle ?? AppTypography.bodyMedium().copyWith(
            color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
          ),
          suffixIcon: widget.enableVoiceInput 
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (widget.suffixIcon != null) widget.suffixIcon!,
                  _buildVoiceInputButton(),
                ],
              )
            : widget.suffixIcon,
          prefixIcon: widget.prefixIcon,
          contentPadding: widget.contentPadding ?? EdgeInsets.symmetric(
            horizontal: DesignTokens.space4,
            vertical: DesignTokens.space3,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
            borderSide: BorderSide(color: colorScheme.outline),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
            borderSide: BorderSide(
              color: colorScheme.outline.withValues(alpha: 0.5),
              width: 1.0,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
            borderSide: BorderSide(
              color: colorScheme.primary,
              width: 2.0,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
            borderSide: BorderSide(
              color: semanticColors.errorVariant,
              width: 1.0,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
            borderSide: BorderSide(
              color: semanticColors.errorVariant,
              width: 2.0,
            ),
          ),
          filled: true,
          fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          // Ensure minimum touch target size
          constraints: const BoxConstraints(
            minHeight: DesignTokens.touchTargetMin,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDescription(),
        widget.autocompleteOptions != null 
          ? _buildAutocompleteField()
          : _buildRegularField(),
        Row(
          children: [
            Expanded(child: _buildCharacterCounter()),
            _buildWordCounter(),
          ],
        ),
      ],
    );
  }
}