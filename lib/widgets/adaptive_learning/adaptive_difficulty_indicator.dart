// Adaptive Difficulty Indicator Widget
// Integrates with AdaptiveDifficultyService

import 'package:flutter/material.dart';
import '../../services/adaptive_difficulty_service.dart';

/// Displays the current adaptive difficulty status for a quiz/session.
class AdaptiveDifficultyIndicator extends StatelessWidget {
  final AdaptiveDifficultyService service;
  final String? categoryName;

  const AdaptiveDifficultyIndicator({
    super.key,
    required this.service,
    this.categoryName,
  });

  @override
  Widget build(BuildContext context) {
    final difficultyLevel = categoryName != null
        ? service.calculateOptimalDifficulty(categoryName!, 'general')
        : 2; // Default if no category

    final color = difficultyLevel >= 4
        ? Colors.red
        : difficultyLevel >= 3
            ? Colors.orange
            : Colors.green;

    final label = difficultyLevel >= 4
        ? 'Difficile'
        : difficultyLevel >= 3
            ? 'Intermédiaire'
            : 'Facile';

    return Row(
      children: [
        Icon(Icons.trending_up, color: color),
        const SizedBox(width: 8),
        Text('Difficulté: $label ($difficultyLevel/5)',
            style: TextStyle(color: color, fontWeight: FontWeight.bold)),
      ],
    );
  }
}

// End of AdaptiveDifficultyIndicator
