// Spaced Repetition Review Widget
// Integrates with SpacedRepetitionService and SpacedRepetitionItem

import 'package:flutter/material.dart';
import '../../services/spaced_repetition_service.dart';
import '../../models/adaptive_learning/spaced_repetition_item.dart';

/// Displays a list of items scheduled for spaced repetition review.
class SpacedRepetitionReviewWidget extends StatelessWidget {
  final SpacedRepetitionService service;

  const SpacedRepetitionReviewWidget({super.key, required this.service});

  @override
  Widget build(BuildContext context) {
    final items = service.getDueItems();
    if (items.isEmpty) {
      return const Center(child: Text('Aucune révision programmée pour aujourd\'hui.'));
    }
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: ListTile(
            title: Text('${item.topic} (${item.categoryName})'),
            subtitle: Text('Prochaine révision: ${item.nextReviewDate.toLocal()}'),
            trailing: Icon(
              item.getMasteryLevel() > 0.8 ? Icons.check_circle : Icons.schedule,
              color: item.getMasteryLevel() > 0.8 ? Colors.green : Colors.orange,
            ),
            onTap: () {
              showDialog(
                context: context,
                builder: (_) => AlertDialog(
                  title: Text('Révision: ${item.topic}'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Catégorie: ${item.categoryName}'),
                      Text('Dernier résultat: ${item.lastResult ? "Correct" : "Incorrect"}'),
                      Text('Intervalle: ${item.interval} jour(s)'),
                      Text('Facteur de facilité: ${item.easinessFactor.toStringAsFixed(2)}'),
                      Text('Révisions réussies: ${item.repetitions}'),
                      Text('Tentatives totales: ${item.totalAttempts}'),
                      Text('Mastery: ${(item.getMasteryLevel() * 100).toStringAsFixed(1)}%'),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Fermer'),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}

// End of SpacedRepetitionReviewWidget
