import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import '../theme/design_tokens.dart';
import '../theme/semantic_colors.dart';
import '../theme/accessibility_theme_extension.dart';
import '../theme/app_typography.dart';
import 'accessibility/semantic_wrapper.dart';

class CustomTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final bool readOnly;
  final VoidCallback? onTap;
  final void Function(String)? onChanged;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final bool obscureText;
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? style;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  
  // Accessibility properties
  final String? semanticLabel;
  final String? semanticHint;
  final String? description;
  final bool isRequired;
  final FocusNode? focusNode;
  final bool autofocus;
  final ValueChanged<bool>? onFocusChange;
  final int? maxLength;
  final bool showCharacterCount;
  final bool announceCharacterCount;
  final String? errorText;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onFieldSubmitted;
  final bool enableVoiceInput;
  final bool enableAutocomplete;
  final Iterable<String>? autofillHints;
  final String? restorationId;
  final bool enableInteractiveSelection;
  final TextInputAction? textInputAction;
  final bool enableSuggestions;
  final bool autocorrect;
  final SmartDashesType? smartDashesType;
  final SmartQuotesType? smartQuotesType;
  final bool enableIMEPersonalizedLearning;
  final MouseCursor? mouseCursor;
  final String? counterText;
  final VoidCallback? onTapOutside;
  final bool canRequestFocus;
  final UndoHistoryController? undoController;

  const CustomTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.keyboardType,
    this.inputFormatters,
    this.validator,
    this.readOnly = false,
    this.onTap,
    this.onChanged,
    this.suffixIcon,
    this.prefixIcon,
    this.obscureText = false,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.contentPadding,
    this.style,
    this.labelStyle,
    this.hintStyle,
    // Accessibility properties
    this.semanticLabel,
    this.semanticHint,
    this.description,
    this.isRequired = false,
    this.focusNode,
    this.autofocus = false,
    this.onFocusChange,
    this.maxLength,
    this.showCharacterCount = false,
    this.announceCharacterCount = false,
    this.errorText,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.enableVoiceInput = false,
    this.enableAutocomplete = true,
    this.autofillHints,
    this.restorationId,
    this.enableInteractiveSelection = true,
    this.textInputAction,
    this.enableSuggestions = true,
    this.autocorrect = true,
    this.smartDashesType,
    this.smartQuotesType,
    this.enableIMEPersonalizedLearning = true,
    this.mouseCursor,
    this.counterText,
    this.onTapOutside,
    this.canRequestFocus = true,
    this.undoController,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  late FocusNode _focusNode;
  String? _currentError;
  int _characterCount = 0;
  bool _isFocused = false;
  String? _previousValue;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
    _characterCount = widget.controller.text.length;
    _previousValue = widget.controller.text;
    widget.controller.addListener(_handleTextChange);
  }

  @override
  void didUpdateWidget(CustomTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Handle focus node changes
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_handleFocusChange);
      if (oldWidget.focusNode == null) {
        _focusNode.dispose();
      }
      _focusNode = widget.focusNode ?? FocusNode();
      _focusNode.addListener(_handleFocusChange);
    }

    // Handle controller changes
    if (widget.controller != oldWidget.controller) {
      oldWidget.controller.removeListener(_handleTextChange);
      widget.controller.addListener(_handleTextChange);
      _characterCount = widget.controller.text.length;
      _previousValue = widget.controller.text;
    }

    // Announce error changes
    if (widget.errorText != oldWidget.errorText) {
      _announceErrorChange(oldWidget.errorText, widget.errorText);
    }
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleTextChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_handleFocusChange);
    }
    super.dispose();
  }

  void _handleFocusChange() {
    final isFocused = _focusNode.hasFocus;
    if (_isFocused != isFocused) {
      setState(() {
        _isFocused = isFocused;
      });
      widget.onFocusChange?.call(isFocused);
      
      if (isFocused) {
        _announceFieldFocus();
      }
    }
  }

  void _handleTextChange() {
    final newLength = widget.controller.text.length;
    final newValue = widget.controller.text;
    
    if (_characterCount != newLength) {
      setState(() {
        _characterCount = newLength;
      });
      
      if (widget.announceCharacterCount && _isFocused) {
        _announceCharacterCount();
      }
    }

    // Announce significant changes for screen readers
    if (_previousValue != newValue && _isFocused) {
      _announceTextChange(_previousValue, newValue);
    }
    
    _previousValue = newValue;
  }

  void _announceFieldFocus() {
    final label = _generateSemanticLabel();
    final announcement = StringBuffer(label);
    
    if (widget.description != null) {
      announcement.write(', ${widget.description}');
    }
    
    if (widget.isRequired) {
      announcement.write(', required');
    }
    
    if (widget.maxLength != null) {
      announcement.write(', maximum ${widget.maxLength} characters');
    }
    
    if (_characterCount > 0) {
      announcement.write(', current text: ${widget.controller.text}');
    }
    
    SemanticsService.announce(announcement.toString(), TextDirection.ltr);
  }

  void _announceCharacterCount() {
    if (widget.maxLength != null) {
      final remaining = widget.maxLength! - _characterCount;
      if (remaining <= 10 && remaining >= 0) {
        SemanticsService.announce(
          '$remaining characters remaining',
          TextDirection.ltr,
        );
      } else if (remaining < 0) {
        SemanticsService.announce(
          '${remaining.abs()} characters over limit',
          TextDirection.ltr,
        );
      }
    } else if (_characterCount > 0 && _characterCount % 50 == 0) {
      SemanticsService.announce(
        '$_characterCount characters entered',
        TextDirection.ltr,
      );
    }
  }

  void _announceTextChange(String? oldValue, String newValue) {
    // Only announce for significant changes to avoid spam
    if (oldValue == null || (newValue.length - oldValue.length).abs() > 5) {
      return;
    }
    
    // Announce word completion
    if (newValue.endsWith(' ') && !oldValue.endsWith(' ')) {
      final words = newValue.trim().split(' ');
      if (words.isNotEmpty) {
        final lastWord = words.last;
        if (lastWord.length > 2) {
          SemanticsService.announce('Word: $lastWord', TextDirection.ltr);
        }
      }
    }
  }

  void _announceErrorChange(String? oldError, String? newError) {
    if (newError != null && newError != oldError) {
      SemanticsService.announce('Error: $newError', TextDirection.ltr);
    } else if (oldError != null && newError == null) {
      SemanticsService.announce('Error cleared', TextDirection.ltr);
    }
  }

  String _generateSemanticLabel() {
    if (widget.semanticLabel != null) {
      return widget.semanticLabel!;
    }
    
    // Generate label based on field type and properties
    final buffer = StringBuffer();
    
    // Add field type information
    if (widget.obscureText) {
      buffer.write('Password field');
    } else if (widget.keyboardType == TextInputType.emailAddress) {
      buffer.write('Email field');
    } else if (widget.keyboardType == TextInputType.phone) {
      buffer.write('Phone number field');
    } else if (widget.keyboardType == TextInputType.number) {
      buffer.write('Number field');
    } else if (widget.keyboardType == TextInputType.multiline || widget.maxLines != 1) {
      buffer.write('Text area');
    } else {
      buffer.write('Text field');
    }
    
    // Add label
    buffer.write(': ${widget.label}');
    
    return buffer.toString();
  }

  String _generateSemanticHint() {
    if (widget.semanticHint != null) {
      return widget.semanticHint!;
    }
    
    final hints = <String>[];
    
    if (widget.hint != null) {
      hints.add(widget.hint!);
    }
    
    if (widget.description != null) {
      hints.add(widget.description!);
    }
    
    if (widget.isRequired) {
      hints.add('Required field');
    }
    
    if (widget.maxLength != null) {
      hints.add('Maximum ${widget.maxLength} characters');
    }
    
    if (widget.readOnly) {
      hints.add('Read only');
    }
    
    return hints.join(', ');
  }

  InputDecoration _buildInputDecoration(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final semanticColors = context.semanticColors;
    
    // Get accessibility theme extension if available
    final accessibilityTheme = Theme.of(context).extension<AccessibilityThemeExtension>();
    final fontScale = accessibilityTheme?.fontScale ?? 1.0;
    final highContrastMode = accessibilityTheme?.highContrastMode ?? false;
    
    // Enhanced border styles for high contrast mode
    final borderColor = highContrastMode 
        ? colorScheme.outline
        : colorScheme.outline.withValues(alpha: 0.5);
    final focusedBorderColor = highContrastMode
        ? colorScheme.primary
        : colorScheme.primary;
    final errorBorderColor = highContrastMode
        ? semanticColors.errorVariant
        : semanticColors.errorVariant;
    
    final borderWidth = highContrastMode ? 2.0 : 1.0;
    final focusedBorderWidth = highContrastMode ? 3.0 : 2.0;
    
    return InputDecoration(
      labelText: widget.label,
      hintText: widget.hint,
      helperText: widget.description,
      errorText: widget.errorText,
      counterText: widget.showCharacterCount ? widget.counterText : null,
      labelStyle: widget.labelStyle ?? AppTypography.labelLarge(fontScale).copyWith(
        color: colorScheme.primary,
        fontWeight: highContrastMode ? FontWeight.w600 : FontWeight.w500,
      ),
      hintStyle: widget.hintStyle ?? AppTypography.bodyMedium(fontScale).copyWith(
        color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
      ),
      helperStyle: AppTypography.bodySmall(fontScale).copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      errorStyle: AppTypography.bodySmall(fontScale).copyWith(
        color: semanticColors.errorVariant,
        fontWeight: highContrastMode ? FontWeight.w600 : FontWeight.w400,
      ),
      suffixIcon: widget.suffixIcon,
      prefixIcon: widget.prefixIcon,
      contentPadding: widget.contentPadding ?? EdgeInsets.symmetric(
        horizontal: DesignTokens.space4,
        vertical: DesignTokens.space3,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
        borderSide: BorderSide(color: borderColor, width: borderWidth),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
        borderSide: BorderSide(color: borderColor, width: borderWidth),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
        borderSide: BorderSide(
          color: focusedBorderColor,
          width: focusedBorderWidth,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
        borderSide: BorderSide(color: errorBorderColor, width: borderWidth),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
        borderSide: BorderSide(
          color: errorBorderColor,
          width: focusedBorderWidth,
        ),
      ),
      filled: true,
      fillColor: highContrastMode
          ? colorScheme.surface
          : colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final accessibilityTheme = Theme.of(context).extension<AccessibilityThemeExtension>();
    final fontScale = accessibilityTheme?.fontScale ?? 1.0;
    final highContrastMode = accessibilityTheme?.highContrastMode ?? false;

    // Build the text field with enhanced accessibility
    Widget textField = TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      validator: widget.validator,
      readOnly: widget.readOnly,
      onTap: widget.onTap,
      onChanged: (value) {
        widget.onChanged?.call(value);
      },
      obscureText: widget.obscureText,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      expands: widget.expands,
      maxLength: widget.maxLength,
      autofocus: widget.autofocus,
      onEditingComplete: widget.onEditingComplete,
      onFieldSubmitted: widget.onFieldSubmitted,
      autofillHints: widget.autofillHints,
      restorationId: widget.restorationId,
      enableInteractiveSelection: widget.enableInteractiveSelection,
      textInputAction: widget.textInputAction,
      enableSuggestions: widget.enableSuggestions,
      autocorrect: widget.autocorrect,
      smartDashesType: widget.smartDashesType,
      smartQuotesType: widget.smartQuotesType,
      enableIMEPersonalizedLearning: widget.enableIMEPersonalizedLearning,
      mouseCursor: widget.mouseCursor,
      onTapOutside: widget.onTapOutside != null 
          ? (_) => widget.onTapOutside!() 
          : null,
      canRequestFocus: widget.canRequestFocus,
      undoController: widget.undoController,
      style: widget.style ?? AppTypography.bodyLarge(fontScale).copyWith(
        color: colorScheme.onSurface,
        fontWeight: highContrastMode ? FontWeight.w500 : FontWeight.w400,
      ),
      decoration: _buildInputDecoration(context),
    );

    // Wrap with semantic information
    // Note: Don't pass focusNode to SemanticWrapper since TextFormField already manages focus
    return SemanticWrapper(
      semanticLabel: _generateSemanticLabel(),
      semanticHint: _generateSemanticHint(),
      semanticRole: SemanticRole.textField,
      autofocus: false, // TextFormField handles autofocus
      canRequestFocus: false, // TextFormField handles focus management
      isEnabled: !widget.readOnly,
      enhancedFocusIndicator: false, // TextFormField provides its own focus indicator
      announceStateChanges: true,
      context: 'form input',
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: DesignTokens.touchTargetMin,
        ),
        child: textField,
      ),
    );
  }
}

// Extension removed - using the one from accessibility_theme_extension.dart

// AccessibilityThemeExtension class removed - using the one from accessibility_theme_extension.dart
