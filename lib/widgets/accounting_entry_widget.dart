import 'package:flutter/material.dart';

class AccountingLine {
  final String account;
  final String label;
  final String amount;

  const AccountingLine({
    required this.account,
    required this.label,
    required this.amount,
  });
}

class AccountingEntryWidget extends StatelessWidget {
  final String title;
  final List<AccountingLine> debitEntries;
  final List<AccountingLine> creditEntries;
  final String explanation;

  const AccountingEntryWidget({
    super.key,
    required this.title,
    required this.debitEntries,
    required this.creditEntries,
    required this.explanation,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              explanation,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
            ),
            const SizedBox(height: 16),
            Table(
              columnWidths: const {
                0: FlexColumnWidth(1),
                1: FlexColumnWidth(3),
                2: FlexColumnWidth(1),
              },
              children: [
                TableRow(
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                  ),
                  children: [
                    const TableCell(
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text('Compte'),
                      ),
                    ),
                    const TableCell(
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text('Libellé'),
                      ),
                    ),
                    const TableCell(
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text('Montant'),
                      ),
                    ),
                  ],
                ),
                ...debitEntries.map((entry) => _buildDebitRow(entry)),
                ...creditEntries.map((entry) => _buildCreditRow(entry)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildDebitRow(AccountingLine entry) {
    return TableRow(
      children: [
        TableCell(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(entry.account),
          ),
        ),
        TableCell(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(entry.label),
          ),
        ),
        TableCell(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              entry.amount,
              textAlign: TextAlign.right,
            ),
          ),
        ),
      ],
    );
  }

  TableRow _buildCreditRow(AccountingLine entry) {
    return TableRow(
      children: [
        TableCell(
          child: Padding(
            padding: const EdgeInsets.only(left: 32.0, top: 8.0, bottom: 8.0),
            child: Text(entry.account),
          ),
        ),
        TableCell(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(entry.label),
          ),
        ),
        TableCell(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              entry.amount,
              textAlign: TextAlign.right,
            ),
          ),
        ),
      ],
    );
  }
}
