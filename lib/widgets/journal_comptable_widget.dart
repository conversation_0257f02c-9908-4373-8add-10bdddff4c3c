import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class JournalComptableWidget extends StatelessWidget {
  final String title;
  final List<JournalEntry> entries;

  const JournalComptableWidget({
    super.key,
    required this.title,
    required this.entries,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(11),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.book,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: entries.map((entry) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (entry.date != null) ...[
                        Text(
                          entry.date!,
                          style: textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                      ],
                      Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.middle,
                        columnWidths: const {
                          0: FixedColumnWidth(80), // Compte
                          1: FixedColumnWidth(200), // Libellé
                          2: FixedColumnWidth(100), // Débit
                          3: FixedColumnWidth(100), // Crédit
                        },
                        children: [
                          TableRow(
                            decoration: BoxDecoration(
                              color: colorScheme.surfaceContainerHighest
                                  .withValues(alpha: 0.3),
                            ),
                            children: [
                              _buildHeaderCell(context, 'Compte'),
                              _buildHeaderCell(context, 'Libellé'),
                              _buildHeaderCell(context, 'Débit',
                                  isNumeric: true),
                              _buildHeaderCell(context, 'Crédit',
                                  isNumeric: true),
                            ],
                          ),
                          ...entry.lines.map((line) {
                            return TableRow(
                              children: [
                                _buildCell(context, line.account),
                                _buildCell(context, line.label),
                                _buildCell(
                                  context,
                                  line.debit?.isNotEmpty == true
                                      ? line.debit!
                                      : '',
                                  isNumeric: true,
                                ),
                                _buildCell(
                                  context,
                                  line.credit?.isNotEmpty == true
                                      ? line.credit!
                                      : '',
                                  isNumeric: true,
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text,
      {bool isNumeric = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  Widget _buildCell(BuildContext context, String text,
      {bool isNumeric = false}) {
    final formattedText = isNumeric && text.isNotEmpty
        ? NumberFormat('#,##0.00').format(double.tryParse(text) ?? 0)
        : text;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Text(
        formattedText,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }
}

class JournalEntry {
  final String? date;
  final List<JournalLine> lines;
  final String? groupLabel;
  final bool showSubtotal;

  const JournalEntry({
    this.date,
    required this.lines,
    this.groupLabel,
    this.showSubtotal = false,
  });

  double get totalDebit => lines
      .where((line) => line.debit != null)
      .map((line) => double.tryParse(line.debit!) ?? 0.0)
      .fold(0.0, (sum, amount) => sum + amount);

  double get totalCredit => lines
      .where((line) => line.credit != null)
      .map((line) => double.tryParse(line.credit!) ?? 0.0)
      .fold(0.0, (sum, amount) => sum + amount);
}

class JournalLine {
  final String account;
  final String label;
  final String? debit;
  final String? credit;
  final bool isHighlighted;
  final bool isBold;

  const JournalLine({
    required this.account,
    required this.label,
    this.debit,
    this.credit,
    this.isHighlighted = false,
    this.isBold = false,
  });
}
