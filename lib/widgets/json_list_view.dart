import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class JsonListView extends StatelessWidget {
  final String jsonPath;
  final Widget Function(BuildContext, Map<String, dynamic>) itemBuilder;
  final String itemsKey;

  const JsonListView({
    super.key,
    required this.jsonPath,
    required this.itemBuilder,
    this.itemsKey = 'categories',
  });

  Future<List<dynamic>> _loadJsonData() async {
    try {
      final String jsonString = await rootBundle.loadString(jsonPath);
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      return List<dynamic>.from(jsonData[itemsKey]);
    } catch (e) {
      debugPrint('Error loading $jsonPath data: $e');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<dynamic>>(
      future: _loadJsonData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Erreur de chargement: ${snapshot.error}'),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text('Aucune donnée disponible'),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(8.0),
          itemCount: snapshot.data!.length,
          itemBuilder: (context, index) {
            final Map<String, dynamic> item = snapshot.data![index];
            return itemBuilder(context, item);
          },
        );
      },
    );
  }
}
