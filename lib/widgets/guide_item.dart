import 'package:flutter/material.dart';
import '../theme/design_tokens.dart';
import '../theme/app_typography.dart';
import '../theme/app_icons.dart';
import '../theme/responsive_breakpoints.dart';

class GuideItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String route;

  const GuideItem({
    super.key,
    required this.title,
    required this.subtitle,
    required this.route,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      margin: EdgeInsets.only(bottom: DesignTokens.space4),
      elevation: context.responsiveCardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(context.responsiveBorderRadius),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.all(DesignTokens.space4),
        title: Text(
          title,
          style: AppTypography.h5().copyWith(
            color: colorScheme.onSurface,
          ),
        ),
        subtitle: Padding(
          padding: EdgeInsets.only(top: DesignTokens.space2),
          child: Text(
            subtitle,
            style: AppTypography.bodyMedium().copyWith(
              color: colorScheme.onSurfaceVariant,
              height: DesignTokens.lineHeightRelaxed,
            ),
          ),
        ),
        trailing: context.icon(
          Icons.arrow_forward_ios,
          size: DesignTokens.iconSm,
          context: IconContext.surfaceVariant,
        ),
        onTap: () => Navigator.pushNamed(context, route),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(context.responsiveBorderRadius),
        ),
      ),
    );
  }
}
