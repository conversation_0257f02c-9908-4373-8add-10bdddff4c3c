import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/is/is_input_data.dart';
import '../../services/is_calculator_service.dart';
import '../../utils/input_validators.dart';
import '../../utils/calculation_utils.dart';

class InputCollectionCard extends StatefulWidget {
  final IsInputData inputData;
  final ValueChanged<IsInputData> onDataChanged;
  final bool showPreview;
  final String selectedRegime;

  const InputCollectionCard({
    super.key,
    required this.inputData,
    required this.onDataChanged,
    this.showPreview = true,
    required this.selectedRegime,
  });

  @override
  State<InputCollectionCard> createState() => _InputCollectionCardState();
}

class _InputCollectionCardState extends State<InputCollectionCard>
    with TickerProviderStateMixin {
  // Form controllers
  late final TextEditingController _accountingResultController;
  late final TextEditingController _revenueController;
  
  // Focus nodes
  final FocusNode _accountingResultFocusNode = FocusNode();
  final FocusNode _revenueFocusNode = FocusNode();
  
  // Form key
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  
  // Expansion states
  bool _basicSectionExpanded = true;
  bool _reintegrationsSectionExpanded = false;
  bool _deductionsSectionExpanded = false;
  
  // Data loading states
  bool _isLoadingReintegrations = false;
  bool _isLoadingDeductions = false;
  Map<String, dynamic> _reintegrationData = {};
  Map<String, dynamic> _deductionData = {};
  
  // Dynamic input controllers for reintegrations and deductions
  final Map<String, TextEditingController> _reintegrationControllers = {};
  final Map<String, TextEditingController> _deductionControllers = {};
  final Map<String, FocusNode> _reintegrationFocusNodes = {};
  final Map<String, FocusNode> _deductionFocusNodes = {};
  
  // Animation controllers
  late AnimationController _previewAnimationController;
  late Animation<double> _previewAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize basic controllers
    _accountingResultController = TextEditingController(
      text: widget.inputData.accountingResult != 0.0 
          ? widget.inputData.accountingResult.toString() 
          : '',
    );
    _revenueController = TextEditingController(
      text: widget.inputData.revenue != 0.0 
          ? widget.inputData.revenue.toString() 
          : '',
    );
    
    // Initialize animation controller
    _previewAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _previewAnimation = CurvedAnimation(
      parent: _previewAnimationController,
      curve: Curves.easeInOut,
    );
    
    // Add listeners
    _accountingResultController.addListener(_onBasicFieldChanged);
    _revenueController.addListener(_onBasicFieldChanged);
    
    // Load reintegration and deduction data
    _loadReintegrationData();
    
    // Initialize dynamic controllers for existing data
    _initializeDynamicControllers();
    
    // Start preview animation if enabled
    if (widget.showPreview) {
      _previewAnimationController.forward();
    }
  }

  @override
  void dispose() {
    _accountingResultController.dispose();
    _revenueController.dispose();
    _accountingResultFocusNode.dispose();
    _revenueFocusNode.dispose();
    _previewAnimationController.dispose();
    
    // Dispose dynamic controllers
    for (final controller in _reintegrationControllers.values) {
      controller.dispose();
    }
    for (final controller in _deductionControllers.values) {
      controller.dispose();
    }
    for (final focusNode in _reintegrationFocusNodes.values) {
      focusNode.dispose();
    }
    for (final focusNode in _deductionFocusNodes.values) {
      focusNode.dispose();
    }
    
    super.dispose();
  }

  void _initializeDynamicControllers() {
    // Initialize reintegration controllers
    for (final entry in widget.inputData.reintegrations.entries) {
      final controller = TextEditingController(text: entry.value.toString());
      controller.addListener(() => _onReintegrationChanged(entry.key));
      _reintegrationControllers[entry.key] = controller;
      _reintegrationFocusNodes[entry.key] = FocusNode();
    }
    
    // Initialize deduction controllers
    for (final entry in widget.inputData.deductions.entries) {
      final controller = TextEditingController(text: entry.value.toString());
      controller.addListener(() => _onDeductionChanged(entry.key));
      _deductionControllers[entry.key] = controller;
      _deductionFocusNodes[entry.key] = FocusNode();
    }
  }

  Future<void> _loadReintegrationData() async {
    setState(() {
      _isLoadingReintegrations = true;
      _isLoadingDeductions = true;
    });

    try {
      final data = await IsCalculatorService.loadIsReintegrations();
      setState(() {
        _reintegrationData = data;
        _deductionData = data;
        _isLoadingReintegrations = false;
        _isLoadingDeductions = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingReintegrations = false;
        _isLoadingDeductions = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des données: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _onBasicFieldChanged() {
    if (!_formKey.currentState!.validate()) return;

    final accountingResult = InputValidators.parseAccountingInput(
      _accountingResultController.text,
    );
    final revenue = InputValidators.parseMonetaryInput(_revenueController.text);

    final updatedData = widget.inputData.copyWith(
      accountingResult: accountingResult,
      revenue: revenue,
    );

    widget.onDataChanged(updatedData);
  }

  void _onReintegrationChanged(String key) {
    final controller = _reintegrationControllers[key];
    if (controller == null) return;

    final value = InputValidators.parseReintegrationInput(controller.text);
    final updatedData = widget.inputData.addReintegration(key, value);
    widget.onDataChanged(updatedData);
  }

  void _onDeductionChanged(String key) {
    final controller = _deductionControllers[key];
    if (controller == null) return;

    final value = InputValidators.parseMonetaryInput(controller.text);
    final updatedData = widget.inputData.addDeduction(key, value);
    widget.onDataChanged(updatedData);
  }

  void _addReintegrationField(String key, String label) {
    if (_reintegrationControllers.containsKey(key)) return;

    final controller = TextEditingController();
    controller.addListener(() => _onReintegrationChanged(key));
    
    setState(() {
      _reintegrationControllers[key] = controller;
      _reintegrationFocusNodes[key] = FocusNode();
    });
  }

  void _removeReintegrationField(String key) {
    final controller = _reintegrationControllers[key];
    final focusNode = _reintegrationFocusNodes[key];
    
    controller?.dispose();
    focusNode?.dispose();
    
    setState(() {
      _reintegrationControllers.remove(key);
      _reintegrationFocusNodes.remove(key);
    });
    
    final updatedData = widget.inputData.removeReintegration(key);
    widget.onDataChanged(updatedData);
  }

  void _addDeductionField(String key, String label) {
    if (_deductionControllers.containsKey(key)) return;

    final controller = TextEditingController();
    controller.addListener(() => _onDeductionChanged(key));
    
    setState(() {
      _deductionControllers[key] = controller;
      _deductionFocusNodes[key] = FocusNode();
    });
  }

  void _removeDeductionField(String key) {
    final controller = _deductionControllers[key];
    final focusNode = _deductionFocusNodes[key];
    
    controller?.dispose();
    focusNode?.dispose();
    
    setState(() {
      _deductionControllers.remove(key);
      _deductionFocusNodes.remove(key);
    });
    
    final updatedData = widget.inputData.removeDeduction(key);
    widget.onDataChanged(updatedData);
  }

  Widget _buildBasicInputsSection() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.primaryContainer.withValues(alpha: 0.3),
              colorScheme.surface,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: ExpansionTile(
          initiallyExpanded: _basicSectionExpanded,
          onExpansionChanged: (expanded) {
            setState(() {
              _basicSectionExpanded = expanded;
            });
          },
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.account_balance,
              color: colorScheme.primary,
              size: 20,
            ),
          ),
          title: Text(
            'Données de base',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          subtitle: Text(
            'Résultat comptable et chiffre d\'affaires',
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Accounting Result
                  TextFormField(
                    controller: _accountingResultController,
                    focusNode: _accountingResultFocusNode,
                    decoration: InputDecoration(
                      labelText: 'Résultat comptable',
                      hintText: 'Saisir le résultat comptable (peut être négatif)',
                      filled: true,
                      fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: colorScheme.outline.withValues(alpha: 0.3),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      prefixIcon: Icon(
                        Icons.trending_up,
                        color: colorScheme.primary,
                      ),
                      suffixText: ' DH',
                      helperText: 'Valeurs négatives autorisées pour les pertes',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                      signed: true,
                    ),
                    inputFormatters: InputValidators.getAccountingInputFormatters(),
                    validator: (value) => InputValidators.validateAccountingResult(value),
                    onFieldSubmitted: (_) {
                      _revenueFocusNode.requestFocus();
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Revenue
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          controller: _revenueController,
                          focusNode: _revenueFocusNode,
                          decoration: InputDecoration(
                            labelText: 'Chiffre d\'affaires',
                            hintText: 'Saisir le CA de l\'exercice',
                            filled: true,
                            fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: colorScheme.outline.withValues(alpha: 0.3),
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: colorScheme.primary,
                                width: 2,
                              ),
                            ),
                            prefixIcon: Icon(
                              Icons.attach_money,
                              color: colorScheme.primary,
                            ),
                            suffixText: ' DH',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: InputValidators.getBusinessAmountFormatters(),
                          validator: (value) => InputValidators.validateBusinessRevenue(value),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 1,
                        child: DropdownButtonFormField<String>(
                          value: widget.inputData.revenueType,
                          decoration: InputDecoration(
                            labelText: 'Type',
                            filled: true,
                            fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: colorScheme.outline.withValues(alpha: 0.3),
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: colorScheme.primary,
                                width: 2,
                              ),
                            ),
                          ),
                          items: const [
                            DropdownMenuItem(value: 'normal', child: Text('Normal')),
                            DropdownMenuItem(value: 'reduced', child: Text('Réduit')),
                            DropdownMenuItem(value: 'special', child: Text('Spécial')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              final updatedData = widget.inputData.copyWith(revenueType: value);
                              widget.onDataChanged(updatedData);
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReintegrationsSection() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.errorContainer.withValues(alpha: 0.3),
              colorScheme.surface,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: ExpansionTile(
          initiallyExpanded: _reintegrationsSectionExpanded,
          onExpansionChanged: (expanded) {
            setState(() {
              _reintegrationsSectionExpanded = expanded;
            });
          },
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.add_circle_outline,
              color: colorScheme.error,
              size: 20,
            ),
          ),
          title: Text(
            'Réintégrations',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.error,
            ),
          ),
          subtitle: Text(
            'Charges non déductibles à réintégrer',
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.inputData.totalReintegrations > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    CalculationUtils.formatMonetary(widget.inputData.totalReintegrations),
                    style: textTheme.labelSmall?.copyWith(
                      color: colorScheme.error,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              const SizedBox(width: 8),
              _isLoadingReintegrations
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: colorScheme.error,
                      ),
                    )
                  : Icon(
                      _reintegrationsSectionExpanded
                          ? Icons.expand_less
                          : Icons.expand_more,
                      color: colorScheme.error,
                    ),
            ],
          ),
          children: [
            if (_isLoadingReintegrations)
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: CircularProgressIndicator()),
              )
            else
              _buildDynamicInputSection(
                data: _reintegrationData['reintegrations'] as List<dynamic>? ?? [],
                controllers: _reintegrationControllers,
                focusNodes: _reintegrationFocusNodes,
                onAdd: _addReintegrationField,
                onRemove: _removeReintegrationField,
                validator: InputValidators.validateReintegrationAmount,
                inputFormatters: InputValidators.getBusinessAmountFormatters(),
                colorScheme: colorScheme,
                textTheme: textTheme,
                isReintegration: true,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeductionsSection() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.tertiaryContainer.withValues(alpha: 0.3),
              colorScheme.surface,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: ExpansionTile(
          initiallyExpanded: _deductionsSectionExpanded,
          onExpansionChanged: (expanded) {
            setState(() {
              _deductionsSectionExpanded = expanded;
            });
          },
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.tertiary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.remove_circle_outline,
              color: colorScheme.tertiary,
              size: 20,
            ),
          ),
          title: Text(
            'Déductions',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.tertiary,
            ),
          ),
          subtitle: Text(
            'Déductions fiscales autorisées',
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.inputData.totalDeductions > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: colorScheme.tertiary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    CalculationUtils.formatMonetary(widget.inputData.totalDeductions),
                    style: textTheme.labelSmall?.copyWith(
                      color: colorScheme.tertiary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              const SizedBox(width: 8),
              _isLoadingDeductions
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: colorScheme.tertiary,
                      ),
                    )
                  : Icon(
                      _deductionsSectionExpanded
                          ? Icons.expand_less
                          : Icons.expand_more,
                      color: colorScheme.tertiary,
                    ),
            ],
          ),
          children: [
            if (_isLoadingDeductions)
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: CircularProgressIndicator()),
              )
            else
              _buildDynamicInputSection(
                data: _deductionData['deductions'] as List<dynamic>? ?? [],
                controllers: _deductionControllers,
                focusNodes: _deductionFocusNodes,
                onAdd: _addDeductionField,
                onRemove: _removeDeductionField,
                validator: InputValidators.validateDeductionAmount,
                inputFormatters: InputValidators.getBusinessAmountFormatters(),
                colorScheme: colorScheme,
                textTheme: textTheme,
                isReintegration: false,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDynamicInputSection({
    required List<dynamic> data,
    required Map<String, TextEditingController> controllers,
    required Map<String, FocusNode> focusNodes,
    required Function(String, String) onAdd,
    required Function(String) onRemove,
    required String? Function(String?) validator,
    required List<TextInputFormatter> inputFormatters,
    required ColorScheme colorScheme,
    required TextTheme textTheme,
    required bool isReintegration,
  }) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Existing fields
          ...controllers.entries.map((entry) {
            final key = entry.key;
            final controller = entry.value;
            final focusNode = focusNodes[key];
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: controller,
                      focusNode: focusNode,
                      decoration: InputDecoration(
                        labelText: _getFieldLabel(key, data),
                        filled: true,
                        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: colorScheme.outline.withValues(alpha: 0.3),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: isReintegration ? colorScheme.error : colorScheme.tertiary,
                            width: 2,
                          ),
                        ),
                        prefixIcon: Icon(
                          isReintegration ? Icons.add : Icons.remove,
                          color: isReintegration ? colorScheme.error : colorScheme.tertiary,
                        ),
                        suffixText: ' DH',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: inputFormatters,
                      validator: validator,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => onRemove(key),
                    icon: Icon(
                      Icons.delete_outline,
                      color: colorScheme.error,
                    ),
                    tooltip: 'Supprimer',
                  ),
                ],
              ),
            );
          }),
          
          // Add new field button
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showAddFieldDialog(data, onAdd, isReintegration),
              icon: Icon(
                Icons.add,
                color: isReintegration ? colorScheme.error : colorScheme.tertiary,
              ),
              label: Text(
                isReintegration ? 'Ajouter une réintégration' : 'Ajouter une déduction',
                style: textTheme.labelLarge?.copyWith(
                  color: isReintegration ? colorScheme.error : colorScheme.tertiary,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: isReintegration ? colorScheme.error : colorScheme.tertiary,
                ),
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getFieldLabel(String key, List<dynamic> data) {
    for (final category in data) {
      if (category == null) continue;
      final items = category['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item != null && item['id'] == key) {
          return item['label'] as String? ?? key;
        }
      }
    }
    return key;
  }

  void _showAddFieldDialog(
    List<dynamic> data,
    Function(String, String) onAdd,
    bool isReintegration,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            isReintegration ? 'Ajouter une réintégration' : 'Ajouter une déduction',
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: data.length,
              itemBuilder: (context, categoryIndex) {
                final category = data[categoryIndex];
                if (category == null) return const SizedBox.shrink();
                
                final categoryName = category['category'] as String? ?? 'Catégorie ${categoryIndex + 1}';
                final items = category['items'] as List<dynamic>? ?? [];
                
                return ExpansionTile(
                  title: Text(categoryName),
                  children: items.map<Widget>((item) {
                    if (item == null) return const SizedBox.shrink();
                    
                    final id = item['id'] as String? ?? '';
                    final label = item['label'] as String? ?? id;
                    final description = item['description'] as String?;
                    
                    return ListTile(
                      title: Text(label),
                      subtitle: description != null ? Text(description) : null,
                      onTap: () {
                        onAdd(id, label);
                        Navigator.of(context).pop();
                      },
                    );
                  }).toList(),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPreviewSection() {
    if (!widget.showPreview) return const SizedBox.shrink();
    
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return FadeTransition(
      opacity: _previewAnimation,
      child: Card(
        elevation: 4,
        surfaceTintColor: colorScheme.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                colorScheme.primaryContainer.withValues(alpha: 0.5),
                colorScheme.surface,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.preview,
                      color: colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Aperçu du calcul',
                          style: textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                        Text(
                          'Résultat imposable calculé en temps réel',
                          style: textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              
              // Calculation breakdown
              _buildCalculationRow(
                'Résultat comptable:',
                widget.inputData.accountingResult,
                colorScheme,
                textTheme,
              ),
              _buildCalculationRow(
                'Total réintégrations:',
                widget.inputData.totalReintegrations,
                colorScheme,
                textTheme,
                isAddition: true,
              ),
              _buildCalculationRow(
                'Total déductions:',
                widget.inputData.totalDeductions,
                colorScheme,
                textTheme,
                isSubtraction: true,
              ),
              const Divider(height: 24),
              _buildCalculationRow(
                'Résultat imposable:',
                widget.inputData.taxableResult,
                colorScheme,
                textTheme,
                isTotal: true,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalculationRow(
    String label,
    double value,
    ColorScheme colorScheme,
    TextTheme textTheme, {
    bool isAddition = false,
    bool isSubtraction = false,
    bool isTotal = false,
  }) {
    Color? valueColor;
    String prefix = '';
    
    if (isAddition) {
      valueColor = colorScheme.error;
      prefix = '+ ';
    } else if (isSubtraction) {
      valueColor = colorScheme.tertiary;
      prefix = '- ';
    } else if (isTotal) {
      valueColor = colorScheme.primary;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '$prefix${CalculationUtils.formatMonetary(value.abs())}',
            style: textTheme.bodyMedium?.copyWith(
              color: valueColor ?? colorScheme.onSurface,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          _buildBasicInputsSection(),
          const SizedBox(height: 16),
          _buildReintegrationsSection(),
          const SizedBox(height: 16),
          _buildDeductionsSection(),
          const SizedBox(height: 16),
          _buildPreviewSection(),
        ],
      ),
    );
  }
}