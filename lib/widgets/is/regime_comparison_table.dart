import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/is/is_input_data.dart';
import '../../models/is/is_calculation_result.dart';
import '../../services/is_calculator_service.dart';

/// A comparison table widget for comparing different tax regimes
/// 
/// This widget allows users to select multiple tax regimes and compare
/// their IS, CM, total payable amounts, and effective rates in a
/// responsive DataTable format.
class RegimeComparisonTable extends StatefulWidget {
  /// Input data for calculations
  final IsInputData inputData;
  
  /// List of regimes available for comparison
  final List<String> availableRegimes;
  
  /// Currently selected regimes for comparison
  final List<String> selectedRegimes;
  
  /// Callback when regime selection changes
  final ValueChanged<List<String>> onRegimeSelectionChanged;
  
  /// Whether to highlight the optimal regime
  final bool showOptimalHighlight;

  const RegimeComparisonTable({
    super.key,
    required this.inputData,
    required this.availableRegimes,
    required this.selectedRegimes,
    required this.onRegimeSelectionChanged,
    this.showOptimalHighlight = true,
  });

  @override
  State<RegimeComparisonTable> createState() => _RegimeComparisonTableState();
}

class _RegimeComparisonTableState extends State<RegimeComparisonTable> {
  List<IsCalculationResult> _comparisonResults = [];
  bool _isLoading = false;
  String? _errorMessage;
  IsCalculationResult? _optimalResult;
  IsCalculationResult? _worstResult;
  final Map<String, bool> _expandedRows = {};

  @override
  void initState() {
    super.initState();
    _calculateComparisons();
  }

  @override
  void didUpdateWidget(RegimeComparisonTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.inputData != widget.inputData ||
        oldWidget.selectedRegimes != widget.selectedRegimes) {
      _calculateComparisons();
    }
  }

  Future<void> _calculateComparisons() async {
    if (widget.selectedRegimes.isEmpty || !widget.inputData.isValid) {
      setState(() {
        _comparisonResults = [];
        _optimalResult = null;
        _worstResult = null;
        _errorMessage = null;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final results = await IsCalculatorService.compareRegimes(
        widget.inputData,
        widget.selectedRegimes,
      );

      setState(() {
        _comparisonResults = results;
        _optimalResult = results.isNotEmpty 
            ? IsCalculationResult.findOptimal(results)
            : null;
        _worstResult = results.isNotEmpty
            ? results.reduce((current, next) => 
                current.totalPayable >= next.totalPayable ? current : next)
            : null;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors du calcul des comparaisons: $e';
        _isLoading = false;
      });
    }
  }

  void _toggleRegimeSelection(String regime) {
    final newSelection = List<String>.from(widget.selectedRegimes);
    if (newSelection.contains(regime)) {
      newSelection.remove(regime);
    } else {
      newSelection.add(regime);
    }
    widget.onRegimeSelectionChanged(newSelection);
  }

  void _selectAllRegimes() {
    widget.onRegimeSelectionChanged(List<String>.from(widget.availableRegimes));
  }

  void _clearSelection() {
    widget.onRegimeSelectionChanged([]);
  }

  void _toggleRowExpansion(String regimeName) {
    setState(() {
      _expandedRows[regimeName] = !(_expandedRows[regimeName] ?? false);
    });
  }

  void _exportResults() {
    // TODO: Implement export functionality
    // For now, copy to clipboard
    final buffer = StringBuffer();
    buffer.writeln('Comparaison des Régimes Fiscaux');
    buffer.writeln('================================');
    buffer.writeln();
    
    for (final result in _comparisonResults) {
      buffer.writeln('Régime: ${result.regimeName}');
      buffer.writeln('IS: ${result.formattedIsAmount}');
      buffer.writeln('CM: ${result.formattedCmAmount}');
      buffer.writeln('Total: ${result.formattedTotalPayable}');
      buffer.writeln('Taux Effectif: ${result.formattedEffectiveRate}');
      if (_worstResult != null) {
        buffer.writeln('Économie: ${result.formattedSavingsComparedTo(_worstResult!)}');
      }
      buffer.writeln('---');
    }

    Clipboard.setData(ClipboardData(text: buffer.toString()));
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
              const SizedBox(width: 8),
              const Text('Résultats copiés dans le presse-papiers'),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.primary,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.compare_arrows,
                  color: colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Comparaison des Régimes Fiscaux',
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (_comparisonResults.isNotEmpty) ...[
                  IconButton(
                    onPressed: _exportResults,
                    icon: Icon(
                      Icons.file_download,
                      color: colorScheme.primary,
                    ),
                    tooltip: 'Exporter les résultats',
                  ),
                ],
              ],
            ),
            const SizedBox(height: 16),

            // Regime Selection
            _buildRegimeSelection(context),
            const SizedBox(height: 20),

            // Comparison Table
            if (_isLoading)
              _buildLoadingState(context)
            else if (_errorMessage != null)
              _buildErrorState(context)
            else if (_comparisonResults.isEmpty)
              _buildEmptyState(context)
            else
              _buildComparisonTable(context),
          ],
        ),
      ),
    );
  }

  Widget _buildRegimeSelection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.outlineVariant,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Sélection des Régimes',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _selectAllRegimes,
                child: Text(
                  'Tout sélectionner',
                  style: TextStyle(color: colorScheme.primary),
                ),
              ),
              TextButton(
                onPressed: _clearSelection,
                child: Text(
                  'Effacer',
                  style: TextStyle(color: colorScheme.error),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.availableRegimes.map((regime) {
              final isSelected = widget.selectedRegimes.contains(regime);
              return FilterChip(
                label: Text(regime),
                selected: isSelected,
                onSelected: (_) => _toggleRegimeSelection(regime),
                selectedColor: colorScheme.primaryContainer,
                checkmarkColor: colorScheme.primary,
                labelStyle: TextStyle(
                  color: isSelected 
                      ? colorScheme.onPrimaryContainer 
                      : colorScheme.onSurface,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),
            const SizedBox(height: 16),
            Text(
              'Calcul des comparaisons en cours...',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.error.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: colorScheme.error,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.error,
              ),
            ),
          ),
          TextButton(
            onPressed: _calculateComparisons,
            child: Text(
              'Réessayer',
              style: TextStyle(color: colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.table_chart_outlined,
              size: 64,
              color: colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'Sélectionnez des régimes à comparer',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choisissez au moins un régime fiscal pour voir la comparaison',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonTable(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.outlineVariant,
        ),
      ),
      child: Column(
        children: [
          // Table Header with optimal regime indicator
          if (widget.showOptimalHighlight && _optimalResult != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.star,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Régime optimal: ${_optimalResult!.regimeName} (${_optimalResult!.formattedTotalPayable})',
                    style: textTheme.titleSmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

          // Scrollable DataTable
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minWidth: MediaQuery.of(context).size.width - 80,
              ),
              child: DataTable(
                headingRowColor: WidgetStateColor.resolveWith(
                  (states) => colorScheme.primaryContainer.withValues(alpha: 0.2),
                ),
                headingTextStyle: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
                dataTextStyle: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface,
                ),
                columns: [
                  DataColumn(
                    label: Text(
                      'Régime',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'IS (DH)',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                    numeric: true,
                  ),
                  DataColumn(
                    label: Text(
                      'CM (DH)',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                    numeric: true,
                  ),
                  DataColumn(
                    label: Text(
                      'Total (DH)',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                    numeric: true,
                  ),
                  DataColumn(
                    label: Text(
                      'Taux Effectif',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                    numeric: true,
                  ),
                  DataColumn(
                    label: Text(
                      'Économie',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                    numeric: true,
                  ),
                  DataColumn(
                    label: Text(
                      'Détails',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                  ),
                ],
                rows: _comparisonResults.map((result) {
                  final isOptimal = widget.showOptimalHighlight && 
                      _optimalResult != null && 
                      result.regimeName == _optimalResult!.regimeName;
                  final savings = _worstResult != null 
                      ? result.savingsComparedTo(_worstResult!)
                      : 0.0;

                  return DataRow(
                    color: WidgetStateColor.resolveWith((states) {
                      if (isOptimal) {
                        return colorScheme.primaryContainer.withValues(alpha: 0.1);
                      }
                      return Colors.transparent;
                    }),
                    cells: [
                      DataCell(
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isOptimal) ...[
                              Icon(
                                Icons.star,
                                color: colorScheme.primary,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                            ],
                            Flexible(
                              child: Text(
                                result.regimeName,
                                style: textTheme.bodyMedium?.copyWith(
                                  color: isOptimal 
                                      ? colorScheme.primary 
                                      : colorScheme.onSurface,
                                  fontWeight: isOptimal 
                                      ? FontWeight.bold 
                                      : FontWeight.normal,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      DataCell(
                        Text(
                          result.formattedIsAmount,
                          style: textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ),
                      DataCell(
                        Text(
                          result.formattedCmAmount,
                          style: textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ),
                      DataCell(
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: isOptimal 
                                ? colorScheme.primaryContainer
                                : colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            result.formattedTotalPayable,
                            style: textTheme.titleSmall?.copyWith(
                              color: isOptimal 
                                  ? colorScheme.onPrimaryContainer
                                  : colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      DataCell(
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: colorScheme.primaryContainer.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            result.formattedEffectiveRate,
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      DataCell(
                        Text(
                          savings > 0 
                              ? result.formattedSavingsComparedTo(_worstResult!)
                              : '-',
                          style: textTheme.bodyMedium?.copyWith(
                            color: savings > 0 
                                ? Colors.green[700]
                                : colorScheme.onSurface,
                            fontWeight: savings > 0 
                                ? FontWeight.bold 
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                      DataCell(
                        IconButton(
                          onPressed: () => _toggleRowExpansion(result.regimeName),
                          icon: Icon(
                            _expandedRows[result.regimeName] == true
                                ? Icons.expand_less
                                : Icons.expand_more,
                            color: colorScheme.primary,
                          ),
                          tooltip: 'Voir les détails',
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),

          // Expanded row details
          ..._comparisonResults.where((result) => 
              _expandedRows[result.regimeName] == true
            ).map((result) => _buildExpandedRowDetails(context, result)),
        ],
      ),
    );
  }

  Widget _buildExpandedRowDetails(BuildContext context, IsCalculationResult result) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.1),
        border: Border(
          top: BorderSide(
            color: colorScheme.outlineVariant,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Détails pour ${result.regimeName}',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  context,
                  'Résultat Imposable',
                  result.formattedTaxableResult,
                  Icons.calculate,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  context,
                  'Taux Appliqué',
                  result.formattedAppliedRate,
                  Icons.percent,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  context,
                  'Type de Taux',
                  result.rateType == 'progressive' ? 'Progressif' : 'Fixe',
                  Icons.trending_up,
                ),
              ),
            ],
          ),
          if (result.fiscalAnalysis.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.analytics,
                        color: colorScheme.primary,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Analyse Fiscale',
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    result.fiscalAnalysis,
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.outlineVariant,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: colorScheme.primary,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: textTheme.titleSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}