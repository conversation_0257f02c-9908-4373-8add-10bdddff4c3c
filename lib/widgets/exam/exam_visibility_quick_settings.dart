import 'package:flutter/material.dart';
import '../../theme/exam_visibility_theme_extension.dart';

/// Quick settings widget for improving exam visibility on Android devices
/// Provides easy access to contrast, font size, and visibility enhancements
class ExamVisibilityQuickSettings extends StatefulWidget {
  const ExamVisibilityQuickSettings({super.key});

  @override
  State<ExamVisibilityQuickSettings> createState() => _ExamVisibilityQuickSettingsState();
}

class _ExamVisibilityQuickSettingsState extends State<ExamVisibilityQuickSettings> {
  bool _isExpanded = false;
  bool _enhancedContrast = false;
  bool _textShadows = false;
  bool _alternatingColors = false;
  double _fontScale = 1.0;

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  void _loadCurrentSettings() {
    // Use post-frame callback to avoid accessing context during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final examVisibility = Theme.of(context).extension<ExamVisibilityThemeExtension>();
        if (examVisibility != null) {
          setState(() {
            _enhancedContrast = examVisibility.enhancedContrastMode;
            _textShadows = examVisibility.enableTextShadows;
            _alternatingColors = examVisibility.useAlternatingAnswerColors;
            _fontScale = examVisibility.examFontScale;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Card(
      margin: const EdgeInsets.all(8),
      elevation: _isExpanded ? 8 : 4,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with toggle button
            ListTile(
              leading: Icon(
                Icons.visibility,
                color: colorScheme.primary,
              ),
              title: Text(
                'Améliorer la visibilité',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              subtitle: Text(
                _isExpanded ? 'Masquer les options' : 'Afficher les options',
                style: theme.textTheme.bodySmall,
              ),
              trailing: IconButton(
                icon: AnimatedRotation(
                  turns: _isExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 300),
                  child: const Icon(Icons.expand_more),
                ),
                onPressed: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
              ),
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
            ),
            
            // Expandable settings
            AnimatedCrossFade(
              firstChild: const SizedBox.shrink(),
              secondChild: _buildSettingsPanel(context, colorScheme, isDark),
              crossFadeState: _isExpanded 
                  ? CrossFadeState.showSecond 
                  : CrossFadeState.showFirst,
              duration: const Duration(milliseconds: 300),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsPanel(BuildContext context, ColorScheme colorScheme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        children: [
          const Divider(),
          const SizedBox(height: 8),
          
          // Quick preset buttons
          Row(
            children: [
              Expanded(
                child: _buildPresetButton(
                  context,
                  'Standard',
                  Icons.visibility_outlined,
                  () => _applyPreset(ExamVisibilityThemeExtension.enhanced()),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildPresetButton(
                  context,
                  'Haute visibilité',
                  Icons.visibility,
                  () => _applyPreset(ExamVisibilityThemeExtension.maximumVisibility()),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Individual settings
          _buildToggleSetting(
            context,
            'Contraste renforcé',
            'Améliore le contraste des réponses',
            Icons.contrast,
            _enhancedContrast,
            (value) => setState(() => _enhancedContrast = value),
          ),
          
          _buildToggleSetting(
            context,
            'Ombres de texte',
            'Ajoute des ombres pour une meilleure lisibilité',
            Icons.text_format,
            _textShadows,
            (value) => setState(() => _textShadows = value),
          ),
          
          _buildToggleSetting(
            context,
            'Couleurs alternées',
            'Utilise des couleurs différentes pour les options',
            Icons.palette,
            _alternatingColors,
            (value) => setState(() => _alternatingColors = value),
          ),
          
          const SizedBox(height: 16),
          
          // Font scale slider
          _buildFontScaleSlider(context),
          
          const SizedBox(height: 16),
          
          // Apply button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _applySettings,
              icon: const Icon(Icons.check),
              label: const Text('Appliquer les paramètres'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetButton(BuildContext context, String label, IconData icon, VoidCallback onPressed) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        side: BorderSide(color: colorScheme.outline),
      ),
    );
  }

  Widget _buildToggleSetting(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(icon, size: 20),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall,
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildFontScaleSlider(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.text_fields, size: 20),
            const SizedBox(width: 8),
            Text(
              'Taille du texte: ${(_fontScale * 100).round()}%',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
        Slider(
          value: _fontScale,
          min: 1.0,
          max: 1.5,
          divisions: 5,
          label: '${(_fontScale * 100).round()}%',
          onChanged: (value) {
            setState(() {
              _fontScale = value;
            });
          },
        ),
      ],
    );
  }

  void _applyPreset(ExamVisibilityThemeExtension preset) {
    setState(() {
      _enhancedContrast = preset.enhancedContrastMode;
      _textShadows = preset.enableTextShadows;
      _alternatingColors = preset.useAlternatingAnswerColors;
      _fontScale = preset.examFontScale;
    });
    _applySettings();
  }

  void _applySettings() {
    // For now, we'll show a confirmation that settings would be applied
    // In a full implementation, this would update the theme service
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Paramètres de visibilité appliqués'),
        action: SnackBarAction(
          label: 'Annuler',
          onPressed: () {
            // Reset to previous settings
            _loadCurrentSettings();
          },
        ),
      ),
    );

    // Collapse the panel after applying
    setState(() {
      _isExpanded = false;
    });
  }
}
