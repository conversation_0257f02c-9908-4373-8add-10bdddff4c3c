import 'package:flutter/material.dart';
import '../../../models/guide/personal_note_data.dart';
import 'note_editor.dart';

/// Widget for displaying and managing a list of personal notes
class NotesListView extends StatefulWidget {
  final List<PersonalNoteData> notes;
  final Function(PersonalNoteData)? onNoteSelected;
  final Function(PersonalNoteData)? onNoteEdited;
  final Function(String)? onNoteDeleted;
  final Function(PersonalNoteData)? onNoteCreated;
  final String? selectedNoteId;
  final bool isCompact;

  const NotesListView({
    super.key,
    required this.notes,
    this.onNoteSelected,
    this.onNoteEdited,
    this.onNoteDeleted,
    this.onNoteCreated,
    this.selectedNoteId,
    this.isCompact = false,
  });

  @override
  State<NotesListView> createState() => _NotesListViewState();
}

class _NotesListViewState extends State<NotesListView> {
  String _searchQuery = '';
  String _selectedTag = '';
  NoteType? _selectedType;
  bool _sortByDateDescending = true;
  
  List<String> get _availableTags {
    final tags = <String>{};
    for (final note in widget.notes) {
      tags.addAll(note.tags);
    }
    return tags.toList()..sort();
  }

  List<PersonalNoteData> get _filteredNotes {
    var filtered = widget.notes.where((note) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!note.title.toLowerCase().contains(query) &&
            !note.content.toLowerCase().contains(query) &&
            !note.tags.any((tag) => tag.toLowerCase().contains(query))) {
          return false;
        }
      }
      
      // Tag filter
      if (_selectedTag.isNotEmpty && !note.tags.contains(_selectedTag)) {
        return false;
      }
      
      // Type filter
      if (_selectedType != null && note.noteType != _selectedType) {
        return false;
      }
      
      return true;
    }).toList();

    // Sort
    filtered.sort((a, b) {
      if (_sortByDateDescending) {
        return b.createdAt.compareTo(a.createdAt);
      } else {
        return a.createdAt.compareTo(b.createdAt);
      }
    });

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(context),
        if (!widget.isCompact) _buildFilters(context),
        Expanded(child: _buildNotesList(context)),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.note,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Mes Notes',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                Text(
                  '${widget.notes.length} note${widget.notes.length > 1 ? 's' : ''}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _createNewNote,
            icon: const Icon(Icons.add),
            tooltip: 'Nouvelle note',
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Rechercher dans les notes...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 12),
          
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Type filter
                FilterChip(
                  label: const Text('Tous types'),
                  selected: _selectedType == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = selected ? null : _selectedType;
                    });
                  },
                ),
                const SizedBox(width: 8),
                ...NoteType.values.map((type) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(_getNoteTypeLabel(type)),
                    selected: _selectedType == type,
                    onSelected: (selected) {
                      setState(() {
                        _selectedType = selected ? type : null;
                      });
                    },
                  ),
                )),
                
                // Tag filters
                if (_availableTags.isNotEmpty) ...[
                  const SizedBox(width: 16),
                  FilterChip(
                    label: const Text('Tous tags'),
                    selected: _selectedTag.isEmpty,
                    onSelected: (selected) {
                      setState(() {
                        _selectedTag = selected ? '' : _selectedTag;
                      });
                    },
                  ),
                  const SizedBox(width: 8),
                  ..._availableTags.map((tag) => Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text('#$tag'),
                      selected: _selectedTag == tag,
                      onSelected: (selected) {
                        setState(() {
                          _selectedTag = selected ? tag : '';
                        });
                      },
                    ),
                  )),
                ],
                
                // Sort button
                const SizedBox(width: 16),
                ActionChip(
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _sortByDateDescending ? Icons.arrow_downward : Icons.arrow_upward,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      const Text('Date'),
                    ],
                  ),
                  onPressed: () {
                    setState(() {
                      _sortByDateDescending = !_sortByDateDescending;
                    });
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesList(BuildContext context) {
    final filteredNotes = _filteredNotes;
    
    if (filteredNotes.isEmpty) {
      return _buildEmptyState(context);
    }

    if (widget.isCompact) {
      return _buildCompactList(context, filteredNotes);
    } else {
      return _buildDetailedList(context, filteredNotes);
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    final hasFilters = _searchQuery.isNotEmpty || 
                      _selectedTag.isNotEmpty || 
                      _selectedType != null;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            hasFilters ? Icons.search_off : Icons.note_add,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            hasFilters ? 'Aucune note trouvée' : 'Aucune note créée',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            hasFilters 
                ? 'Essayez de modifier vos critères de recherche'
                : 'Créez votre première note personnelle',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          if (hasFilters)
            OutlinedButton(
              onPressed: _clearFilters,
              child: const Text('Effacer les filtres'),
            )
          else
            ElevatedButton.icon(
              onPressed: _createNewNote,
              icon: const Icon(Icons.add),
              label: const Text('Créer une note'),
            ),
        ],
      ),
    );
  }

  Widget _buildCompactList(BuildContext context, List<PersonalNoteData> notes) {
    return ListView.builder(
      itemCount: notes.length,
      itemBuilder: (context, index) {
        final note = notes[index];
        return _buildCompactNoteCard(context, note);
      },
    );
  }

  Widget _buildDetailedList(BuildContext context, List<PersonalNoteData> notes) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notes.length,
      itemBuilder: (context, index) {
        final note = notes[index];
        return _buildDetailedNoteCard(context, note);
      },
    );
  }

  Widget _buildCompactNoteCard(BuildContext context, PersonalNoteData note) {
    final isSelected = note.id == widget.selectedNoteId;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Material(
        color: isSelected 
            ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: () => widget.onNoteSelected?.call(note),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                _buildNoteTypeIcon(note.noteType),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        note.title,
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _formatDate(note.createdAt),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                if (note.isPrivate)
                  Icon(
                    Icons.lock,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailedNoteCard(BuildContext context, PersonalNoteData note) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildNoteCardHeader(context, note),
          _buildNoteCardContent(context, note),
          if (note.tags.isNotEmpty) _buildNoteCardTags(context, note),
          _buildNoteCardActions(context, note),
        ],
      ),
    );
  }

  Widget _buildNoteCardHeader(BuildContext context, PersonalNoteData note) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getNoteTypeColor(note.noteType).withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          _buildNoteTypeIcon(note.noteType),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        note.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _getNoteTypeColor(note.noteType),
                        ),
                      ),
                    ),
                    if (note.isPrivate)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.lock,
                              size: 12,
                              color: Colors.orange[700],
                            ),
                            const SizedBox(width: 2),
                            Text(
                              'Privé',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.orange[700],
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDate(note.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleNoteAction(value, note),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('Modifier'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy),
                    SizedBox(width: 8),
                    Text('Dupliquer'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Supprimer', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNoteCardContent(BuildContext context, PersonalNoteData note) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            note.content,
            style: Theme.of(context).textTheme.bodyMedium,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          if (note.reminderDate != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.notifications,
                    size: 16,
                    color: Colors.amber[700],
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Rappel: ${_formatDate(note.reminderDate!)}',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.amber[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNoteCardTags(BuildContext context, PersonalNoteData note) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
      child: Wrap(
        spacing: 6,
        runSpacing: 6,
        children: note.tags.map((tag) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '#$tag',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNoteCardActions(BuildContext context, PersonalNoteData note) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          TextButton.icon(
            onPressed: () => widget.onNoteSelected?.call(note),
            icon: const Icon(Icons.visibility),
            label: const Text('Voir'),
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: () => _editNote(note),
            icon: const Icon(Icons.edit),
            label: const Text('Modifier'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteTypeIcon(NoteType type) {
    IconData icon;
    Color color;
    
    switch (type) {
      case NoteType.text:
        icon = Icons.text_fields;
        color = Colors.blue;
        break;
      case NoteType.checklist:
        icon = Icons.checklist;
        color = Colors.green;
        break;
      case NoteType.reminder:
        icon = Icons.notifications;
        color = Colors.amber;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(
        icon,
        size: 18,
        color: color,
      ),
    );
  }

  Color _getNoteTypeColor(NoteType type) {
    switch (type) {
      case NoteType.text:
        return Colors.blue;
      case NoteType.checklist:
        return Colors.green;
      case NoteType.reminder:
        return Colors.amber;
    }
  }

  String _getNoteTypeLabel(NoteType type) {
    switch (type) {
      case NoteType.text:
        return 'Texte';
      case NoteType.checklist:
        return 'Liste';
      case NoteType.reminder:
        return 'Rappel';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Aujourd\'hui';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jours';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _createNewNote() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: NoteEditor(
          guideId: 'current_guide',
          sectionId: 'current_section',
          onNoteSaved: (note) {
            Navigator.of(context).pop();
            widget.onNoteCreated?.call(note);
          },
          onCancel: () => Navigator.of(context).pop(),
          isFullScreen: false,
        ),
      ),
    );
  }

  void _editNote(PersonalNoteData note) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: NoteEditor(
          initialNote: note,
          guideId: note.guideId,
          sectionId: note.sectionId,
          onNoteSaved: (updatedNote) {
            Navigator.of(context).pop();
            widget.onNoteEdited?.call(updatedNote);
          },
          onCancel: () => Navigator.of(context).pop(),
          isFullScreen: false,
        ),
      ),
    );
  }

  void _handleNoteAction(String action, PersonalNoteData note) {
    switch (action) {
      case 'edit':
        _editNote(note);
        break;
      case 'duplicate':
        _duplicateNote(note);
        break;
      case 'delete':
        _deleteNote(note);
        break;
    }
  }

  void _duplicateNote(PersonalNoteData note) {
    final duplicatedNote = PersonalNoteData(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      guideId: note.guideId,
      sectionId: note.sectionId,
      title: '${note.title} (Copie)',
      content: note.content,
      noteType: note.noteType,
      tags: List.from(note.tags),
      isPrivate: note.isPrivate,
      reminderDate: note.reminderDate,
      formattedContent: note.formattedContent,
    );
    
    widget.onNoteCreated?.call(duplicatedNote);
  }

  void _deleteNote(PersonalNoteData note) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la note'),
        content: Text('Êtes-vous sûr de vouloir supprimer "${note.title}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onNoteDeleted?.call(note.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedTag = '';
      _selectedType = null;
    });
  }

  /// Factory method to create a compact notes list
  static Widget compact({
    required List<PersonalNoteData> notes,
    Function(PersonalNoteData)? onNoteSelected,
    String? selectedNoteId,
  }) {
    return NotesListView(
      notes: notes,
      onNoteSelected: onNoteSelected,
      selectedNoteId: selectedNoteId,
      isCompact: true,
    );
  }
}