import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import '../../../models/guide/personal_note_data.dart';
import '../../../services/note_linking_service.dart';

/// Widget for managing note sharing
class NoteSharingWidget extends ConsumerStatefulWidget {
  final PersonalNoteData note;
  final bool isCompact;

  const NoteSharingWidget({
    super.key,
    required this.note,
    this.isCompact = false,
  });

  @override
  ConsumerState<NoteSharingWidget> createState() => _NoteSharingWidgetState();
}

class _NoteSharingWidgetState extends ConsumerState<NoteSharingWidget> {
  final NoteLinkingService _linkingService = NoteLinkingService();
  NoteShareInfo? _shareInfo;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadShareInfo();
  }

  Future<void> _loadShareInfo() async {
    setState(() => _isLoading = true);
    try {
      await _linkingService.initialize();
      final shareInfo = await _linkingService.getShareInfoForNote(widget.note.id);
      setState(() {
        _shareInfo = shareInfo;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (!widget.note.canBeShared) {
      return _buildPrivateNoteCard();
    }

    return widget.isCompact ? _buildCompactView() : _buildDetailedView();
  }

  Widget _buildPrivateNoteCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.lock,
              size: 48,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 8),
            Text(
              'Note privée',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Cette note est privée et ne peut pas être partagée',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactView() {
    return Card(
      child: ListTile(
        leading: Icon(
          _shareInfo != null ? Icons.share : Icons.share_outlined,
          color: _shareInfo != null 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.outline,
        ),
        title: Text(_shareInfo != null ? 'Note partagée' : 'Non partagée'),
        subtitle: _shareInfo != null
            ? Text('Partagée le ${_formatDate(_shareInfo!.createdAt)}')
            : const Text('Cliquez pour partager cette note'),
        trailing: _shareInfo != null
            ? IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: () => _showSharingOptions(),
              )
            : null,
        onTap: _shareInfo != null ? _showSharingOptions : _createShare,
      ),
    );
  }

  Widget _buildDetailedView() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.share,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Partage de note',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                if (_shareInfo != null)
                  IconButton(
                    icon: const Icon(Icons.more_vert),
                    onPressed: _showSharingOptions,
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (_shareInfo == null) ...[
              _buildNotSharedState(),
            ] else ...[
              _buildSharedState(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotSharedState() {
    return Column(
      children: [
        Icon(
          Icons.share_outlined,
          size: 48,
          color: Theme.of(context).colorScheme.outline,
        ),
        const SizedBox(height: 8),
        Text(
          'Note non partagée',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 4),
        Text(
          'Partagez cette note pour permettre à d\'autres de la consulter',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.outline,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            OutlinedButton.icon(
              onPressed: _createShare,
              icon: const Icon(Icons.link),
              label: const Text('Créer un lien'),
            ),
            ElevatedButton.icon(
              onPressed: _shareDirectly,
              icon: const Icon(Icons.share),
              label: const Text('Partager'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSharedState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Note partagée',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    Text(
                      'Créée le ${_formatDate(_shareInfo!.createdAt)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        _buildShareDetails(),
        const SizedBox(height: 16),
        _buildShareActions(),
      ],
    );
  }

  Widget _buildShareDetails() {
    return Column(
      children: [
        _buildDetailRow('Type', _shareInfo!.isPublic ? 'Public' : 'Privé'),
        if (_shareInfo!.expiresAt != null)
          _buildDetailRow('Expire le', _formatDate(_shareInfo!.expiresAt!)),
        _buildDetailRow('Commentaires', _shareInfo!.allowComments ? 'Autorisés' : 'Interdits'),
        _buildDetailRow('Édition', _shareInfo!.allowEditing ? 'Autorisée' : 'Interdite'),
        if (_shareInfo!.allowedUsers.isNotEmpty)
          _buildDetailRow('Utilisateurs autorisés', '${_shareInfo!.allowedUsers.length}'),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareActions() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        OutlinedButton.icon(
          onPressed: _copyShareLink,
          icon: const Icon(Icons.copy, size: 16),
          label: const Text('Copier le lien'),
        ),
        OutlinedButton.icon(
          onPressed: _shareDirectly,
          icon: const Icon(Icons.share, size: 16),
          label: const Text('Partager'),
        ),
        OutlinedButton.icon(
          onPressed: _editShareSettings,
          icon: const Icon(Icons.settings, size: 16),
          label: const Text('Paramètres'),
        ),
        OutlinedButton.icon(
          onPressed: _removeShare,
          icon: const Icon(Icons.link_off, size: 16),
          label: const Text('Arrêter le partage'),
          style: OutlinedButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.error,
          ),
        ),
      ],
    );
  }

  void _createShare() {
    showDialog(
      context: context,
      builder: (context) => CreateShareDialog(
        note: widget.note,
        onShareCreated: (shareInfo) {
          setState(() => _shareInfo = shareInfo);
        },
      ),
    );
  }

  void _editShareSettings() {
    if (_shareInfo == null) return;
    
    showDialog(
      context: context,
      builder: (context) => EditShareDialog(
        shareInfo: _shareInfo!,
        onShareUpdated: (shareInfo) {
          setState(() => _shareInfo = shareInfo);
        },
      ),
    );
  }

  void _copyShareLink() {
    if (_shareInfo == null) return;
    
    final shareUrl = 'https://app.example.com/shared/${_shareInfo!.shareToken}';
    Clipboard.setData(ClipboardData(text: shareUrl));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Lien copié dans le presse-papiers')),
    );
  }

  void _shareDirectly() {
    final content = '''
${widget.note.title}

${widget.note.content}

${_shareInfo != null ? '\nLien de partage: https://app.example.com/shared/${_shareInfo!.shareToken}' : ''}
''';
    
    Share.share(
      content,
      subject: 'Note partagée: ${widget.note.title}',
    );
  }

  void _removeShare() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Arrêter le partage'),
        content: const Text('Êtes-vous sûr de vouloir arrêter le partage de cette note ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              if (_shareInfo != null) {
                await _linkingService.removeShareInfo(_shareInfo!.id);
                setState(() => _shareInfo = null);
              }
            },
            child: const Text('Arrêter'),
          ),
        ],
      ),
    );
  }

  void _showSharingOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copier le lien'),
              onTap: () {
                Navigator.pop(context);
                _copyShareLink();
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Partager'),
              onTap: () {
                Navigator.pop(context);
                _shareDirectly();
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Paramètres de partage'),
              onTap: () {
                Navigator.pop(context);
                _editShareSettings();
              },
            ),
            ListTile(
              leading: const Icon(Icons.link_off),
              title: const Text('Arrêter le partage'),
              onTap: () {
                Navigator.pop(context);
                _removeShare();
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Dialog for creating a new share
class CreateShareDialog extends StatefulWidget {
  final PersonalNoteData note;
  final Function(NoteShareInfo) onShareCreated;

  const CreateShareDialog({
    super.key,
    required this.note,
    required this.onShareCreated,
  });

  @override
  State<CreateShareDialog> createState() => _CreateShareDialogState();
}

class _CreateShareDialogState extends State<CreateShareDialog> {
  final NoteLinkingService _linkingService = NoteLinkingService();
  bool _isPublic = false;
  bool _allowComments = false;
  bool _allowEditing = false;
  DateTime? _expiresAt;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Créer un partage'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SwitchListTile(
            title: const Text('Partage public'),
            subtitle: const Text('Accessible à tous avec le lien'),
            value: _isPublic,
            onChanged: (value) => setState(() => _isPublic = value),
          ),
          SwitchListTile(
            title: const Text('Autoriser les commentaires'),
            value: _allowComments,
            onChanged: (value) => setState(() => _allowComments = value),
          ),
          SwitchListTile(
            title: const Text('Autoriser l\'édition'),
            value: _allowEditing,
            onChanged: (value) => setState(() => _allowEditing = value),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _createShare,
          child: const Text('Créer'),
        ),
      ],
    );
  }

  Future<void> _createShare() async {
    try {
      await _linkingService.initialize();
      final shareToken = _linkingService.generateShareToken();
      
      final shareInfo = await _linkingService.createShareInfo(
        noteId: widget.note.id,
        shareToken: shareToken,
        isPublic: _isPublic,
        expiresAt: _expiresAt,
        allowComments: _allowComments,
        allowEditing: _allowEditing,
      );

      widget.onShareCreated(shareInfo);
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la création du partage: $e')),
        );
      }
    }
  }
}

/// Dialog for editing share settings
class EditShareDialog extends StatefulWidget {
  final NoteShareInfo shareInfo;
  final Function(NoteShareInfo) onShareUpdated;

  const EditShareDialog({
    super.key,
    required this.shareInfo,
    required this.onShareUpdated,
  });

  @override
  State<EditShareDialog> createState() => _EditShareDialogState();
}

class _EditShareDialogState extends State<EditShareDialog> {
  // Implementation would be similar to CreateShareDialog
  // but with pre-filled values from existing shareInfo
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Modifier le partage'),
      content: const Text('Fonctionnalité à implémenter'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Fermer'),
        ),
      ],
    );
  }
}
