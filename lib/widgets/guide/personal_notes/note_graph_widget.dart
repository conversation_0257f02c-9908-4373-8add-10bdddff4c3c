import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../../../models/guide/personal_note_data.dart';
import '../../../services/note_linking_service.dart';

/// Widget for visualizing note connections as a graph
class NoteGraphWidget extends ConsumerStatefulWidget {
  final List<PersonalNoteData> notes;
  final Function(String)? onNoteSelected;
  final String? highlightedNoteId;

  const NoteGraphWidget({
    super.key,
    required this.notes,
    this.onNoteSelected,
    this.highlightedNoteId,
  });

  @override
  ConsumerState<NoteGraphWidget> createState() => _NoteGraphWidgetState();
}

class _NoteGraphWidgetState extends ConsumerState<NoteGraphWidget>
    with TickerProviderStateMixin {
  final NoteLinkingService _linkingService = NoteLinkingService();
  List<NoteLink> _links = [];
  final Map<String, NoteNode> _nodes = {};
  bool _isLoading = true;
  late AnimationController _animationController;
  double _scale = 1.0;
  Offset _panOffset = Offset.zero;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _loadGraphData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadGraphData() async {
    setState(() => _isLoading = true);
    try {
      await _linkingService.initialize();
      final links = await _linkingService.getAllLinks();
      
      // Filter links to only include notes in our current list
      final noteIds = widget.notes.map((note) => note.id).toSet();
      final filteredLinks = links.where((link) =>
          noteIds.contains(link.sourceNoteId) && noteIds.contains(link.targetNoteId)).toList();
      
      _generateNodePositions(filteredLinks);
      
      setState(() {
        _links = filteredLinks;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  void _generateNodePositions(List<NoteLink> links) {
    _nodes.clear();
    
    // Create nodes for all notes
    for (int i = 0; i < widget.notes.length; i++) {
      final note = widget.notes[i];
      final angle = (2 * math.pi * i) / widget.notes.length;
      final radius = math.min(150, widget.notes.length * 20.0);
      
      _nodes[note.id] = NoteNode(
        id: note.id,
        title: note.title,
        position: Offset(
          math.cos(angle) * radius,
          math.sin(angle) * radius,
        ),
        connections: 0,
        noteType: note.noteType,
      );
    }
    
    // Count connections for each node
    for (final link in links) {
      if (_nodes.containsKey(link.sourceNoteId)) {
        _nodes[link.sourceNoteId]!.connections++;
      }
      if (_nodes.containsKey(link.targetNoteId)) {
        _nodes[link.targetNoteId]!.connections++;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_nodes.isEmpty) {
      return _buildEmptyState();
    }

    return Card(
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: GestureDetector(
              onScaleStart: (details) {
                // Handle scale start
              },
              onScaleUpdate: (details) {
                setState(() {
                  _scale = (_scale * details.scale).clamp(0.5, 3.0);
                  _panOffset += details.focalPointDelta;
                });
              },
              child: CustomPaint(
                painter: NoteGraphPainter(
                  nodes: _nodes,
                  links: _links,
                  scale: _scale,
                  panOffset: _panOffset,
                  highlightedNoteId: widget.highlightedNoteId,
                  onNodeTapped: widget.onNoteSelected,
                ),
                size: Size.infinite,
              ),
            ),
          ),
          _buildControls(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Card(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_tree_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune connexion',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Créez des liaisons entre vos notes pour voir le graphique',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.account_tree,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Graphique des liaisons',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const Spacer(),
          Text(
            '${_nodes.length} notes, ${_links.length} liaisons',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            onPressed: () {
              setState(() {
                _scale = (_scale * 1.2).clamp(0.5, 3.0);
              });
            },
            icon: const Icon(Icons.zoom_in),
            tooltip: 'Zoom avant',
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _scale = (_scale / 1.2).clamp(0.5, 3.0);
              });
            },
            icon: const Icon(Icons.zoom_out),
            tooltip: 'Zoom arrière',
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _scale = 1.0;
                _panOffset = Offset.zero;
              });
            },
            icon: const Icon(Icons.center_focus_strong),
            tooltip: 'Centrer',
          ),
          IconButton(
            onPressed: _loadGraphData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Actualiser',
          ),
        ],
      ),
    );
  }
}

/// Data class for graph nodes
class NoteNode {
  final String id;
  final String title;
  final Offset position;
  int connections;
  final NoteType noteType;

  NoteNode({
    required this.id,
    required this.title,
    required this.position,
    required this.connections,
    required this.noteType,
  });
}

/// Custom painter for the note graph
class NoteGraphPainter extends CustomPainter {
  final Map<String, NoteNode> nodes;
  final List<NoteLink> links;
  final double scale;
  final Offset panOffset;
  final String? highlightedNoteId;
  final Function(String)? onNodeTapped;

  NoteGraphPainter({
    required this.nodes,
    required this.links,
    required this.scale,
    required this.panOffset,
    this.highlightedNoteId,
    this.onNodeTapped,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Apply transformations
    canvas.save();
    canvas.translate(center.dx + panOffset.dx, center.dy + panOffset.dy);
    canvas.scale(scale);

    // Draw links first (behind nodes)
    _drawLinks(canvas);
    
    // Draw nodes
    _drawNodes(canvas);
    
    canvas.restore();
  }

  void _drawLinks(Canvas canvas) {
    final linkPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.6)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    for (final link in links) {
      final sourceNode = nodes[link.sourceNoteId];
      final targetNode = nodes[link.targetNoteId];
      
      if (sourceNode != null && targetNode != null) {
        // Draw arrow line
        canvas.drawLine(sourceNode.position, targetNode.position, linkPaint);
        
        // Draw arrow head
        _drawArrowHead(canvas, sourceNode.position, targetNode.position, linkPaint);
      }
    }
  }

  void _drawArrowHead(Canvas canvas, Offset start, Offset end, Paint paint) {
    const arrowLength = 10.0;
    const arrowAngle = math.pi / 6;
    
    final direction = (end - start).direction;
    final arrowPoint1 = end + Offset.fromDirection(direction + math.pi - arrowAngle, arrowLength);
    final arrowPoint2 = end + Offset.fromDirection(direction + math.pi + arrowAngle, arrowLength);
    
    final path = Path()
      ..moveTo(end.dx, end.dy)
      ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
      ..moveTo(end.dx, end.dy)
      ..lineTo(arrowPoint2.dx, arrowPoint2.dy);
    
    canvas.drawPath(path, paint);
  }

  void _drawNodes(Canvas canvas) {
    for (final node in nodes.values) {
      final isHighlighted = node.id == highlightedNoteId;
      final nodeRadius = _getNodeRadius(node.connections);
      
      // Node background
      final nodePaint = Paint()
        ..color = isHighlighted 
            ? Colors.blue.withValues(alpha: 0.8)
            : _getNodeColor(node.noteType).withValues(alpha: 0.7)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(node.position, nodeRadius, nodePaint);
      
      // Node border
      final borderPaint = Paint()
        ..color = isHighlighted ? Colors.blue : Colors.grey
        ..strokeWidth = isHighlighted ? 3.0 : 1.0
        ..style = PaintingStyle.stroke;
      
      canvas.drawCircle(node.position, nodeRadius, borderPaint);
      
      // Node text
      _drawNodeText(canvas, node, nodeRadius);
    }
  }

  void _drawNodeText(Canvas canvas, NoteNode node, double radius) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: node.title.length > 10 ? '${node.title.substring(0, 10)}...' : node.title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    final textOffset = Offset(
      node.position.dx - textPainter.width / 2,
      node.position.dy - textPainter.height / 2,
    );
    
    textPainter.paint(canvas, textOffset);
  }

  double _getNodeRadius(int connections) {
    return math.max(20.0, math.min(40.0, 20.0 + connections * 3.0));
  }

  Color _getNodeColor(NoteType noteType) {
    switch (noteType) {
      case NoteType.text:
        return Colors.blue;
      case NoteType.checklist:
        return Colors.green;
      case NoteType.reminder:
        return Colors.orange;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
