import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/guide/note_collaboration_data.dart';
import '../../../models/guide/personal_note_data.dart';
import '../../../services/note_collaboration_service.dart';

/// Widget for displaying and managing note comments
class NoteCommentsWidget extends ConsumerStatefulWidget {
  final PersonalNoteData note;
  final String currentUserId;
  final String currentUserName;
  final bool isCompact;

  const NoteCommentsWidget({
    super.key,
    required this.note,
    required this.currentUserId,
    required this.currentUserName,
    this.isCompact = false,
  });

  @override
  ConsumerState<NoteCommentsWidget> createState() => _NoteCommentsWidgetState();
}

class _NoteCommentsWidgetState extends ConsumerState<NoteCommentsWidget> {
  final NoteCollaborationService _collaborationService = NoteCollaborationService();
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<NoteComment> _comments = [];
  bool _isLoading = true;
  CommentType _selectedCommentType = CommentType.general;
  String? _replyingToCommentId;

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadComments() async {
    setState(() => _isLoading = true);
    try {
      await _collaborationService.initialize();
      final comments = await _collaborationService.getCommentsForNote(widget.note.id);
      setState(() {
        _comments = comments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return widget.isCompact ? _buildCompactView() : _buildDetailedView();
  }

  Widget _buildCompactView() {
    final unresolvedCount = _comments.where((c) => !c.isResolved).length;
    
    return Card(
      child: ListTile(
        leading: Icon(
          Icons.comment,
          color: unresolvedCount > 0 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.outline,
        ),
        title: Text('${_comments.length} commentaire(s)'),
        subtitle: unresolvedCount > 0
            ? Text('$unresolvedCount non résolu(s)', 
                style: TextStyle(color: Theme.of(context).colorScheme.error))
            : const Text('Tous les commentaires sont résolus'),
        trailing: IconButton(
          icon: const Icon(Icons.add_comment),
          onPressed: () => _showAddCommentDialog(),
        ),
        onTap: () => _showCommentsBottomSheet(),
      ),
    );
  }

  Widget _buildDetailedView() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            if (_comments.isEmpty) ...[
              _buildEmptyState(),
            ] else ...[
              _buildCommentsList(),
            ],
            const SizedBox(height: 16),
            _buildAddCommentSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final unresolvedCount = _comments.where((c) => !c.isResolved).length;
    
    return Row(
      children: [
        Icon(
          Icons.comment,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          'Commentaires',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const Spacer(),
        if (unresolvedCount > 0)
          Chip(
            label: Text('$unresolvedCount non résolu(s)'),
            backgroundColor: Theme.of(context).colorScheme.errorContainer,
            labelStyle: TextStyle(
              color: Theme.of(context).colorScheme.onErrorContainer,
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.comment_outlined,
            size: 48,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 8),
          Text(
            'Aucun commentaire',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Soyez le premier à commenter cette note',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsList() {
    // Group comments by thread
    final topLevelComments = _comments.where((c) => c.parentCommentId == null).toList();
    
    return SizedBox(
      height: 300,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: topLevelComments.length,
        itemBuilder: (context, index) {
          final comment = topLevelComments[index];
          return _buildCommentThread(comment);
        },
      ),
    );
  }

  Widget _buildCommentThread(NoteComment comment) {
    final replies = _comments.where((c) => c.parentCommentId == comment.id).toList();
    
    return Column(
      children: [
        _buildCommentItem(comment),
        if (replies.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.only(left: 32),
            child: Column(
              children: replies.map((reply) => _buildCommentItem(reply)).toList(),
            ),
          ),
        ],
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildCommentItem(NoteComment comment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: comment.isResolved
            ? Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)
            : Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: comment.getCommentTypeColor().withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                comment.getCommentTypeIcon(),
                size: 16,
                color: comment.getCommentTypeColor(),
              ),
              const SizedBox(width: 4),
              Text(
                comment.authorName,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _formatDate(comment.createdAt),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              const Spacer(),
              if (comment.isResolved)
                Icon(
                  Icons.check_circle,
                  size: 16,
                  color: Colors.green,
                ),
              PopupMenuButton<String>(
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'reply', child: Text('Répondre')),
                  if (!comment.isResolved)
                    const PopupMenuItem(value: 'resolve', child: Text('Résoudre')),
                  if (comment.authorId == widget.currentUserId)
                    const PopupMenuItem(value: 'edit', child: Text('Modifier')),
                  if (comment.authorId == widget.currentUserId)
                    const PopupMenuItem(value: 'delete', child: Text('Supprimer')),
                ],
                onSelected: (value) => _handleCommentAction(comment, value),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(comment.content),
          if (comment.commentType != CommentType.general) ...[
            const SizedBox(height: 4),
            Chip(
              label: Text(comment.getCommentTypeLabel()),
              backgroundColor: comment.getCommentTypeColor().withValues(alpha: 0.1),
              labelStyle: TextStyle(
                color: comment.getCommentTypeColor(),
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAddCommentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_replyingToCommentId != null) ...[
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                Text(
                  'Réponse en cours...',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => setState(() => _replyingToCommentId = null),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
        Row(
          children: [
            DropdownButton<CommentType>(
              value: _selectedCommentType,
              items: CommentType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getCommentTypeIcon(type),
                        size: 16,
                        color: _getCommentTypeColor(type),
                      ),
                      const SizedBox(width: 4),
                      Text(_getCommentTypeLabel(type)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedCommentType = value!);
              },
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: _commentController,
                decoration: const InputDecoration(
                  hintText: 'Ajouter un commentaire...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.send),
              onPressed: _addComment,
            ),
          ],
        ),
      ],
    );
  }

  void _showAddCommentDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ajouter un commentaire'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<CommentType>(
              value: _selectedCommentType,
              decoration: const InputDecoration(labelText: 'Type de commentaire'),
              items: CommentType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(_getCommentTypeLabel(type)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedCommentType = value!);
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _commentController,
              decoration: const InputDecoration(
                labelText: 'Commentaire',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _addComment();
            },
            child: const Text('Ajouter'),
          ),
        ],
      ),
    );
  }

  void _showCommentsBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                'Commentaires',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: _buildDetailedView(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _addComment() async {
    if (_commentController.text.trim().isEmpty) return;

    try {
      await _collaborationService.addComment(
        noteId: widget.note.id,
        authorId: widget.currentUserId,
        authorName: widget.currentUserName,
        content: _commentController.text.trim(),
        commentType: _selectedCommentType,
        parentCommentId: _replyingToCommentId,
      );

      _commentController.clear();
      setState(() => _replyingToCommentId = null);
      _loadComments();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'ajout du commentaire: $e')),
        );
      }
    }
  }

  void _handleCommentAction(NoteComment comment, String action) {
    switch (action) {
      case 'reply':
        setState(() => _replyingToCommentId = comment.id);
        break;
      case 'resolve':
        _resolveComment(comment);
        break;
      case 'edit':
        _editComment(comment);
        break;
      case 'delete':
        _deleteComment(comment);
        break;
    }
  }

  Future<void> _resolveComment(NoteComment comment) async {
    try {
      await _collaborationService.resolveComment(
        comment.id,
        widget.currentUserId,
        widget.currentUserName,
      );
      _loadComments();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la résolution: $e')),
        );
      }
    }
  }

  void _editComment(NoteComment comment) {
    // Implementation for editing comments
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fonctionnalité d\'édition à implémenter')),
    );
  }

  Future<void> _deleteComment(NoteComment comment) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le commentaire'),
        content: const Text('Êtes-vous sûr de vouloir supprimer ce commentaire ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _collaborationService.deleteComment(comment.id);
        _loadComments();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur lors de la suppression: $e')),
          );
        }
      }
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}j';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'maintenant';
    }
  }

  IconData _getCommentTypeIcon(CommentType type) {
    switch (type) {
      case CommentType.general:
        return Icons.comment;
      case CommentType.suggestion:
        return Icons.lightbulb_outline;
      case CommentType.question:
        return Icons.help_outline;
      case CommentType.approval:
        return Icons.check_circle_outline;
      case CommentType.correction:
        return Icons.edit_outlined;
    }
  }

  Color _getCommentTypeColor(CommentType type) {
    switch (type) {
      case CommentType.general:
        return Colors.blue;
      case CommentType.suggestion:
        return Colors.orange;
      case CommentType.question:
        return Colors.purple;
      case CommentType.approval:
        return Colors.green;
      case CommentType.correction:
        return Colors.red;
    }
  }

  String _getCommentTypeLabel(CommentType type) {
    switch (type) {
      case CommentType.general:
        return 'Commentaire';
      case CommentType.suggestion:
        return 'Suggestion';
      case CommentType.question:
        return 'Question';
      case CommentType.approval:
        return 'Approbation';
      case CommentType.correction:
        return 'Correction';
    }
  }
}
