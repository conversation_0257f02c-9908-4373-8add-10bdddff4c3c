import 'dart:async';
import 'package:flutter/material.dart';
import '../../../models/guide/personal_note_data.dart';

/// Rich note editor widget for creating and editing personal notes
class NoteEditor extends StatefulWidget {
  final PersonalNoteData? initialNote;
  final String guideId;
  final String sectionId;
  final Function(PersonalNoteData) onNoteSaved;
  final VoidCallback? onCancel;
  final bool isFullScreen;

  const NoteEditor({
    super.key,
    this.initialNote,
    required this.guideId,
    required this.sectionId,
    required this.onNoteSaved,
    this.onCancel,
    this.isFullScreen = false,
  });

  @override
  State<NoteEditor> createState() => _NoteEditorState();
}

class _NoteEditorState extends State<NoteEditor> with TickerProviderStateMixin {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  late TextEditingController _tagController;
  
  late TabController _tabController;
  
  NoteType _selectedNoteType = NoteType.text;
  List<String> _tags = [];
  bool _isPrivate = false;
  DateTime? _reminderDate;
  bool _hasUnsavedChanges = false;
  bool _isPreviewMode = false;
  
  // Auto-save
  Timer? _autoSaveTimer;
  bool _isAutoSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _tabController = TabController(length: 2, vsync: this);
    _setupAutoSave();
  }

  void _initializeControllers() {
    final note = widget.initialNote;
    
    _titleController = TextEditingController(text: note?.title ?? '');
    _contentController = TextEditingController(text: note?.content ?? '');
    _tagController = TextEditingController();
    
    if (note != null) {
      _selectedNoteType = note.noteType;
      _tags = List.from(note.tags);
      _isPrivate = note.isPrivate;
      _reminderDate = note.reminderDate;
    }
    
    // Listen for changes
    _titleController.addListener(_onContentChanged);
    _contentController.addListener(_onContentChanged);
  }

  void _setupAutoSave() {
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_hasUnsavedChanges && !_isAutoSaving) {
        _autoSave();
      }
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagController.dispose();
    _tabController.dispose();
    _autoSaveTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isFullScreen) {
      return Scaffold(
        appBar: _buildAppBar(),
        body: _buildEditorBody(),
        bottomNavigationBar: _buildBottomBar(),
      );
    }

    return Container(
      height: 500,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(child: _buildEditorBody()),
          _buildBottomBar(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.initialNote == null ? 'Nouvelle note' : 'Modifier la note'),
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: _handleCancel,
      ),
      actions: [
        if (_isAutoSaving)
          const Padding(
            padding: EdgeInsets.all(16),
            child: SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        IconButton(
          icon: Icon(_isPreviewMode ? Icons.edit : Icons.visibility),
          onPressed: () {
            setState(() => _isPreviewMode = !_isPreviewMode);
          },
          tooltip: _isPreviewMode ? 'Modifier' : 'Aperçu',
        ),
        IconButton(
          icon: const Icon(Icons.save),
          onPressed: _saveNote,
          tooltip: 'Sauvegarder',
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.note_add,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.initialNote == null ? 'Nouvelle note' : 'Modifier la note',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          IconButton(
            icon: Icon(_isPreviewMode ? Icons.edit : Icons.visibility),
            onPressed: () {
              setState(() => _isPreviewMode = !_isPreviewMode);
            },
            tooltip: _isPreviewMode ? 'Modifier' : 'Aperçu',
          ),
          if (_hasUnsavedChanges)
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.orange,
                shape: BoxShape.circle,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEditorBody() {
    return Column(
      children: [
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildContentTab(),
              _buildSettingsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: 'Contenu', icon: Icon(Icons.edit_note)),
          Tab(text: 'Options', icon: Icon(Icons.settings)),
        ],
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
        indicatorColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildContentTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildTitleField(),
          const SizedBox(height: 16),
          _buildTypeSelector(),
          const SizedBox(height: 16),
          Expanded(child: _buildContentEditor()),
        ],
      ),
    );
  }

  Widget _buildTitleField() {
    return TextField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: 'Titre de la note',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.title),
      ),
      style: Theme.of(context).textTheme.titleMedium,
      textCapitalization: TextCapitalization.sentences,
    );
  }

  Widget _buildTypeSelector() {
    return Row(
      children: [
        Text(
          'Type de note:',
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: SegmentedButton<NoteType>(
            segments: [
              ButtonSegment(
                value: NoteType.text,
                label: const Text('Texte'),
                icon: const Icon(Icons.text_fields, size: 16),
              ),
              ButtonSegment(
                value: NoteType.checklist,
                label: const Text('Liste'),
                icon: const Icon(Icons.checklist, size: 16),
              ),
              ButtonSegment(
                value: NoteType.reminder,
                label: const Text('Rappel'),
                icon: const Icon(Icons.notifications, size: 16),
              ),
            ],
            selected: {_selectedNoteType},
            onSelectionChanged: (Set<NoteType> selection) {
              setState(() => _selectedNoteType = selection.first);
              _onContentChanged();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContentEditor() {
    if (_isPreviewMode) {
      return _buildPreview();
    }

    switch (_selectedNoteType) {
      case NoteType.text:
        return _buildTextEditor();
      case NoteType.checklist:
        return _buildChecklistEditor();
      case NoteType.reminder:
        return _buildReminderEditor();
    }
  }

  Widget _buildTextEditor() {
    return Column(
      children: [
        _buildFormattingToolbar(),
        const SizedBox(height: 8),
        Expanded(
          child: TextField(
            controller: _contentController,
            decoration: const InputDecoration(
              hintText: 'Écrivez votre note ici...\n\nVous pouvez utiliser:\n• **gras**\n• *italique*\n• - puces\n• 1. numérotées',
              border: OutlineInputBorder(),
              alignLabelWithHint: true,
            ),
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildFormattingToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.format_bold, size: 18),
            onPressed: () => _insertFormatting('**', '**'),
            tooltip: 'Gras',
          ),
          IconButton(
            icon: const Icon(Icons.format_italic, size: 18),
            onPressed: () => _insertFormatting('*', '*'),
            tooltip: 'Italique',
          ),
          IconButton(
            icon: const Icon(Icons.format_list_bulleted, size: 18),
            onPressed: () => _insertFormatting('• ', ''),
            tooltip: 'Puce',
          ),
          IconButton(
            icon: const Icon(Icons.format_list_numbered, size: 18),
            onPressed: () => _insertFormatting('1. ', ''),
            tooltip: 'Numérotée',
          ),
          const Spacer(),
          Text(
            '${_contentController.text.length} caractères',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChecklistEditor() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Créez une liste de contrôle. Chaque ligne sera convertie en élément cochable.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.blue[800],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Expanded(
          child: TextField(
            controller: _contentController,
            decoration: const InputDecoration(
              hintText: 'Élément 1\nÉlément 2\nÉlément 3\n...',
              border: OutlineInputBorder(),
              alignLabelWithHint: true,
            ),
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildReminderEditor() {
    return Column(
      children: [
        _buildReminderDatePicker(),
        const SizedBox(height: 16),
        Expanded(
          child: TextField(
            controller: _contentController,
            decoration: const InputDecoration(
              labelText: 'Message du rappel',
              hintText: 'Qu\'est-ce que vous voulez vous rappeler ?',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.alarm),
            ),
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildReminderDatePicker() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.schedule, color: Colors.amber),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Date du rappel',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.amber[800],
                  ),
                ),
                Text(
                  _reminderDate?.toString().split(' ')[0] ?? 'Aucune date sélectionnée',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.amber[700],
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: _selectReminderDate,
            child: const Text('Choisir'),
          ),
        ],
      ),
    );
  }

  Widget _buildPreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_titleController.text.isNotEmpty) ...[
              Text(
                _titleController.text,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
            ],
            _buildFormattedContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildFormattedContent() {
    final content = _contentController.text;
    
    switch (_selectedNoteType) {
      case NoteType.text:
        return _buildMarkdownContent(content);
      case NoteType.checklist:
        return _buildChecklistContent(content);
      case NoteType.reminder:
        return _buildReminderContent(content);
    }
  }

  Widget _buildMarkdownContent(String content) {
    // Simple markdown rendering
    final lines = content.split('\n');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: lines.map((line) {
        if (line.startsWith('• ') || line.startsWith('- ')) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4, left: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('• '),
                Expanded(child: _formatText(line.substring(2))),
              ],
            ),
          );
        } else if (RegExp(r'^\d+\. ').hasMatch(line)) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4, left: 16),
            child: _formatText(line),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _formatText(line),
          );
        }
      }).toList(),
    );
  }

  Widget _buildChecklistContent(String content) {
    final items = content.split('\n').where((line) => line.trim().isNotEmpty);
    return Column(
      children: items.map((item) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Checkbox(
                value: false,
                onChanged: null, // Preview mode
              ),
              Expanded(child: Text(item.trim())),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildReminderContent(String content) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.notifications, color: Colors.amber),
              const SizedBox(width: 8),
              Text(
                'Rappel',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.amber[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(content),
          if (_reminderDate != null) ...[
            const SizedBox(height: 8),
            Text(
              'Date: ${_reminderDate!.toString().split(' ')[0]}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.amber[700],
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTagsSection(),
          const SizedBox(height: 24),
          _buildPrivacySection(),
          const SizedBox(height: 24),
          _buildTemplatesSection(),
        ],
      ),
    );
  }

  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _tagController,
          decoration: InputDecoration(
            hintText: 'Ajouter un tag...',
            border: const OutlineInputBorder(),
            suffixIcon: IconButton(
              icon: const Icon(Icons.add),
              onPressed: _addTag,
            ),
          ),
          onSubmitted: (_) => _addTag(),
        ),
        const SizedBox(height: 8),
        if (_tags.isNotEmpty)
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _tags.map((tag) => _buildTagChip(tag)).toList(),
          ),
      ],
    );
  }

  Widget _buildTagChip(String tag) {
    return Chip(
      label: Text(tag),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: () => _removeTag(tag),
      backgroundColor: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.5),
    );
  }

  Widget _buildPrivacySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Confidentialité',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        SwitchListTile(
          title: const Text('Note privée'),
          subtitle: const Text('Visible uniquement par vous'),
          value: _isPrivate,
          onChanged: (value) {
            setState(() => _isPrivate = value);
            _onContentChanged();
          },
        ),
      ],
    );
  }

  Widget _buildTemplatesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Modèles rapides',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildTemplateChip('Résumé de section', _applyResumeTemplate),
            _buildTemplateChip('Questions importantes', _applyQuestionsTemplate),
            _buildTemplateChip('Formules clés', _applyFormulasTemplate),
            _buildTemplateChip('Points à retenir', _applyKeyPointsTemplate),
          ],
        ),
      ],
    );
  }

  Widget _buildTemplateChip(String label, VoidCallback onTap) {
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor: Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.5),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: widget.isFullScreen 
            ? null 
            : const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
      ),
      child: Row(
        children: [
          if (_isAutoSaving) ...[
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            Text(
              'Sauvegarde automatique...',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ] else if (_hasUnsavedChanges) ...[
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.orange,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Modifications non sauvegardées',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.orange,
              ),
            ),
          ],
          const Spacer(),
          if (widget.onCancel != null)
            TextButton(
              onPressed: _handleCancel,
              child: const Text('Annuler'),
            ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: _saveNote,
            child: const Text('Sauvegarder'),
          ),
        ],
      ),
    );
  }

  // Helper methods
  void _onContentChanged() {
    if (!_hasUnsavedChanges) {
      setState(() => _hasUnsavedChanges = true);
    }
  }

  void _insertFormatting(String start, String end) {
    final selection = _contentController.selection;
    final text = _contentController.text;
    
    if (selection.isValid) {
      final selectedText = text.substring(selection.start, selection.end);
      final newText = '$start$selectedText$end';
      
      _contentController.text = text.replaceRange(
        selection.start,
        selection.end,
        newText,
      );
      
      _contentController.selection = TextSelection.collapsed(
        offset: selection.start + start.length + selectedText.length + end.length,
      );
    }
    
    _onContentChanged();
  }

  Text _formatText(String text) {
    // Simple text formatting
    if (text.contains('**')) {
      final parts = text.split('**');
      return Text.rich(
        TextSpan(
          children: parts.asMap().entries.map((entry) {
            final isOdd = entry.key % 2 == 1;
            return TextSpan(
              text: entry.value,
              style: isOdd ? const TextStyle(fontWeight: FontWeight.bold) : null,
            );
          }).toList(),
        ),
      );
    } else if (text.contains('*')) {
      final parts = text.split('*');
      return Text.rich(
        TextSpan(
          children: parts.asMap().entries.map((entry) {
            final isOdd = entry.key % 2 == 1;
            return TextSpan(
              text: entry.value,
              style: isOdd ? const TextStyle(fontStyle: FontStyle.italic) : null,
            );
          }).toList(),
        ),
      );
    }
    
    return Text(text);
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
      });
      _onContentChanged();
    }
  }

  void _removeTag(String tag) {
    setState(() => _tags.remove(tag));
    _onContentChanged();
  }

  void _selectReminderDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _reminderDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() => _reminderDate = date);
      _onContentChanged();
    }
  }

  void _applyResumeTemplate() {
    _contentController.text = '''**Résumé de la section**

**Points clés:**
• 
• 
• 

**Concepts importants:**
• 
• 

**Formules à retenir:**
• 

**Questions pour révision:**
• 
• ''';
    _onContentChanged();
  }

  void _applyQuestionsTemplate() {
    _contentController.text = '''**Questions importantes**

**À clarifier:**
• 
• 

**Pour approfondir:**
• 
• 

**Exercices à faire:**
• 
• ''';
    _onContentChanged();
  }

  void _applyFormulasTemplate() {
    _contentController.text = '''**Formules clés**

**Formule 1:**
• Expression: 
• Quand l'utiliser: 
• Exemple: 

**Formule 2:**
• Expression: 
• Quand l'utiliser: 
• Exemple: ''';
    _onContentChanged();
  }

  void _applyKeyPointsTemplate() {
    _contentController.text = '''**Points à retenir**

**⭐ Très important:**
• 
• 

**💡 Bon à savoir:**
• 
• 

**⚠️ Attention:**
• 
• ''';
    _onContentChanged();
  }

  void _autoSave() async {
    if (_titleController.text.trim().isEmpty) return;
    
    setState(() => _isAutoSaving = true);
    
    try {
      final note = _createNoteData();
      // Save to local storage or service
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate save
      setState(() {
        _hasUnsavedChanges = false;
        _isAutoSaving = false;
      });
    } catch (e) {
      setState(() => _isAutoSaving = false);
      debugPrint('Auto-save failed: $e');
    }
  }

  void _saveNote() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez saisir un titre pour la note')),
      );
      return;
    }

    final note = _createNoteData();
    widget.onNoteSaved(note);
    
    setState(() => _hasUnsavedChanges = false);
  }

  PersonalNoteData _createNoteData() {
    return PersonalNoteData(
      id: widget.initialNote?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      guideId: widget.guideId,
      sectionId: widget.sectionId,
      title: _titleController.text.trim(),
      content: _contentController.text.trim(),
      noteType: _selectedNoteType,
      tags: _tags,
      isPrivate: _isPrivate,
      reminderDate: _reminderDate,
      formattedContent: _contentController.text.trim(),
    );
  }

  void _handleCancel() {
    if (_hasUnsavedChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Modifications non sauvegardées'),
          content: const Text(
            'Vous avez des modifications non sauvegardées. Voulez-vous les perdre ?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Continuer à modifier'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (widget.onCancel != null) {
                  widget.onCancel!();
                } else {
                  Navigator.of(context).pop();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Perdre les modifications'),
            ),
          ],
        ),
      );
    } else {
      if (widget.onCancel != null) {
        widget.onCancel!();
      } else {
        Navigator.of(context).pop();
      }
    }
  }
}