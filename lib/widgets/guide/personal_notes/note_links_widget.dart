import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/guide/personal_note_data.dart';
import '../../../services/note_linking_service.dart';

/// Widget for displaying and managing note links
class NoteLinksWidget extends ConsumerStatefulWidget {
  final PersonalNoteData note;
  final Function(String)? onNoteSelected;
  final bool isCompact;

  const NoteLinksWidget({
    super.key,
    required this.note,
    this.onNoteSelected,
    this.isCompact = false,
  });

  @override
  ConsumerState<NoteLinksWidget> createState() => _NoteLinksWidgetState();
}

class _NoteLinksWidgetState extends ConsumerState<NoteLinksWidget> {
  final NoteLinkingService _linkingService = NoteLinkingService();
  List<NoteLink> _outgoingLinks = [];
  List<NoteLink> _incomingLinks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLinks();
  }

  Future<void> _loadLinks() async {
    setState(() => _isLoading = true);
    try {
      await _linkingService.initialize();
      final outgoing = await _linkingService.getOutgoingLinks(widget.note.id);
      final incoming = await _linkingService.getIncomingLinks(widget.note.id);
      
      setState(() {
        _outgoingLinks = outgoing;
        _incomingLinks = incoming;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_outgoingLinks.isEmpty && _incomingLinks.isEmpty) {
      return _buildEmptyState();
    }

    return widget.isCompact ? _buildCompactView() : _buildDetailedView();
  }

  Widget _buildEmptyState() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.link_off,
              size: 48,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 8),
            Text(
              'Aucune liaison',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Cette note n\'est liée à aucune autre note',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              onPressed: () => _showAddLinkDialog(),
              icon: const Icon(Icons.add_link),
              label: const Text('Ajouter une liaison'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactView() {
    final totalLinks = _outgoingLinks.length + _incomingLinks.length;
    
    return Card(
      child: ListTile(
        leading: Icon(
          Icons.link,
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text('$totalLinks liaison(s)'),
        subtitle: Text(
          '${_outgoingLinks.length} sortantes, ${_incomingLinks.length} entrantes',
        ),
        trailing: IconButton(
          icon: const Icon(Icons.add_link),
          onPressed: () => _showAddLinkDialog(),
        ),
        onTap: () => _showLinksBottomSheet(),
      ),
    );
  }

  Widget _buildDetailedView() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.link,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Liaisons de notes',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.add_link),
                  onPressed: () => _showAddLinkDialog(),
                  tooltip: 'Ajouter une liaison',
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_outgoingLinks.isNotEmpty) ...[
              _buildLinkSection('Liens sortants', _outgoingLinks, true),
              const SizedBox(height: 16),
            ],
            if (_incomingLinks.isNotEmpty) ...[
              _buildLinkSection('Liens entrants', _incomingLinks, false),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLinkSection(String title, List<NoteLink> links, bool isOutgoing) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 8),
        ...links.map((link) => _buildLinkItem(link, isOutgoing)),
      ],
    );
  }

  Widget _buildLinkItem(NoteLink link, bool isOutgoing) {
    final targetNoteId = isOutgoing ? link.targetNoteId : link.sourceNoteId;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        dense: true,
        leading: _buildLinkTypeIcon(link.linkType),
        title: Text(
          targetNoteId, // In a real app, you'd fetch the note title
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        subtitle: link.description != null
            ? Text(
                link.description!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
              )
            : null,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Chip(
              label: Text(
                link.getLinkTypeLabel(),
                style: Theme.of(context).textTheme.labelSmall,
              ),
              backgroundColor: _getLinkTypeColor(link.linkType).withValues(alpha: 0.1),
              side: BorderSide(
                color: _getLinkTypeColor(link.linkType),
                width: 1,
              ),
            ),
            IconButton(
              icon: const Icon(Icons.delete_outline, size: 16),
              onPressed: () => _confirmDeleteLink(link),
            ),
          ],
        ),
        onTap: () => widget.onNoteSelected?.call(targetNoteId),
      ),
    );
  }

  Widget _buildLinkTypeIcon(NoteLinkType linkType) {
    IconData iconData;
    Color color;
    
    switch (linkType) {
      case NoteLinkType.reference:
        iconData = Icons.link;
        color = Colors.blue;
        break;
      case NoteLinkType.related:
        iconData = Icons.connect_without_contact;
        color = Colors.green;
        break;
      case NoteLinkType.followUp:
        iconData = Icons.arrow_forward;
        color = Colors.orange;
        break;
      case NoteLinkType.prerequisite:
        iconData = Icons.arrow_back;
        color = Colors.red;
        break;
      case NoteLinkType.child:
        iconData = Icons.subdirectory_arrow_right;
        color = Colors.purple;
        break;
      case NoteLinkType.parent:
        iconData = Icons.subdirectory_arrow_left;
        color = Colors.indigo;
        break;
    }
    
    return Icon(iconData, color: color, size: 20);
  }

  Color _getLinkTypeColor(NoteLinkType linkType) {
    switch (linkType) {
      case NoteLinkType.reference:
        return Colors.blue;
      case NoteLinkType.related:
        return Colors.green;
      case NoteLinkType.followUp:
        return Colors.orange;
      case NoteLinkType.prerequisite:
        return Colors.red;
      case NoteLinkType.child:
        return Colors.purple;
      case NoteLinkType.parent:
        return Colors.indigo;
    }
  }

  void _showAddLinkDialog() {
    showDialog(
      context: context,
      builder: (context) => AddNoteLinkDialog(
        sourceNoteId: widget.note.id,
        onLinkCreated: (link) {
          _loadLinks();
        },
      ),
    );
  }

  void _showLinksBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                'Liaisons de notes',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: _buildDetailedView(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _confirmDeleteLink(NoteLink link) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la liaison'),
        content: const Text('Êtes-vous sûr de vouloir supprimer cette liaison ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _linkingService.removeLink(link.id);
              _loadLinks();
            },
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}

/// Dialog for adding a new note link
class AddNoteLinkDialog extends StatefulWidget {
  final String sourceNoteId;
  final Function(NoteLink) onLinkCreated;

  const AddNoteLinkDialog({
    super.key,
    required this.sourceNoteId,
    required this.onLinkCreated,
  });

  @override
  State<AddNoteLinkDialog> createState() => _AddNoteLinkDialogState();
}

class _AddNoteLinkDialogState extends State<AddNoteLinkDialog> {
  final _formKey = GlobalKey<FormState>();
  final _targetNoteController = TextEditingController();
  final _descriptionController = TextEditingController();
  NoteLinkType _selectedLinkType = NoteLinkType.reference;
  final NoteLinkingService _linkingService = NoteLinkingService();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Ajouter une liaison'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _targetNoteController,
              decoration: const InputDecoration(
                labelText: 'ID de la note cible',
                hintText: 'Entrez l\'ID de la note à lier',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez entrer un ID de note';
                }
                if (value == widget.sourceNoteId) {
                  return 'Une note ne peut pas se lier à elle-même';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<NoteLinkType>(
              value: _selectedLinkType,
              decoration: const InputDecoration(
                labelText: 'Type de liaison',
              ),
              items: NoteLinkType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(_getLinkTypeLabel(type)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedLinkType = value!);
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (optionnel)',
                hintText: 'Décrivez la relation entre les notes',
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _createLink,
          child: const Text('Créer'),
        ),
      ],
    );
  }

  String _getLinkTypeLabel(NoteLinkType type) {
    switch (type) {
      case NoteLinkType.reference:
        return 'Référence';
      case NoteLinkType.related:
        return 'Lié';
      case NoteLinkType.followUp:
        return 'Suivi';
      case NoteLinkType.prerequisite:
        return 'Prérequis';
      case NoteLinkType.child:
        return 'Sous-note';
      case NoteLinkType.parent:
        return 'Note parent';
    }
  }

  Future<void> _createLink() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      await _linkingService.initialize();
      final link = await _linkingService.createLink(
        sourceNoteId: widget.sourceNoteId,
        targetNoteId: _targetNoteController.text.trim(),
        linkType: _selectedLinkType,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      widget.onLinkCreated(link);
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la création de la liaison: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _targetNoteController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
