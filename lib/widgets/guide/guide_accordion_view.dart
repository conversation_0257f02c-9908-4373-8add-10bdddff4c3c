import 'package:flutter/material.dart';
import '../../models/guide/guide_section_data.dart';
import '../../models/guide/guide_progress_data.dart';
import '../../services/guide_progress_service.dart';
import 'guide_progress_indicator.dart';

/// Main accordion widget for guide sections
class GuideAccordionView extends StatefulWidget {
  final List<GuideSectionData> sections;
  final String guideId;
  final Function(String sectionId)? onSectionCompleted;
  final Function(String sectionId, double scrollProgress)? onScrollProgress;
  final String? initialExpandedSection;
  final bool allowMultipleExpanded;
  final EdgeInsetsGeometry padding;

  const GuideAccordionView({
    super.key,
    required this.sections,
    required this.guideId,
    this.onSectionCompleted,
    this.onScrollProgress,
    this.initialExpandedSection,
    this.allowMultipleExpanded = false,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  State<GuideAccordionView> createState() => GuideAccordionViewState();
}

class GuideAccordionViewState extends State<GuideAccordionView> {
  final GuideProgressService _progressService = GuideProgressService();
  final Map<String, ScrollController> _scrollControllers = {};
  final Map<String, GuideProgressData?> _sectionProgress = {};
  
  String? _expandedSection;
  final Set<String> _expandedSections = {};

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadProgress();
    
    // Set initial expanded section
    if (widget.initialExpandedSection != null) {
      if (widget.allowMultipleExpanded) {
        _expandedSections.add(widget.initialExpandedSection!);
      } else {
        _expandedSection = widget.initialExpandedSection;
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _scrollControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeControllers() {
    for (final section in widget.sections) {
      _scrollControllers[section.id] = ScrollController();
    }
  }

  Future<void> _loadProgress() async {
    for (final section in widget.sections) {
      final progress = await _progressService.getSectionProgress(
        widget.guideId, 
        section.id,
      );
      if (mounted) {
        setState(() {
          _sectionProgress[section.id] = progress;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.sections.isEmpty) {
      return _buildEmptyState(context);
    }

    return Padding(
      padding: widget.padding,
      child: widget.allowMultipleExpanded
          ? _buildMultipleExpansionView()
          : _buildSingleExpansionView(),
    );
  }

  Widget _buildSingleExpansionView() {
    return ExpansionPanelList.radio(
      elevation: 2,
      expandedHeaderPadding: const EdgeInsets.all(0),
      children: widget.sections.map((section) {
        return ExpansionPanelRadio(
          value: section.id,
          headerBuilder: (context, isExpanded) {
            return _buildSectionHeader(section, isExpanded);
          },
          body: _buildSectionBody(section),
          canTapOnHeader: true,
        );
      }).toList(),
    );
  }

  Widget _buildMultipleExpansionView() {
    return ExpansionPanelList(
      elevation: 2,
      expandedHeaderPadding: const EdgeInsets.all(0),
      children: widget.sections.map((section) {
        final isExpanded = _expandedSections.contains(section.id);
        return ExpansionPanel(
          headerBuilder: (context, isExpanded) {
            return _buildSectionHeader(section, isExpanded);
          },
          body: _buildSectionBody(section),
          isExpanded: isExpanded,
          canTapOnHeader: true,
        );
      }).toList(),
      expansionCallback: (panelIndex, isExpanded) {
        setState(() {
          final sectionId = widget.sections[panelIndex].id;
          if (isExpanded) {
            _expandedSections.remove(sectionId);
          } else {
            _expandedSections.add(sectionId);
            _trackSectionView(sectionId);
          }
        });
      },
    );
  }

  Widget _buildSectionHeader(GuideSectionData section, bool isExpanded) {
    final theme = Theme.of(context);
    final progress = _sectionProgress[section.id];
    final isCompleted = progress?.isCompleted ?? false;
    final progressValue = progress?.completionPercentage ?? 0.0;

    return ListTile(
      leading: CompactProgressIndicator(
        progress: progressValue,
        isCompleted: isCompleted,
      ),
      title: Text(
        section.title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: isCompleted 
              ? theme.colorScheme.primary 
              : theme.colorScheme.onSurface,
        ),
      ),
      subtitle: Row(
        children: [
          Icon(
            _getTypeIcon(section.type),
            size: 16,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Text(
            '${section.estimatedReadTime} min',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          if (isCompleted) ...[
            const SizedBox(width: 8),
            Icon(
              Icons.check_circle,
              size: 16,
              color: theme.colorScheme.primary,
            ),
          ],
        ],
      ),
      trailing: Icon(
        isExpanded ? Icons.expand_less : Icons.expand_more,
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildSectionBody(GuideSectionData section) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (section.customWidget != null)
            section.customWidget!
          else
            _buildDefaultSectionContent(section),
          const SizedBox(height: 16),
          _buildSectionActions(section),
        ],
      ),
    );
  }

  Widget _buildDefaultSectionContent(GuideSectionData section) {
    final theme = Theme.of(context);
    
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        _handleScrollNotification(notification, section.id);
        return false;
      },
      child: SingleChildScrollView(
        controller: _scrollControllers[section.id],
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (section.content.isNotEmpty) ...[
              Text(
                section.content,
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
            ],
            if (section.items.isNotEmpty) ...[
              ...section.items.map((item) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 6,
                      height: 6,
                      margin: const EdgeInsets.only(top: 8, right: 12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        item,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              )),
              const SizedBox(height: 16),
            ],
            if (section.subsections.isNotEmpty) ...[
              ...section.subsections.map((subsection) => 
                _buildSubsection(subsection)
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSubsection(GuideSubsection subsection) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              subsection.title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (subsection.content.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                subsection.content,
                style: theme.textTheme.bodyMedium,
              ),
            ],
            if (subsection.items.isNotEmpty) ...[
              const SizedBox(height: 8),
              ...subsection.items.map((item) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  '• $item',
                  style: theme.textTheme.bodyMedium,
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSectionActions(GuideSectionData section) {
    final theme = Theme.of(context);
    final progress = _sectionProgress[section.id];
    final isCompleted = progress?.isCompleted ?? false;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (!isCompleted)
          ElevatedButton.icon(
            onPressed: () => _markSectionCompleted(section.id),
            icon: const Icon(Icons.check),
            label: const Text('Marquer comme terminé'),
          )
        else
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Section terminée',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        if (progress?.formattedTimeSpent != null)
          Text(
            'Temps: ${progress!.formattedTimeSpent}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.menu_book_outlined,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucune section disponible',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  void _handleScrollNotification(ScrollNotification notification, String sectionId) {
    if (notification is ScrollUpdateNotification) {
      final controller = _scrollControllers[sectionId];
      if (controller != null && controller.hasClients) {
        final maxScroll = controller.position.maxScrollExtent;
        final currentScroll = controller.position.pixels;
        final scrollProgress = maxScroll > 0 ? (currentScroll / maxScroll).clamp(0.0, 1.0) : 1.0;
        
        widget.onScrollProgress?.call(sectionId, scrollProgress);
        
        // Auto-complete when 90% scrolled
        if (scrollProgress >= 0.9) {
          _updateSectionProgress(sectionId, scrollProgress: scrollProgress, markCompleted: true);
        } else {
          _updateSectionProgress(sectionId, scrollProgress: scrollProgress);
        }
      }
    }
  }

  void _trackSectionView(String sectionId) {
    _progressService.updateSectionProgress(
      widget.guideId,
      sectionId,
      additionalTimeSpent: 0,
    );
  }

  void _updateSectionProgress(String sectionId, {double? scrollProgress, bool? markCompleted}) {
    _progressService.updateSectionProgress(
      widget.guideId,
      sectionId,
      scrollProgress: scrollProgress,
      markCompleted: markCompleted,
    );
  }

  void _markSectionCompleted(String sectionId) {
    _progressService.markSectionCompleted(widget.guideId, sectionId);
    widget.onSectionCompleted?.call(sectionId);
    _loadProgress(); // Refresh progress display
  }

  IconData _getTypeIcon(GuideSectionType type) {
    switch (type) {
      case GuideSectionType.list:
        return Icons.list;
      case GuideSectionType.calculator:
        return Icons.calculate;
      case GuideSectionType.example:
        return Icons.lightbulb_outline;
      case GuideSectionType.definition:
        return Icons.book;
      case GuideSectionType.formula:
        return Icons.functions;
      case GuideSectionType.table:
        return Icons.table_chart;
      case GuideSectionType.widget:
        return Icons.widgets;
      default:
        return Icons.article;
    }
  }

  /// Expands a specific section (useful for search results)
  void expandSection(String sectionId) {
    setState(() {
      if (widget.allowMultipleExpanded) {
        _expandedSections.add(sectionId);
      } else {
        _expandedSection = sectionId;
      }
    });
    
    // Scroll to section after a brief delay
    Future.delayed(const Duration(milliseconds: 300), () {
      final controller = _scrollControllers[sectionId];
      if (controller != null && controller.hasClients) {
        controller.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }
}
