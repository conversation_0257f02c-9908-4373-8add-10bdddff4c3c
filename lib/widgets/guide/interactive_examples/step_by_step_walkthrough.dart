import 'package:flutter/material.dart';
import '../../../models/guide/interactive_step_data.dart';
import '../../../services/guide_progress_service.dart';

/// Main interactive walkthrough widget that guides users through complex accounting concepts
class StepByStepWalkthrough extends StatefulWidget {
  final List<InteractiveStepData> steps;
  final String walkthroughId;
  final String title;
  final VoidCallback? onComplete;
  final Function(int)? onStepChanged;

  const StepByStepWalkthrough({
    super.key,
    required this.steps,
    required this.walkthroughId,
    required this.title,
    this.onComplete,
    this.onStepChanged,
  });

  @override
  State<StepByStepWalkthrough> createState() => _StepByStepWalkthroughState();
}

class _StepByStepWalkthroughState extends State<StepByStepWalkthrough>
    with TickerProviderStateMixin {
  late AnimationController _progressAnimationController;
  late AnimationController _stepAnimationController;
  late Animation<double> _progressAnimation;
  late Animation<Offset> _stepSlideAnimation;
  
  int _currentStepIndex = 0;
  final Map<String, dynamic> _userInputs = {};
  final Map<String, int> _attemptCounts = {};
  bool _isStepComplete = false;
  String? _feedbackMessage;
  bool _showHint = false;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeAttemptCounts();
  }
  
  void _initializeAnimations() {
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    
    _stepSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _stepAnimationController,
      curve: Curves.easeOutCubic,
    ));
    
    _stepAnimationController.forward();
  }
  
  void _initializeAttemptCounts() {
    for (final step in widget.steps) {
      _attemptCounts[step.stepId] = 0;
    }
  }
  
  @override
  void dispose() {
    _progressAnimationController.dispose();
    _stepAnimationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    if (widget.steps.isEmpty) {
      return _buildEmptyState();
    }
    
    final currentStep = widget.steps[_currentStepIndex];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildProgressIndicator(),
          const SizedBox(height: 24),
          _buildStepContent(currentStep),
          const SizedBox(height: 16),
          if (_feedbackMessage != null) _buildFeedback(),
          if (_showHint) _buildHint(currentStep),
          const SizedBox(height: 16),
          _buildNavigationControls(currentStep),
        ],
      ),
    );
  }
  
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.help_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucune étape disponible',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ce guide interactif n\'a pas encore été configuré.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.school,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              Text(
                'Guide interactif étape par étape',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            _showWalkthroughHelp(context);
          },
          icon: Icon(
            Icons.help_outline,
            color: Theme.of(context).colorScheme.primary,
          ),
          tooltip: 'Aide',
        ),
      ],
    );
  }
  
  Widget _buildProgressIndicator() {
    final progress = widget.steps.isEmpty ? 0.0 : (_currentStepIndex + 1) / widget.steps.length;
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Étape ${_currentStepIndex + 1} sur ${widget.steps.length}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return LinearProgressIndicator(
              value: progress * _progressAnimation.value,
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
              minHeight: 6,
            );
          },
        ),
      ],
    );
  }
  
  Widget _buildStepContent(InteractiveStepData step) {
    return SlideTransition(
      position: _stepSlideAnimation,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getStepTypeIcon(step.stepType),
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      step.getStepTypeLabel(),
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  step.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
                if (step.description.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    step.description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (step.content.isNotEmpty) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                step.content,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(height: 16),
          ],
          _buildInteractionWidget(step),
        ],
      ),
    );
  }
  
  Widget _buildInteractionWidget(InteractiveStepData step) {
    switch (step.interactionType) {
      case InteractionType.input:
        return _buildInputWidget(step);
      case InteractionType.select:
        return _buildSelectionWidget(step);
      case InteractionType.calculate:
        return _buildCalculationWidget(step);
      case InteractionType.tap:
        return _buildTapWidget(step);
      default:
        return _buildDefaultInteractionWidget(step);
    }
  }
  
  Widget _buildInputWidget(InteractiveStepData step) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Votre réponse:',
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          key: ValueKey('input_${step.stepId}'),
          onChanged: (value) {
            _userInputs[step.stepId] = value;
            _validateCurrentStep();
          },
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            hintText: 'Entrez votre réponse...',
            suffixIcon: _isStepComplete
                ? Icon(
                    Icons.check_circle,
                    color: Colors.green,
                  )
                : null,
          ),
          keyboardType: step.expectedInput is num
              ? TextInputType.number
              : TextInputType.text,
        ),
      ],
    );
  }
  
  Widget _buildSelectionWidget(InteractiveStepData step) {
    final options = step.visualElements['options'] as List<dynamic>? ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choisissez la bonne réponse:',
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...options.asMap().entries.map((entry) {
          final index = entry.key;
          final option = entry.value.toString();
          final isSelected = _userInputs[step.stepId] == option;
          
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap: () {
                setState(() {
                  _userInputs[step.stepId] = option;
                });
                _validateCurrentStep();
              },
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primaryContainer
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.transparent,
                        border: Border.all(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.outline,
                        ),
                      ),
                      child: isSelected
                          ? Icon(
                              Icons.check,
                              size: 14,
                              color: Theme.of(context).colorScheme.onPrimary,
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        option,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isSelected
                              ? Theme.of(context).colorScheme.onPrimaryContainer
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ],
    );
  }
  
  Widget _buildCalculationWidget(InteractiveStepData step) {
    final formula = step.visualElements['formula'] as String? ?? '';
    final variables = step.visualElements['variables'] as Map<String, dynamic>? ?? {};
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (formula.isNotEmpty) ...[
          Text(
            'Formule:',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              formula,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontFamily: 'monospace',
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16),
        ],
        Text(
          'Calculez le résultat:',
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          key: ValueKey('calculation_${step.stepId}'),
          onChanged: (value) {
            _userInputs[step.stepId] = value;
            _validateCurrentStep();
          },
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'Entrez le résultat du calcul...',
            prefixIcon: Icon(Icons.calculate),
          ),
          keyboardType: TextInputType.number,
        ),
      ],
    );
  }
  
  Widget _buildTapWidget(InteractiveStepData step) {
    return Center(
      child: ElevatedButton.icon(
        onPressed: () {
          setState(() {
            _userInputs[step.stepId] = 'tapped';
            _isStepComplete = true;
          });
        },
        icon: const Icon(Icons.touch_app),
        label: Text(step.getInteractionTypeLabel()),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    );
  }
  
  Widget _buildDefaultInteractionWidget(InteractiveStepData step) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            size: 48,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 8),
          Text(
            'Lisez attentivement le contenu ci-dessus',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _userInputs[step.stepId] = 'read';
                _isStepComplete = true;
              });
            },
            child: const Text('J\'ai compris'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeedback() {
    final isSuccess = _isStepComplete;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isSuccess
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSuccess ? Colors.green : Colors.red,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            color: isSuccess ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _feedbackMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isSuccess ? Colors.green[800] : Colors.red[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildHint(InteractiveStepData step) {
    final attemptCount = _attemptCounts[step.stepId] ?? 0;
    final hint = step.getHint(attemptCount);
    
    if (hint == null) return const SizedBox.shrink();
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.amber,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: Colors.amber[700],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Indice:',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.amber[800],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  hint,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.amber[800],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNavigationControls(InteractiveStepData step) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Previous button
        TextButton.icon(
          onPressed: _currentStepIndex > 0 ? _goToPreviousStep : null,
          icon: const Icon(Icons.arrow_back),
          label: const Text('Précédent'),
        ),
        
        // Hint button
        if (!_isStepComplete && step.hints.isNotEmpty)
          TextButton.icon(
            onPressed: () {
              setState(() {
                _showHint = !_showHint;
              });
            },
            icon: Icon(_showHint ? Icons.lightbulb : Icons.lightbulb_outline),
            label: Text(_showHint ? 'Masquer l\'indice' : 'Voir un indice'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.amber[700],
            ),
          ),
        
        // Next button
        ElevatedButton.icon(
          onPressed: _isStepComplete ? _goToNextStep : _validateCurrentStep,
          icon: Icon(_isStepComplete ? Icons.arrow_forward : Icons.check),
          label: Text(_isStepComplete 
              ? (_currentStepIndex >= widget.steps.length - 1 ? 'Terminer' : 'Suivant')
              : 'Valider'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
      ],
    );
  }
  
  void _validateCurrentStep() {
    if (_currentStepIndex >= widget.steps.length) return;
    
    final step = widget.steps[_currentStepIndex];
    final userInput = _userInputs[step.stepId];
    
    if (userInput == null) {
      setState(() {
        _isStepComplete = false;
        _feedbackMessage = null;
      });
      return;
    }
    
    final validationResult = step.validateUserInput(userInput);
    final attemptCount = _attemptCounts[step.stepId] ?? 0;
    
    setState(() {
      _isStepComplete = validationResult.isValid;
      _feedbackMessage = validationResult.message;
      
      if (!validationResult.isValid) {
        _attemptCounts[step.stepId] = attemptCount + 1;
        
        // Show hint automatically after 2 failed attempts
        if (attemptCount >= 1 && step.hints.isNotEmpty) {
          _showHint = true;
        }
      } else {
        _showHint = false;
      }
    });
    
    if (_isStepComplete) {
      _progressAnimationController.forward();
    }
  }
  
  void _goToNextStep() {
    if (_currentStepIndex >= widget.steps.length - 1) {
      // Walkthrough complete
      widget.onComplete?.call();
      return;
    }
    
    setState(() {
      _currentStepIndex++;
      _isStepComplete = false;
      _feedbackMessage = null;
      _showHint = false;
    });
    
    widget.onStepChanged?.call(_currentStepIndex);
    
    _stepAnimationController.reset();
    _stepAnimationController.forward();
    _progressAnimationController.reset();
  }
  
  void _goToPreviousStep() {
    if (_currentStepIndex <= 0) return;
    
    setState(() {
      _currentStepIndex--;
      _isStepComplete = _userInputs.containsKey(widget.steps[_currentStepIndex].stepId);
      _feedbackMessage = null;
      _showHint = false;
    });
    
    widget.onStepChanged?.call(_currentStepIndex);
    
    _stepAnimationController.reset();
    _stepAnimationController.forward();
  }
  
  IconData _getStepTypeIcon(StepType stepType) {
    switch (stepType) {
      case StepType.explanation:
        return Icons.menu_book;
      case StepType.input:
        return Icons.edit;
      case StepType.calculation:
        return Icons.calculate;
      case StepType.validation:
        return Icons.verified;
      case StepType.selection:
        return Icons.checklist;
    }
  }
  
  void _showWalkthroughHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aide - Guide Interactif'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Ce guide vous accompagne étape par étape dans l\'apprentissage.'),
            SizedBox(height: 16),
            Text('Instructions:', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('• Lisez attentivement chaque étape'),
            Text('• Complétez les exercices demandés'),
            Text('• Validez vos réponses avant de continuer'),
            Text('• Utilisez les indices si nécessaire'),
            Text('• Prenez votre temps pour bien comprendre'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Compris'),
          ),
        ],
      ),
    );
  }
}