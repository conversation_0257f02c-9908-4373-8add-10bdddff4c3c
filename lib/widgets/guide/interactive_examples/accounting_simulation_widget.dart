import 'package:flutter/material.dart';
import '../../../theme/semantic_colors.dart';
import 'step_by_step_walkthrough.dart';

/// Interactive accounting simulation widget for hands-on practice
class AccountingSimulationWidget extends StatefulWidget {
  final String simulationId;
  final String title;
  final Map<String, dynamic> scenarioData;
  final VoidCallback? onComplete;

  const AccountingSimulationWidget({
    super.key,
    required this.simulationId,
    required this.title,
    required this.scenarioData,
    this.onComplete,
  });

  @override
  State<AccountingSimulationWidget> createState() => _AccountingSimulationWidgetState();
}

class _AccountingSimulationWidgetState extends State<AccountingSimulationWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  // Simulation state
  final List<JournalEntryData> _journalEntries = [];
  final Map<String, AccountLedger> _ledgerAccounts = {};
  final List<String> _validationErrors = [];
  final bool _isBalanced = true;
  final double _totalDebits = 0.0;
  final double _totalCredits = 0.0;
  
  // UI state
  final int _currentEntryIndex = 0;
  final bool _showValidation = false;
  final bool _simulationComplete = false;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeSimulation();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  void _initializeSimulation() {
    // Initialize accounts from scenario data
    final accountsData = widget.scenarioData['accounts'] as Map<String, dynamic>? ?? {};
    accountsData.forEach((accountId, accountInfo) {
      final info = accountInfo as Map<String, dynamic>;
      _ledgerAccounts[accountId] = AccountLedger(
        accountId: accountId,
        accountName: info['name'] as String? ?? accountId,
        accountType: _parseAccountType(info['type'] as String?),
        initialBalance: (info['initialBalance'] as num?)?.toDouble() ?? 0.0,
      );
    });
    
    // Initialize with first entry if available
    final transactionsData = widget.scenarioData['transactions'] as List<dynamic>? ?? [];
    if (transactionsData.isNotEmpty) {
      _createNewJournalEntry();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 600,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(child: _buildTabContent()),
          _buildBottomControls(),
        ],
      ),
    );
  }
  
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.account_balance_wallet,
              color: Theme.of(context).colorScheme.onPrimary,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                Text(
                  'Simulation comptable interactive',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          _buildBalanceIndicator(),
        ],
      ),
    );
  }
  
  Widget _buildBalanceIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _isBalanced ? Colors.green : Colors.red,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _isBalanced ? Icons.check_circle : Icons.error,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            _isBalanced ? 'Équilibré' : 'Déséquilibré',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: 'Journal', icon: Icon(Icons.book)),
          Tab(text: 'Grand Livre', icon: Icon(Icons.account_balance)),
          Tab(text: 'Validation', icon: Icon(Icons.check_circle)),
        ],
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
        indicatorColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }
  
  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildJournalView(),
        _buildLedgerView(),
        _buildValidationView(),
      ],
    );
  }
  
  Widget _buildJournalView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildJournalHeader(),
          const SizedBox(height: 16),
          Expanded(child: _buildJournalEntries()),
          const SizedBox(height: 16),
          _buildJournalEntryForm(),
        ],
      ),
    );
  }
  
  Widget _buildJournalHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(flex: 2, child: Text('Date', style: _headerTextStyle(context))),
          Expanded(flex: 3, child: Text('Libellé', style: _headerTextStyle(context))),
          Expanded(flex: 2, child: Text('Compte', style: _headerTextStyle(context))),
          Expanded(flex: 2, child: Text('Débit', style: _headerTextStyle(context))),
          Expanded(flex: 2, child: Text('Crédit', style: _headerTextStyle(context))),
          const SizedBox(width: 40), // Actions column
        ],
      ),
    );
  }
  
  Widget _buildJournalEntries() {
    if (_journalEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.article_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune écriture dans le journal',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Créez votre première écriture comptable ci-dessous',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      itemCount: _journalEntries.length,
      itemBuilder: (context, index) {
        return _buildJournalEntryCard(_journalEntries[index], index);
      },
    );
  }
  
  Widget _buildJournalEntryCard(JournalEntryData entry, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: entry.lines.asMap().entries.map((lineEntry) {
          final lineIndex = lineEntry.key;
          final line = lineEntry.value;
          final isFirst = lineIndex == 0;
          
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: lineIndex > 0 ? Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                ),
              ) : null,
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    isFirst ? entry.date : '',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    isFirst ? entry.description : '',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    line.accountName,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    line.debit > 0 ? line.debit.toStringAsFixed(2) : '',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green[700],
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    line.credit > 0 ? line.credit.toStringAsFixed(2) : '',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red[700],
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: isFirst ? IconButton(
                    icon: const Icon(Icons.delete_outline, size: 18),
                    onPressed: () => _deleteJournalEntry(index),
                    color: Colors.red,
                  ) : null,
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
  
  Widget _buildJournalEntryForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Nouvelle écriture comptable',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Date',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  controller: TextEditingController(
                    text: DateTime.now().toString().split(' ')[0],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Libellé de l\'opération',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: _clearCurrentEntry,
                child: const Text('Annuler'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _saveJournalEntry,
                child: const Text('Ajouter une ligne'),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildLedgerView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Grand Livre - Comptes en T',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _ledgerAccounts.length,
              itemBuilder: (context, index) {
                final account = _ledgerAccounts.values.elementAt(index);
                return _buildTAccountWidget(account);
              },
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTAccountWidget(AccountLedger account) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          // Account header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getAccountTypeColor(account.accountType).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
            child: Text(
              account.accountName,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: _getAccountTypeColor(account.accountType),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // T-Account body
          Expanded(
            child: Row(
              children: [
                // Debit side
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(4),
                          color: Colors.green.withValues(alpha: 0.1),
                          child: Text(
                            'DÉBIT',
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.green[700],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(4),
                            child: Column(
                              children: account.debits.map((debit) {
                                return Text(
                                  debit.toStringAsFixed(2),
                                  style: Theme.of(context).textTheme.bodySmall,
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Credit side
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(4),
                        color: Colors.red.withValues(alpha: 0.1),
                        child: Text(
                          'CRÉDIT',
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.red[700],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(4),
                          child: Column(
                            children: account.credits.map((credit) {
                              return Text(
                                credit.toStringAsFixed(2),
                                style: Theme.of(context).textTheme.bodySmall,
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildValidationView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildValidationSummary(),
          const SizedBox(height: 16),
          if (_validationErrors.isNotEmpty) _buildValidationErrors(),
          const Spacer(),
          if (_isBalanced && _journalEntries.isNotEmpty)
            _buildCompletionCelebration(),
        ],
      ),
    );
  }
  
  Widget _buildValidationSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _isBalanced 
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isBalanced ? Colors.green : Colors.orange,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                _isBalanced ? Icons.check_circle : Icons.warning,
                color: _isBalanced ? Colors.green : Colors.orange,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Validation des Écritures',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _isBalanced ? Colors.green[800] : Colors.orange[800],
                      ),
                    ),
                    Text(
                      _isBalanced 
                          ? 'Toutes les écritures sont équilibrées'
                          : 'Des corrections sont nécessaires',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: _isBalanced ? Colors.green[700] : Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Text(
                      'Total Débits',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${_totalDebits.toStringAsFixed(2)} DH',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.green[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 40,
                width: 1,
                color: Theme.of(context).colorScheme.outline,
              ),
              Expanded(
                child: Column(
                  children: [
                    Text(
                      'Total Crédits',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${_totalCredits.toStringAsFixed(2)} DH',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.red[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildValidationErrors() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.error, color: Colors.red),
              const SizedBox(width: 8),
              Text(
                'Erreurs détectées',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ..._validationErrors.map((error) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                const Text('• '),
                Expanded(child: Text(error)),
              ],
            ),
          )),
        ],
      ),
    );
  }
  
  Widget _buildCompletionCelebration() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.withValues(alpha: 0.1),
            Colors.blue.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.celebration,
            size: 48,
            color: Colors.green,
          ),
          const SizedBox(height: 16),
          Text(
            'Félicitations ! 🎉',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Vous avez terminé cette simulation comptable avec succès.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.green[700],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: widget.onComplete,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Continuer'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton.icon(
            onPressed: _resetSimulation,
            icon: const Icon(Icons.refresh),
            label: const Text('Recommencer'),
          ),
          Row(
            children: [
              TextButton.icon(
                onPressed: _undoLastEntry,
                icon: const Icon(Icons.undo),
                label: const Text('Annuler'),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: _validateAllEntries,
                icon: const Icon(Icons.check),
                label: const Text('Valider tout'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  // Helper methods
  TextStyle _headerTextStyle(BuildContext context) {
    return Theme.of(context).textTheme.labelMedium?.copyWith(
      fontWeight: FontWeight.bold,
      color: Theme.of(context).colorScheme.onSurfaceVariant,
    ) ?? const TextStyle();
  }
  
  Color _getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.asset:
        return Colors.blue;
      case AccountType.liability:
        return Colors.orange;
      case AccountType.equity:
        return Colors.green;
      case AccountType.revenue:
        return Colors.purple;
      case AccountType.expense:
        return Colors.red;
    }
  }
  
  AccountType _parseAccountType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'asset':
      case 'actif':
        return AccountType.asset;
      case 'liability':
      case 'passif':
        return AccountType.liability;
      case 'equity':
      case 'capitaux':
        return AccountType.equity;
      case 'revenue':
      case 'produit':
        return AccountType.revenue;
      case 'expense':
      case 'charge':
        return AccountType.expense;
      default:
        return AccountType.asset;
    }
  }
  
  void _createNewJournalEntry() {
    // Implementation for creating new journal entry
  }
  
  void _saveJournalEntry() {
    // Implementation for saving journal entry
  }
  
  void _deleteJournalEntry(int index) {
    // Implementation for deleting journal entry
  }
  
  void _clearCurrentEntry() {
    // Implementation for clearing current entry
  }
  
  void _validateAllEntries() {
    // Implementation for validating all entries
  }
  
  void _undoLastEntry() {
    // Implementation for undoing last entry
  }
  
  void _resetSimulation() {
    // Implementation for resetting simulation
  }
}

// Data models
enum AccountType { asset, liability, equity, revenue, expense }

class JournalEntryData {
  final String date;
  final String description;
  final List<JournalLine> lines;
  
  JournalEntryData({
    required this.date,
    required this.description,
    required this.lines,
  });
}

class JournalLine {
  final String accountName;
  final double debit;
  final double credit;
  
  JournalLine({
    required this.accountName,
    this.debit = 0.0,
    this.credit = 0.0,
  });
}

class AccountLedger {
  final String accountId;
  final String accountName;
  final AccountType accountType;
  final double initialBalance;
  final List<double> debits = [];
  final List<double> credits = [];
  
  AccountLedger({
    required this.accountId,
    required this.accountName,
    required this.accountType,
    this.initialBalance = 0.0,
  });
  
  double get balance {
    final totalDebits = debits.fold(0.0, (sum, debit) => sum + debit);
    final totalCredits = credits.fold(0.0, (sum, credit) => sum + credit);
    
    switch (accountType) {
      case AccountType.asset:
      case AccountType.expense:
        return initialBalance + totalDebits - totalCredits;
      case AccountType.liability:
      case AccountType.equity:
      case AccountType.revenue:
        return initialBalance + totalCredits - totalDebits;
    }
  }
}