import 'package:flutter/material.dart';
import '../../models/guide/guide_section_data.dart';
import '../../services/guide_content_service.dart';

/// Search result item for guide sections
class GuideSearchResult {
  final GuideSectionData section;
  final String snippet;
  final double relevanceScore;

  const GuideSearchResult({
    required this.section,
    required this.snippet,
    required this.relevanceScore,
  });
}

/// Search delegate for searching within guide content
class GuideSearchDelegate extends SearchDelegate<GuideSectionData?> {
  final String guideId;
  final List<GuideSectionData> sections;
  final Function(String sectionId)? onSectionSelected;
  final GuideContentService _contentService = GuideContentService();

  GuideSearchDelegate({
    required this.guideId,
    required this.sections,
    this.onSectionSelected,
  }) : super(
          searchFieldLabel: 'Rechercher dans le guide...',
          searchFieldStyle: const TextStyle(fontSize: 16),
        );

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            query = '';
            showSuggestions(context);
          },
          tooltip: 'Effacer',
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
      tooltip: 'Retour',
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return _buildRecentSections(context);
    }
    return _buildSearchResults(context);
  }

  Widget _buildSearchResults(BuildContext context) {
    if (query.isEmpty) {
      return const SizedBox.shrink();
    }

    final results = _searchSections(query);
    
    if (results.isEmpty) {
      return _buildNoResults(context);
    }

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final result = results[index];
        return _buildSearchResultTile(context, result);
      },
    );
  }

  Widget _buildSearchResultTile(BuildContext context, GuideSearchResult result) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        title: Text(
          result.section.title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (result.snippet.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                result.snippet,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  _getTypeIcon(result.section.type),
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  _getTypeLabel(result.section.type),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  '${result.section.estimatedReadTime} min',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: theme.colorScheme.onSurfaceVariant,
        ),
        onTap: () {
          // Close search and navigate to section
          close(context, result.section);
          onSectionSelected?.call(result.section.id);
        },
      ),
    );
  }

  Widget _buildRecentSections(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'Sections récentes',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: sections.take(5).length,
            itemBuilder: (context, index) {
              final section = sections[index];
              return ListTile(
                leading: Icon(
                  _getTypeIcon(section.type),
                  color: theme.colorScheme.primary,
                ),
                title: Text(section.title),
                subtitle: Text('${section.estimatedReadTime} min de lecture'),
                trailing: const Icon(Icons.history),
                onTap: () {
                  query = section.title;
                  showResults(context);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNoResults(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun résultat trouvé',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Essayez avec des mots-clés différents',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  List<GuideSearchResult> _searchSections(String query) {
    if (query.isEmpty) return [];
    
    final results = <GuideSearchResult>[];
    final queryLower = query.toLowerCase();
    final queryWords = queryLower.split(' ').where((w) => w.length > 2).toList();
    
    for (final section in sections) {
      final relevanceScore = _calculateRelevanceScore(section, queryWords);
      if (relevanceScore > 0) {
        final snippet = _generateSnippet(section, queryWords);
        results.add(GuideSearchResult(
          section: section,
          snippet: snippet,
          relevanceScore: relevanceScore,
        ));
      }
    }
    
    // Sort by relevance score (highest first)
    results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    
    return results;
  }

  double _calculateRelevanceScore(GuideSectionData section, List<String> queryWords) {
    double score = 0.0;
    final titleLower = section.title.toLowerCase();
    final contentLower = section.content.toLowerCase();
    final searchableContent = section.searchableContent.toLowerCase();
    
    for (final word in queryWords) {
      // Title matches are worth more
      if (titleLower.contains(word)) {
        score += titleLower == word ? 10.0 : 5.0;
      }
      
      // Content matches
      if (contentLower.contains(word)) {
        score += 2.0;
      }
      
      // General searchable content matches
      if (searchableContent.contains(word)) {
        score += 1.0;
      }
    }
    
    return score;
  }

  String _generateSnippet(GuideSectionData section, List<String> queryWords) {
    final content = section.content;
    if (content.isEmpty) return '';
    
    // Find the first occurrence of any query word
    int bestIndex = -1;
    for (final word in queryWords) {
      final index = content.toLowerCase().indexOf(word);
      if (index != -1 && (bestIndex == -1 || index < bestIndex)) {
        bestIndex = index;
      }
    }
    
    if (bestIndex == -1) return content.substring(0, 100.clamp(0, content.length));
    
    // Extract snippet around the found word
    const snippetLength = 150;
    final start = (bestIndex - 50).clamp(0, content.length);
    final end = (start + snippetLength).clamp(0, content.length);
    
    String snippet = content.substring(start, end);
    if (start > 0) snippet = '...$snippet';
    if (end < content.length) snippet = '$snippet...';
    
    return snippet;
  }

  IconData _getTypeIcon(GuideSectionType type) {
    switch (type) {
      case GuideSectionType.list:
        return Icons.list;
      case GuideSectionType.calculator:
        return Icons.calculate;
      case GuideSectionType.example:
        return Icons.lightbulb_outline;
      case GuideSectionType.definition:
        return Icons.book;
      case GuideSectionType.formula:
        return Icons.functions;
      case GuideSectionType.table:
        return Icons.table_chart;
      case GuideSectionType.widget:
        return Icons.widgets;
      default:
        return Icons.article;
    }
  }

  String _getTypeLabel(GuideSectionType type) {
    switch (type) {
      case GuideSectionType.list:
        return 'Liste';
      case GuideSectionType.calculator:
        return 'Calculateur';
      case GuideSectionType.example:
        return 'Exemple';
      case GuideSectionType.definition:
        return 'Définition';
      case GuideSectionType.formula:
        return 'Formule';
      case GuideSectionType.table:
        return 'Tableau';
      case GuideSectionType.widget:
        return 'Outil';
      default:
        return 'Texte';
    }
  }
}
