import 'package:flutter/material.dart';

/// Breadcrumb item data
class BreadcrumbItem {
  final String title;
  final VoidCallback? onTap;
  final IconData? icon;

  const BreadcrumbItem({
    required this.title,
    this.onTap,
    this.icon,
  });
}

/// Breadcrumb navigation widget for guide navigation
class BreadcrumbNavigation extends StatelessWidget {
  final List<BreadcrumbItem> items;
  final Color? textColor;
  final Color? separatorColor;
  final double fontSize;
  final EdgeInsetsGeometry padding;
  final bool showHomeIcon;

  const BreadcrumbNavigation({
    super.key,
    required this.items,
    this.textColor,
    this.separatorColor,
    this.fontSize = 14,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.showHomeIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveTextColor = textColor ?? theme.colorScheme.onSurfaceVariant;
    final effectiveSeparatorColor = separatorColor ?? theme.colorScheme.outline;

    if (items.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: padding,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showHomeIcon) ...[
              Icon(
                Icons.home_outlined,
                size: fontSize + 2,
                color: effectiveTextColor,
              ),
              if (items.isNotEmpty) ...[
                const SizedBox(width: 8),
                _buildSeparator(effectiveSeparatorColor),
                const SizedBox(width: 8),
              ],
            ],
            ..._buildBreadcrumbItems(
              context,
              effectiveTextColor,
              effectiveSeparatorColor,
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildBreadcrumbItems(
    BuildContext context,
    Color textColor,
    Color separatorColor,
  ) {
    final widgets = <Widget>[];

    for (int i = 0; i < items.length; i++) {
      final item = items[i];
      final isLast = i == items.length - 1;

      // Add breadcrumb item
      widgets.add(_buildBreadcrumbItem(
        context,
        item,
        textColor,
        isLast,
      ));

      // Add separator if not last item
      if (!isLast) {
        widgets.add(const SizedBox(width: 8));
        widgets.add(_buildSeparator(separatorColor));
        widgets.add(const SizedBox(width: 8));
      }
    }

    return widgets;
  }

  Widget _buildBreadcrumbItem(
    BuildContext context,
    BreadcrumbItem item,
    Color textColor,
    bool isLast,
  ) {
    final theme = Theme.of(context);
    final isClickable = item.onTap != null && !isLast;

    Widget content = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (item.icon != null) ...[
          Icon(
            item.icon,
            size: fontSize,
            color: isLast ? textColor : theme.colorScheme.primary,
          ),
          const SizedBox(width: 4),
        ],
        Flexible(
          child: Text(
            item.title,
            style: TextStyle(
              fontSize: fontSize,
              color: isLast ? textColor : theme.colorScheme.primary,
              fontWeight: isLast ? FontWeight.w600 : FontWeight.w500,
              decoration: isClickable ? TextDecoration.underline : null,
              decorationColor: theme.colorScheme.primary,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );

    if (isClickable) {
      return InkWell(
        onTap: item.onTap,
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          child: content,
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: content,
    );
  }

  Widget _buildSeparator(Color color) {
    return Icon(
      Icons.chevron_right,
      size: fontSize + 2,
      color: color,
    );
  }
}

/// Helper class for building breadcrumb navigation
class BreadcrumbBuilder {
  static List<BreadcrumbItem> buildGuideBreadcrumbs({
    required String guideTitle,
    String? moduleTitle,
    String? sectionTitle,
    VoidCallback? onGuideNavigate,
    VoidCallback? onModuleNavigate,
  }) {
    final items = <BreadcrumbItem>[];

    // Add guide level
    items.add(BreadcrumbItem(
      title: 'Guide',
      icon: Icons.menu_book,
      onTap: onGuideNavigate,
    ));

    // Add guide title
    items.add(BreadcrumbItem(
      title: guideTitle,
      onTap: onGuideNavigate,
    ));

    // Add module if provided
    if (moduleTitle != null) {
      items.add(BreadcrumbItem(
        title: moduleTitle,
        onTap: onModuleNavigate,
      ));
    }

    // Add section if provided (no onTap as it's the current page)
    if (sectionTitle != null) {
      items.add(BreadcrumbItem(
        title: sectionTitle,
      ));
    }

    return items;
  }

  static List<BreadcrumbItem> buildFiscaliteBreadcrumbs({
    required String fiscaliteType, // 'IR', 'IS', 'Droits d\'Enregistrement'
    String? sectionTitle,
    VoidCallback? onFiscaliteNavigate,
  }) {
    return buildGuideBreadcrumbs(
      guideTitle: 'Fiscalité',
      moduleTitle: fiscaliteType,
      sectionTitle: sectionTitle,
      onGuideNavigate: onFiscaliteNavigate,
      onModuleNavigate: onFiscaliteNavigate,
    );
  }

  static List<BreadcrumbItem> buildComptabiliteBreadcrumbs({
    String? moduleTitle,
    String? sectionTitle,
    VoidCallback? onComptabiliteNavigate,
    VoidCallback? onModuleNavigate,
  }) {
    return buildGuideBreadcrumbs(
      guideTitle: 'Comptabilité Générale',
      moduleTitle: moduleTitle,
      sectionTitle: sectionTitle,
      onGuideNavigate: onComptabiliteNavigate,
      onModuleNavigate: onModuleNavigate,
    );
  }
}

/// Responsive breadcrumb navigation that adapts to screen size
class ResponsiveBreadcrumbNavigation extends StatelessWidget {
  final List<BreadcrumbItem> items;
  final Color? textColor;
  final Color? separatorColor;
  final bool showHomeIcon;

  const ResponsiveBreadcrumbNavigation({
    super.key,
    required this.items,
    this.textColor,
    this.separatorColor,
    this.showHomeIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // On small screens, show only the last 2 items
        if (constraints.maxWidth < 400 && items.length > 2) {
          final truncatedItems = [
            const BreadcrumbItem(title: '...'),
            ...items.skip(items.length - 2),
          ];
          
          return BreadcrumbNavigation(
            items: truncatedItems,
            textColor: textColor,
            separatorColor: separatorColor,
            showHomeIcon: false, // Don't show home icon when truncated
          );
        }

        // On medium screens, show only the last 3 items
        if (constraints.maxWidth < 600 && items.length > 3) {
          final truncatedItems = [
            const BreadcrumbItem(title: '...'),
            ...items.skip(items.length - 3),
          ];
          
          return BreadcrumbNavigation(
            items: truncatedItems,
            textColor: textColor,
            separatorColor: separatorColor,
            showHomeIcon: showHomeIcon,
          );
        }

        // On larger screens, show all items
        return BreadcrumbNavigation(
          items: items,
          textColor: textColor,
          separatorColor: separatorColor,
          showHomeIcon: showHomeIcon,
        );
      },
    );
  }
}
