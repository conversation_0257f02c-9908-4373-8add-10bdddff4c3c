import 'package:flutter/material.dart';
import '../../services/guide_progress_service.dart';
import '../../models/guide/guide_section_data.dart';

/// Prerequisite checking widget that validates completed sections before accessing advanced content
class Prerequisite<PERSON>he<PERSON> extends StatefulWidget {
  final String guideId;
  final GuideSectionData sectionData;
  final Function(List<String>)? onPrerequisiteNavigate;
  final VoidCallback? onSkipPrerequisites;
  final bool allowSkip;

  const PrerequisiteChecker({
    super.key,
    required this.guideId,
    required this.sectionData,
    this.onPrerequisiteNavigate,
    this.onSkipPrerequisites,
    this.allowSkip = true,
  });

  @override
  State<PrerequisiteChecker> createState() => _PrerequisiteCheckerState();
}

class _PrerequisiteCheckerState extends State<PrerequisiteChecker> {
  late GuideProgressService _progressService;
  List<String> _completedSections = [];
  final Map<String, PrerequisiteStatus> _prerequisiteStatuses = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _progressService = GuideProgressService();
    _loadPrerequisiteStatus();
  }

  Future<void> _loadPrerequisiteStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get completed sections from progress service
      _completedSections = await _progressService.getCompletedSections(widget.guideId);
      
      // Check status for each prerequisite
      for (final prerequisite in widget.sectionData.prerequisites) {
        final isCompleted = _completedSections.contains(prerequisite);
        final estimatedTime = await _getEstimatedTimeForSection(prerequisite);
        
        _prerequisiteStatuses[prerequisite] = PrerequisiteStatus(
          sectionId: prerequisite,
          isCompleted: isCompleted,
          estimatedTime: estimatedTime,
          title: await _getSectionTitle(prerequisite),
        );
      }
    } catch (e) {
      // Handle error
      debugPrint('Error loading prerequisite status: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<int> _getEstimatedTimeForSection(String sectionId) async {
    // This would typically fetch from the content service
    // For now, return a default value
    return 15; // minutes
  }

  Future<String> _getSectionTitle(String sectionId) async {
    // This would typically fetch from the content service
    // For now, return a formatted title
    return sectionId.replaceAll('_', ' ').split(' ').map((word) => 
        word.isEmpty ? word : word[0].toUpperCase() + word.substring(1)).join(' ');
  }

  @override
  Widget build(BuildContext context) {
    if (widget.sectionData.prerequisites.isEmpty) {
      return const SizedBox.shrink();
    }

    if (_isLoading) {
      return _buildLoadingState();
    }

    final allPrerequisitesMet = widget.sectionData.hasPrerequisitesMet(_completedSections);

    if (allPrerequisitesMet) {
      return _buildPrerequisitesMetState();
    } else {
      return _buildPrerequisitesNotMetState();
    }
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 16),
          Text(
            'Vérification des prérequis...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrerequisitesMetState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Prérequis validés',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[800],
                  ),
                ),
                Text(
                  'Vous avez terminé toutes les sections requises',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.lock_open,
            color: Colors.green[600],
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildPrerequisitesNotMetState() {
    final completedCount = _prerequisiteStatuses.values
        .where((status) => status.isCompleted)
        .length;
    final totalCount = _prerequisiteStatuses.length;
    final remainingTime = _prerequisiteStatuses.values
        .where((status) => !status.isCompleted)
        .fold<int>(0, (sum, status) => sum + status.estimatedTime);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPrerequisiteHeader(completedCount, totalCount, remainingTime),
          const SizedBox(height: 16),
          _buildPrerequisitesList(),
          const SizedBox(height: 16),
          _buildActionButtons(remainingTime),
        ],
      ),
    );
  }

  Widget _buildPrerequisiteHeader(int completedCount, int totalCount, int remainingTime) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.orange,
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(
            Icons.school,
            color: Colors.white,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Prérequis requis',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange[800],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.orange[600],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '$completedCount/$totalCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 2),
              Text(
                'Temps estimé restant: $remainingTime min',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.orange[700],
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.lock,
          color: Colors.orange[600],
          size: 20,
        ),
      ],
    );
  }

  Widget _buildPrerequisitesList() {
    return Column(
      children: _prerequisiteStatuses.entries.map((entry) {
        final prerequisite = entry.key;
        final status = entry.value;
        
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: _buildPrerequisiteItem(prerequisite, status),
        );
      }).toList(),
    );
  }

  Widget _buildPrerequisiteItem(String prerequisite, PrerequisiteStatus status) {
    return InkWell(
      onTap: status.isCompleted 
          ? null 
          : () => widget.onPrerequisiteNavigate?.call([prerequisite]),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: status.isCompleted 
              ? Colors.green.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: status.isCompleted 
                ? Colors.green.withValues(alpha: 0.3)
                : Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: status.isCompleted ? Colors.green : Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                status.isCompleted ? Icons.check : Icons.circle_outlined,
                size: 16,
                color: status.isCompleted ? Colors.white : Colors.grey[600],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    status.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: status.isCompleted 
                          ? Colors.green[800]
                          : Theme.of(context).colorScheme.onSurface,
                      decoration: status.isCompleted 
                          ? TextDecoration.lineThrough 
                          : null,
                    ),
                  ),
                  if (!status.isCompleted) ...[
                    const SizedBox(height: 2),
                    Text(
                      'Temps estimé: ${status.estimatedTime} min',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (!status.isCompleted) ...[
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(int remainingTime) {
    return Row(
      children: [
        if (widget.allowSkip) ...[
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _showSkipWarningDialog,
              icon: const Icon(Icons.skip_next),
              label: const Text('Ignorer les prérequis'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.orange[700],
                side: BorderSide(color: Colors.orange.withOpacity(0.5)),
              ),
            ),
          ),
          const SizedBox(width: 12),
        ],
        Expanded(
          flex: widget.allowSkip ? 2 : 1,
          child: ElevatedButton.icon(
            onPressed: _navigateToMissingPrerequisites,
            icon: const Icon(Icons.school),
            label: Text('Commencer ($remainingTime min)'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  void _showSkipWarningDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('Attention'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Vous vous apprêtez à ignorer les prérequis recommandés.'),
            SizedBox(height: 16),
            Text('Cela pourrait rendre la compréhension plus difficile. Êtes-vous sûr de vouloir continuer ?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onSkipPrerequisites?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Continuer quand même'),
          ),
        ],
      ),
    );
  }

  void _navigateToMissingPrerequisites() {
    final missingPrerequisites = _prerequisiteStatuses.entries
        .where((entry) => !entry.value.isCompleted)
        .map((entry) => entry.key)
        .toList();
    
    if (missingPrerequisites.isNotEmpty) {
      widget.onPrerequisiteNavigate?.call(missingPrerequisites);
    }
  }

  /// Creates a simple prerequisite status indicator
  static Widget statusIndicator(
    GuideSectionData sectionData,
    List<String> completedSections,
  ) {
    if (sectionData.prerequisites.isEmpty) {
      return const SizedBox.shrink();
    }

    final allMet = sectionData.hasPrerequisitesMet(completedSections);
    final completedCount = sectionData.prerequisites
        .where((prereq) => completedSections.contains(prereq))
        .length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: allMet ? Colors.green : Colors.orange,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            allMet ? Icons.check_circle : Icons.school,
            size: 14,
            color: Colors.white,
          ),
          const SizedBox(width: 4),
          Text(
            allMet 
                ? 'Prêt'
                : '$completedCount/${sectionData.prerequisites.length}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// Data model for prerequisite status
class PrerequisiteStatus {
  final String sectionId;
  final bool isCompleted;
  final int estimatedTime;
  final String title;

  const PrerequisiteStatus({
    required this.sectionId,
    required this.isCompleted,
    required this.estimatedTime,
    required this.title,
  });
}