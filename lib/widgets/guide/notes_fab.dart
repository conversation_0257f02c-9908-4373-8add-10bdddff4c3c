import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/guide/personal_note_data.dart';
import '../../providers/personal_notes_provider.dart';
import 'personal_notes/note_editor.dart';

/// Floating Action Button for quick note creation in guide screens
class NotesFAB extends ConsumerStatefulWidget {
  final String guideId;
  final String sectionId;
  final String? sectionTitle;

  const NotesFAB({
    super.key,
    required this.guideId,
    required this.sectionId,
    this.sectionTitle,
  });

  @override
  ConsumerState<NotesFAB> createState() => _NotesFABState();
}

class _NotesFABState extends ConsumerState<NotesFAB>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Expanded options
        if (_isExpanded) ...[
          ScaleTransition(
            scale: _scaleAnimation,
            child: FloatingActionButton.small(
              heroTag: "view_notes",
              onPressed: _viewNotes,
              backgroundColor: colorScheme.secondaryContainer,
              foregroundColor: colorScheme.onSecondaryContainer,
              tooltip: 'Voir les notes',
              child: const Icon(Icons.list_alt),
            ),
          ),
          const SizedBox(height: 8),
          ScaleTransition(
            scale: _scaleAnimation,
            child: FloatingActionButton.small(
              heroTag: "quick_note",
              onPressed: _createQuickNote,
              backgroundColor: colorScheme.tertiaryContainer,
              foregroundColor: colorScheme.onTertiaryContainer,
              tooltip: 'Note rapide',
              child: const Icon(Icons.note_add),
            ),
          ),
          const SizedBox(height: 8),
        ],
        
        // Main FAB
        FloatingActionButton(
          heroTag: "main_notes",
          onPressed: _toggleExpanded,
          backgroundColor: colorScheme.primaryContainer,
          foregroundColor: colorScheme.onPrimaryContainer,
          tooltip: _isExpanded ? 'Fermer' : 'Notes',
          child: AnimatedRotation(
            turns: _isExpanded ? 0.125 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: Icon(_isExpanded ? Icons.close : Icons.note),
          ),
        ),
      ],
    );
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _createQuickNote() {
    _showNoteEditor(context);
  }

  void _viewNotes() {
    Navigator.pushNamed(context, '/personal_notes');
  }

  void _showNoteEditor(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: NoteEditor(
          guideId: widget.guideId,
          sectionId: widget.sectionId,
          onNoteSaved: (note) {
            _saveNote(note);
            Navigator.pop(context);
          },
          onCancel: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _saveNote(PersonalNoteData note) async {
    try {
      await ref.read(personalNotesNotifierProvider.notifier).addNote(note);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Note "${note.title}" sauvegardée'),
            action: SnackBarAction(
              label: 'Voir',
              onPressed: () => Navigator.pushNamed(context, '/personal_notes'),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sauvegarde: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
