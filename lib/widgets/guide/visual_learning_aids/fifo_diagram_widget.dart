import 'package:flutter/material.dart';
import '../../../theme/design_tokens.dart';

/// Specialized FIFO (First In, First Out) diagram widget
class FIFODiagramWidget extends StatefulWidget {
  final Map<String, dynamic> schema;
  final bool isInteractive;
  final VoidCallback? onComplete;

  const FIFODiagramWidget({
    super.key,
    required this.schema,
    this.isInteractive = true,
    this.onComplete,
  });

  @override
  State<FIFODiagramWidget> createState() => _FIFODiagramWidgetState();
}

class _FIFODiagramWidgetState extends State<FIFODiagramWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentStep = 0;
  List<FIFOEntry> _entries = [];
  List<FIFOExit> _exits = [];
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    _parseSchemaData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _parseSchemaData() {
    final data = widget.schema['data'] as Map<String, dynamic>? ?? {};
    
    // Parse entries
    final entriesData = data['entries'] as List<dynamic>? ?? [];
    _entries = entriesData.map((entry) {
      final e = entry as Map<String, dynamic>;
      return FIFOEntry(
        date: e['date'] as String? ?? '',
        quantity: (e['quantity'] as num?)?.toDouble() ?? 0,
        unitPrice: (e['unitPrice'] as num?)?.toDouble() ?? 0,
        description: e['description'] as String? ?? '',
      );
    }).toList();

    // Parse exits
    final exitsData = data['exits'] as List<dynamic>? ?? [];
    _exits = exitsData.map((exit) {
      final e = exit as Map<String, dynamic>;
      return FIFOExit(
        date: e['date'] as String? ?? '',
        quantity: (e['quantity'] as num?)?.toDouble() ?? 0,
        description: e['description'] as String? ?? '',
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: DesignTokens.spacingM),
          _buildDiagram(),
          const SizedBox(height: DesignTokens.spacingM),
          if (widget.isInteractive) _buildControls(),
          if (_currentStep > 0) _buildCalculationDisplay(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.inventory_2,
          color: Theme.of(context).colorScheme.primary,
          size: 28,
        ),
        const SizedBox(width: DesignTokens.spacingS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Méthode FIFO (Premier Entré, Premier Sorti)',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              Text(
                'Visualisation du flux d\'inventaire',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDiagram() {
    return SizedBox(
      height: 400,
      child: Row(
        children: [
          // Entries column
          Expanded(
            flex: 2,
            child: _buildEntriesColumn(),
          ),
          // Flow arrows
          SizedBox(
            width: 80,
            child: _buildFlowArrows(),
          ),
          // Exits column
          Expanded(
            flex: 2,
            child: _buildExitsColumn(),
          ),
          // Calculation column
          Expanded(
            flex: 2,
            child: _buildCalculationColumn(),
          ),
        ],
      ),
    );
  }

  Widget _buildEntriesColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(DesignTokens.spacingS),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(DesignTokens.radiusS),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.input, color: Colors.green, size: 20),
              const SizedBox(width: DesignTokens.spacingXS),
              Text(
                'ENTRÉES',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: DesignTokens.spacingS),
        Expanded(
          child: ListView.builder(
            itemCount: _entries.length,
            itemBuilder: (context, index) {
              final entry = _entries[index];
              final isActive = _currentStep > index;
              return AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return _buildEntryCard(entry, index, isActive);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEntryCard(FIFOEntry entry, int index, bool isActive) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingS),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.all(DesignTokens.spacingS),
        decoration: BoxDecoration(
          color: isActive 
              ? Colors.green.withValues(alpha: 0.2)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(DesignTokens.radiusS),
          border: Border.all(
            color: isActive 
                ? Colors.green 
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            width: isActive ? 2 : 1,
          ),
          boxShadow: isActive ? [
            BoxShadow(
              color: Colors.green.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  entry.date,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (isActive)
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 16,
                  ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'Qté: ${entry.quantity.toStringAsFixed(0)} unités',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'Prix: ${entry.unitPrice.toStringAsFixed(2)} DH/unité',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'Total: ${(entry.quantity * entry.unitPrice).toStringAsFixed(2)} DH',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlowArrows() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        for (int i = 0; i < _entries.length; i++)
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              final isActive = _currentStep > i;
              return Container(
                margin: const EdgeInsets.symmetric(vertical: DesignTokens.spacingS),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: 40,
                      height: 2,
                      color: isActive 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      child: Icon(
                        Icons.arrow_forward,
                        color: isActive 
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                        size: 20,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildExitsColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(DesignTokens.spacingS),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(DesignTokens.radiusS),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.output, color: Colors.red, size: 20),
              const SizedBox(width: DesignTokens.spacingXS),
              Text(
                'SORTIES',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red[700],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: DesignTokens.spacingS),
        Expanded(
          child: ListView.builder(
            itemCount: _exits.length,
            itemBuilder: (context, index) {
              final exit = _exits[index];
              final isActive = _currentStep > index;
              return _buildExitCard(exit, index, isActive);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildExitCard(FIFOExit exit, int index, bool isActive) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingS),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.all(DesignTokens.spacingS),
        decoration: BoxDecoration(
          color: isActive 
              ? Colors.red.withValues(alpha: 0.2)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(DesignTokens.radiusS),
          border: Border.all(
            color: isActive 
                ? Colors.red 
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            width: isActive ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              exit.date,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Qté sortie: ${exit.quantity.toStringAsFixed(0)} unités',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              exit.description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(DesignTokens.spacingS),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(DesignTokens.radiusS),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.calculate, 
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: DesignTokens.spacingXS),
              Text(
                'CALCULS',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: DesignTokens.spacingS),
        Expanded(
          child: _buildStepByStepCalculation(),
        ),
      ],
    );
  }

  Widget _buildStepByStepCalculation() {
    if (_currentStep == 0) {
      return Center(
        child: Text(
          'Cliquez sur "Étape suivante" pour voir les calculs',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontStyle: FontStyle.italic,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return ListView.builder(
      itemCount: _currentStep,
      itemBuilder: (context, index) {
        return _buildCalculationStep(index);
      },
    );
  }

  Widget _buildCalculationStep(int stepIndex) {
    if (stepIndex >= _entries.length || stepIndex >= _exits.length) {
      return const SizedBox.shrink();
    }

    final entry = _entries[stepIndex];
    final exit = _exits[stepIndex];
    final cost = exit.quantity * entry.unitPrice;

    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingS),
      padding: const EdgeInsets.all(DesignTokens.spacingS),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Étape ${stepIndex + 1}',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Sortie: ${exit.quantity} × ${entry.unitPrice} DH',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Text(
            'Coût: ${cost.toStringAsFixed(2)} DH',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton.icon(
          onPressed: _currentStep > 0 ? _previousStep : null,
          icon: const Icon(Icons.skip_previous),
          label: const Text('Précédent'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.secondary,
          ),
        ),
        const SizedBox(width: DesignTokens.spacingM),
        ElevatedButton.icon(
          onPressed: _isAnimating ? null : _nextStep,
          icon: _isAnimating 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.skip_next),
          label: Text(_currentStep >= _entries.length ? 'Terminer' : 'Étape suivante'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildCalculationDisplay() {
    final totalCost = _calculateTotalCost();
    
    return Container(
      margin: const EdgeInsets.only(top: DesignTokens.spacingM),
      padding: const EdgeInsets.all(DesignTokens.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),  
        ),
      ),
      child: Column(
        children: [
          Text(
            'Résumé des Calculs FIFO',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingS),
          Text(
            'Coût total des sorties: ${totalCost.toStringAsFixed(2)} DH',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  void _nextStep() {
    if (_currentStep >= _entries.length) {
      widget.onComplete?.call();
      return;
    }

    setState(() {
      _isAnimating = true;
      _currentStep++;
    });

    _animationController.forward().then((_) {
      setState(() {
        _isAnimating = false;
      });
      _animationController.reset();
    });
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  double _calculateTotalCost() {
    double total = 0;
    for (int i = 0; i < _currentStep && i < _entries.length && i < _exits.length; i++) {
      total += _exits[i].quantity * _entries[i].unitPrice;
    }
    return total;
  }
}

/// Data model for FIFO entries
class FIFOEntry {
  final String date;
  final double quantity;
  final double unitPrice;
  final String description;

  const FIFOEntry({
    required this.date,
    required this.quantity,
    required this.unitPrice,
    this.description = '',
  });
}

/// Data model for FIFO exits
class FIFOExit {
  final String date;
  final double quantity;
  final String description;

  const FIFOExit({
    required this.date,
    required this.quantity,
    this.description = '',
  });
}