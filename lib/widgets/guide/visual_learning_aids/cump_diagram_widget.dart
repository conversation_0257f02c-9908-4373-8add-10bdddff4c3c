import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../theme/design_tokens.dart';

/// CUMP (Weighted Average Cost) diagram widget that visualizes the calculation process
class CUMPDiagramWidget extends StatefulWidget {
  final Map<String, dynamic> schema;
  final bool isInteractive;
  final VoidCallback? onComplete;

  const CUMPDiagramWidget({
    super.key,
    required this.schema,
    this.isInteractive = true,
    this.onComplete,
  });

  @override
  State<CUMPDiagramWidget> createState() => _CUMPDiagramWidgetState();
}

class _CUMPDiagramWidgetState extends State<CUMPDiagramWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentStep = 0;
  List<StockMovement> _movements = [];
  final List<CUMPCalculation> _calculations = [];
  bool _isAnimating = false;
  bool _showFormula = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    _parseSchemaData();
    _calculateCUMP();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _parseSchemaData() {
    final data = widget.schema['data'] as Map<String, dynamic>? ?? {};
    
    // Parse stock movements
    final movementsData = data['movements'] as List<dynamic>? ?? [];
    _movements = movementsData.map((movement) {
      final m = movement as Map<String, dynamic>;
      return StockMovement(
        date: m['date'] as String? ?? '',
        type: m['type'] as String? ?? '',
        quantity: (m['quantity'] as num?)?.toDouble() ?? 0,
        unitPrice: (m['unitPrice'] as num?)?.toDouble() ?? 0,
        description: m['description'] as String? ?? '',
      );
    }).toList();
  }

  void _calculateCUMP() {
    double totalQuantity = 0;
    double totalValue = 0;
    
    for (final movement in _movements) {
      if (movement.type.toLowerCase() == 'entry' || movement.type.toLowerCase() == 'entrée') {
        totalQuantity += movement.quantity;
        totalValue += movement.quantity * movement.unitPrice;
      }
      
      final averageCost = totalQuantity > 0 ? (totalValue / totalQuantity).toDouble() : 0.0;
      
      _calculations.add(CUMPCalculation(
        movement: movement,
        totalQuantity: totalQuantity,
        totalValue: totalValue,
        averageCost: averageCost,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: DesignTokens.spacingM),
          if (_showFormula) _buildFormula(),
          if (_showFormula) const SizedBox(height: DesignTokens.spacingM),
          _buildVisualizationTabs(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.trending_up,
          color: Theme.of(context).colorScheme.primary,
          size: 28,
        ),
        const SizedBox(width: DesignTokens.spacingS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Méthode CUMP (Coût Unitaire Moyen Pondéré)',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              Text(
                'Calcul du coût moyen pondéré des stocks',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        if (widget.isInteractive)
          IconButton(
            onPressed: () {
              setState(() {
                _showFormula = !_showFormula;
              });
            },
            icon: Icon(
              _showFormula ? Icons.visibility_off : Icons.functions,
              color: Theme.of(context).colorScheme.primary,
            ),
            tooltip: _showFormula ? 'Masquer la formule' : 'Afficher la formule',
          ),
      ],
    );
  }

  Widget _buildFormula() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.all(DesignTokens.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            'Formule du CUMP',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingS),
          Container(
            padding: const EdgeInsets.all(DesignTokens.spacingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(DesignTokens.radiusS),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Text(
                  'CUMP =',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: DesignTokens.spacingS),
                Container(
                  padding: const EdgeInsets.all(DesignTokens.spacingS),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline,
                      width: 2,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Valeur totale du stock',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      Container(
                        height: 1,
                        width: double.infinity,
                        color: Theme.of(context).colorScheme.outline,
                        margin: const EdgeInsets.symmetric(vertical: 4),
                      ),
                      Text(
                        'Quantité totale en stock',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVisualizationTabs() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          TabBar(
            tabs: const [
              Tab(text: 'Mouvements', icon: Icon(Icons.list)),
              Tab(text: 'Graphique', icon: Icon(Icons.show_chart)),
              Tab(text: 'Calculs', icon: Icon(Icons.calculate)),
            ],
            labelColor: Theme.of(context).colorScheme.primary,
            unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
            indicatorColor: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: DesignTokens.spacingM),
          SizedBox(
            height: 400,
            child: TabBarView(
              children: [
                _buildMovementsView(),
                _buildChartView(),
                _buildCalculationsView(),
              ],
            ),
          ),
          if (widget.isInteractive) ...[
            const SizedBox(height: DesignTokens.spacingM),
            _buildControls(),
          ],
        ],
      ),
    );
  }

  Widget _buildMovementsView() {
    return ListView.builder(
      itemCount: _movements.length,
      itemBuilder: (context, index) {
        final movement = _movements[index];
        final isActive = index < _currentStep;
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return _buildMovementCard(movement, index, isActive);
          },
        );
      },
    );
  }

  Widget _buildMovementCard(StockMovement movement, int index, bool isActive) {
    final isEntry = movement.type.toLowerCase() == 'entry' || 
                   movement.type.toLowerCase() == 'entrée';
    final color = isEntry ? Colors.green : Colors.red;

    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingS),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.all(DesignTokens.spacingM),
        decoration: BoxDecoration(
          color: isActive 
              ? color.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(DesignTokens.radiusS),
          border: Border.all(
            color: isActive 
                ? color
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            width: isActive ? 2 : 1,
          ),
          boxShadow: isActive ? [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacingS),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(DesignTokens.radiusS),
              ),
              child: Icon(
                isEntry ? Icons.add : Icons.remove,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: DesignTokens.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        movement.date,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (isActive)
                        Icon(
                          Icons.check_circle,
                          color: color,
                          size: 16,
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    movement.description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'Qté: ${movement.quantity.toStringAsFixed(0)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(width: DesignTokens.spacingM),
                      Text(
                        'Prix: ${movement.unitPrice.toStringAsFixed(2)} DH',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const Spacer(),
                      Text(
                        'Total: ${(movement.quantity * movement.unitPrice).toStringAsFixed(2)} DH',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartView() {
    if (_calculations.isEmpty) {
      return const Center(
        child: Text('Aucune donnée disponible pour le graphique'),
      );
    }

    final chartData = _calculations.take(_currentStep).map((calc) {
      return FlSpot(
        _calculations.indexOf(calc).toDouble(),
        calc.averageCost,
      );
    }).toList();

    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingM),
      child: Column(
        children: [
          Text(
            'Évolution du Coût Unitaire Moyen Pondéré',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: DesignTokens.spacingM),
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(show: true),
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < _calculations.length) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              'M${index + 1}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 50,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        return Text(
                          '${value.toStringAsFixed(1)} DH',
                          style: Theme.of(context).textTheme.bodySmall,
                        );
                      },
                    ),
                  ),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: true),
                lineBarsData: [
                  LineChartBarData(
                    spots: chartData,
                    isCurved: true,
                    color: Theme.of(context).colorScheme.primary,
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 6,
                          color: Theme.of(context).colorScheme.primary,
                          strokeWidth: 2,
                          strokeColor: Theme.of(context).colorScheme.surface,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                    ),
                  ),
                ],
                lineTouchData: LineTouchData(
                  enabled: widget.isInteractive,
                  touchTooltipData: LineTouchTooltipData(
                    getTooltipItems: (touchedSpots) {
                      return touchedSpots.map((spot) {
                        final index = spot.x.toInt();
                        if (index < _calculations.length) {
                          return LineTooltipItem(
                            'Mouvement ${index + 1}\nCUMP: ${spot.y.toStringAsFixed(2)} DH',
                            Theme.of(context).textTheme.bodySmall!,
                          );
                        }
                        return null;
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationsView() {
    return ListView.builder(
      itemCount: _currentStep,
      itemBuilder: (context, index) {
        if (index >= _calculations.length) return const SizedBox.shrink();
        return _buildCalculationStep(_calculations[index], index);
      },
    );
  }

  Widget _buildCalculationStep(CUMPCalculation calculation, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacingM),
      padding: const EdgeInsets.all(DesignTokens.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Mouvement ${index + 1}',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const Spacer(),
              Text(
                calculation.movement.date,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          const SizedBox(height: DesignTokens.spacingS),
          _buildCalculationFormula(calculation),
          const SizedBox(height: DesignTokens.spacingS),
          Container(
            padding: const EdgeInsets.all(DesignTokens.spacingS),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(DesignTokens.radiusS),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'CUMP calculé:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${calculation.averageCost.toStringAsFixed(2)} DH/unité',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationFormula(CUMPCalculation calculation) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingS),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'CUMP = ',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Column(
                children: [
                  Text(
                    '${calculation.totalValue.toStringAsFixed(2)} DH',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  Container(
                    height: 1,
                    width: 100,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  Text(
                    '${calculation.totalQuantity.toStringAsFixed(0)} unités',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
              Text(
                ' = ${calculation.averageCost.toStringAsFixed(2)} DH',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton.icon(
          onPressed: _currentStep > 0 ? _previousStep : null,
          icon: const Icon(Icons.skip_previous),
          label: const Text('Précédent'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.secondary,
          ),
        ),
        const SizedBox(width: DesignTokens.spacingM),
        ElevatedButton.icon(
          onPressed: _isAnimating ? null : _nextStep,
          icon: _isAnimating 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.skip_next),
          label: Text(_currentStep >= _movements.length ? 'Terminer' : 'Mouvement suivant'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
      ],
    );
  }

  void _nextStep() {
    if (_currentStep >= _movements.length) {
      widget.onComplete?.call();
      return;
    }

    setState(() {
      _isAnimating = true;
      _currentStep++;
    });

    _animationController.forward().then((_) {
      setState(() {
        _isAnimating = false;
      });
      _animationController.reset();
    });
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }
}

/// Data model for stock movements
class StockMovement {
  final String date;
  final String type; // 'entry' or 'exit'
  final double quantity;
  final double unitPrice;
  final String description;

  const StockMovement({
    required this.date,
    required this.type,
    required this.quantity,
    required this.unitPrice,
    this.description = '',
  });
}

/// Data model for CUMP calculations
class CUMPCalculation {
  final StockMovement movement;
  final double totalQuantity;
  final double totalValue;
  final double averageCost;

  const CUMPCalculation({
    required this.movement,
    required this.totalQuantity,
    required this.totalValue,
    required this.averageCost,
  });
}