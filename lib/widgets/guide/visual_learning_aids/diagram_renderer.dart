import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../theme/semantic_colors.dart';
import 'fifo_diagram_widget.dart';
import 'double_entry_diagram_widget.dart';
import 'cump_diagram_widget.dart';

/// Universal diagram renderer that creates appropriate diagram widgets based on schema type
class DiagramRenderer extends StatelessWidget {
  final Map<String, dynamic> schema;
  final bool isInteractive;
  final VoidCallback? onInteractionComplete;

  const DiagramRenderer({
    super.key,
    required this.schema,
    this.isInteractive = true,
    this.onInteractionComplete,
  });

  @override
  Widget build(BuildContext context) {
    final diagramType = schema['type'] as String?;
    
    if (diagramType == null) {
      return _buildErrorWidget('Type de diagramme non spécifié');
    }

    try {
      return _buildDiagramWidget(context, diagramType);
    } catch (e) {
      return _buildErrorWidget('Erreur lors du rendu du diagramme: $e');
    }
  }

  /// Builds the appropriate diagram widget based on type
  Widget _buildDiagramWidget(BuildContext context, String diagramType) {
    switch (diagramType.toLowerCase()) {
      case 'fifodiagram':
      case 'fifo_diagram':
        return FIFODiagramWidget(
          schema: schema,
          isInteractive: isInteractive,
          onComplete: onInteractionComplete,
        );

      case 'doubleentrydiagram':
      case 'double_entry_diagram':
        return DoubleEntryDiagramWidget(
          schema: schema,
          isInteractive: isInteractive,
          onComplete: onInteractionComplete,
        );

      case 'cumpdiagram':
      case 'cump_diagram':
        return CUMPDiagramWidget(
          schema: schema,
          isInteractive: isInteractive,
          onComplete: onInteractionComplete,
        );

      case 'barchart':
        return _buildBarChart(context);

      case 'linechart':
        return _buildLineChart(context);

      case 'piechart':
        return _buildPieChart(context);

      case 'flowchart':
        return _buildFlowChart(context);

      case 'accountingtable':
        return _buildAccountingTable(context);

      default:
        return _buildUnsupportedWidget(diagramType);
    }
  }

  /// Builds a bar chart using fl_chart
  Widget _buildBarChart(BuildContext context) {
    final data = schema['data'] as Map<String, dynamic>? ?? {};
    final values = (data['values'] as List<dynamic>?)?.cast<double>() ?? [];
    final labels = (data['labels'] as List<dynamic>?)?.cast<String>() ?? [];

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: values.isNotEmpty ? values.reduce((a, b) => a > b ? a : b) * 1.2 : 100,
          barTouchData: BarTouchData(
            enabled: isInteractive,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  '${labels.length > groupIndex ? labels[groupIndex] : 'Item ${groupIndex + 1}'}\n${rod.toY}',
                  TextStyle(color: SemanticColors.onSurface(context)),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (double value, TitleMeta meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < labels.length) {
                    return Text(labels[index], style: Theme.of(context).textTheme.bodySmall);
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
              ),
            ),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          barGroups: values.asMap().entries.map((entry) {
            return BarChartGroupData(
              x: entry.key,
              barRods: [
                BarChartRodData(
                  toY: entry.value,
                  color: SemanticColors.primary(context),
                  width: 16,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Builds a line chart using fl_chart
  Widget _buildLineChart(BuildContext context) {
    final data = schema['data'] as Map<String, dynamic>? ?? {};
    final points = (data['points'] as List<dynamic>?)?.map((point) {
      final p = point as Map<String, dynamic>;
      return FlSpot(
        (p['x'] as num).toDouble(),
        (p['y'] as num).toDouble(),
      );
    }).toList() ?? [];

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: const FlGridData(show: true),
          titlesData: const FlTitlesData(
            bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: true)),
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: true, reservedSize: 40)),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: true),
          lineBarsData: [
            LineChartBarData(
              spots: points,
              isCurved: true,
              color: SemanticColors.primary(context),
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: SemanticColors.primary(context),
                    strokeWidth: 2,
                    strokeColor: SemanticColors.onPrimary(context),
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: SemanticColors.primary(context).withValues(alpha: 0.2),
              ),
            ),
          ],
          lineTouchData: LineTouchData(
            enabled: isInteractive,
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (touchedSpots) {
                return touchedSpots.map((spot) {
                  return LineTooltipItem(
                    'x: ${spot.x}\ny: ${spot.y}',
                    TextStyle(color: SemanticColors.onSurface(context)),
                  );
                }).toList();
              },
            ),
          ),
        ),
      ),
    );
  }

  /// Builds a pie chart using fl_chart
  Widget _buildPieChart(BuildContext context) {
    final data = schema['data'] as Map<String, dynamic>? ?? {};
    final values = (data['values'] as List<dynamic>?)?.cast<double>() ?? [];
    final labels = (data['labels'] as List<dynamic>?)?.cast<String>() ?? [];
    final colors = [
      SemanticColors.primary(context),
      SemanticColors.secondary(context),
      SemanticColors.tertiary(context),
      Colors.green,
      Colors.orange,
      Colors.purple,
    ];

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      child: PieChart(
        PieChartData(
          sections: values.asMap().entries.map((entry) {
            final index = entry.key;
            final value = entry.value;
            return PieChartSectionData(
              color: colors[index % colors.length],
              value: value,
              title: '${value.toStringAsFixed(1)}%',
              radius: 100,
              titleStyle: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: SemanticColors.onPrimary(context),
              ),
            );
          }).toList(),
          sectionsSpace: 2,
          centerSpaceRadius: 40,
          pieTouchData: PieTouchData(
            enabled: isInteractive,
            touchCallback: (FlTouchEvent event, pieTouchResponse) {
              // Handle touch interactions
            },
          ),
        ),
      ),
    );
  }

  /// Builds a flow chart widget
  Widget _buildFlowChart(BuildContext context) {
    final steps = (schema['steps'] as List<dynamic>?)?.cast<Map<String, dynamic>>() ?? [];
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: steps.asMap().entries.map((entry) {
          final index = entry.key;
          final step = entry.value;
          final isLast = index == steps.length - 1;
          
          return Column(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: SemanticColors.primaryContainer(context),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: SemanticColors.outline(context)),
                ),
                child: Column(
                  children: [
                    Text(
                      step['title'] ?? 'Étape ${index + 1}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: SemanticColors.onPrimaryContainer(context),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      step['description'] ?? '',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: SemanticColors.onPrimaryContainer(context),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              if (!isLast) ...[
                const SizedBox(height: 8),
                Icon(
                  Icons.arrow_downward,
                  color: SemanticColors.primary(context),
                  size: 24,
                ),
                const SizedBox(height: 8),
              ],
            ],
          );
        }).toList(),
      ),
    );
  }

  /// Builds an accounting table widget
  Widget _buildAccountingTable(BuildContext context) {
    final data = schema['data'] as Map<String, dynamic>? ?? {};
    final headers = (data['headers'] as List<dynamic>?)?.cast<String>() ?? [];
    final rows = (data['rows'] as List<dynamic>?)?.map((row) => 
        (row as List<dynamic>).cast<String>()).toList() ?? [];

    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: headers.map((header) => DataColumn(
            label: Text(
              header,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          )).toList(),
          rows: rows.map((row) => DataRow(
            cells: row.map((cell) => DataCell(Text(cell))).toList(),
          )).toList(),
          headingRowColor: WidgetStateProperty.all(
            SemanticColors.surfaceVariant(context),
          ),
          border: TableBorder.all(
            color: SemanticColors.outline(context),
            width: 1,
          ),
        ),
      ),
    );
  }

  /// Builds an error widget when diagram rendering fails
  Widget _buildErrorWidget(String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a widget for unsupported diagram types
  Widget _buildUnsupportedWidget(String diagramType) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.orange),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Type de diagramme non supporté: $diagramType',
              style: TextStyle(color: Colors.orange[800]),
            ),
          ),
        ],
      ),
    );
  }

  /// Factory method to create diagram renderer from schema
  static Widget fromSchema(
    Map<String, dynamic> schema, {
    bool isInteractive = true,
    VoidCallback? onComplete,
  }) {
    return DiagramRenderer(
      schema: schema,
      isInteractive: isInteractive,
      onInteractionComplete: onComplete,
    );
  }
}