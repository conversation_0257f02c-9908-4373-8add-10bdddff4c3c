import 'package:flutter/material.dart';

class TvaExemptionBadge extends StatelessWidget {
  final bool isExempt;
  final String? exemptionReason;
  final double? vatRate;
  final EdgeInsets? padding;
  final TextStyle? textStyle;

  const TvaExemptionBadge({
    super.key,
    required this.isExempt,
    this.exemptionReason,
    this.vatRate,
    this.padding,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final backgroundColor = isExempt
        ? colorScheme.primaryContainer
        : colorScheme.surfaceContainerHighest;
    
    final textColor = isExempt
        ? colorScheme.onPrimaryContainer
        : colorScheme.onSurfaceVariant;

    final icon = isExempt
        ? Icons.check_circle
        : Icons.percent;

    final displayText = isExempt
        ? 'Exonéré'
        : '${vatRate?.toStringAsFixed(0) ?? '20'}% TVA';

    final badge = AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isExempt
              ? colorScheme.primary.withValues(alpha: 0.2)
              : colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: textColor,
          ),
          const SizedBox(width: 6),
          Text(
            displayText,
            style: textStyle ??
                textTheme.labelMedium?.copyWith(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );

    // If there's an exemption reason, wrap in a tooltip
    if (isExempt && exemptionReason != null && exemptionReason!.isNotEmpty) {
      return Tooltip(
        message: exemptionReason!,
        decoration: BoxDecoration(
          color: colorScheme.inverseSurface,
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: textTheme.bodySmall?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
        preferBelow: false,
        child: badge,
      );
    }

    return badge;
  }
}