import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:moroccanaccounting/models/salary_calculator/salary_data.dart';

class BaseSalaryField extends StatelessWidget {
  final double value;
  final ValueChanged<double> onChanged;

  const BaseSalaryField({
    required this.value,
    required this.onChanged,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: value.toString(),
      decoration: InputDecoration(
        labelText: 'Salaire de base',
        suffixText: 'DH',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        helperText: 'Entrez le salaire brut de base',
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Ce champ est obligatoire';
        }
        final salary = double.tryParse(value);
        if (salary == null || salary <= 0) {
          return 'Veuillez entrer un montant valide';
        }
        return null;
      },
      onChanged: (value) => onChanged(double.tryParse(value) ?? 0),
    );
  }
}

class SectorToggle extends StatelessWidget {
  final bool isAgricultural;
  final ValueChanged<bool> onChanged;

  const SectorToggle({
    required this.isAgricultural,
    required this.onChanged,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: SegmentedButton<bool>(
        segments: const [
          ButtonSegment(
            value: false,
            label: Text('Non Agricole'),
            icon: Icon(Icons.business),
          ),
          ButtonSegment(
            value: true,
            label: Text('Agricole'),
            icon: Icon(Icons.agriculture),
          ),
        ],
        selected: {isAgricultural},
        onSelectionChanged: (Set<bool> newSelection) {
          onChanged(newSelection.first);
        },
      ),
    );
  }
}

class EmployeeTypeSelector extends StatelessWidget {
  final String type;
  final ValueChanged<String> onChanged;

  const EmployeeTypeSelector({
    required this.type,
    required this.onChanged,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: SegmentedButton<String>(
        segments: const [
          ButtonSegment(
            value: 'salarie',
            label: Text('Salarié'),
            icon: Icon(Icons.person_outline),
          ),
          ButtonSegment(
            value: 'cadre',
            label: Text('Cadre'),
            icon: Icon(Icons.business_center),
          ),
        ],
        selected: {type},
        onSelectionChanged: (Set<String> newSelection) {
          onChanged(newSelection.first);
        },
      ),
    );
  }
}

class OvertimeHoursInput extends StatelessWidget {
  final OvertimeHours hours;
  final ValueChanged<OvertimeHours> onChanged;

  const OvertimeHoursInput({
    required this.hours,
    required this.onChanged,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Heures supplémentaires',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildHourField(
                label: 'Jour (25%)',
                value: hours.regular,
                onChanged: (value) {
                  final newHours = OvertimeHours(
                    regular: value,
                    holiday: hours.holiday,
                    night: hours.night,
                  );
                  onChanged(newHours);
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildHourField(
                label: 'Férié (50%)',
                value: hours.holiday,
                onChanged: (value) {
                  final newHours = OvertimeHours(
                    regular: hours.regular,
                    holiday: value,
                    night: hours.night,
                  );
                  onChanged(newHours);
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildHourField(
                label: 'Nuit (100%)',
                value: hours.night,
                onChanged: (value) {
                  final newHours = OvertimeHours(
                    regular: hours.regular,
                    holiday: hours.holiday,
                    night: value,
                  );
                  onChanged(newHours);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildHourField({
    required String label,
    required double value,
    required ValueChanged<double> onChanged,
  }) {
    return TextFormField(
      initialValue: value.toString(),
      decoration: InputDecoration(
        labelText: label,
        suffixText: 'h',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      onChanged: (value) => onChanged(double.tryParse(value) ?? 0),
    );
  }
}

class BonusField extends StatelessWidget {
  final String label;
  final BonusEntry value;
  final ValueChanged<BonusEntry> onChanged;
  final bool showAnnualToggle;

  const BonusField({
    required this.label,
    required this.value,
    required this.onChanged,
    this.showAnnualToggle = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Checkbox to enable/disable the bonus
        Checkbox(
          value: value.enabled,
          onChanged: (enabled) {
            onChanged(value.copyWith(enabled: enabled));
          },
        ),
        // Amount input field
        Expanded(
          child: TextFormField(
            enabled: value.enabled,
            initialValue: value.amount.toString(),
            decoration: InputDecoration(
              labelText: label,
              suffixText: 'DH',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
            ],
            onChanged: (newValue) => onChanged(value.copyWith(
              amount: double.tryParse(newValue) ?? 0,
            )),
          ),
        ),
        // Annual toggle on the right
        if (showAnnualToggle) ...[
          const SizedBox(width: 8),
          Checkbox(
            value: value.isAnnual,
            onChanged: value.enabled
                ? (isAnnual) {
                    onChanged(value.copyWith(isAnnual: isAnnual));
                  }
                : null,
          ),
          const Text('Annuel'),
        ],
      ],
    );
  }
}
