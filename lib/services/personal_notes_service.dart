import 'dart:async';
import 'package:hive/hive.dart';
import '../models/guide/personal_note_data.dart';

/// Enhanced service for managing personal notes with advanced features
class PersonalNotesService {
  static const String _boxName = 'personal_notes';
  static const String _settingsBoxName = 'notes_settings';
  static Box<PersonalNoteData>? _box;
  static Box<Map<String, dynamic>>? _settingsBox;
  
  final StreamController<List<PersonalNoteData>> _notesController = 
      StreamController<List<PersonalNoteData>>.broadcast();
  final StreamController<NoteStatistics> _statisticsController = 
      StreamController<NoteStatistics>.broadcast();
  
  // Auto-save configuration
  Timer? _autoSaveTimer;
  final Map<String, Timer> _draftTimers = {};
  final Duration _autoSaveInterval = const Duration(seconds: 5);
  
  // Search index for faster searching
  final Map<String, Set<String>> _searchIndex = {};
  
  /// Stream of all notes
  Stream<List<PersonalNoteData>> get notesStream => _notesController.stream;
  
  /// Stream of note statistics
  Stream<NoteStatistics> get statisticsStream => _statisticsController.stream;
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_box == null) {
      _box = await Hive.openBox<PersonalNoteData>(_boxName);
      _settingsBox = await Hive.openBox<Map<String, dynamic>>(_settingsBoxName);
      await _buildSearchIndex();
      _notifyListeners();
      _notifyStatistics();
    }
  }
  
  /// Get all notes with optional sorting
  Future<List<PersonalNoteData>> getAllNotes({
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();
    final notes = _box!.values.where((note) => !note.id.endsWith('_draft')).toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get notes for a specific guide
  Future<List<PersonalNoteData>> getNotesByGuide(
    String guideId, {
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();
    final notes = _box!.values
        .where((note) => note.guideId == guideId && !note.id.endsWith('_draft'))
        .toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get notes for a specific section
  Future<List<PersonalNoteData>> getNotesBySection(
    String guideId, 
    String sectionId, {
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();
    final notes = _box!.values
        .where((note) => 
            note.guideId == guideId && 
            note.sectionId == sectionId &&
            !note.id.endsWith('_draft'))
        .toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get notes by type
  Future<List<PersonalNoteData>> getNotesByType(
    NoteType type, {
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();
    final notes = _box!.values
        .where((note) => note.noteType == type && !note.id.endsWith('_draft'))
        .toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get notes by tags with AND/OR logic
  Future<List<PersonalNoteData>> getNotesByTags(
    List<String> tags, {
    bool useAndLogic = false,
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();
    final notes = _box!.values.where((note) {
      if (note.id.endsWith('_draft')) return false;
      
      if (useAndLogic) {
        // All tags must be present
        return tags.every((tag) => note.tags.contains(tag));
      } else {
        // Any tag must be present
        return note.tags.any((tag) => tags.contains(tag));
      }
    }).toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get private notes only
  Future<List<PersonalNoteData>> getPrivateNotes({
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();
    final notes = _box!.values
        .where((note) => note.isPrivate && !note.id.endsWith('_draft'))
        .toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get public notes only
  Future<List<PersonalNoteData>> getPublicNotes({
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();
    final notes = _box!.values
        .where((note) => !note.isPrivate && !note.id.endsWith('_draft'))
        .toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get notes with reminders
  Future<List<PersonalNoteData>> getNotesWithReminders({
    NoteSortOrder sortOrder = NoteSortOrder.reminderDate,
  }) async {
    await _ensureInitialized();
    final notes = _box!.values
        .where((note) => note.reminderDate != null && !note.id.endsWith('_draft'))
        .toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get upcoming reminders
  Future<List<PersonalNoteData>> getUpcomingReminders({
    int daysAhead = 7,
    NoteSortOrder sortOrder = NoteSortOrder.reminderDate,
  }) async {
    await _ensureInitialized();
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: daysAhead));
    
    final notes = _box!.values
        .where((note) => 
            note.reminderDate != null &&
            note.reminderDate!.isAfter(now) &&
            note.reminderDate!.isBefore(futureDate) &&
            !note.id.endsWith('_draft'))
        .toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get overdue reminders
  Future<List<PersonalNoteData>> getOverdueReminders({
    NoteSortOrder sortOrder = NoteSortOrder.reminderDate,
  }) async {
    await _ensureInitialized();
    final now = DateTime.now();
    
    final notes = _box!.values
        .where((note) => 
            note.reminderDate != null &&
            note.reminderDate!.isBefore(now) &&
            !note.id.endsWith('_draft'))
        .toList();
    return _sortNotes(notes, sortOrder);
  }
  
  /// Get a specific note by ID
  Future<PersonalNoteData?> getNote(String id) async {
    await _ensureInitialized();
    return _box!.get(id);
  }
  
  /// Add a new note
  Future<String> addNote(PersonalNoteData note) async {
    await _ensureInitialized();
    await _box!.put(note.id, note);
    await _updateSearchIndex(note);
    _notifyListeners();
    _notifyStatistics();
    return note.id;
  }
  
  /// Update an existing note
  Future<void> updateNote(PersonalNoteData note) async {
    await _ensureInitialized();
    if (_box!.containsKey(note.id)) {
      note.lastModified = DateTime.now();
      await _box!.put(note.id, note);
      await _updateSearchIndex(note);
      _notifyListeners();
      _notifyStatistics();
    }
  }
  
  /// Delete a note by ID
  Future<void> deleteNote(String id) async {
    await _ensureInitialized();
    final note = _box!.get(id);
    if (note != null) {
      await _removeFromSearchIndex(note);
      await _box!.delete(id);
      // Also delete any draft version
      await _box!.delete('${id}_draft');
      _notifyListeners();
      _notifyStatistics();
    }
  }
  
  /// Delete multiple notes by IDs
  Future<void> deleteNotes(List<String> ids) async {
    await _ensureInitialized();
    for (final id in ids) {
      final note = _box!.get(id);
      if (note != null) {
        await _removeFromSearchIndex(note);
        await _box!.delete(id);
        await _box!.delete('${id}_draft');
      }
    }
    _notifyListeners();
    _notifyStatistics();
  }
  
  /// Delete notes for a specific section
  Future<void> deleteNotesBySection(String guideId, String sectionId) async {
    await _ensureInitialized();
    final notesToDelete = _box!.values
        .where((note) => 
            note.guideId == guideId && 
            note.sectionId == sectionId)
        .map((note) => note.id)
        .toList();
    
    await deleteNotes(notesToDelete);
  }
  
  /// Clear all notes
  Future<void> clearAllNotes() async {
    await _ensureInitialized();
    await _box!.clear();
    _searchIndex.clear();
    _notifyListeners();
    _notifyStatistics();
  }

  /// Advanced search with multiple criteria
  Future<List<PersonalNoteData>> searchNotes(
    String query, {
    List<String>? tags,
    List<NoteType>? types,
    String? guideId,
    String? sectionId,
    bool? isPrivate,
    DateTime? fromDate,
    DateTime? toDate,
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();

    if (query.isEmpty &&
        (tags?.isEmpty ?? true) &&
        (types?.isEmpty ?? true) &&
        guideId == null &&
        sectionId == null &&
        isPrivate == null &&
        fromDate == null &&
        toDate == null) {
      return getAllNotes(sortOrder: sortOrder);
    }

    final lowerQuery = query.toLowerCase();
    final notes = _box!.values.where((note) {
      if (note.id.endsWith('_draft')) return false;

      // Text search
      if (query.isNotEmpty) {
        final searchableText = note.getSearchableText();
        if (!searchableText.contains(lowerQuery)) return false;
      }

      // Tag filter
      if (tags != null && tags.isNotEmpty) {
        if (!note.tags.any((tag) => tags.contains(tag))) return false;
      }

      // Type filter
      if (types != null && types.isNotEmpty) {
        if (!types.contains(note.noteType)) return false;
      }

      // Guide filter
      if (guideId != null && note.guideId != guideId) return false;

      // Section filter
      if (sectionId != null && note.sectionId != sectionId) return false;

      // Privacy filter
      if (isPrivate != null && note.isPrivate != isPrivate) return false;

      // Date range filter
      if (fromDate != null && note.createdAt.isBefore(fromDate)) return false;
      if (toDate != null && note.createdAt.isAfter(toDate)) return false;

      return true;
    }).toList();

    return _sortNotes(notes, sortOrder);
  }

  /// Fast search using search index
  Future<List<PersonalNoteData>> fastSearch(String query) async {
    await _ensureInitialized();

    if (query.isEmpty) return getAllNotes();

    final words = query.toLowerCase().split(' ').where((w) => w.length > 2).toList();
    if (words.isEmpty) return searchNotes(query);

    Set<String>? matchingNoteIds;

    for (final word in words) {
      final wordMatches = _searchIndex[word] ?? <String>{};
      if (matchingNoteIds == null) {
        matchingNoteIds = Set.from(wordMatches);
      } else {
        matchingNoteIds = matchingNoteIds.intersection(wordMatches);
      }
    }

    if (matchingNoteIds == null || matchingNoteIds.isEmpty) {
      return [];
    }

    final notes = matchingNoteIds
        .map((id) => _box!.get(id))
        .where((note) => note != null && !note.id.endsWith('_draft'))
        .cast<PersonalNoteData>()
        .toList();

    return _sortNotes(notes, NoteSortOrder.dateDescending);
  }

  /// Get all unique tags with usage count
  Future<Map<String, int>> getTagsWithCount() async {
    await _ensureInitialized();
    final tagCount = <String, int>{};

    for (final note in _box!.values) {
      if (note.id.endsWith('_draft')) continue;
      for (final tag in note.tags) {
        tagCount[tag] = (tagCount[tag] ?? 0) + 1;
      }
    }

    return tagCount;
  }

  /// Get all unique tags
  Future<List<String>> getAllTags() async {
    final tagCount = await getTagsWithCount();
    return tagCount.keys.toList()..sort();
  }

  /// Get popular tags (most used)
  Future<List<String>> getPopularTags({int limit = 10}) async {
    final tagCount = await getTagsWithCount();
    final sortedTags = tagCount.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedTags.take(limit).map((e) => e.key).toList();
  }

  /// Get note statistics
  Future<NoteStatistics> getStatistics() async {
    await _ensureInitialized();
    final notes = _box!.values.where((note) => !note.id.endsWith('_draft')).toList();

    final Map<String, int> guideCount = {};
    final Map<NoteType, int> typeCount = {};
    final Set<String> uniqueTags = {};
    int privateCount = 0;
    int reminderCount = 0;

    for (final note in notes) {
      // Count by guide
      guideCount[note.guideId] = (guideCount[note.guideId] ?? 0) + 1;

      // Count by type
      typeCount[note.noteType] = (typeCount[note.noteType] ?? 0) + 1;

      // Collect unique tags
      uniqueTags.addAll(note.tags);

      // Count private notes
      if (note.isPrivate) privateCount++;

      // Count notes with reminders
      if (note.reminderDate != null) reminderCount++;
    }

    return NoteStatistics(
      totalNotes: notes.length,
      privateNotes: privateCount,
      publicNotes: notes.length - privateCount,
      notesWithReminders: reminderCount,
      notesByGuide: guideCount,
      notesByType: typeCount,
      uniqueTags: uniqueTags.toList()..sort(),
      oldestNote: notes.isEmpty
          ? null
          : notes.reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b).createdAt,
      newestNote: notes.isEmpty
          ? null
          : notes.reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b).createdAt,
    );
  }

  /// Export notes to JSON
  Future<Map<String, dynamic>> exportNotes({bool includePrivate = false}) async {
    await _ensureInitialized();
    final notes = await getAllNotes();
    final exportNotes = includePrivate
        ? notes
        : notes.where((note) => !note.isPrivate).toList();

    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'includePrivate': includePrivate,
      'notes': exportNotes.map((note) => note.toJson()).toList(),
    };
  }

  /// Import notes from JSON
  Future<ImportResult> importNotes(Map<String, dynamic> data, {bool merge = true}) async {
    await _ensureInitialized();

    try {
      final notesList = data['notes'] as List<dynamic>?;
      if (notesList == null) {
        return ImportResult(success: false, message: 'Invalid data format');
      }

      final notes = notesList
          .map((json) => PersonalNoteData.fromJson(json as Map<String, dynamic>))
          .toList();

      int imported = 0;
      int skipped = 0;

      if (!merge) {
        await clearAllNotes();
      }

      for (final note in notes) {
        if (merge && _box!.containsKey(note.id)) {
          skipped++;
        } else {
          await _box!.put(note.id, note);
          await _updateSearchIndex(note);
          imported++;
        }
      }

      _notifyListeners();
      _notifyStatistics();

      return ImportResult(
        success: true,
        message: 'Import completed: $imported imported, $skipped skipped',
        importedCount: imported,
        skippedCount: skipped,
      );
    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Import failed: $e',
      );
    }
  }

  /// Get recent notes
  Future<List<PersonalNoteData>> getRecentNotes({
    int limit = 10,
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    final notes = await getAllNotes(sortOrder: sortOrder);
    return notes.take(limit).toList();
  }

  /// Get notes created in the last N days
  Future<List<PersonalNoteData>> getRecentNotesByDays(
    int days, {
    NoteSortOrder sortOrder = NoteSortOrder.dateDescending,
  }) async {
    await _ensureInitialized();
    final cutoffDate = DateTime.now().subtract(Duration(days: days));

    final notes = _box!.values
        .where((note) =>
            note.createdAt.isAfter(cutoffDate) &&
            !note.id.endsWith('_draft'))
        .toList();

    return _sortNotes(notes, sortOrder);
  }

  /// Auto-save a note (used for draft functionality)
  Future<void> autoSaveNote(PersonalNoteData note) async {
    await _ensureInitialized();

    // Cancel existing timer for this note
    _draftTimers[note.id]?.cancel();

    // Create a draft version with auto-save flag
    final draftNote = PersonalNoteData(
      id: '${note.id}_draft',
      guideId: note.guideId,
      sectionId: note.sectionId,
      title: note.title,
      content: note.content,
      noteType: note.noteType,
      tags: note.tags,
      isPrivate: note.isPrivate,
      reminderDate: note.reminderDate,
      formattedContent: note.formattedContent,
    );
    draftNote.lastModified = DateTime.now();

    // Set up auto-save timer
    _draftTimers[note.id] = Timer(_autoSaveInterval, () async {
      await _box!.put(draftNote.id, draftNote);
    });
  }

  /// Get draft version of a note
  Future<PersonalNoteData?> getDraftNote(String originalId) async {
    await _ensureInitialized();
    return _box!.get('${originalId}_draft');
  }

  /// Delete draft version of a note
  Future<void> deleteDraftNote(String originalId) async {
    await _ensureInitialized();
    await _box!.delete('${originalId}_draft');
    _draftTimers[originalId]?.cancel();
    _draftTimers.remove(originalId);
  }

  /// Promote draft to final note
  Future<void> promoteDraftToNote(String originalId) async {
    final draft = await getDraftNote(originalId);
    if (draft != null) {
      final finalNote = draft.copyWith(id: originalId);
      await updateNote(finalNote);
      await deleteDraftNote(originalId);
    }
  }

  /// Get user preferences for notes
  Future<Map<String, dynamic>> getUserPreferences() async {
    await _ensureInitialized();
    return _settingsBox!.get('preferences') ?? <String, dynamic>{};
  }

  /// Save user preferences for notes
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    await _ensureInitialized();
    await _settingsBox!.put('preferences', preferences);
  }

  /// Private helper methods
  Future<void> _ensureInitialized() async {
    if (_box == null) {
      await initialize();
    }
  }

  void _notifyListeners() {
    if (_box != null) {
      // Filter out draft notes from regular notifications
      final regularNotes = _box!.values
          .where((note) => !note.id.endsWith('_draft'))
          .toList();
      final sortedNotes = _sortNotes(regularNotes, NoteSortOrder.dateDescending);
      _notesController.add(sortedNotes);
    }
  }

  void _notifyStatistics() async {
    if (_box != null) {
      final stats = await getStatistics();
      _statisticsController.add(stats);
    }
  }

  List<PersonalNoteData> _sortNotes(List<PersonalNoteData> notes, NoteSortOrder sortOrder) {
    switch (sortOrder) {
      case NoteSortOrder.dateAscending:
        notes.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case NoteSortOrder.dateDescending:
        notes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case NoteSortOrder.titleAscending:
        notes.sort((a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()));
        break;
      case NoteSortOrder.titleDescending:
        notes.sort((a, b) => b.title.toLowerCase().compareTo(a.title.toLowerCase()));
        break;
      case NoteSortOrder.reminderDate:
        notes.sort((a, b) {
          if (a.reminderDate == null && b.reminderDate == null) return 0;
          if (a.reminderDate == null) return 1;
          if (b.reminderDate == null) return -1;
          return a.reminderDate!.compareTo(b.reminderDate!);
        });
        break;
      case NoteSortOrder.typeGrouped:
        notes.sort((a, b) {
          final typeComparison = a.noteType.index.compareTo(b.noteType.index);
          if (typeComparison != 0) return typeComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
    }
    return notes;
  }

  Future<void> _buildSearchIndex() async {
    _searchIndex.clear();
    for (final note in _box!.values) {
      if (!note.id.endsWith('_draft')) {
        await _updateSearchIndex(note);
      }
    }
  }

  Future<void> _updateSearchIndex(PersonalNoteData note) async {
    // Remove old index entries for this note
    await _removeFromSearchIndex(note);

    // Add new index entries
    final words = _extractWords(note.getSearchableText());
    for (final word in words) {
      _searchIndex.putIfAbsent(word, () => <String>{}).add(note.id);
    }
  }

  Future<void> _removeFromSearchIndex(PersonalNoteData note) async {
    _searchIndex.forEach((word, noteIds) {
      noteIds.remove(note.id);
    });
    // Remove empty entries
    _searchIndex.removeWhere((word, noteIds) => noteIds.isEmpty);
  }

  Set<String> _extractWords(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), ' ')
        .split(RegExp(r'\s+'))
        .where((word) => word.length > 2)
        .toSet();
  }

  /// Dispose of resources
  void dispose() {
    _notesController.close();
    _statisticsController.close();
    _autoSaveTimer?.cancel();
    for (final timer in _draftTimers.values) {
      timer.cancel();
    }
    _draftTimers.clear();
  }
}

/// Enumeration for note sorting options
enum NoteSortOrder {
  dateAscending,
  dateDescending,
  titleAscending,
  titleDescending,
  reminderDate,
  typeGrouped,
}

/// Statistics about notes
class NoteStatistics {
  final int totalNotes;
  final int privateNotes;
  final int publicNotes;
  final int notesWithReminders;
  final Map<String, int> notesByGuide;
  final Map<NoteType, int> notesByType;
  final List<String> uniqueTags;
  final DateTime? oldestNote;
  final DateTime? newestNote;

  const NoteStatistics({
    required this.totalNotes,
    required this.privateNotes,
    required this.publicNotes,
    required this.notesWithReminders,
    required this.notesByGuide,
    required this.notesByType,
    required this.uniqueTags,
    this.oldestNote,
    this.newestNote,
  });

  /// Get average notes per guide
  double get averageNotesPerGuide {
    if (notesByGuide.isEmpty) return 0.0;
    return totalNotes / notesByGuide.length;
  }

  /// Get most active guide
  String? get mostActiveGuide {
    if (notesByGuide.isEmpty) return null;
    return notesByGuide.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Get most used note type
  NoteType? get mostUsedNoteType {
    if (notesByType.isEmpty) return null;
    return notesByType.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
}

/// Result of import operation
class ImportResult {
  final bool success;
  final String message;
  final int importedCount;
  final int skippedCount;

  const ImportResult({
    required this.success,
    required this.message,
    this.importedCount = 0,
    this.skippedCount = 0,
  });

  /// Get success rate as percentage
  double get successRate {
    final total = importedCount + skippedCount;
    if (total == 0) return 0.0;
    return (importedCount / total) * 100;
  }
}
