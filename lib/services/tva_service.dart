import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/tva/tva_data.dart';
import '../models/tva/ras_tva_data.dart';
import '../models/tva/calcul_tva_data.dart';
import '../models/tva/nouveautes_tva_data.dart';
import '../models/tva/exercices_tva_data.dart';
import '../models/tva/remboursement_tva_data.dart';

class TvaService {
  static final TvaService _instance = TvaService._internal();
  factory TvaService() => _instance;
  TvaService._internal();

  TvaData? _cachedTvaData2025;
  RasTvaData? _cachedRasTvaData;
  CalculTvaData? _cachedCalculTvaData;
  NouveautesTvaData? _cachedNouveautesTvaData;
  ExercicesTvaData? _cachedExercicesTvaData;
  RemboursementTvaData? _cachedRemboursementTvaData;

  Future<TvaData> getTva2025() async {
    if (_cachedTvaData2025 != null) {
      print('Returning cached TVA 2025 data');
      return _cachedTvaData2025!;
    }

    try {
      print('Loading TVA 2025 data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/tva_2025.json');
      final data = json.decode(jsonString);
      _cachedTvaData2025 = TvaData.fromJson(data);
      print('Successfully loaded TVA 2025 data');
      return _cachedTvaData2025!;
    } catch (e) {
      print('Error loading TVA 2025 data: $e');
      rethrow;
    }
  }

  Future<RasTvaData> getRasTva() async {
    if (_cachedRasTvaData != null) {
      print('Returning cached RAS TVA data');
      return _cachedRasTvaData!;
    }

    try {
      print('Loading RAS TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/ras_tva.json');
      final data = json.decode(jsonString);
      _cachedRasTvaData = RasTvaData.fromJson(data);
      print('Successfully loaded RAS TVA data');
      return _cachedRasTvaData!;
    } catch (e) {
      print('Error loading RAS TVA data: $e');
      rethrow;
    }
  }

  Future<CalculTvaData> getCalculTva() async {
    if (_cachedCalculTvaData != null) {
      print('Returning cached Calcul TVA data');
      return _cachedCalculTvaData!;
    }

    try {
      print('Loading Calcul TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/calcul_tva.json');
      final data = json.decode(jsonString);
      _cachedCalculTvaData = CalculTvaData.fromJson(data);
      print('Successfully loaded Calcul TVA data');
      return _cachedCalculTvaData!;
    } catch (e) {
      print('Error loading Calcul TVA data: $e');
      rethrow;
    }
  }

  Future<NouveautesTvaData> getNouveautesTva() async {
    if (_cachedNouveautesTvaData != null) {
      print('Returning cached Nouveautes TVA data');
      return _cachedNouveautesTvaData!;
    }

    try {
      print('Loading Nouveautes TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/nouveautes_tva.json');
      final data = json.decode(jsonString);
      _cachedNouveautesTvaData = NouveautesTvaData.fromJson(data);
      print('Successfully loaded Nouveautes TVA data');
      return _cachedNouveautesTvaData!;
    } catch (e) {
      print('Error loading Nouveautes TVA data: $e');
      rethrow;
    }
  }

  Future<ExercicesTvaData> getExercicesTva() async {
    if (_cachedExercicesTvaData != null) {
      print('Returning cached Exercices TVA data');
      return _cachedExercicesTvaData!;
    }

    try {
      print('Loading Exercices TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/exercices_tva.json');
      final data = json.decode(jsonString);
      _cachedExercicesTvaData = ExercicesTvaData.fromJson(data);
      print('Successfully loaded Exercices TVA data');
      return _cachedExercicesTvaData!;
    } catch (e) {
      print('Error loading Exercices TVA data: $e');
      rethrow;
    }
  }

  Future<RemboursementTvaData> getRemboursementTvaData() async {
    if (_cachedRemboursementTvaData != null) {
      print('Returning cached Remboursement TVA data');
      return _cachedRemboursementTvaData!;
    }

    try {
      print('Loading Remboursement TVA data from assets');
      final String jsonString =
          await rootBundle.loadString('assets/tva/remboursement_tva_2025.json');
      final data = json.decode(jsonString);
      _cachedRemboursementTvaData = RemboursementTvaData.fromJson(data);
      print('Successfully loaded Remboursement TVA data');
      return _cachedRemboursementTvaData!;
    } catch (e) {
      print('Error loading Remboursement TVA data: $e');
      rethrow;
    }
  }

  void clearCache() {
    print('Clearing TVA data cache');
    _cachedTvaData2025 = null;
    _cachedRasTvaData = null;
    _cachedCalculTvaData = null;
    _cachedNouveautesTvaData = null;
    _cachedExercicesTvaData = null;
    _cachedRemboursementTvaData = null;
  }

  /// Finds the VAT rate for a given product name
  /// Performs case-insensitive matching on product names
  /// Returns 20.0 as default if product not found
  Future<double> findVatRate(String productName, {int year = 2025}) async {
    final tvaData = await getTva2025();
    
    final productNameLower = productName.toLowerCase().trim();
    
    for (final category in tvaData.categories) {
      for (final item in category.produitsServices) {
        if (item.nom.toLowerCase().trim() == productNameLower) {
          final rate = year == 2025 ? item.tva2025 : item.tva2026;
          return rate.toDouble();
        }
      }
    }
    
    // Return default rate if product not found
    return 20.0;
  }

  /// Checks if a product is exempt from VAT (rate is 0.0)
  Future<bool> isProductExempt(String productName, {int year = 2025}) async {
    final rate = await findVatRate(productName, year: year);
    return rate == 0.0;
  }

  /// Gets the exemption reason for a product if it's exempt
  /// Returns null if not exempt or not found
  Future<String?> getExemptionReason(String productName, {int year = 2025}) async {
    final tvaData = await getTva2025();
    
    final productNameLower = productName.toLowerCase().trim();
    
    for (final category in tvaData.categories) {
      for (final item in category.produitsServices) {
        if (item.nom.toLowerCase().trim() == productNameLower) {
          final rate = year == 2025 ? item.tva2025 : item.tva2026;
          if (rate == 0.0) {
            return item.remarques;
          }
          return null;
        }
      }
    }
    
    return null;
  }

  /// Gets all product names from all categories for autocomplete functionality
  Future<List<String>> getAllProductNames() async {
    final tvaData = await getTva2025();
    
    final Set<String> productNames = <String>{};
    
    for (final category in tvaData.categories) {
      for (final item in category.produitsServices) {
        productNames.add(item.nom);
      }
    }
    
    final sortedNames = productNames.toList()..sort();
    return sortedNames;
  }

  /// Gets all categories from cached data, loads data if not cached
  Future<List<TvaCategory>> getAllCategories() async {
    final tvaData = await getTva2025();
    return tvaData.categories;
  }
}
