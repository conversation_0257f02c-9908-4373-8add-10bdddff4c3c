import 'package:hive/hive.dart';
import '../models/calculators/calculation_history_item.dart';
import '../utils/excel_export_utils.dart';

class CalculationHistoryService {
  static const String _boxName = 'calculation_history';
  Box<CalculationHistoryItem>? _box;

  Future<void> initialize() async {
    if (_box == null || !_box!.isOpen) {
      _box = await Hive.openBox<CalculationHistoryItem>(_boxName);
    }
  }

  Future<void> saveCalculation(CalculationHistoryItem item) async {
    await initialize();
    await _box!.put(item.id, item);
  }

  Future<List<CalculationHistoryItem>> getHistory({
    String? calculatorType,
    int? limit,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    await initialize();
    
    var items = _box!.values.toList();
    
    // Filter by calculator type
    if (calculatorType != null) {
      items = items.where((item) => 
        item.calculatorType.name == calculatorType).toList();
    }
    
    // Filter by date range
    if (startDate != null) {
      items = items.where((item) => 
        item.createdAt.isAfter(startDate.subtract(const Duration(days: 1)))).toList();
    }
    
    if (endDate != null) {
      items = items.where((item) => 
        item.createdAt.isBefore(endDate.add(const Duration(days: 1)))).toList();
    }
    
    // Sort by creation date (newest first)
    items.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    // Apply limit
    if (limit != null && limit > 0) {
      items = items.take(limit).toList();
    }
    
    return items;
  }

  Future<List<CalculationHistoryItem>> searchHistory(String query) async {
    await initialize();
    
    if (query.isEmpty) {
      return await getHistory();
    }
    
    final items = _box!.values.where((item) => item.matchesQuery(query)).toList();
    items.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    return items;
  }

  Future<List<CalculationHistoryItem>> getFavorites() async {
    await initialize();
    
    final items = _box!.values.where((item) => item.isFavorite).toList();
    items.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    return items;
  }

  Future<void> deleteCalculation(String id) async {
    await initialize();
    await _box!.delete(id);
  }

  Future<void> toggleFavorite(String id) async {
    await initialize();
    
    final item = _box!.get(id);
    if (item != null) {
      final updatedItem = item.copyWith(isFavorite: !item.isFavorite);
      await _box!.put(id, updatedItem);
    }
  }

  Future<void> updateCalculation(CalculationHistoryItem item) async {
    await initialize();
    await _box!.put(item.id, item);
  }

  Future<Map<String, int>> getCalculationCounts() async {
    await initialize();
    
    final counts = <String, int>{};
    for (final item in _box!.values) {
      final type = item.calculatorType.name;
      counts[type] = (counts[type] ?? 0) + 1;
    }
    
    return counts;
  }

  Future<List<String>> getMostUsedCalculators() async {
    final counts = await getCalculationCounts();
    final sortedEntries = counts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedEntries.map((e) => e.key).toList();
  }

  Future<List<CalculationHistoryItem>> getRecentActivity({int days = 7}) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return await getHistory(startDate: cutoffDate);
  }

  Future<Map<String, dynamic>> getStatistics() async {
    await initialize();
    
    final items = _box!.values.cast<CalculationHistoryItem>().toList();
    final now = DateTime.now();
    
    // Calculate statistics
    final totalCalculations = items.length;
    final favoriteCount = items.where((item) => item.isFavorite).length;
    
    // Recent activity (last 30 days)
    final last30Days = items.where((item) => 
      item.createdAt.isAfter(now.subtract(const Duration(days: 30)))).length;
    
    // Most active calculator type
    final typeCounts = <CalculatorType, int>{};
    for (final item in items) {
      typeCounts[item.calculatorType] = (typeCounts[item.calculatorType] ?? 0) + 1;
    }
    
    CalculatorType? mostUsedType;
    int maxCount = 0;
    for (final entry in typeCounts.entries) {
      if (entry.value > maxCount) {
        maxCount = entry.value;
        mostUsedType = entry.key;
      }
    }
    
    // Average calculations per week
    final oldestDate = items.isEmpty ? now :
      items.reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b).createdAt;
    final weeksSinceFirst = now.difference(oldestDate).inDays / 7;
    final avgPerWeek = weeksSinceFirst > 0 ? totalCalculations / weeksSinceFirst : 0;
    
    return {
      'totalCalculations': totalCalculations,
      'favoriteCount': favoriteCount,
      'last30Days': last30Days,
      'mostUsedType': mostUsedType?.displayName ?? 'Aucun',
      'averagePerWeek': avgPerWeek.toStringAsFixed(1),
      'typeBreakdown': typeCounts.map((k, v) => MapEntry(k.displayName, v)),
    };
  }

  Future<void> exportHistory(
    List<String> ids, 
    ExportFormat format,
  ) async {
    await initialize();
    
    final items = ids.map((id) => _box!.get(id)).where((item) => item != null).cast<CalculationHistoryItem>().toList();
    
    if (items.isEmpty) return;
    
    final tableData = TableData(
      name: 'historique_calculs',
      title: 'Historique des Calculs',
      headers: [
        'Date',
        'Calculateur',
        'Nom',
        'Tags',
        'Favori',
      ],
      rows: items.map((item) => [
        _formatDate(item.createdAt),
        item.calculatorType.displayName,
        item.displayName,
        item.tags.join(', '),
        item.isFavorite ? 'Oui' : 'Non',
      ]).toList(),
    );
    
    switch (format) {
      case ExportFormat.excel:
        await ExcelExportUtils.exportMultipleTablesExcel([tableData], fileName: 'historique_calculs');
        break;
      case ExportFormat.pdf:
        // TODO: Implement PDF export when PDF utils are available
        break;
      case ExportFormat.csv:
        // TODO: Implement CSV export
        break;
    }
  }

  Future<void> cleanupOldEntries({int maxAgeInDays = 365}) async {
    await initialize();
    
    final cutoffDate = DateTime.now().subtract(Duration(days: maxAgeInDays));
    final keysToDelete = <String>[];
    
    for (final item in _box!.values) {
      if (item.createdAt.isBefore(cutoffDate) && !item.isFavorite) {
        keysToDelete.add(item.id);
      }
    }
    
    for (final key in keysToDelete) {
      await _box!.delete(key);
    }
  }

  Future<Map<String, dynamic>> exportBackup() async {
    await initialize();
    
    final items = _box!.values.toList();
    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
    };
  }

  Future<void> importBackup(Map<String, dynamic> backup) async {
    await initialize();
    
    final items = backup['items'] as List<dynamic>?;
    if (items == null) return;
    
    for (final itemJson in items) {
      try {
        final item = CalculationHistoryItem.fromJson(itemJson as Map<String, dynamic>);
        await _box!.put(item.id, item);
      } catch (e) {
        // Skip invalid items
        continue;
      }
    }
  }

  Future<void> dispose() async {
    await _box?.close();
    _box = null;
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

// ExportFormat enum moved to calculation_history_item.dart to avoid duplication
