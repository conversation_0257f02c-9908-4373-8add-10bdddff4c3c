import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:hive/hive.dart';
import '../models/guide/interactive_step_data.dart';

/// Service for managing interactive content and walkthroughs
class InteractiveContentService {
  static InteractiveContentService? _instance;
  static InteractiveContentService get instance => _instance ??= InteractiveContentService._();

  InteractiveContentService._();

  // Cache for loaded content
  final Map<String, Map<String, dynamic>> _contentCache = {};
  final Map<String, List<InteractiveStepData>> _stepsCache = {};

  // Progress tracking
  static const String _progressBoxName = 'interactive_progress';
  static const String _userStateBoxName = 'interactive_user_state';
  static Box<Map<String, dynamic>>? _progressBox;
  static Box<Map<String, dynamic>>? _userStateBox;

  // Stream controllers for real-time updates
  final StreamController<InteractiveProgress> _progressController =
      StreamController<InteractiveProgress>.broadcast();
  final StreamController<UserInteractionState> _stateController =
      StreamController<UserInteractionState>.broadcast();

  /// Stream of progress updates
  Stream<InteractiveProgress> get progressStream => _progressController.stream;

  /// Stream of user state updates
  Stream<UserInteractionState> get stateStream => _stateController.stream;

  /// Initialize the service
  Future<void> initialize() async {
    if (_progressBox == null) {
      _progressBox = await Hive.openBox<Map<String, dynamic>>(_progressBoxName);
      _userStateBox = await Hive.openBox<Map<String, dynamic>>(_userStateBoxName);
    }
  }
  
  /// Load interactive content for a specific guide and section
  Future<Map<String, dynamic>?> loadInteractiveContent(String guideId, String sectionId) async {
    try {
      final cacheKey = '${guideId}_$sectionId';
      
      // Return cached content if available
      if (_contentCache.containsKey(cacheKey)) {
        return _contentCache[cacheKey];
      }
      
      // Load from assets
      final assetPath = 'assets/guides/$guideId/interactive/$sectionId.json';
      final String jsonString = await rootBundle.loadString(assetPath);
      final Map<String, dynamic> content = json.decode(jsonString);
      
      // Cache the content
      _contentCache[cacheKey] = content;
      
      return content;
    } catch (e) {
      // Return null if content doesn't exist or fails to load
      return null;
    }
  }
  
  /// Load interactive steps for a walkthrough
  Future<List<InteractiveStepData>> loadInteractiveSteps(String guideId, String walkthroughId) async {
    try {
      final cacheKey = '${guideId}_$walkthroughId';
      
      // Return cached steps if available
      if (_stepsCache.containsKey(cacheKey)) {
        return _stepsCache[cacheKey]!;
      }
      
      // Load content and extract steps
      final content = await loadInteractiveContent(guideId, walkthroughId);
      if (content == null) return [];
      
      final stepsData = content['steps'] as List<dynamic>? ?? [];
      final steps = stepsData
          .map((stepJson) => InteractiveStepData.fromJson(stepJson as Map<String, dynamic>))
          .toList();
      
      // Cache the steps
      _stepsCache[cacheKey] = steps;
      
      return steps;
    } catch (e) {
      return [];
    }
  }
  
  /// Get simulation scenario data
  Future<Map<String, dynamic>?> getSimulationScenario(String guideId, String simulationId) async {
    try {
      final content = await loadInteractiveContent(guideId, simulationId);
      return content?['scenario'] as Map<String, dynamic>?;
    } catch (e) {
      return null;
    }
  }
  
  /// Get diagram configuration
  Future<Map<String, dynamic>?> getDiagramConfig(String guideId, String diagramId) async {
    try {
      final content = await loadInteractiveContent(guideId, diagramId);
      return content?['diagram'] as Map<String, dynamic>?;
    } catch (e) {
      return null;
    }
  }
  
  /// Check if interactive content exists
  Future<bool> hasInteractiveContent(String guideId, String sectionId) async {
    try {
      final content = await loadInteractiveContent(guideId, sectionId);
      return content != null;
    } catch (e) {
      return false;
    }
  }
  
  /// Get available interactive content types for a section
  Future<List<InteractiveContentType>> getAvailableContentTypes(String guideId, String sectionId) async {
    try {
      final content = await loadInteractiveContent(guideId, sectionId);
      if (content == null) return [];
      
      final List<InteractiveContentType> types = [];
      
      if (content.containsKey('steps')) {
        types.add(InteractiveContentType.walkthrough);
      }
      
      if (content.containsKey('scenario')) {
        types.add(InteractiveContentType.simulation);
      }
      
      if (content.containsKey('diagram')) {
        types.add(InteractiveContentType.diagram);
      }
      
      if (content.containsKey('quiz')) {
        types.add(InteractiveContentType.quiz);
      }
      
      if (content.containsKey('calculator')) {
        types.add(InteractiveContentType.calculator);
      }
      
      return types;
    } catch (e) {
      return [];
    }
  }
  
  /// Validate user input for a step
  Future<ValidationResult> validateStepInput(
    InteractiveStepData step,
    dynamic userInput,
  ) async {
    try {
      return step.validateUserInput(userInput);
    } catch (e) {
      return ValidationResult(
        isValid: false,
        message: 'Erreur de validation: $e',
      );
    }
  }
  
  /// Get hints for a step
  Future<List<String>> getStepHints(InteractiveStepData step) async {
    return step.hints;
  }
  
  /// Get step visual elements (images, videos, etc.)
  Future<Map<String, dynamic>> getStepVisualElements(InteractiveStepData step) async {
    return step.visualElements;
  }
  
  /// Track user progress through interactive content
  Future<void> trackProgress(
    String guideId,
    String contentId,
    String stepId,
    double score, {
    Map<String, dynamic>? metadata,
  }) async {
    await _ensureInitialized();

    final progressKey = '${guideId}_$contentId';
    final existingProgress = _progressBox!.get(progressKey) ?? <String, dynamic>{};

    // Update step progress
    final stepProgress = <String, dynamic>{
      'stepId': stepId,
      'score': score,
      'completedAt': DateTime.now().toIso8601String(),
      'attempts': (existingProgress['steps']?[stepId]?['attempts'] ?? 0) + 1,
      'metadata': metadata ?? {},
    };

    final steps = Map<String, dynamic>.from(existingProgress['steps'] ?? {});
    steps[stepId] = stepProgress;

    // Calculate overall progress
    final totalSteps = steps.length;
    final completedSteps = steps.values.where((step) => step['score'] >= 0.7).length;
    final averageScore = steps.values
        .map<double>((step) => step['score'] as double)
        .reduce((a, b) => a + b) / totalSteps;

    final updatedProgress = <String, dynamic>{
      'guideId': guideId,
      'contentId': contentId,
      'steps': steps,
      'totalSteps': totalSteps,
      'completedSteps': completedSteps,
      'progressPercentage': (completedSteps / totalSteps) * 100,
      'averageScore': averageScore,
      'lastUpdated': DateTime.now().toIso8601String(),
      'isCompleted': completedSteps == totalSteps,
    };

    await _progressBox!.put(progressKey, updatedProgress);

    // Notify listeners
    final progress = InteractiveProgress.fromMap(updatedProgress);
    _progressController.add(progress);
  }
  
  /// Get user's progress for interactive content
  Future<InteractiveProgress?> getUserProgress(String guideId, String contentId) async {
    await _ensureInitialized();

    final progressKey = '${guideId}_$contentId';
    final progressData = _progressBox!.get(progressKey);

    if (progressData == null) return null;

    return InteractiveProgress.fromMap(progressData);
  }

  /// Get all user progress
  Future<List<InteractiveProgress>> getAllUserProgress() async {
    await _ensureInitialized();

    return _progressBox!.values
        .map((data) => InteractiveProgress.fromMap(data))
        .toList();
  }

  /// Save user interaction state
  Future<void> saveUserState(
    String guideId,
    String contentId,
    String stepId,
    Map<String, dynamic> state,
  ) async {
    await _ensureInitialized();

    final stateKey = '${guideId}_${contentId}_$stepId';
    final userState = <String, dynamic>{
      'guideId': guideId,
      'contentId': contentId,
      'stepId': stepId,
      'state': state,
      'savedAt': DateTime.now().toIso8601String(),
    };

    await _userStateBox!.put(stateKey, userState);

    // Notify listeners
    final interactionState = UserInteractionState.fromMap(userState);
    _stateController.add(interactionState);
  }

  /// Get user interaction state
  Future<UserInteractionState?> getUserState(
    String guideId,
    String contentId,
    String stepId,
  ) async {
    await _ensureInitialized();

    final stateKey = '${guideId}_${contentId}_$stepId';
    final stateData = _userStateBox!.get(stateKey);

    if (stateData == null) return null;

    return UserInteractionState.fromMap(stateData);
  }

  /// Clear user state for a specific content
  Future<void> clearUserState(String guideId, String contentId) async {
    await _ensureInitialized();

    final keysToRemove = _userStateBox!.keys
        .where((key) => key.toString().startsWith('${guideId}_$contentId'))
        .toList();

    for (final key in keysToRemove) {
      await _userStateBox!.delete(key);
    }
  }

  /// Reset progress for specific content
  Future<void> resetProgress(String guideId, String contentId) async {
    await _ensureInitialized();

    final progressKey = '${guideId}_$contentId';
    await _progressBox!.delete(progressKey);
    await clearUserState(guideId, contentId);
  }

  /// Get content completion statistics
  Future<ContentStatistics> getContentStatistics(String guideId, String contentId) async {
    final progress = await getUserProgress(guideId, contentId);

    if (progress == null) {
      return ContentStatistics(
        totalSteps: 0,
        completedSteps: 0,
        progressPercentage: 0.0,
        averageScore: 0.0,
        totalAttempts: 0,
        timeSpent: Duration.zero,
        isCompleted: false,
      );
    }

    return ContentStatistics(
      totalSteps: progress.totalSteps,
      completedSteps: progress.completedSteps,
      progressPercentage: progress.progressPercentage,
      averageScore: progress.averageScore,
      totalAttempts: progress.totalAttempts,
      timeSpent: progress.timeSpent,
      isCompleted: progress.isCompleted,
    );
  }
  
  /// Generate dynamic content based on user's previous answers
  Future<List<InteractiveStepData>> generateAdaptiveSteps(
    String guideId,
    String baseContentId,
    Map<String, dynamic> userHistory,
  ) async {
    try {
      final baseSteps = await loadInteractiveSteps(guideId, baseContentId);
      
      // For now, return base steps
      // In a full implementation, this would adapt content based on user history
      return baseSteps;
    } catch (e) {
      return [];
    }
  }
  
  /// Create custom walkthrough from template
  Future<List<InteractiveStepData>> createCustomWalkthrough(
    String templateType,
    Map<String, dynamic> parameters,
  ) async {
    try {
      // Load template
      final templatePath = 'assets/templates/interactive/$templateType.json';
      final String templateString = await rootBundle.loadString(templatePath);
      final Map<String, dynamic> template = json.decode(templateString);
      
      // Generate steps from template with parameters
      final stepsData = template['steps'] as List<dynamic>? ?? [];
      final steps = stepsData
          .map((stepJson) => _processStepTemplate(stepJson, parameters))
          .map((processedJson) => InteractiveStepData.fromJson(processedJson))
          .toList();
      
      return steps;
    } catch (e) {
      return [];
    }
  }
  
  /// Process step template with parameters
  Map<String, dynamic> _processStepTemplate(
    dynamic stepTemplate,
    Map<String, dynamic> parameters,
  ) {
    final stepJson = Map<String, dynamic>.from(stepTemplate as Map);
    
    // Replace template variables with actual values
    _replaceTemplateVariables(stepJson, parameters);
    
    return stepJson;
  }
  
  /// Replace template variables recursively
  void _replaceTemplateVariables(
    Map<String, dynamic> data,
    Map<String, dynamic> parameters,
  ) {
    data.forEach((key, value) {
      if (value is String) {
        // Replace template variables like {{variable}}
        String newValue = value;
        parameters.forEach((paramKey, paramValue) {
          newValue = newValue.replaceAll('{{$paramKey}}', paramValue.toString());
        });
        data[key] = newValue;
      } else if (value is Map<String, dynamic>) {
        _replaceTemplateVariables(value, parameters);
      } else if (value is List) {
        for (int i = 0; i < value.length; i++) {
          if (value[i] is Map<String, dynamic>) {
            _replaceTemplateVariables(value[i], parameters);
          } else if (value[i] is String) {
            String newValue = value[i];
            parameters.forEach((paramKey, paramValue) {
              newValue = newValue.replaceAll('{{$paramKey}}', paramValue.toString());
            });
            value[i] = newValue;
          }
        }
      }
    });
  }
  
  /// Export user's interactive progress
  Future<Map<String, dynamic>> exportInteractiveProgress(String userId) async {
    // This would export all interactive progress for a user
    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'userId': userId,
      'progress': {}, // Would contain actual progress data
    };
  }
  
  /// Clear cache
  void clearCache() {
    _contentCache.clear();
    _stepsCache.clear();
  }
  
  /// Preload content for better performance
  Future<void> preloadContent(String guideId, List<String> sectionIds) async {
    final futures = sectionIds.map((sectionId) => loadInteractiveContent(guideId, sectionId));
    await Future.wait(futures);
  }

  /// Get adaptive content recommendations based on user performance
  Future<List<String>> getAdaptiveRecommendations(
    String guideId,
    String currentContentId,
  ) async {
    final progress = await getUserProgress(guideId, currentContentId);
    final recommendations = <String>[];

    if (progress == null) return recommendations;

    // Recommend easier content if struggling
    if (progress.averageScore < 0.5) {
      recommendations.add('${currentContentId}_basic');
      recommendations.add('${currentContentId}_tutorial');
    }
    // Recommend advanced content if performing well
    else if (progress.averageScore > 0.8) {
      recommendations.add('${currentContentId}_advanced');
      recommendations.add('${currentContentId}_challenge');
    }

    return recommendations;
  }

  /// Private helper methods
  Future<void> _ensureInitialized() async {
    if (_progressBox == null) {
      await initialize();
    }
  }

  /// Dispose resources
  void dispose() {
    _progressController.close();
    _stateController.close();
  }
}

/// Data model for interactive progress tracking
class InteractiveProgress {
  final String guideId;
  final String contentId;
  final Map<String, dynamic> steps;
  final int totalSteps;
  final int completedSteps;
  final double progressPercentage;
  final double averageScore;
  final DateTime lastUpdated;
  final bool isCompleted;

  const InteractiveProgress({
    required this.guideId,
    required this.contentId,
    required this.steps,
    required this.totalSteps,
    required this.completedSteps,
    required this.progressPercentage,
    required this.averageScore,
    required this.lastUpdated,
    required this.isCompleted,
  });

  factory InteractiveProgress.fromMap(Map<String, dynamic> map) {
    return InteractiveProgress(
      guideId: map['guideId'] ?? '',
      contentId: map['contentId'] ?? '',
      steps: Map<String, dynamic>.from(map['steps'] ?? {}),
      totalSteps: map['totalSteps'] ?? 0,
      completedSteps: map['completedSteps'] ?? 0,
      progressPercentage: (map['progressPercentage'] ?? 0.0).toDouble(),
      averageScore: (map['averageScore'] ?? 0.0).toDouble(),
      lastUpdated: DateTime.tryParse(map['lastUpdated'] ?? '') ?? DateTime.now(),
      isCompleted: map['isCompleted'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'guideId': guideId,
      'contentId': contentId,
      'steps': steps,
      'totalSteps': totalSteps,
      'completedSteps': completedSteps,
      'progressPercentage': progressPercentage,
      'averageScore': averageScore,
      'lastUpdated': lastUpdated.toIso8601String(),
      'isCompleted': isCompleted,
    };
  }

  /// Get total attempts across all steps
  int get totalAttempts {
    return steps.values
        .map<int>((step) => step['attempts'] ?? 0)
        .fold(0, (sum, attempts) => sum + attempts);
  }

  /// Get estimated time spent (placeholder calculation)
  Duration get timeSpent {
    // Rough estimate: 2 minutes per step attempt
    return Duration(minutes: totalAttempts * 2);
  }
}

/// Data model for user interaction state
class UserInteractionState {
  final String guideId;
  final String contentId;
  final String stepId;
  final Map<String, dynamic> state;
  final DateTime savedAt;

  const UserInteractionState({
    required this.guideId,
    required this.contentId,
    required this.stepId,
    required this.state,
    required this.savedAt,
  });

  factory UserInteractionState.fromMap(Map<String, dynamic> map) {
    return UserInteractionState(
      guideId: map['guideId'] ?? '',
      contentId: map['contentId'] ?? '',
      stepId: map['stepId'] ?? '',
      state: Map<String, dynamic>.from(map['state'] ?? {}),
      savedAt: DateTime.tryParse(map['savedAt'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'guideId': guideId,
      'contentId': contentId,
      'stepId': stepId,
      'state': state,
      'savedAt': savedAt.toIso8601String(),
    };
  }
}

/// Data model for content statistics
class ContentStatistics {
  final int totalSteps;
  final int completedSteps;
  final double progressPercentage;
  final double averageScore;
  final int totalAttempts;
  final Duration timeSpent;
  final bool isCompleted;

  const ContentStatistics({
    required this.totalSteps,
    required this.completedSteps,
    required this.progressPercentage,
    required this.averageScore,
    required this.totalAttempts,
    required this.timeSpent,
    required this.isCompleted,
  });

  /// Get completion rate as a percentage
  double get completionRate {
    if (totalSteps == 0) return 0.0;
    return (completedSteps / totalSteps) * 100;
  }

  /// Get performance grade based on average score
  String get performanceGrade {
    if (averageScore >= 0.9) return 'Excellent';
    if (averageScore >= 0.8) return 'Très bien';
    if (averageScore >= 0.7) return 'Bien';
    if (averageScore >= 0.6) return 'Satisfaisant';
    if (averageScore >= 0.5) return 'Passable';
    return 'À améliorer';
  }

  /// Get estimated difficulty based on attempts and scores
  String get estimatedDifficulty {
    final avgAttemptsPerStep = totalSteps > 0 ? totalAttempts / totalSteps : 0;

    if (avgAttemptsPerStep <= 1.2 && averageScore >= 0.8) return 'Facile';
    if (avgAttemptsPerStep <= 2.0 && averageScore >= 0.6) return 'Moyen';
    if (avgAttemptsPerStep <= 3.0) return 'Difficile';
    return 'Très difficile';
  }
}

/// Types of interactive content
enum InteractiveContentType {
  walkthrough,
  simulation,
  diagram,
  quiz,
  calculator,
}

/// Extension to get content type labels
extension InteractiveContentTypeExtension on InteractiveContentType {
  String get label {
    switch (this) {
      case InteractiveContentType.walkthrough:
        return 'Parcours guidé';
      case InteractiveContentType.simulation:
        return 'Simulation';
      case InteractiveContentType.diagram:
        return 'Diagramme interactif';
      case InteractiveContentType.quiz:
        return 'Quiz';
      case InteractiveContentType.calculator:
        return 'Calculatrice';
    }
  }
  
  String get icon {
    switch (this) {
      case InteractiveContentType.walkthrough:
        return 'walk';
      case InteractiveContentType.simulation:
        return 'play_circle';
      case InteractiveContentType.diagram:
        return 'schema';
      case InteractiveContentType.quiz:
        return 'quiz';
      case InteractiveContentType.calculator:
        return 'calculate';
    }
  }
}