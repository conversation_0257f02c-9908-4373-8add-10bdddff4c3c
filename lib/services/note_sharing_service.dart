import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../models/guide/personal_note_data.dart';
import 'note_linking_service.dart';

/// Export formats supported by the service
enum ExportFormat {
  json,
  markdown,
  html,
  txt,
  csv,
}

/// Service for advanced note sharing and export functionality
class NoteSharingService {
  final NoteLinkingService _linkingService = NoteLinkingService();

  /// Initialize the service
  Future<void> initialize() async {
    await _linkingService.initialize();
  }

  /// Export a single note in the specified format
  Future<String> exportNote(
    PersonalNoteData note, 
    ExportFormat format, {
    bool includeMetadata = true,
    bool includeLinks = true,
  }) async {
    switch (format) {
      case ExportFormat.json:
        return _exportNoteAsJson(note, includeMetadata, includeLinks);
      case ExportFormat.markdown:
        return _exportNoteAsMarkdown(note, includeMetadata, includeLinks);
      case ExportFormat.html:
        return _exportNoteAsHtml(note, includeMetadata, includeLinks);
      case ExportFormat.txt:
        return _exportNoteAsText(note, includeMetadata, includeLinks);
      case ExportFormat.csv:
        return _exportNoteAsCsv(note, includeMetadata, includeLinks);
    }
  }

  /// Export multiple notes in the specified format
  Future<String> exportNotes(
    List<PersonalNoteData> notes, 
    ExportFormat format, {
    bool includeMetadata = true,
    bool includeLinks = true,
    bool includePrivate = false,
  }) async {
    final filteredNotes = includePrivate 
        ? notes 
        : notes.where((note) => !note.isPrivate).toList();

    switch (format) {
      case ExportFormat.json:
        return _exportNotesAsJson(filteredNotes, includeMetadata, includeLinks);
      case ExportFormat.markdown:
        return _exportNotesAsMarkdown(filteredNotes, includeMetadata, includeLinks);
      case ExportFormat.html:
        return _exportNotesAsHtml(filteredNotes, includeMetadata, includeLinks);
      case ExportFormat.txt:
        return _exportNotesAsText(filteredNotes, includeMetadata, includeLinks);
      case ExportFormat.csv:
        return _exportNotesAsCsv(filteredNotes, includeMetadata, includeLinks);
    }
  }

  /// Share a note using the platform's share functionality
  Future<void> shareNote(
    PersonalNoteData note, {
    ExportFormat format = ExportFormat.markdown,
    bool includeMetadata = true,
    bool includeLinks = true,
  }) async {
    final content = await exportNote(note, format, 
        includeMetadata: includeMetadata, includeLinks: includeLinks);
    
    await Share.share(
      content,
      subject: 'Note partagée: ${note.title}',
    );
  }

  /// Share multiple notes
  Future<void> shareNotes(
    List<PersonalNoteData> notes, {
    ExportFormat format = ExportFormat.markdown,
    bool includeMetadata = true,
    bool includeLinks = true,
    bool includePrivate = false,
  }) async {
    final content = await exportNotes(notes, format,
        includeMetadata: includeMetadata, 
        includeLinks: includeLinks,
        includePrivate: includePrivate);
    
    await Share.share(
      content,
      subject: 'Notes partagées (${notes.length} notes)',
    );
  }

  /// Save and share a note as a file
  Future<void> shareNoteAsFile(
    PersonalNoteData note, {
    ExportFormat format = ExportFormat.markdown,
    bool includeMetadata = true,
    bool includeLinks = true,
  }) async {
    final content = await exportNote(note, format,
        includeMetadata: includeMetadata, includeLinks: includeLinks);
    
    final fileName = '${_sanitizeFileName(note.title)}.${_getFileExtension(format)}';
    final file = await _saveToTempFile(content, fileName);
    
    await Share.shareXFiles(
      [XFile(file.path)],
      subject: 'Note: ${note.title}',
    );
  }

  /// Create a shareable link for a note
  Future<String> createShareableLink(
    PersonalNoteData note, {
    bool isPublic = false,
    DateTime? expiresAt,
    bool allowComments = false,
    bool allowEditing = false,
  }) async {
    final shareToken = _linkingService.generateShareToken();
    
    await _linkingService.createShareInfo(
      noteId: note.id,
      shareToken: shareToken,
      isPublic: isPublic,
      expiresAt: expiresAt,
      allowComments: allowComments,
      allowEditing: allowEditing,
    );
    
    return 'https://app.example.com/shared/$shareToken';
  }

  /// Copy note content to clipboard
  Future<void> copyToClipboard(
    PersonalNoteData note, {
    ExportFormat format = ExportFormat.markdown,
    bool includeMetadata = false,
    bool includeLinks = true,
  }) async {
    final content = await exportNote(note, format,
        includeMetadata: includeMetadata, includeLinks: includeLinks);
    
    await Clipboard.setData(ClipboardData(text: content));
  }

  /// Export note network (note + connected notes)
  Future<String> exportNoteNetwork(
    PersonalNoteData centerNote,
    List<PersonalNoteData> allNotes, {
    ExportFormat format = ExportFormat.json,
    int maxDepth = 2,
  }) async {
    final networkNotes = await _getNetworkNotes(centerNote, allNotes, maxDepth);
    final links = await _linkingService.getLinksForNote(centerNote.id);
    
    if (format == ExportFormat.json) {
      return jsonEncode({
        'centerNote': centerNote.toJson(),
        'connectedNotes': networkNotes.map((note) => note.toJson()).toList(),
        'links': links.map((link) => link.toJson()).toList(),
        'exportDate': DateTime.now().toIso8601String(),
        'maxDepth': maxDepth,
      });
    } else {
      // For other formats, export as a collection
      return exportNotes([centerNote, ...networkNotes], format);
    }
  }

  /// Private helper methods for different export formats

  Future<String> _exportNoteAsJson(PersonalNoteData note, bool includeMetadata, bool includeLinks) async {
    final data = note.toJson();
    
    if (includeLinks) {
      final links = await _linkingService.getLinksForNote(note.id);
      data['links'] = links.map((link) => link.toJson()).toList();
    }
    
    if (!includeMetadata) {
      data.removeWhere((key, value) => 
          ['createdAt', 'lastModified', 'viewCount', 'metadata'].contains(key));
    }
    
    return const JsonEncoder.withIndent('  ').convert(data);
  }

  Future<String> _exportNoteAsMarkdown(PersonalNoteData note, bool includeMetadata, bool includeLinks) async {
    final buffer = StringBuffer();
    
    // Title
    buffer.writeln('# ${note.title}');
    buffer.writeln();
    
    // Metadata
    if (includeMetadata) {
      buffer.writeln('---');
      buffer.writeln('**Type:** ${note.getNoteTypeLabel()}');
      buffer.writeln('**Créé le:** ${_formatDate(note.createdAt)}');
      buffer.writeln('**Modifié le:** ${_formatDate(note.lastModified)}');
      if (note.tags.isNotEmpty) {
        buffer.writeln('**Tags:** ${note.tags.join(', ')}');
      }
      if (note.reminderDate != null) {
        buffer.writeln('**Rappel:** ${_formatDate(note.reminderDate!)}');
      }
      buffer.writeln('---');
      buffer.writeln();
    }
    
    // Content
    buffer.writeln(note.content);
    
    // Links
    if (includeLinks) {
      final links = await _linkingService.getLinksForNote(note.id);
      if (links.isNotEmpty) {
        buffer.writeln();
        buffer.writeln('## Liaisons');
        for (final link in links) {
          buffer.writeln('- [${link.getLinkTypeLabel()}] ${link.targetNoteId}');
          if (link.description != null) {
            buffer.writeln('  - ${link.description}');
          }
        }
      }
    }
    
    return buffer.toString();
  }

  Future<String> _exportNoteAsHtml(PersonalNoteData note, bool includeMetadata, bool includeLinks) async {
    final buffer = StringBuffer();
    
    buffer.writeln('<!DOCTYPE html>');
    buffer.writeln('<html>');
    buffer.writeln('<head>');
    buffer.writeln('<meta charset="UTF-8">');
    buffer.writeln('<title>${_escapeHtml(note.title)}</title>');
    buffer.writeln('<style>');
    buffer.writeln('body { font-family: Arial, sans-serif; margin: 40px; }');
    buffer.writeln('h1 { color: #333; }');
    buffer.writeln('.metadata { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; }');
    buffer.writeln('.content { line-height: 1.6; }');
    buffer.writeln('.links { margin-top: 30px; }');
    buffer.writeln('</style>');
    buffer.writeln('</head>');
    buffer.writeln('<body>');
    
    buffer.writeln('<h1>${_escapeHtml(note.title)}</h1>');
    
    if (includeMetadata) {
      buffer.writeln('<div class="metadata">');
      buffer.writeln('<strong>Type:</strong> ${_escapeHtml(note.getNoteTypeLabel())}<br>');
      buffer.writeln('<strong>Créé le:</strong> ${_formatDate(note.createdAt)}<br>');
      buffer.writeln('<strong>Modifié le:</strong> ${_formatDate(note.lastModified)}<br>');
      if (note.tags.isNotEmpty) {
        buffer.writeln('<strong>Tags:</strong> ${_escapeHtml(note.tags.join(', '))}<br>');
      }
      buffer.writeln('</div>');
    }
    
    buffer.writeln('<div class="content">');
    buffer.writeln(_escapeHtml(note.content).replaceAll('\n', '<br>'));
    buffer.writeln('</div>');
    
    if (includeLinks) {
      final links = await _linkingService.getLinksForNote(note.id);
      if (links.isNotEmpty) {
        buffer.writeln('<div class="links">');
        buffer.writeln('<h2>Liaisons</h2>');
        buffer.writeln('<ul>');
        for (final link in links) {
          buffer.writeln('<li><strong>${_escapeHtml(link.getLinkTypeLabel())}:</strong> ${_escapeHtml(link.targetNoteId)}');
          if (link.description != null) {
            buffer.writeln('<br><em>${_escapeHtml(link.description!)}</em>');
          }
          buffer.writeln('</li>');
        }
        buffer.writeln('</ul>');
        buffer.writeln('</div>');
      }
    }
    
    buffer.writeln('</body>');
    buffer.writeln('</html>');
    
    return buffer.toString();
  }

  Future<String> _exportNoteAsText(PersonalNoteData note, bool includeMetadata, bool includeLinks) async {
    final buffer = StringBuffer();
    
    buffer.writeln(note.title);
    buffer.writeln('=' * note.title.length);
    buffer.writeln();
    
    if (includeMetadata) {
      buffer.writeln('Type: ${note.getNoteTypeLabel()}');
      buffer.writeln('Créé le: ${_formatDate(note.createdAt)}');
      buffer.writeln('Modifié le: ${_formatDate(note.lastModified)}');
      if (note.tags.isNotEmpty) {
        buffer.writeln('Tags: ${note.tags.join(', ')}');
      }
      buffer.writeln();
    }
    
    buffer.writeln(note.content);
    
    if (includeLinks) {
      final links = await _linkingService.getLinksForNote(note.id);
      if (links.isNotEmpty) {
        buffer.writeln();
        buffer.writeln('LIAISONS:');
        for (final link in links) {
          buffer.writeln('- ${link.getLinkTypeLabel()}: ${link.targetNoteId}');
          if (link.description != null) {
            buffer.writeln('  ${link.description}');
          }
        }
      }
    }
    
    return buffer.toString();
  }

  Future<String> _exportNoteAsCsv(PersonalNoteData note, bool includeMetadata, bool includeLinks) async {
    final buffer = StringBuffer();
    
    // CSV Header
    buffer.write('ID,Title,Content,Type,Created,Modified');
    if (includeMetadata) {
      buffer.write(',Tags,Private,Reminder');
    }
    if (includeLinks) {
      buffer.write(',Links');
    }
    buffer.writeln();
    
    // CSV Data
    buffer.write('"${_escapeCsv(note.id)}",');
    buffer.write('"${_escapeCsv(note.title)}",');
    buffer.write('"${_escapeCsv(note.content)}",');
    buffer.write('"${_escapeCsv(note.getNoteTypeLabel())}",');
    buffer.write('"${_formatDate(note.createdAt)}",');
    buffer.write('"${_formatDate(note.lastModified)}"');
    
    if (includeMetadata) {
      buffer.write(',"${_escapeCsv(note.tags.join('; '))}",');
      buffer.write('"${note.isPrivate}",');
      buffer.write('"${note.reminderDate != null ? _formatDate(note.reminderDate!) : ''}"');
    }
    
    if (includeLinks) {
      final links = await _linkingService.getLinksForNote(note.id);
      final linkStrings = links.map((link) => '${link.getLinkTypeLabel()}: ${link.targetNoteId}').toList();
      buffer.write(',"${_escapeCsv(linkStrings.join('; '))}"');
    }
    
    buffer.writeln();
    return buffer.toString();
  }

  Future<String> _exportNotesAsJson(List<PersonalNoteData> notes, bool includeMetadata, bool includeLinks) async {
    final data = <String, dynamic>{
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'noteCount': notes.length,
      'notes': [],
    };
    
    for (final note in notes) {
      final noteData = note.toJson();
      
      if (includeLinks) {
        final links = await _linkingService.getLinksForNote(note.id);
        noteData['links'] = links.map((link) => link.toJson()).toList();
      }
      
      if (!includeMetadata) {
        noteData.removeWhere((key, value) => 
            ['createdAt', 'lastModified', 'viewCount', 'metadata'].contains(key));
      }
      
      data['notes'].add(noteData);
    }
    
    return const JsonEncoder.withIndent('  ').convert(data);
  }

  Future<String> _exportNotesAsMarkdown(List<PersonalNoteData> notes, bool includeMetadata, bool includeLinks) async {
    final buffer = StringBuffer();
    
    buffer.writeln('# Collection de Notes');
    buffer.writeln();
    buffer.writeln('Exporté le: ${_formatDate(DateTime.now())}');
    buffer.writeln('Nombre de notes: ${notes.length}');
    buffer.writeln();
    
    for (int i = 0; i < notes.length; i++) {
      if (i > 0) buffer.writeln('\n---\n');
      final noteMarkdown = await _exportNoteAsMarkdown(notes[i], includeMetadata, includeLinks);
      buffer.write(noteMarkdown);
    }
    
    return buffer.toString();
  }

  Future<String> _exportNotesAsHtml(List<PersonalNoteData> notes, bool includeMetadata, bool includeLinks) async {
    final buffer = StringBuffer();
    
    buffer.writeln('<!DOCTYPE html>');
    buffer.writeln('<html>');
    buffer.writeln('<head>');
    buffer.writeln('<meta charset="UTF-8">');
    buffer.writeln('<title>Collection de Notes</title>');
    buffer.writeln('<style>');
    buffer.writeln('body { font-family: Arial, sans-serif; margin: 40px; }');
    buffer.writeln('h1, h2 { color: #333; }');
    buffer.writeln('.note { margin-bottom: 40px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }');
    buffer.writeln('.metadata { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; }');
    buffer.writeln('.content { line-height: 1.6; }');
    buffer.writeln('</style>');
    buffer.writeln('</head>');
    buffer.writeln('<body>');
    
    buffer.writeln('<h1>Collection de Notes</h1>');
    buffer.writeln('<p>Exporté le: ${_formatDate(DateTime.now())}</p>');
    buffer.writeln('<p>Nombre de notes: ${notes.length}</p>');
    
    for (final note in notes) {
      buffer.writeln('<div class="note">');
      final noteHtml = await _exportNoteAsHtml(note, includeMetadata, includeLinks);
      // Extract body content from the note HTML
      final bodyStart = noteHtml.indexOf('<body>') + 6;
      final bodyEnd = noteHtml.indexOf('</body>');
      buffer.write(noteHtml.substring(bodyStart, bodyEnd));
      buffer.writeln('</div>');
    }
    
    buffer.writeln('</body>');
    buffer.writeln('</html>');
    
    return buffer.toString();
  }

  Future<String> _exportNotesAsText(List<PersonalNoteData> notes, bool includeMetadata, bool includeLinks) async {
    final buffer = StringBuffer();
    
    buffer.writeln('COLLECTION DE NOTES');
    buffer.writeln('==================');
    buffer.writeln();
    buffer.writeln('Exporté le: ${_formatDate(DateTime.now())}');
    buffer.writeln('Nombre de notes: ${notes.length}');
    buffer.writeln();
    
    for (int i = 0; i < notes.length; i++) {
      if (i > 0) buffer.writeln('\n${'=' * 50}\n');
      final noteText = await _exportNoteAsText(notes[i], includeMetadata, includeLinks);
      buffer.write(noteText);
    }
    
    return buffer.toString();
  }

  Future<String> _exportNotesAsCsv(List<PersonalNoteData> notes, bool includeMetadata, bool includeLinks) async {
    final buffer = StringBuffer();
    
    // CSV Header
    buffer.write('ID,Title,Content,Type,Created,Modified');
    if (includeMetadata) {
      buffer.write(',Tags,Private,Reminder');
    }
    if (includeLinks) {
      buffer.write(',Links');
    }
    buffer.writeln();
    
    // CSV Data for each note
    for (final note in notes) {
      buffer.write('"${_escapeCsv(note.id)}",');
      buffer.write('"${_escapeCsv(note.title)}",');
      buffer.write('"${_escapeCsv(note.content)}",');
      buffer.write('"${_escapeCsv(note.getNoteTypeLabel())}",');
      buffer.write('"${_formatDate(note.createdAt)}",');
      buffer.write('"${_formatDate(note.lastModified)}"');
      
      if (includeMetadata) {
        buffer.write(',"${_escapeCsv(note.tags.join('; '))}",');
        buffer.write('"${note.isPrivate}",');
        buffer.write('"${note.reminderDate != null ? _formatDate(note.reminderDate!) : ''}"');
      }
      
      if (includeLinks) {
        final links = await _linkingService.getLinksForNote(note.id);
        final linkStrings = links.map((link) => '${link.getLinkTypeLabel()}: ${link.targetNoteId}').toList();
        buffer.write(',"${_escapeCsv(linkStrings.join('; '))}"');
      }
      
      buffer.writeln();
    }
    
    return buffer.toString();
  }

  /// Helper methods

  Future<List<PersonalNoteData>> _getNetworkNotes(
    PersonalNoteData centerNote,
    List<PersonalNoteData> allNotes,
    int maxDepth,
  ) async {
    final Set<String> visitedIds = {};
    final List<PersonalNoteData> networkNotes = [];
    
    await _collectNetworkNotes(centerNote.id, allNotes, visitedIds, networkNotes, 0, maxDepth);
    
    return networkNotes;
  }

  Future<void> _collectNetworkNotes(
    String noteId,
    List<PersonalNoteData> allNotes,
    Set<String> visitedIds,
    List<PersonalNoteData> networkNotes,
    int currentDepth,
    int maxDepth,
  ) async {
    if (currentDepth >= maxDepth || visitedIds.contains(noteId)) return;
    
    visitedIds.add(noteId);
    
    final connectedIds = await _linkingService.getConnectedNoteIds(noteId);
    
    for (final connectedId in connectedIds) {
      final connectedNote = allNotes.firstWhere(
        (note) => note.id == connectedId,
        orElse: () => throw Exception('Note not found: $connectedId'),
      );
      
      if (!networkNotes.any((note) => note.id == connectedId)) {
        networkNotes.add(connectedNote);
      }
      
      await _collectNetworkNotes(
        connectedId, allNotes, visitedIds, networkNotes, currentDepth + 1, maxDepth);
    }
  }

  Future<File> _saveToTempFile(String content, String fileName) async {
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/$fileName');
    await file.writeAsString(content);
    return file;
  }

  String _sanitizeFileName(String fileName) {
    return fileName.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
  }

  String _getFileExtension(ExportFormat format) {
    switch (format) {
      case ExportFormat.json:
        return 'json';
      case ExportFormat.markdown:
        return 'md';
      case ExportFormat.html:
        return 'html';
      case ExportFormat.txt:
        return 'txt';
      case ExportFormat.csv:
        return 'csv';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _escapeHtml(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;');
  }

  String _escapeCsv(String text) {
    if (text.contains('"')) {
      text = text.replaceAll('"', '""');
    }
    return text;
  }
}
