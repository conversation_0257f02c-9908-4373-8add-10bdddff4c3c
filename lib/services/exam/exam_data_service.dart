import 'dart:convert';
import 'package:flutter/services.dart' show rootBundle;
import 'package:moroccanaccounting/models/exam/exam.dart';

class ExamDataService {
  List<Exam> _exams = [];
  bool _isLoaded = false;

  // Load all exams from the main manifest file (or potentially individual files)
  Future<void> loadExams() async {
    if (_isLoaded) return;

    try {
      print('Starting to load exams...');
      
      // Load all json files from the assets/exams directory using AssetManifest
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = json.decode(manifestContent);
      
      print('Asset manifest loaded, keys count: ${manifestMap.keys.length}');
      
      // Debug: Print all keys to see available assets
      print('All available assets:');
      for (var key in manifestMap.keys) {
        print('  - $key');
      }

      // Modified to find all JSON files in any subdirectory under assets/exams
      final examFiles = manifestMap.keys
          .where((String key) => 
              key.contains('assets/exams/') && 
              key.endsWith('.json'))
          .toList();
          
      print('Found ${examFiles.length} exam files in assets');
      for (var file in examFiles) {
        print('  - $file');
      }

      _exams = []; // Clear previous list before loading
      
      // If no exam files found in the manifest, try loading the specific file we know exists
      if (examFiles.isEmpty) {
        print('No exam files found in manifest, trying direct load of known exam file');
        try {
          // Try loading a specific exam file we know exists
          final String jsonString = await rootBundle.loadString(
              'assets/exams/comptabilite_generale/ibn_zohr/global/exam_ordinaire_2017_2018.json');
          final Map<String, dynamic> jsonMap = json.decode(jsonString) as Map<String, dynamic>;
          _exams.add(Exam.fromJson(jsonMap));
          print('Successfully loaded exam directly: exam_ordinaire_2017_2018.json');
        } catch (e) {
          print('Error loading specific exam file directly: $e');
        }
        
        // Try loading the generated exam files directly
        try {
          final generatedExams = [
            'assets/exams/comptabilite_generale/generated/exam_generated_1.json',
            'assets/exams/comptabilite_generale/generated/exam_generated_2.json',
            'assets/exams/comptabilite_generale/generated/exam_generated_3.json'
          ];
          
          print('Attempting to load ${generatedExams.length} generated exam files directly');
          
          for (final examPath in generatedExams) {
            try {
              final String jsonString = await rootBundle.loadString(examPath);
              final Map<String, dynamic> jsonMap = json.decode(jsonString) as Map<String, dynamic>;
              _exams.add(Exam.fromJson(jsonMap));
              print('Successfully loaded generated exam: $examPath');
            } catch (e) {
              print('Error loading generated exam file $examPath: $e');
            }
          }
        } catch (e) {
          print('Error loading generated exam files: $e');
        }
      } else {
        // Normal loading of all files found in manifest
        for (final examFile in examFiles) {
          try {
            final String jsonString = await rootBundle.loadString(examFile);
            final Map<String, dynamic> jsonMap = json.decode(jsonString) as Map<String, dynamic>;
            _exams.add(Exam.fromJson(jsonMap));
            print('Loaded exam: $examFile');
          } catch (e) {
            print('Error loading or parsing exam file $examFile: $e');
            // Optionally skip this file and continue with others
          }
        }
      }
      
      print('Total exams loaded: ${_exams.length}');
      _exams.sort((a, b) => a.title.compareTo(b.title)); // Sort exams alphabetically by title

      _isLoaded = true;
    } catch (e) {
      // Handle errors (e.g., file not found, JSON parsing error)
      print('Error loading exam data: $e');
      _exams = []; // Ensure exams list is empty on error
      _isLoaded = false; // Allow retrying if needed
    }
  }

  // Get the list of all available exams (basic info like title, id)
  Future<List<Exam>> getExamList() async {
    if (!_isLoaded) {
      await loadExams();
    }
    // Return a list of exams, potentially just with ID and Title for list view efficiency later
    return List<Exam>.from(_exams);
  }

  // Get the full details of a specific exam by its ID
  Future<Exam?> getExamById(String examId) async {
     if (!_isLoaded) {
      await loadExams();
    }
    try {
      return _exams.firstWhere((exam) => exam.id == examId);
    } catch (e) {
      // Exam not found
      return null;
    }
  }
}
