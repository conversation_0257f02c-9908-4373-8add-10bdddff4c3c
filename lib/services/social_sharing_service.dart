// SocialSharingService for Moroccan Accounting App
// Manages achievement sharing, content generation, image creation, and user preferences.

import 'package:share_plus/share_plus.dart';
import 'package:flutter/material.dart';
import '../models/gamification/social_share_data.dart';

class SocialSharingService {
  final List<SocialShareData> shareHistory = [];

  // Shares achievement using share_plus
  Future<void> shareAchievement(SocialShareData shareData) async {
    final content = generateShareContent(shareData);
    await Share.share(content);
    trackShareActivity(shareData);
  }

  // Generates share content for achievement
  String generateShareContent(SocialShareData shareData) {
    return shareData.generateAchievementShareText(shareData.achievementId);
  }

  // Creates shareable badge image (placeholder)
  Future<Image> createShareableImage(SocialShareData shareData) async {
    return await shareData.createShareableImage();
  }

  // Gets share templates for achievement types/platforms
  String getShareTemplate(SocialPlatform platform, String achievementTitle) {
    switch (platform) {
      case SocialPlatform.facebook:
        return "I unlocked '$achievementTitle'! #MoroccanAccounting";
      case SocialPlatform.twitter:
        return "Just earned '$achievementTitle' in Moroccan Accounting! 🚀";
      case SocialPlatform.instagram:
        return "Achievement: '$achievementTitle' 🎉";
      case SocialPlatform.whatsapp:
        return "Check out my achievement '$achievementTitle'!";
      default:
        return "I unlocked '$achievementTitle'!";
    }
  }

  // Tracks share activity and updates history
  void trackShareActivity(SocialShareData shareData) {
    shareData.shareCount += 1;
    shareHistory.add(shareData);
  }

  // Privacy controls and user preferences (placeholder)
  void setPrivacy(SocialShareData shareData, bool isPrivate) {
    shareData.isPrivate = isPrivate;
  }

  void setUserPreference(SocialShareData shareData, bool prefersSharing) {
    shareData.userPrefersSharing = prefersSharing;
  }
}

// End of social_sharing_service.dart
