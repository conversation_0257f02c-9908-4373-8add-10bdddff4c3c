import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/rates/social_security_rates.dart';
import '../models/rates/tax_rates.dart';
import '../models/salary/salary_data.dart';
import '../models/salary/salary_result.dart';
import 'ir_tax_rate_loader.dart';

class SalaryCalculatorService {
  Future<SalaryResult> calculate(SalaryData data, {bool useAnnualMode = false}) async {
    // 1. Charger les taux
    final taxRates = await _loadTaxRates(useAnnualMode: useAnnualMode);
    final socialRates = await _loadSocialRates();

    // 2. Calculer le salaire brut
    final grossSalary = _calculateGrossSalary(data);

    // 3. Calculer les cotisations sociales
    final socialContributions =
        _calculateSocialContributions(grossSalary, socialRates);

    // 4. Calculer l'IR
    final taxableIncome =
        _calculateTaxableIncome(grossSalary, socialContributions, taxRates);
    final irResult = _calculateIR(
        taxableIncome, data.familyStatus, taxRates, data, socialRates);

    // 5. Générer le résultat
    return SalaryResult(
      baseSalary: data.baseSalary,
      grossSalary: grossSalary,
      socialContributions: socialContributions,
      irResult: irResult,
      netSalary: grossSalary - socialContributions.total - irResult.finalTax,
    );
  }

  double _calculateGrossSalary(SalaryData data) {
    // 1. Salaire de base + ancienneté
    double total = data.baseSalary;
    final seniority =
        data.baseSalary * _getSeniorityRate(data.yearsOfService) / 100;
    total += seniority;

    // 2. Heures supplémentaires
    if (data.overtime != null) {
      final hourlyRate = data.baseSalary / 191;
      // Regular day (25%): 196.34 DH
      total += _calculateOvertimeAmount(
          data.baseSalary, data.overtime!.regularHours, 0.25);
      // Holiday (50%): 235.60 DH
      total += _calculateOvertimeAmount(
          data.baseSalary, data.overtime!.holidayHours, 0.50);
      // Night (50%): 235.60 DH
      total += _calculateOvertimeAmount(
          data.baseSalary, data.overtime!.nightHours, 0.50);
    }

    // 3. Indemnités
    total += data.transportAllowance;
    total += data.housingAllowance;
    total += data.otherAllowances;

    return total;
  }

  double _calculateOvertimeAmount(
      double baseSalary, double hours, double rate) {
    final hourlyRate = baseSalary / 191;
    final overtimeRate = 1 + rate;
    return hours * hourlyRate * overtimeRate;
  }

  SocialContributions _calculateSocialContributions(
    double grossSalary,
    SocialSecurityRates rates,
  ) {
    // 1. CNSS (4.48%, plafonné à 6000)
    final cnssBase = grossSalary.clamp(0, 6000.0);
    final cnss = cnssBase * 4.48 / 100;

    // 2. AMO (2.26% sur le brut total)
    final amo = grossSalary * 2.26 / 100;

    return SocialContributions(
      cnss: cnss,
      amo: amo,
    );
  }

  double _calculateTaxableIncome(
    double grossSalary,
    SocialContributions socialContributions,
    TaxRates taxRates,
  ) {
    // 1. Base = Brut - Cotisations sociales
    double base = grossSalary - socialContributions.total;

    // 2. Calculate professional expenses based on the base
    final (rate, max) = taxRates.professionalExpenses.getTierForSalary(base);
    final professionalExpenses = (base * rate / 100).clamp(0, max);

    // 3. Calculate taxable income
    return base - professionalExpenses;
  }

  IRResult _calculateIR(
    double taxableIncome,
    FamilyStatus familyStatus,
    TaxRates taxRates,
    SalaryData data,
    SocialSecurityRates socialRates,
  ) {
    // 1. Find the correct bracket
    final bracket = taxRates.irBrackets.firstWhere(
      (b) =>
          taxableIncome >= b.min &&
          (b.max == null || taxableIncome <= (b.max ?? double.infinity)),
      orElse: () => taxRates.irBrackets.first,
    );

    // 2. Calculate raw tax
    final rawTax = (taxableIncome * bracket.rate) - bracket.deduction;

    // 3. Calculate family charges based on eligible dependents
    double familyCharges = 0;
    final eligibleDependents = familyStatus.dependents
        .where((dep) => dep.isDisabled || dep.age < 27)
        .toList();
    final dependentCount = eligibleDependents.length;

    if (familyStatus.isMarried) {
      if (dependentCount == 0) {
        familyCharges = taxRates.familyCharges.spouse;
      } else {
        switch (dependentCount) {
          case 1:
            familyCharges = taxRates.familyCharges.child1;
            break;
          case 2:
            familyCharges = taxRates.familyCharges.child2;
            break;
          case 3:
            familyCharges = taxRates.familyCharges.child3;
            break;
          case 4:
            familyCharges = taxRates.familyCharges.child4;
            break;
          default:
            familyCharges = taxRates.familyCharges.child5;
            break;
        }
      }
    }

    // 4. Calculate final tax
    final finalTax =
        (rawTax > 0 ? (rawTax - familyCharges).clamp(0, double.infinity) : 0)
            .toDouble();

    // 5. Get professional expenses details
    final grossSalary = _calculateGrossSalary(data);
    final socialContributions =
        _calculateSocialContributions(grossSalary, socialRates);
    final base = grossSalary - socialContributions.total;
    final (rate, max) = taxRates.professionalExpenses.getTierForSalary(base);
    final professionalExpenses = ((base * rate / 100).clamp(0, max)).toDouble();

    return IRResult(
      taxableIncome: taxableIncome,
      bracket: bracket,
      rawTax: rawTax,
      familyCharges: familyCharges,
      finalTax: finalTax,
      seniority: _calculateSeniority(data.baseSalary, data.yearsOfService),
      seniorityRate: _getSeniorityRate(data.yearsOfService),
      overtime: OvertimeDetails(
        regular: _calculateOvertimeAmount(
            data.baseSalary, data.overtime?.regularHours ?? 0, 0.25),
        holiday: _calculateOvertimeAmount(
            data.baseSalary, data.overtime?.holidayHours ?? 0, 0.50),
        night: _calculateOvertimeAmount(
            data.baseSalary, data.overtime?.nightHours ?? 0, 0.50),
        total: _calculateTotalOvertime(data.baseSalary, data.overtime),
      ),
      allowances: AllowanceDetails(
        transport: data.transportAllowance,
        housing: data.housingAllowance,
        other: data.otherAllowances,
        total: data.transportAllowance +
            data.housingAllowance +
            data.otherAllowances,
      ),
      bonuses: _calculateBonuses(data.bonuses),
      bonusesTotal: _calculateTotalBonuses(data.bonuses),
      professionalExpenses: professionalExpenses,
      professionalExpensesRate: rate,
      professionalExpensesBase: base,
    );
  }

  double _calculateSeniority(double baseSalary, int yearsOfService) {
    final rate = _getSeniorityRate(yearsOfService);
    return baseSalary * rate / 100;
  }

  double _getSeniorityRate(int yearsOfService) {
    if (yearsOfService < 2) return 0;
    if (yearsOfService < 5) return 5;
    if (yearsOfService < 12) return 10;
    if (yearsOfService < 20) return 15;
    if (yearsOfService < 25) return 20;
    return 25;
  }

  double _calculateTotalOvertime(double baseSalary, OvertimeHours? overtime) {
    if (overtime == null) return 0;
    return _calculateOvertimeAmount(baseSalary, overtime.regularHours, 0.25) +
        _calculateOvertimeAmount(baseSalary, overtime.holidayHours, 0.50) +
        _calculateOvertimeAmount(baseSalary, overtime.nightHours, 0.50);
  }

  List<BonusDetail> _calculateBonuses(Map<String, BonusEntry> bonuses) {
    return bonuses.entries
        .where((e) => e.value.enabled)
        .map((e) => BonusDetail(
              name: e.key,
              amount: e.value.isAnnual ? e.value.amount / 12 : e.value.amount,
              isAnnual: e.value.isAnnual,
            ))
        .toList();
  }

  double _calculateTotalBonuses(Map<String, BonusEntry> bonuses) {
    return bonuses.entries
        .where((e) => e.value.enabled)
        .map((e) => e.value.isAnnual ? e.value.amount / 12 : e.value.amount)
        .fold(0.0, (sum, amount) => sum + amount);
  }

  double _calculateProfessionalExpenses(
    double taxableIncome,
    ProfessionalExpenses rates,
    double grossSalary,
  ) {
    final (rate, max) = rates.getTierForSalary(grossSalary);
    final monthlyMax = max / 12; // Convert annual max to monthly
    return (taxableIncome * rate / 100).clamp(0, monthlyMax);
  }

  Future<TaxRates> _loadTaxRates({bool useAnnualMode = false}) async {
    try {
      // Load 2025 IR brackets from the new loader
      final irBrackets = await IrTaxRateLoader.instance.loadBrackets(
        isAnnual: useAnnualMode,
        year: 2025,
      );
      
      // Load other tax data from the existing file
      final jsonString = await rootBundle.loadString('assets/tax_rates.json');
      final json = jsonDecode(jsonString);
      final originalTaxRates = TaxRates.fromJson(json);
      
      // Return combined tax rates with 2025 IR brackets
      return TaxRates(
        irBrackets: irBrackets.isNotEmpty ? irBrackets : originalTaxRates.irBrackets,
        familyCharges: originalTaxRates.familyCharges,
        professionalExpenses: originalTaxRates.professionalExpenses,
      );
    } catch (e) {
      // Fallback to original tax rates if loading fails
      final jsonString = await rootBundle.loadString('assets/tax_rates.json');
      final json = jsonDecode(jsonString);
      return TaxRates.fromJson(json);
    }
  }

  Future<SocialSecurityRates> _loadSocialRates() async {
    final jsonString =
        await rootBundle.loadString('assets/social_security_rates.json');
    final json = jsonDecode(jsonString);
    return SocialSecurityRates.fromJson(json);
  }
}
