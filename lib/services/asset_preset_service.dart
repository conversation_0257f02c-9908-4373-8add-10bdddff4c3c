import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/immobilisations/asset_preset.dart';

/// Service class for managing asset presets used in amortization calculations
/// 
/// This service follows the singleton pattern and provides comprehensive
/// functionality for loading, caching, and managing asset presets from JSON
/// with fallback to hardcoded values for backward compatibility.
class AssetPresetService {
  // Private constructor for singleton pattern
  AssetPresetService._privateConstructor();

  // The single instance (lazily initialized)
  static final AssetPresetService _instance = AssetPresetService._privateConstructor();

  // Public factory constructor returns the same instance
  static AssetPresetService get instance => _instance;

  // Cache for loaded presets
  List<AssetPreset>? _cachedPresets;
  bool _isLoading = false;
  bool _hasLoadingError = false;

  // Fallback hardcoded presets for backward compatibility
  static const Map<String, double> _tauxStandards = {
    'Immeuble à usage d\'habitation ou commercial': 4,
    'Immeuble industriel': 5,
    'Construction légère': 10,
    'Matériel et installations': 10,
    'Matériel informatique': 20,
    'Matériel de transport': 20,
    'Mobilier et agencements': 10,
    'Outillage': 10,
  };

  /// Load presets from JSON asset with caching
  /// 
  /// Returns cached presets if already loaded, otherwise loads from JSON.
  /// Falls back to hardcoded presets if JSON loading fails.
  Future<List<AssetPreset>> loadPresets() async {
    // Return cached presets if available
    if (_cachedPresets != null) {
      return _cachedPresets!;
    }

    // Prevent multiple simultaneous loading attempts
    if (_isLoading) {
      // Wait for current loading to complete
      while (_isLoading) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return _cachedPresets ?? _getFallbackPresets();
    }

    _isLoading = true;
    _hasLoadingError = false;

    try {
      // Load JSON from assets
      final String jsonString = await rootBundle.loadString('assets/immobilisations/asset_presets.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      
      // Parse presets from JSON
      final List<dynamic> presetsJson = jsonData['presets'] as List<dynamic>;
      _cachedPresets = presetsJson
          .map((presetJson) => AssetPreset.fromJson(presetJson as Map<String, dynamic>))
          .toList();

      _isLoading = false;
      return _cachedPresets!;
    } catch (e) {
      // Handle loading errors gracefully
      _hasLoadingError = true;
      _isLoading = false;
      
      // Fall back to hardcoded presets
      _cachedPresets = _getFallbackPresets();
      return _cachedPresets!;
    }
  }

  /// Get all loaded presets
  /// 
  /// Returns cached presets if available, otherwise returns empty list.
  /// Call loadPresets() first to ensure presets are loaded.
  List<AssetPreset> getAllPresets() {
    return _cachedPresets ?? [];
  }

  /// Get unique categories from loaded presets
  List<String> getCategories() {
    final presets = getAllPresets();
    final categories = presets.map((preset) => preset.category).toSet().toList();
    categories.sort();
    return categories;
  }

  /// Filter presets by category
  List<AssetPreset> getPresetsByCategory(String category) {
    return getAllPresets()
        .where((preset) => preset.category == category)
        .toList();
  }

  /// Find a specific preset by ID
  AssetPreset? findPresetById(String id) {
    try {
      return getAllPresets().firstWhere((preset) => preset.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Search presets by name or description
  /// 
  /// Performs case-insensitive search across preset names, descriptions,
  /// categories, and examples.
  List<AssetPreset> searchPresets(String query) {
    if (query.isEmpty) return getAllPresets();

    final lowerQuery = query.toLowerCase();
    return getAllPresets().where((preset) {
      return preset.name.toLowerCase().contains(lowerQuery) ||
             preset.description.toLowerCase().contains(lowerQuery) ||
             preset.category.toLowerCase().contains(lowerQuery) ||
             preset.examples.any((example) => example.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// Get all preset names for autocomplete functionality
  List<String> getPresetNames() {
    return getAllPresets().map((preset) => preset.name).toList();
  }

  /// Find preset by exact name match
  AssetPreset? findPresetByName(String name) {
    try {
      return getAllPresets().firstWhere((preset) => preset.name == name);
    } catch (e) {
      return null;
    }
  }

  /// Find similar presets based on category and rate
  /// 
  /// Returns presets from the same category or with similar amortization rates.
  List<AssetPreset> getSimilarPresets(AssetPreset preset) {
    final allPresets = getAllPresets();
    final similar = <AssetPreset>[];

    // Add presets from same category
    similar.addAll(allPresets.where((p) => 
        p.id != preset.id && p.category == preset.category));

    // Add presets with similar rates (within 5% difference)
    final rateDifference = 5.0;
    similar.addAll(allPresets.where((p) => 
        p.id != preset.id && 
        !similar.contains(p) &&
        (p.standardRate - preset.standardRate).abs() <= rateDifference));

    return similar;
  }

  /// Validate preset configuration against user inputs
  /// 
  /// Checks if the provided duration and mode are valid for the given preset.
  bool validatePresetConfiguration(AssetPreset preset, int duration, String mode) {
    return preset.isValidDuration(duration) && preset.isModeAllowed(mode);
  }

  /// Get detailed validation errors for preset configuration
  /// 
  /// Returns a list of validation error messages for the given inputs.
  List<String> getValidationErrors(AssetPreset preset, Map<String, dynamic> inputs) {
    final errors = <String>[];

    // Validate duration
    final duration = inputs['duration'] as int?;
    if (duration != null && !preset.isValidDuration(duration)) {
      errors.add('La durée doit être comprise entre ${preset.minDuration} et ${preset.maxDuration} ans pour ce type d\'actif.');
    }

    // Validate mode
    final mode = inputs['mode'] as String?;
    if (mode != null && !preset.isModeAllowed(mode)) {
      errors.add('Le mode d\'amortissement "$mode" n\'est pas autorisé pour ce type d\'actif. Modes autorisés: ${preset.allowedModes.join(', ')}.');
    }

    // Validate rate if provided
    final rate = inputs['rate'] as double?;
    if (rate != null) {
      final expectedRate = 100.0 / preset.defaultDuration;
      final rateDifference = (rate - expectedRate).abs();
      if (rateDifference > 5.0) {
        errors.add('Le taux d\'amortissement semble élevé par rapport à la durée standard pour ce type d\'actif.');
      }
    }

    // Validate residual value
    final residualValue = inputs['residualValue'] as double?;
    if (residualValue != null && residualValue < 0) {
      errors.add('La valeur résiduelle ne peut pas être négative.');
    }

    return errors;
  }

  /// Check if presets are currently loading
  bool get isLoading => _isLoading;

  /// Check if there was an error loading presets
  bool get hasLoadingError => _hasLoadingError;

  /// Clear cached presets and force reload on next access
  void clearCache() {
    _cachedPresets = null;
    _hasLoadingError = false;
  }

  /// Get fallback presets based on existing hardcoded values
  /// 
  /// Provides backward compatibility with existing _tauxStandards map.
  List<AssetPreset> _getFallbackPresets() {
    return _tauxStandards.entries.map((entry) {
      final name = entry.key;
      final rate = entry.value;
      final duration = (100 / rate).round();
      
      return AssetPreset(
        id: _generateIdFromName(name),
        name: name,
        category: _getCategoryFromName(name),
        standardRate: rate,
        defaultDuration: duration,
        defaultMode: rate >= 15 ? 'degressive' : 'linear',
        residualValuePercent: 0.0,
        description: 'Actif standard selon la réglementation marocaine',
        iconName: _getIconFromName(name),
        examples: [name],
        minDuration: (duration * 0.5).round().clamp(1, 50),
        maxDuration: (duration * 1.5).round().clamp(1, 50),
        allowedModes: rate >= 15 ? ['linear', 'degressive'] : ['linear'],
        complianceNote: 'Conforme à la réglementation fiscale marocaine',
      );
    }).toList();
  }

  /// Generate a unique ID from asset name
  String _generateIdFromName(String name) {
    return name
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '');
  }

  /// Determine category from asset name
  String _getCategoryFromName(String name) {
    final lowerName = name.toLowerCase();
    
    if (lowerName.contains('immeuble') || lowerName.contains('construction')) {
      return 'Immobilier';
    } else if (lowerName.contains('matériel') || lowerName.contains('outillage')) {
      return 'Équipements';
    } else if (lowerName.contains('informatique')) {
      return 'Informatique';
    } else if (lowerName.contains('transport')) {
      return 'Véhicules';
    } else if (lowerName.contains('mobilier')) {
      return 'Mobilier';
    } else {
      return 'Autres';
    }
  }

  /// Get appropriate icon name from asset name
  String _getIconFromName(String name) {
    final lowerName = name.toLowerCase();
    
    if (lowerName.contains('immeuble')) {
      return 'business';
    } else if (lowerName.contains('construction')) {
      return 'construction';
    } else if (lowerName.contains('matériel') && lowerName.contains('informatique')) {
      return 'computer';
    } else if (lowerName.contains('matériel')) {
      return 'precision_manufacturing';
    } else if (lowerName.contains('transport')) {
      return 'directions_car';
    } else if (lowerName.contains('mobilier')) {
      return 'chair';
    } else if (lowerName.contains('outillage')) {
      return 'build';
    } else {
      return 'category';
    }
  }

  /// Get legacy taux standards map for backward compatibility
  Map<String, double> get tauxStandards => Map.unmodifiable(_tauxStandards);

  /// Check if a preset exists for the given asset name (legacy compatibility)
  bool hasPresetForAssetName(String assetName) {
    return _tauxStandards.containsKey(assetName) || 
           findPresetByName(assetName) != null;
  }

  /// Get rate for asset name (legacy compatibility)
  double? getRateForAssetName(String assetName) {
    // First check hardcoded values for exact compatibility
    if (_tauxStandards.containsKey(assetName)) {
      return _tauxStandards[assetName];
    }
    
    // Then check loaded presets
    final preset = findPresetByName(assetName);
    return preset?.standardRate;
  }
}