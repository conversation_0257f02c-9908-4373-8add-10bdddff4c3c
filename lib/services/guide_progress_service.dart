import 'package:hive/hive.dart';
import '../models/guide/guide_progress_data.dart';
import 'user_progress_service.dart';

/// Service for managing guide section completion tracking
class GuideProgressService {
  static final GuideProgressService _instance = GuideProgressService._internal();
  factory GuideProgressService() => _instance;
  GuideProgressService._internal();

  static const String _boxName = 'guide_progress';
  Box<GuideProgressData>? _box;
  final UserProgressService _userProgressService = UserProgressService();

  /// Initialize the service
  Future<void> initialize() async {
    if (_box == null || !_box!.isOpen) {
      _box = await Hive.openBox<GuideProgressData>(_boxName);
    }
  }

  /// Ensures the box is initialized
  Future<Box<GuideProgressData>> _getBox() async {
    if (_box == null || !_box!.isOpen) {
      await initialize();
    }
    return _box!;
  }

  /// Marks a section as completed
  Future<void> markSectionCompleted(
    String guideId, 
    String sectionId, {
    int timeSpent = 0,
    double scrollProgress = 1.0,
  }) async {
    final box = await _getBox();
    final progressKey = '${guideId}_$sectionId';
    
    // Get existing progress or create new
    final existingProgress = box.get(progressKey);
    final updatedProgress = existingProgress?.copyWith(
      isCompleted: true,
      completionTimestamp: DateTime.now(),
      timeSpent: existingProgress.timeSpent + timeSpent,
      scrollProgress: scrollProgress,
      lastVisited: DateTime.now(),
    ) ?? GuideProgressData.completed(
      guideId: guideId,
      sectionId: sectionId,
      timeSpent: timeSpent,
      scrollProgress: scrollProgress,
    );

    // Save to Hive
    await box.put(progressKey, updatedProgress);
    
    // Update overall guide progress
    await _updateGuideProgress(guideId);
  }

  /// Updates section progress (for tracking scroll progress and time)
  Future<void> updateSectionProgress(
    String guideId,
    String sectionId, {
    int? additionalTimeSpent,
    double? scrollProgress,
    bool? markCompleted,
  }) async {
    final box = await _getBox();
    final progressKey = '${guideId}_$sectionId';
    
    // Get existing progress or create new
    final existingProgress = box.get(progressKey) ?? 
        GuideProgressData.newSection(guideId: guideId, sectionId: sectionId);
    
    final updatedProgress = existingProgress.updateVisit(
      additionalTimeSpent: additionalTimeSpent,
      newScrollProgress: scrollProgress,
      markCompleted: markCompleted,
    );

    // Save to Hive
    await box.put(progressKey, updatedProgress);
    
    // Update overall guide progress if section was completed
    if (updatedProgress.isCompleted && !existingProgress.isCompleted) {
      await _updateGuideProgress(guideId);
    }
  }

  /// Gets progress for a specific section
  Future<GuideProgressData?> getSectionProgress(String guideId, String sectionId) async {
    final box = await _getBox();
    final progressKey = '${guideId}_$sectionId';
    return box.get(progressKey);
  }

  /// Gets completion percentage for a guide
  Future<double> getGuideCompletionPercentage(String guideId, List<String> sectionIds) async {
    if (sectionIds.isEmpty) return 0.0;
    
    final box = await _getBox();
    int completedSections = 0;
    
    for (final sectionId in sectionIds) {
      final progressKey = '${guideId}_$sectionId';
      final progress = box.get(progressKey);
      if (progress?.isCompleted == true) {
        completedSections++;
      }
    }
    
    return completedSections / sectionIds.length;
  }

  /// Gets all completed sections for a guide
  Future<List<String>> getCompletedSections(String guideId) async {
    final box = await _getBox();
    final completedSections = <String>[];
    
    for (final key in box.keys) {
      if (key.toString().startsWith('${guideId}_')) {
        final progress = box.get(key);
        if (progress?.isCompleted == true) {
          completedSections.add(progress!.sectionId);
        }
      }
    }
    
    return completedSections;
  }

  /// Gets all progress data for a guide
  Future<List<GuideProgressData>> getGuideProgress(String guideId) async {
    final box = await _getBox();
    final progressList = <GuideProgressData>[];
    
    for (final key in box.keys) {
      if (key.toString().startsWith('${guideId}_')) {
        final progress = box.get(key);
        if (progress != null) {
          progressList.add(progress);
        }
      }
    }
    
    return progressList;
  }

  /// Gets recently visited sections across all guides
  Future<List<GuideProgressData>> getRecentlyVisitedSections({int limit = 10}) async {
    final box = await _getBox();
    final allProgress = box.values.toList();
    
    // Sort by last visited date (most recent first)
    allProgress.sort((a, b) => b.lastVisited.compareTo(a.lastVisited));
    
    return allProgress.take(limit).toList();
  }

  /// Gets total time spent on a guide
  Future<int> getTotalTimeSpent(String guideId) async {
    final progressList = await getGuideProgress(guideId);
    return progressList.fold<int>(0, (sum, progress) => sum + progress.timeSpent);
  }

  /// Gets learning statistics for all guides
  Future<Map<String, dynamic>> getLearningStatistics() async {
    final box = await _getBox();
    final allProgress = box.values.toList();
    
    final guideStats = <String, Map<String, dynamic>>{};
    int totalTimeSpent = 0;
    int totalSectionsCompleted = 0;
    
    for (final progress in allProgress) {
      totalTimeSpent += progress.timeSpent;
      if (progress.isCompleted) totalSectionsCompleted++;
      
      final guideId = progress.guideId;
      if (!guideStats.containsKey(guideId)) {
        guideStats[guideId] = {
          'sectionsCompleted': 0,
          'totalSections': 0,
          'timeSpent': 0,
          'lastVisited': progress.lastVisited,
        };
      }
      
      final stats = guideStats[guideId]!;
      stats['totalSections'] = (stats['totalSections'] as int) + 1;
      stats['timeSpent'] = (stats['timeSpent'] as int) + progress.timeSpent;
      
      if (progress.isCompleted) {
        stats['sectionsCompleted'] = (stats['sectionsCompleted'] as int) + 1;
      }
      
      // Update last visited if this is more recent
      final lastVisited = stats['lastVisited'] as DateTime;
      if (progress.lastVisited.isAfter(lastVisited)) {
        stats['lastVisited'] = progress.lastVisited;
      }
    }
    
    return {
      'totalTimeSpent': totalTimeSpent,
      'totalSectionsCompleted': totalSectionsCompleted,
      'guideStats': guideStats,
    };
  }

  /// Resets progress for a specific guide
  Future<void> resetGuideProgress(String guideId) async {
    final box = await _getBox();
    final keysToDelete = <String>[];
    
    for (final key in box.keys) {
      if (key.toString().startsWith('${guideId}_')) {
        keysToDelete.add(key.toString());
      }
    }
    
    for (final key in keysToDelete) {
      await box.delete(key);
    }
    
    // Update overall guide progress
    await _updateGuideProgress(guideId);
  }

  /// Resets progress for a specific section
  Future<void> resetSectionProgress(String guideId, String sectionId) async {
    final box = await _getBox();
    final progressKey = '${guideId}_$sectionId';
    await box.delete(progressKey);
    
    // Update overall guide progress
    await _updateGuideProgress(guideId);
  }

  /// Updates the overall guide progress in UserProgressService
  Future<void> _updateGuideProgress(String guideId) async {
    // This would need to be implemented based on how many sections each guide has
    // For now, we'll use a placeholder implementation
    final progressList = await getGuideProgress(guideId);
    final completedSections = progressList.where((p) => p.isCompleted).length;
    final totalSections = progressList.length;
    
    if (totalSections > 0) {
      final percentage = completedSections / totalSections;
      await _userProgressService.updateGuideProgress(guideId, percentage);
    }
  }

  /// Closes the service and box
  Future<void> close() async {
    await _box?.close();
    _box = null;
  }
}
