import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/rates/tax_rates.dart';
import '../models/rates/social_security_rates.dart';
import '../models/rates/termination_rates.dart';

class RatesService {
  static const String _taxRatesPath = 'assets/tax_rates.json';
  static const String _socialSecurityRatesPath =
      'assets/social_security_rates.json';
  static const String _terminationRatesPath = 'assets/termination_rates.json';

  // Singleton instance
  static final RatesService _instance = RatesService._internal();
  factory RatesService() => _instance;
  RatesService._internal();

  // Cached rates
  TaxRates? _taxRates;
  SocialSecurityRates? _socialSecurityRates;
  TerminationRates? _terminationRates;

  // Getters with lazy loading
  Future<TaxRates> get taxRates async {
    _taxRates ??= await _loadTaxRates();
    return _taxRates!;
  }

  Future<SocialSecurityRates> get socialSecurityRates async {
    _socialSecurityRates ??= await _loadSocialSecurityRates();
    return _socialSecurityRates!;
  }

  Future<TerminationRates> get terminationRates async {
    _terminationRates ??= await _loadTerminationRates();
    return _terminationRates!;
  }

  // Load methods
  Future<TaxRates> _loadTaxRates() async {
    final jsonString = await rootBundle.loadString(_taxRatesPath);
    final json = jsonDecode(jsonString);
    return TaxRates.fromJson(json);
  }

  Future<SocialSecurityRates> _loadSocialSecurityRates() async {
    final jsonString = await rootBundle.loadString(_socialSecurityRatesPath);
    final json = jsonDecode(jsonString);
    return SocialSecurityRates.fromJson(json);
  }

  Future<TerminationRates> _loadTerminationRates() async {
    final jsonString = await rootBundle.loadString(_terminationRatesPath);
    final json = jsonDecode(jsonString);
    return TerminationRates.fromJson(json);
  }

  // Reload methods (useful if files are updated)
  Future<void> reloadAll() async {
    _taxRates = null;
    _socialSecurityRates = null;
    _terminationRates = null;
    await Future.wait([
      taxRates,
      socialSecurityRates,
      terminationRates,
    ]);
  }

  Future<void> reloadTaxRates() async {
    _taxRates = null;
    await taxRates;
  }

  Future<void> reloadSocialSecurityRates() async {
    _socialSecurityRates = null;
    await socialSecurityRates;
  }

  Future<void> reloadTerminationRates() async {
    _terminationRates = null;
    await terminationRates;
  }
}
