import 'dart:math';
import '../models/adaptive_learning_models.dart';
import '../models/quiz_model.dart';
import 'adaptive_learning_service.dart';
import 'user_progress_service.dart';

// Enhanced adaptive difficulty service that builds upon the existing AdaptiveLearningService
class AdaptiveDifficultyService {
  final AdaptiveLearningService _adaptiveLearningService;
  final UserProgressService _userProgressService;

  AdaptiveDifficultyService({
    required AdaptiveLearningService adaptiveLearningService,
    required UserProgressService userProgressService,
  }) : _adaptiveLearningService = adaptiveLearningService,
       _userProgressService = userProgressService;

  // Calculate optimal difficulty for user using weighted recent performance
  int calculateOptimalDifficulty(String categoryName, String topic) {
    final performances = _adaptiveLearningService.getUserPerformance(categoryName);
    
    if (performances.isEmpty) {
      return 2; // Start with easy-medium difficulty for new users
    }

    // Get recent performance data (last 20 questions or last 7 days)
    final recentPerformances = _getRecentPerformances(performances);
    
    if (recentPerformances.isEmpty) {
      return 2; // Default to easy-medium if no recent data
    }

    // Calculate topic-specific performance if available
    double topicAccuracy = _calculateTopicAccuracy(recentPerformances, topic);
    
    // Calculate overall recent accuracy
    double overallAccuracy = _calculateOverallAccuracy(recentPerformances);
    
    // Weight topic performance more heavily if we have enough data
    double weightedAccuracy = topicAccuracy > 0 
        ? (topicAccuracy * 0.7 + overallAccuracy * 0.3)
        : overallAccuracy;

    // Consider time factor - faster responses suggest confidence
    double avgResponseTime = _calculateAverageResponseTime(recentPerformances);
    double timeConfidenceFactor = _calculateTimeConfidenceFactor(avgResponseTime);
    
    // Adjust accuracy based on response time confidence
    weightedAccuracy = weightedAccuracy * (0.8 + timeConfidenceFactor * 0.2);
    
    // Consider streak factor for confidence boost
    int maxStreak = recentPerformances.fold(0, (max, p) => p.streakCount > max ? p.streakCount : max);
    double streakFactor = _calculateStreakFactor(maxStreak);
    
    // Final difficulty calculation with all factors
    double finalScore = weightedAccuracy * (0.7 + streakFactor * 0.3);
    
    // Map score to difficulty level (1-5)
    return _mapScoreToDifficulty(finalScore);
  }

  // Adjust question selection based on user performance
  List<QuizQuestion> adjustQuestionSelection(
    List<QuizQuestion> questions, 
    UserPerformance performance
  ) {
    if (questions.isEmpty) return questions;

    final optimalDifficulty = calculateOptimalDifficulty(
      performance.categoryName, 
      'general' // Default topic for mixed selection
    );

    // Create difficulty distribution around optimal level
    final difficultyRange = _getDifficultyRange(optimalDifficulty);
    
    // Filter questions within the difficulty range
    final filteredQuestions = questions
        .where((q) => difficultyRange.contains(q.difficulty))
        .toList();

    if (filteredQuestions.isEmpty) {
      return questions; // Return original if no questions match range
    }

    // Sort by proximity to optimal difficulty
    filteredQuestions.sort((a, b) {
      final aDistance = (a.difficulty - optimalDifficulty).abs();
      final bDistance = (b.difficulty - optimalDifficulty).abs();
      return aDistance.compareTo(bDistance);
    });

    return filteredQuestions;
  }

  // Get next difficulty level for progression
  int getNextDifficultyLevel(UserPerformance performance) {
    final currentOptimal = calculateOptimalDifficulty(performance.categoryName, 'general');
    final accuracy = performance.accuracy;
    final recentQuestions = performance.questionsAnswered;

    // Don't adjust if not enough data
    if (recentQuestions < 5) {
      return currentOptimal;
    }

    // Progressive difficulty adjustment based on performance
    if (accuracy >= 0.9 && performance.streakCount >= 5) {
      // Excellent performance - increase difficulty
      return min(5, currentOptimal + 1);
    } else if (accuracy >= 0.8 && performance.streakCount >= 3) {
      // Good performance - slight increase or maintain
      return min(5, currentOptimal + (Random().nextBool() ? 1 : 0));
    } else if (accuracy < 0.5) {
      // Poor performance - decrease difficulty
      return max(1, currentOptimal - 1);
    } else if (accuracy < 0.6 && performance.streakCount == 0) {
      // Struggling - consider decrease
      return max(1, currentOptimal - (Random().nextBool() ? 1 : 0));
    }

    return currentOptimal; // Maintain current level
  }

  // Analyze performance patterns for insights
  PerformancePatterns analyzePerformancePatterns(List<QuizAttempt> attempts) {
    if (attempts.isEmpty) {
      return PerformancePatterns.empty();
    }

    // Sort attempts by timestamp
    attempts.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Calculate trend analysis
    final trend = _calculatePerformanceTrend(attempts);
    
    // Identify difficulty preferences
    final difficultyPreferences = _analyzeDifficultyPreferences(attempts);
    
    // Detect time-of-day patterns
    final timePatterns = _analyzeTimePatterns(attempts);
    
    // Calculate consistency metrics
    final consistency = _calculateConsistencyMetrics(attempts);
    
    // Identify learning velocity
    final velocity = _calculateLearningVelocity(attempts);

    return PerformancePatterns(
      trend: trend,
      difficultyPreferences: difficultyPreferences,
      timePatterns: timePatterns,
      consistency: consistency,
      learningVelocity: velocity,
      totalAttempts: attempts.length,
      analysisDate: DateTime.now(),
    );
  }

  // Generate difficulty recommendations based on analysis
  DifficultyRecommendations generateDifficultyRecommendations() {
    final allPerformances = <UserPerformance>[];
    
    // Collect all performance data
    // This would need to be implemented based on available categories
    final categories = ['TVA', 'IS', 'IR', 'CNSS']; // Example categories
    
    for (final category in categories) {
      allPerformances.addAll(_adaptiveLearningService.getUserPerformance(category));
    }

    if (allPerformances.isEmpty) {
      return DifficultyRecommendations.forNewUser();
    }

    // Analyze overall performance
    final overallAccuracy = allPerformances
        .map((p) => p.accuracy)
        .reduce((a, b) => a + b) / allPerformances.length;

    // Generate category-specific recommendations
    final categoryRecommendations = <String, int>{};
    for (final category in categories) {
      final categoryPerformances = allPerformances
          .where((p) => p.categoryName == category)
          .toList();
      
      if (categoryPerformances.isNotEmpty) {
        categoryRecommendations[category] = calculateOptimalDifficulty(category, 'general');
      }
    }

    // Generate general recommendations
    final recommendations = <String>[];
    
    if (overallAccuracy < 0.5) {
      recommendations.add("Concentrez-vous sur les niveaux 1-2 pour renforcer les bases");
    } else if (overallAccuracy < 0.7) {
      recommendations.add("Alternez entre les niveaux 2-3 pour progresser");
    } else if (overallAccuracy < 0.85) {
      recommendations.add("Vous êtes prêt(e) pour les niveaux 3-4");
    } else {
      recommendations.add("Excellent niveau ! Explorez les niveaux 4-5 pour vous challenger");
    }

    // Add time-based recommendations
    final avgResponseTime = allPerformances
        .map((p) => p.averageTimePerQuestion)
        .reduce((a, b) => a + b) / allPerformances.length;

    if (avgResponseTime > 60) {
      recommendations.add("Travaillez sur la vitesse de réponse avec des questions de révision");
    }

    return DifficultyRecommendations(
      overallRecommendedDifficulty: _mapScoreToDifficulty(overallAccuracy),
      categoryRecommendations: categoryRecommendations,
      recommendations: recommendations,
      confidenceLevel: _calculateConfidenceLevel(allPerformances),
    );
  }

  // Helper methods for calculations

  List<UserPerformance> _getRecentPerformances(List<UserPerformance> performances) {
    final cutoffDate = DateTime.now().subtract(Duration(days: 7));
    return performances
        .where((p) => p.lastUpdated.isAfter(cutoffDate))
        .toList()
        ..sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
  }

  double _calculateTopicAccuracy(List<UserPerformance> performances, String topic) {
    double totalAccuracy = 0.0;
    int count = 0;

    for (final performance in performances) {
      final topicCorrect = performance.topicAccuracy[topic];
      if (topicCorrect != null) {
        // Estimate total attempts for this topic (this should be tracked separately)
        final estimatedAttempts = max(1, (performance.questionsAnswered * 0.2).round());
        final accuracy = topicCorrect / estimatedAttempts;
        totalAccuracy += accuracy;
        count++;
      }
    }

    return count > 0 ? totalAccuracy / count : 0.0;
  }

  double _calculateOverallAccuracy(List<UserPerformance> performances) {
    if (performances.isEmpty) return 0.0;
    
    return performances
        .map((p) => p.accuracy)
        .reduce((a, b) => a + b) / performances.length;
  }

  double _calculateAverageResponseTime(List<UserPerformance> performances) {
    if (performances.isEmpty) return 30.0; // Default 30 seconds
    
    return performances
        .map((p) => p.averageTimePerQuestion)
        .reduce((a, b) => a + b) / performances.length;
  }

  double _calculateTimeConfidenceFactor(double avgResponseTime) {
    // Optimal response time is around 20-40 seconds
    if (avgResponseTime <= 15) return 0.0; // Too fast, might be guessing
    if (avgResponseTime <= 30) return 1.0; // Optimal confidence
    if (avgResponseTime <= 60) return 0.5; // Moderate confidence
    return 0.2; // Low confidence, taking too long
  }

  double _calculateStreakFactor(int maxStreak) {
    if (maxStreak >= 10) return 1.0;
    if (maxStreak >= 5) return 0.5;
    if (maxStreak >= 3) return 0.3;
    return 0.0;
  }

  int _mapScoreToDifficulty(double score) {
    if (score < 0.3) return 1;
    if (score < 0.5) return 2;
    if (score < 0.7) return 3;
    if (score < 0.85) return 4;
    return 5;
  }

  List<int> _getDifficultyRange(int optimalDifficulty) {
    // Create a range of ±1 around optimal difficulty
    final minDifficulty = max(1, optimalDifficulty - 1);
    final maxDifficulty = min(5, optimalDifficulty + 1);
    
    return List.generate(maxDifficulty - minDifficulty + 1, (index) => minDifficulty + index);
  }

  PerformanceTrend _calculatePerformanceTrend(List<QuizAttempt> attempts) {
    if (attempts.length < 10) {
      return PerformanceTrend.stable(); // Not enough data
    }

    final firstHalf = attempts.take(attempts.length ~/ 2).toList();
    final secondHalf = attempts.skip(attempts.length ~/ 2).toList();

    final firstHalfAccuracy = firstHalf
        .map((a) => a.isCorrect ? 1.0 : 0.0)
        .reduce((a, b) => a + b) / firstHalf.length;

    final secondHalfAccuracy = secondHalf
        .map((a) => a.isCorrect ? 1.0 : 0.0)
        .reduce((a, b) => a + b) / secondHalf.length;

    final change = secondHalfAccuracy - firstHalfAccuracy;

    if (change > 0.1) return PerformanceTrend.improving();
    if (change < -0.1) return PerformanceTrend.declining();
    return PerformanceTrend.stable();
  }

  Map<int, double> _analyzeDifficultyPreferences(List<QuizAttempt> attempts) {
    final difficultyPerformance = <int, List<bool>>{};

    for (final attempt in attempts) {
      final difficulty = attempt.difficulty;
      difficultyPerformance.putIfAbsent(difficulty, () => []).add(attempt.isCorrect);
    }

    final preferences = <int, double>{};
    for (final entry in difficultyPerformance.entries) {
      final accuracy = entry.value.where((correct) => correct).length / entry.value.length;
      preferences[entry.key] = accuracy;
    }

    return preferences;
  }

  Map<int, double> _analyzeTimePatterns(List<QuizAttempt> attempts) {
    final hourlyPerformance = <int, List<bool>>{};

    for (final attempt in attempts) {
      final hour = attempt.timestamp.hour;
      hourlyPerformance.putIfAbsent(hour, () => []).add(attempt.isCorrect);
    }

    final patterns = <int, double>{};
    for (final entry in hourlyPerformance.entries) {
      final accuracy = entry.value.where((correct) => correct).length / entry.value.length;
      patterns[entry.key] = accuracy;
    }

    return patterns;
  }

  double _calculateConsistencyMetrics(List<QuizAttempt> attempts) {
    if (attempts.length < 5) return 0.5; // Default moderate consistency

    final accuracies = <double>[];
    final windowSize = 5;

    for (int i = 0; i <= attempts.length - windowSize; i++) {
      final window = attempts.skip(i).take(windowSize);
      final windowAccuracy = window.where((a) => a.isCorrect).length / window.length;
      accuracies.add(windowAccuracy);
    }

    // Calculate standard deviation
    final mean = accuracies.reduce((a, b) => a + b) / accuracies.length;
    final variance = accuracies
        .map((a) => pow(a - mean, 2))
        .reduce((a, b) => a + b) / accuracies.length;
    final stdDev = sqrt(variance);

    // Lower standard deviation = higher consistency
    return max(0.0, 1.0 - stdDev * 2);
  }

  double _calculateLearningVelocity(List<QuizAttempt> attempts) {
    if (attempts.length < 10) return 0.5; // Default moderate velocity

    // Calculate improvement rate over time
    final firstQuarter = attempts.take(attempts.length ~/ 4);
    final lastQuarter = attempts.skip((attempts.length * 3) ~/ 4);

    final firstAccuracy = firstQuarter.where((a) => a.isCorrect).length / firstQuarter.length;
    final lastAccuracy = lastQuarter.where((a) => a.isCorrect).length / lastQuarter.length;

    final improvement = lastAccuracy - firstAccuracy;
    return (improvement + 1.0) / 2.0; // Normalize to 0-1 range
  }

  double _calculateConfidenceLevel(List<UserPerformance> performances) {
    if (performances.isEmpty) return 0.0;

    final totalQuestions = performances.fold(0, (sum, p) => sum + p.questionsAnswered);
    final avgAccuracy = performances
        .map((p) => p.accuracy)
        .reduce((a, b) => a + b) / performances.length;

    // Confidence based on data quantity and performance stability
    double quantityFactor = min(1.0, totalQuestions / 100.0);
    double stabilityFactor = avgAccuracy > 0.5 ? avgAccuracy : 0.5;

    return (quantityFactor + stabilityFactor) / 2.0;
  }
}

// Supporting classes for analysis results

class PerformancePatterns {
  final PerformanceTrend trend;
  final Map<int, double> difficultyPreferences;
  final Map<int, double> timePatterns;
  final double consistency;
  final double learningVelocity;
  final int totalAttempts;
  final DateTime analysisDate;

  PerformancePatterns({
    required this.trend,
    required this.difficultyPreferences,
    required this.timePatterns,
    required this.consistency,
    required this.learningVelocity,
    required this.totalAttempts,
    required this.analysisDate,
  });

  static PerformancePatterns empty() {
    return PerformancePatterns(
      trend: PerformanceTrend.stable(),
      difficultyPreferences: {},
      timePatterns: {},
      consistency: 0.5,
      learningVelocity: 0.5,
      totalAttempts: 0,
      analysisDate: DateTime.now(),
    );
  }
}

class PerformanceTrend {
  final String direction;
  final double magnitude;
  final String description;

  PerformanceTrend({
    required this.direction,
    required this.magnitude,
    required this.description,
  });

  static PerformanceTrend improving() {
    return PerformanceTrend(
      direction: 'improving',
      magnitude: 0.8,
      description: 'Vos performances s\'améliorent',
    );
  }

  static PerformanceTrend declining() {
    return PerformanceTrend(
      direction: 'declining',
      magnitude: 0.8,
      description: 'Vos performances diminuent',
    );
  }

  static PerformanceTrend stable() {
    return PerformanceTrend(
      direction: 'stable',
      magnitude: 0.5,
      description: 'Vos performances sont stables',
    );
  }
}

class DifficultyRecommendations {
  final int overallRecommendedDifficulty;
  final Map<String, int> categoryRecommendations;
  final List<String> recommendations;
  final double confidenceLevel;

  DifficultyRecommendations({
    required this.overallRecommendedDifficulty,
    required this.categoryRecommendations,
    required this.recommendations,
    required this.confidenceLevel,
  });

  static DifficultyRecommendations forNewUser() {
    return DifficultyRecommendations(
      overallRecommendedDifficulty: 2,
      categoryRecommendations: {},
      recommendations: [
        'Commencez par les niveaux 1-2 pour vous familiariser',
        'Prenez votre temps pour bien comprendre les concepts',
        'N\'hésitez pas à relire les explications',
      ],
      confidenceLevel: 0.0,
    );
  }
}

class QuizAttempt {
  final bool isCorrect;
  final int difficulty;
  final DateTime timestamp;
  final Duration responseTime;
  final String topic;

  QuizAttempt({
    required this.isCorrect,
    required this.difficulty,
    required this.timestamp,
    required this.responseTime,
    required this.topic,
  });
}
