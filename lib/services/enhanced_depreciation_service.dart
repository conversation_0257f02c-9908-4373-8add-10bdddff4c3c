import 'dart:math';
import '../models/calculators/enhanced_depreciation_data.dart';
import '../models/immobilisations/amortization_row.dart';

class EnhancedDepreciationService {
  
  EnhancedDepreciationResult calculateDepreciation(EnhancedDepreciationInput input) {
    if (!input.isValid) {
      throw ArgumentError('Invalid depreciation input parameters');
    }
    
    List<AmortizationRow> amortizationTable;
    
    switch (input.method) {
      case DepreciationMethod.linear:
        amortizationTable = calculateLinearDepreciation(input);
        break;
      case DepreciationMethod.degressive:
        amortizationTable = calculateDegressiveDepreciation(input);
        break;
      case DepreciationMethod.sumOfYearsDigits:
        amortizationTable = calculateSumOfYearsDigits(input);
        break;
      case DepreciationMethod.unitsOfProduction:
        amortizationTable = calculateUnitsOfProduction(input);
        break;
    }
    
    final summary = _createSummary(amortizationTable, input);
    final taxAdvice = _generateTaxOptimizationAdvice(input, amortizationTable);
    
    return EnhancedDepreciationResult(
      amortizationTable: amortizationTable,
      summary: summary,
      taxAdvice: taxAdvice,
    );
  }

  List<AmortizationRow> calculateLinearDepreciation(EnhancedDepreciationInput input) {
    final annualDepreciation = input.depreciableAmount / input.usefulLifeYears;
    final rows = <AmortizationRow>[];
    
    double cumulativeDepreciation = 0;
    
    for (int year = 1; year <= input.usefulLifeYears; year++) {
      double yearlyDepreciation = annualDepreciation;
      
      // Handle mid-year convention for first year
      if (year == 1 && input.midYearConvention) {
        yearlyDepreciation = annualDepreciation * 0.5;
      }
      
      // Handle mid-year convention for last year (if first year was half)
      if (year == input.usefulLifeYears && input.midYearConvention) {
        yearlyDepreciation = annualDepreciation * 0.5;
      }
      
      cumulativeDepreciation += yearlyDepreciation;
      final bookValue = input.assetCost - cumulativeDepreciation;
      
      rows.add(AmortizationRow(
        year: year,
        yearLabel: 'Année $year',
        baseAmount: input.assetCost,
        rate: (1.0 / input.usefulLifeYears) * 100, // Convert to percentage
        annuity: yearlyDepreciation,
        cumulativeAnnuity: cumulativeDepreciation,
        netBookValue: bookValue,
      ));
    }
    
    return rows;
  }

  List<AmortizationRow> calculateDegressiveDepreciation(EnhancedDepreciationInput input) {
    final coefficient = input.degressiveCoefficient ?? 2.0;
    final degressiveRate = coefficient / input.usefulLifeYears;
    final linearRate = 1.0 / input.usefulLifeYears;
    
    final rows = <AmortizationRow>[];
    double bookValue = input.assetCost;
    double cumulativeDepreciation = 0;
    
    for (int year = 1; year <= input.usefulLifeYears; year++) {
      final remainingYears = input.usefulLifeYears - year + 1;
      final remainingLinearRate = 1.0 / remainingYears;
      
      // Choose the higher rate between degressive and linear
      final rate = max(degressiveRate, remainingLinearRate);
      
      double yearlyDepreciation = bookValue * rate;
      
      // Ensure we don't depreciate below residual value
      final maxDepreciation = bookValue - input.residualValue;
      yearlyDepreciation = min(yearlyDepreciation, maxDepreciation);
      
      // Handle mid-year convention for first year
      if (year == 1 && input.midYearConvention) {
        yearlyDepreciation *= 0.5;
      }
      
      cumulativeDepreciation += yearlyDepreciation;
      final startValue = bookValue;
      bookValue -= yearlyDepreciation;
      
      rows.add(AmortizationRow(
        year: year,
        yearLabel: 'Année $year',
        baseAmount: input.assetCost,
        rate: rate * 100, // Convert to percentage
        annuity: yearlyDepreciation,
        cumulativeAnnuity: cumulativeDepreciation,
        netBookValue: bookValue,
        degressiveRate: degressiveRate * 100,
        linearRate: linearRate * 100,
        isLinearSwitchYear: rate == remainingLinearRate,
      ));
      
      // Stop if we've reached residual value
      if (bookValue <= input.residualValue) {
        break;
      }
    }
    
    return rows;
  }

  List<AmortizationRow> calculateSumOfYearsDigits(EnhancedDepreciationInput input) {
    final sumOfYears = (input.usefulLifeYears * (input.usefulLifeYears + 1)) / 2;
    final rows = <AmortizationRow>[];
    
    double cumulativeDepreciation = 0;
    
    for (int year = 1; year <= input.usefulLifeYears; year++) {
      final remainingYears = input.usefulLifeYears - year + 1;
      final fraction = remainingYears / sumOfYears;
      
      double yearlyDepreciation = input.depreciableAmount * fraction;
      
      // Handle mid-year convention for first year
      if (year == 1 && input.midYearConvention) {
        yearlyDepreciation *= 0.5;
      }
      
      cumulativeDepreciation += yearlyDepreciation;
      final bookValue = input.assetCost - cumulativeDepreciation;
      
      rows.add(AmortizationRow(
        year: year,
        yearLabel: 'Année $year',
        baseAmount: input.assetCost,
        rate: (yearlyDepreciation / input.assetCost) * 100, // Convert to percentage
        annuity: yearlyDepreciation,
        cumulativeAnnuity: cumulativeDepreciation,
        netBookValue: bookValue,
      ));
    }
    
    return rows;
  }

  List<AmortizationRow> calculateUnitsOfProduction(EnhancedDepreciationInput input) {
    if (input.totalUnits == null || input.annualUnitsProduced == null) {
      throw ArgumentError('Units of production method requires total units and annual production data');
    }
    
    final depreciationPerUnit = input.depreciableAmount / input.totalUnits!;
    final rows = <AmortizationRow>[];
    
    double cumulativeDepreciation = 0;
    
    for (int year = 0; year < input.annualUnitsProduced!.length; year++) {
      final unitsProduced = input.annualUnitsProduced![year];
      double yearlyDepreciation = unitsProduced * depreciationPerUnit;
      
      // Ensure we don't exceed total depreciable amount
      final remainingDepreciation = input.depreciableAmount - cumulativeDepreciation;
      yearlyDepreciation = min(yearlyDepreciation, remainingDepreciation);
      
      cumulativeDepreciation += yearlyDepreciation;
      final bookValue = input.assetCost - cumulativeDepreciation;
      
      rows.add(AmortizationRow(
        year: year + 1,
        yearLabel: 'Année ${year + 1}',
        baseAmount: input.assetCost,
        rate: (yearlyDepreciation / input.assetCost) * 100, // Convert to percentage
        annuity: yearlyDepreciation,
        cumulativeAnnuity: cumulativeDepreciation,
        netBookValue: bookValue,
      ));
      
      // Stop if fully depreciated
      if (cumulativeDepreciation >= input.depreciableAmount) {
        break;
      }
    }
    
    return rows;
  }

  List<DepreciationComparison> compareDepreciationMethods(EnhancedDepreciationInput input) {
    final comparisons = <DepreciationComparison>[];
    
    // Calculate for each method
    for (final method in DepreciationMethod.values) {
      if (method == DepreciationMethod.unitsOfProduction && 
          (input.totalUnits == null || input.annualUnitsProduced == null)) {
        continue; // Skip if units data not available
      }
      
      try {
        final methodInput = input.copyWith(method: method);
        final result = calculateDepreciation(methodInput);
        
        final firstYearDepreciation = result.amortizationTable.isNotEmpty
            ? result.amortizationTable.first.annuity
            : 0.0;
        
        final npv = _calculateNetPresentValue(result.amortizationTable, 0.08); // 8% discount rate
        final taxBenefit = _calculateTaxBenefit(result.amortizationTable, 0.31); // 31% tax rate
        
        comparisons.add(DepreciationComparison(
          method: method,
          totalDepreciation: result.summary.totalDepreciation,
          firstYearDepreciation: firstYearDepreciation,
          netPresentValue: npv,
          taxBenefit: taxBenefit,
          recommendation: _getMethodRecommendation(method, result),
        ));
      } catch (e) {
        // Skip methods that can't be calculated
        continue;
      }
    }
    
    return comparisons;
  }

  DepreciationSummary _createSummary(List<AmortizationRow> table, EnhancedDepreciationInput input) {
    final totalDepreciation = table.fold<double>(0, (sum, row) => sum + row.annuity);
    final remainingValue = input.assetCost - totalDepreciation;
    final averageAnnual = table.isNotEmpty ? totalDepreciation / table.length : 0.0;
    
    return DepreciationSummary(
      totalDepreciation: totalDepreciation,
      remainingValue: remainingValue,
      averageAnnualDepreciation: averageAnnual,
      totalYears: table.length,
      method: input.method,
      firstYearDepreciation: table.isNotEmpty ? table.first.annuity : 0,
      lastYearDepreciation: table.isNotEmpty ? table.last.annuity : 0,
    );
  }

  TaxOptimizationAdvice _generateTaxOptimizationAdvice(
    EnhancedDepreciationInput input, 
    List<AmortizationRow> table,
  ) {
    final comparisons = compareDepreciationMethods(input);
    
    if (comparisons.isEmpty) {
      return const TaxOptimizationAdvice(
        recommendedMethod: 'Linéaire',
        estimatedTaxSavings: 0,
        reasoning: 'Analyse non disponible',
        considerations: [],
        implementationTiming: 'Immédiat',
      );
    }
    
    // Find method with highest NPV of tax benefits
    comparisons.sort((a, b) => b.taxBenefit.compareTo(a.taxBenefit));
    final bestMethod = comparisons.first;
    
    final considerations = <String>[
      'Conformité avec la réglementation comptable marocaine',
      'Impact sur la trésorerie de l\'entreprise',
      'Cohérence avec la stratégie fiscale globale',
    ];
    
    if (input.method != bestMethod.method) {
      considerations.add('Changement de méthode nécessaire');
    }
    
    return TaxOptimizationAdvice(
      recommendedMethod: bestMethod.method.displayName,
      estimatedTaxSavings: bestMethod.taxBenefit,
      reasoning: _getOptimizationReasoning(bestMethod, input),
      considerations: considerations,
      implementationTiming: 'Début d\'exercice comptable',
    );
  }

  double _calculateNetPresentValue(List<AmortizationRow> table, double discountRate) {
    double npv = 0;
    for (final row in table) {
      final presentValue = row.annuity / pow(1 + discountRate, row.year);
      npv += presentValue;
    }
    return npv;
  }

  double _calculateTaxBenefit(List<AmortizationRow> table, double taxRate) {
    double totalBenefit = 0;
    for (final row in table) {
      final yearlyBenefit = row.annuity * taxRate;
      final presentValue = yearlyBenefit / pow(1.08, row.year); // 8% discount
      totalBenefit += presentValue;
    }
    return totalBenefit;
  }

  String _getMethodRecommendation(DepreciationMethod method, EnhancedDepreciationResult result) {
    switch (method) {
      case DepreciationMethod.linear:
        return 'Simplicité et conformité, idéal pour les actifs à usage régulier';
      case DepreciationMethod.degressive:
        return 'Optimise la trésorerie en début de période, adapté aux actifs technologiques';
      case DepreciationMethod.sumOfYearsDigits:
        return 'Compromis entre linéaire et dégressif, pour actifs à dépréciation rapide';
      case DepreciationMethod.unitsOfProduction:
        return 'Reflète l\'usage réel, idéal pour les équipements de production';
    }
  }

  String _getOptimizationReasoning(DepreciationComparison bestMethod, EnhancedDepreciationInput input) {
    final buffer = StringBuffer();
    
    buffer.writeln('La méthode ${bestMethod.method.displayName} est recommandée car :');
    buffer.writeln('• Avantage fiscal estimé : ${bestMethod.taxBenefit.toStringAsFixed(0)} DH');
    buffer.writeln('• Valeur actuelle nette : ${bestMethod.netPresentValue.toStringAsFixed(0)} DH');
    buffer.writeln('• Dépréciation première année : ${bestMethod.firstYearDepreciation.toStringAsFixed(0)} DH');
    
    if (bestMethod.method == DepreciationMethod.degressive) {
      buffer.writeln('• Améliore la trésorerie en début de période');
    } else if (bestMethod.method == DepreciationMethod.linear) {
      buffer.writeln('• Simplicité de gestion et conformité assurée');
    }
    
    return buffer.toString();
  }
}
