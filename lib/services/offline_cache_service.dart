import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import '../models/hive/cache_entry.dart';



/// Cache statistics model
class CacheStatistics {
  final int totalEntries;
  final int totalSizeBytes;
  final int expiredEntries;
  final int compressedEntries;
  final double hitRate;
  final double compressionRatio;
  final Map<String, int> contentTypeCounts;
  final DateTime lastOptimization;

  CacheStatistics({
    required this.totalEntries,
    required this.totalSizeBytes,
    required this.expiredEntries,
    required this.compressedEntries,
    required this.hitRate,
    required this.compressionRatio,
    required this.contentTypeCounts,
    required this.lastOptimization,
  });

  String get formattedSize {
    if (totalSizeBytes < 1024) return '$totalSizeBytes B';
    if (totalSizeBytes < 1024 * 1024) return '${(totalSizeBytes / 1024).toStringAsFixed(1)} KB';
    if (totalSizeBytes < 1024 * 1024 * 1024) return '${(totalSizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(totalSizeBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// Service for managing offline content caching with compression and optimization
class OfflineCacheService {
  static const String _cacheBoxName = 'offline_cache_metadata';
  static const String _statsBoxName = 'cache_statistics';
  static const String _cacheDirectoryName = 'offline_cache';
  static const int _maxCacheSizeBytes = 500 * 1024 * 1024; // 500MB
  static const Duration _defaultExpiration = Duration(days: 7);
  static const Duration _backgroundSyncInterval = Duration(hours: 6);

  static final OfflineCacheService _instance = OfflineCacheService._internal();
  factory OfflineCacheService() => _instance;
  OfflineCacheService._internal();

  Box<CacheEntry>? _cacheBox;
  Box? _statsBox;
  Directory? _cacheDirectory;
  Timer? _backgroundSyncTimer;
  Timer? _cleanupTimer;

  // Statistics tracking
  int _cacheHits = 0;
  int _cacheMisses = 0;
  DateTime _lastOptimization = DateTime.now();

  // Essential content identifiers
  static const List<String> _essentialContent = [
    'ir_basics',
    'is_fundamentals',
    'comptabilite_generale_intro',
    'tax_calculator_data',
    'common_forms',
  ];

  /// Initialize the cache service
  Future<void> initialize() async {
    try {
      // Initialize Hive boxes
      _cacheBox = await Hive.openBox<CacheEntry>(_cacheBoxName);
      _statsBox = await Hive.openBox(_statsBoxName);

      // Initialize cache directory
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/$_cacheDirectoryName');
      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }

      // Start background tasks
      _startBackgroundSync();
      _startPeriodicCleanup();

      // Perform initial cleanup
      await _performInitialCleanup();

      debugPrint('OfflineCacheService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing OfflineCacheService: $e');
      rethrow;
    }
  }

  /// Preload essential content for offline use
  Future<void> preloadEssentialContent() async {
    debugPrint('Preloading essential content...');
    
    for (final contentId in _essentialContent) {
      try {
        await _preloadContentById(contentId);
      } catch (e) {
        debugPrint('Error preloading $contentId: $e');
      }
    }

    debugPrint('Essential content preloading completed');
  }

  /// Cache guide content with compression
  Future<bool> cacheGuideContent(String guideId) async {
    try {
      // Load guide content from assets
      final contentPath = _getGuideContentPath(guideId);
      final contentString = await rootBundle.loadString(contentPath);
      
      // Create cache entry
      final cacheKey = 'guide_$guideId';
      final compressedData = _compressString(contentString);
      
      // Save to file system
      final success = await _saveToFileSystem(cacheKey, compressedData);
      if (!success) return false;

      // Create metadata entry
      final entry = CacheEntry(
        key: cacheKey,
        createdAt: DateTime.now(),
        lastAccessed: DateTime.now(),
        expiresAt: DateTime.now().add(_defaultExpiration),
        sizeBytes: compressedData.length,
        contentType: 'application/json',
        isCompressed: true,
        metadata: {
          'guideId': guideId,
          'originalSize': contentString.length,
          'compressionRatio': compressedData.length / contentString.length,
        },
      );

      await _cacheBox!.put(cacheKey, entry);
      debugPrint('Cached guide content: $guideId (${entry.sizeBytes} bytes)');
      return true;
    } catch (e) {
      debugPrint('Error caching guide content $guideId: $e');
      return false;
    }
  }

  /// Cache quiz data
  Future<bool> cacheQuizData() async {
    try {
      // Load quiz data from assets
      final quizData = await _loadQuizDataFromAssets();
      final jsonString = jsonEncode(quizData);
      
      // Compress and cache
      final compressedData = _compressString(jsonString);
      const cacheKey = 'quiz_data';
      
      final success = await _saveToFileSystem(cacheKey, compressedData);
      if (!success) return false;

      final entry = CacheEntry(
        key: cacheKey,
        createdAt: DateTime.now(),
        lastAccessed: DateTime.now(),
        expiresAt: DateTime.now().add(_defaultExpiration),
        sizeBytes: compressedData.length,
        contentType: 'application/json',
        isCompressed: true,
        metadata: {
          'type': 'quiz_data',
          'questionCount': quizData['questions']?.length ?? 0,
          'originalSize': jsonString.length,
        },
      );

      await _cacheBox!.put(cacheKey, entry);
      debugPrint('Cached quiz data (${entry.sizeBytes} bytes)');
      return true;
    } catch (e) {
      debugPrint('Error caching quiz data: $e');
      return false;
    }
  }

  /// Get cached content by key
  Future<String?> getCachedContent(String key) async {
    try {
      final entry = _cacheBox!.get(key);
      if (entry == null || entry.isExpired) {
        _cacheMisses++;
        return null;
      }

      // Update last accessed time
      final updatedEntry = entry.copyWith(lastAccessed: DateTime.now());
      await _cacheBox!.put(key, updatedEntry);

      // Load from file system
      final data = await _loadFromFileSystem(key);
      if (data == null) {
        _cacheMisses++;
        return null;
      }

      _cacheHits++;
      
      // Decompress if needed
      if (entry.isCompressed) {
        return _decompressToString(data);
      } else {
        return String.fromCharCodes(data);
      }
    } catch (e) {
      debugPrint('Error getting cached content $key: $e');
      _cacheMisses++;
      return null;
    }
  }

  /// Check if cache entry is valid
  bool isCacheValid(String key) {
    final entry = _cacheBox!.get(key);
    return entry != null && entry.isValid;
  }

  /// Clear expired cache entries
  Future<int> clearExpiredCache() async {
    int removedCount = 0;
    
    try {
      final expiredKeys = <String>[];
      
      for (final entry in _cacheBox!.values) {
        if (entry.isExpired) {
          expiredKeys.add(entry.key);
        }
      }

      for (final key in expiredKeys) {
        await _removeCacheEntry(key);
        removedCount++;
      }

      debugPrint('Cleared $removedCount expired cache entries');
    } catch (e) {
      debugPrint('Error clearing expired cache: $e');
    }

    return removedCount;
  }

  /// Get total cache size in bytes
  Future<int> getCacheSize() async {
    try {
      int totalSize = 0;
      
      for (final entry in _cacheBox!.values) {
        totalSize += entry.sizeBytes;
      }

      return totalSize;
    } catch (e) {
      debugPrint('Error calculating cache size: $e');
      return 0;
    }
  }

  /// Clear all cache
  Future<void> clearCache() async {
    try {
      // Clear metadata
      await _cacheBox!.clear();
      
      // Clear file system cache
      if (await _cacheDirectory!.exists()) {
        await _cacheDirectory!.delete(recursive: true);
        await _cacheDirectory!.create(recursive: true);
      }

      // Reset statistics
      _cacheHits = 0;
      _cacheMisses = 0;
      _lastOptimization = DateTime.now();

      debugPrint('Cache cleared successfully');
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }

  /// Optimize cache by removing least recently used entries
  Future<void> optimizeCache() async {
    try {
      final currentSize = await getCacheSize();
      
      if (currentSize <= _maxCacheSizeBytes) {
        debugPrint('Cache optimization not needed (${currentSize} bytes)');
        return;
      }

      debugPrint('Optimizing cache (current size: ${currentSize} bytes)');

      // Get all entries sorted by last accessed time
      final entries = _cacheBox!.values.toList();
      entries.sort((a, b) => a.lastAccessed.compareTo(b.lastAccessed));

      int removedSize = 0;
      int removedCount = 0;
      final targetReduction = currentSize - (_maxCacheSizeBytes * 0.8).round();

      for (final entry in entries) {
        if (removedSize >= targetReduction) break;
        
        // Don't remove essential content
        if (_isEssentialContent(entry.key)) continue;

        await _removeCacheEntry(entry.key);
        removedSize += entry.sizeBytes;
        removedCount++;
      }

      _lastOptimization = DateTime.now();
      debugPrint('Cache optimized: removed $removedCount entries (${removedSize} bytes)');
    } catch (e) {
      debugPrint('Error optimizing cache: $e');
    }
  }

  /// Get cache statistics
  Future<CacheStatistics> getCacheStatistics() async {
    try {
      final entries = _cacheBox!.values.toList();
      final totalEntries = entries.length;
      final totalSize = entries.fold<int>(0, (sum, entry) => sum + entry.sizeBytes);
      final expiredEntries = entries.where((entry) => entry.isExpired).length;
      final compressedEntries = entries.where((entry) => entry.isCompressed).length;
      
      final contentTypeCounts = <String, int>{};
      for (final entry in entries) {
        contentTypeCounts[entry.contentType] = (contentTypeCounts[entry.contentType] ?? 0) + 1;
      }

      final totalRequests = _cacheHits + _cacheMisses;
      final hitRate = totalRequests > 0 ? _cacheHits / totalRequests : 0.0;

      final compressedSize = entries
          .where((entry) => entry.isCompressed)
          .fold<int>(0, (sum, entry) => sum + entry.sizeBytes);
      final originalSize = entries
          .where((entry) => entry.isCompressed && entry.metadata?['originalSize'] != null)
          .fold<int>(0, (sum, entry) => sum + (entry.metadata!['originalSize'] as int));
      final compressionRatio = originalSize > 0 ? compressedSize / originalSize : 1.0;

      return CacheStatistics(
        totalEntries: totalEntries,
        totalSizeBytes: totalSize,
        expiredEntries: expiredEntries,
        compressedEntries: compressedEntries,
        hitRate: hitRate,
        compressionRatio: compressionRatio,
        contentTypeCounts: contentTypeCounts,
        lastOptimization: _lastOptimization,
      );
    } catch (e) {
      debugPrint('Error getting cache statistics: $e');
      return CacheStatistics(
        totalEntries: 0,
        totalSizeBytes: 0,
        expiredEntries: 0,
        compressedEntries: 0,
        hitRate: 0.0,
        compressionRatio: 1.0,
        contentTypeCounts: {},
        lastOptimization: DateTime.now(),
      );
    }
  }

  /// Check if device is online
  Future<bool> isOnline() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      return false;
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    _backgroundSyncTimer?.cancel();
    _cleanupTimer?.cancel();
    await _cacheBox?.close();
    await _statsBox?.close();
  }

  // Private helper methods

  Future<void> _preloadContentById(String contentId) async {
    try {
      switch (contentId) {
        case 'ir_basics':
          await cacheGuideContent('ir');
          break;
        case 'is_fundamentals':
          await cacheGuideContent('is');
          break;
        case 'comptabilite_generale_intro':
          await cacheGuideContent('comptabilite_generale');
          break;
        case 'tax_calculator_data':
          await _cacheTaxCalculatorData();
          break;
        case 'common_forms':
          await _cacheCommonForms();
          break;
      }
    } catch (e) {
      debugPrint('Error preloading content $contentId: $e');
    }
  }

  String _getGuideContentPath(String guideId) {
    switch (guideId) {
      case 'ir':
        return 'assets/ir/presentation.json';
      case 'is':
        return 'assets/is/presentation.json';
      case 'comptabilite_generale':
        return 'assets/compta_generale/introduction.json';
      case 'droits_enregistrement':
        return 'assets/fiscalite/droits_enregistrement.json';
      default:
        return 'assets/$guideId/content.json';
    }
  }

  Uint8List _compressString(String data) {
    final bytes = utf8.encode(data);
    final compressed = GZipEncoder().encode(bytes);
    return Uint8List.fromList(compressed!);
  }

  String _decompressToString(Uint8List compressed) {
    final decompressed = GZipDecoder().decodeBytes(compressed);
    return utf8.decode(decompressed);
  }

  Future<bool> _saveToFileSystem(String key, Uint8List data) async {
    try {
      final file = File('${_cacheDirectory!.path}/${_hashKey(key)}.cache');
      await file.writeAsBytes(data);
      return true;
    } catch (e) {
      debugPrint('Error saving to file system: $e');
      return false;
    }
  }

  Future<Uint8List?> _loadFromFileSystem(String key) async {
    try {
      final file = File('${_cacheDirectory!.path}/${_hashKey(key)}.cache');
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      debugPrint('Error loading from file system: $e');
      return null;
    }
  }

  String _hashKey(String key) {
    final bytes = utf8.encode(key);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<void> _removeCacheEntry(String key) async {
    try {
      // Remove metadata
      await _cacheBox!.delete(key);
      
      // Remove file
      final file = File('${_cacheDirectory!.path}/${_hashKey(key)}.cache');
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      debugPrint('Error removing cache entry $key: $e');
    }
  }

  bool _isEssentialContent(String key) {
    return key.startsWith('guide_ir') ||
           key.startsWith('guide_is') ||
           key.startsWith('guide_comptabilite_generale') ||
           key == 'quiz_data' ||
           key == 'tax_calculator_data' ||
           key == 'common_forms';
  }

  void _startBackgroundSync() {
    _backgroundSyncTimer = Timer.periodic(_backgroundSyncInterval, (timer) async {
      if (await isOnline()) {
        await _performBackgroundSync();
      }
    });
  }

  void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(const Duration(hours: 24), (timer) async {
      await clearExpiredCache();
      await optimizeCache();
    });
  }

  Future<void> _performInitialCleanup() async {
    await clearExpiredCache();
    
    final currentSize = await getCacheSize();
    if (currentSize > _maxCacheSizeBytes) {
      await optimizeCache();
    }
  }

  Future<void> _performBackgroundSync() async {
    try {
      debugPrint('Performing background sync...');
      
      // Update essential content if needed
      for (final contentId in _essentialContent) {
        final cacheKey = 'guide_${contentId.replaceAll('_basics', '').replaceAll('_fundamentals', '').replaceAll('_intro', '')}';
        final entry = _cacheBox!.get(cacheKey);
        
        if (entry == null || _shouldRefreshContent(entry)) {
          await _preloadContentById(contentId);
        }
      }
      
      debugPrint('Background sync completed');
    } catch (e) {
      debugPrint('Error during background sync: $e');
    }
  }

  bool _shouldRefreshContent(CacheEntry entry) {
    final age = DateTime.now().difference(entry.createdAt);
    return age.inDays > 3; // Refresh content older than 3 days
  }

  Future<Map<String, dynamic>> _loadQuizDataFromAssets() async {
    try {
      // This would load quiz data from assets
      // For now, return a placeholder structure
      return {
        'questions': [],
        'categories': [],
        'metadata': {
          'version': '1.0',
          'lastUpdated': DateTime.now().toIso8601String(),
        },
      };
    } catch (e) {
      debugPrint('Error loading quiz data: $e');
      return {};
    }
  }

  Future<void> _cacheTaxCalculatorData() async {
    try {
      const data = {
        'taxBrackets': [],
        'deductions': [],
        'rates': {},
        'lastUpdated': '2024-01-01',
      };
      
      final jsonString = jsonEncode(data);
      final compressedData = _compressString(jsonString);
      const cacheKey = 'tax_calculator_data';
      
      await _saveToFileSystem(cacheKey, compressedData);
      
      final entry = CacheEntry(
        key: cacheKey,
        createdAt: DateTime.now(),
        lastAccessed: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(days: 30)),
        sizeBytes: compressedData.length,
        contentType: 'application/json',
        isCompressed: true,
        metadata: {'type': 'tax_calculator'},
      );
      
      await _cacheBox!.put(cacheKey, entry);
    } catch (e) {
      debugPrint('Error caching tax calculator data: $e');
    }
  }

  Future<void> _cacheCommonForms() async {
    try {
      const data = {
        'forms': [],
        'templates': [],
        'lastUpdated': '2024-01-01',
      };
      
      final jsonString = jsonEncode(data);
      final compressedData = _compressString(jsonString);
      const cacheKey = 'common_forms';
      
      await _saveToFileSystem(cacheKey, compressedData);
      
      final entry = CacheEntry(
        key: cacheKey,
        createdAt: DateTime.now(),
        lastAccessed: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(days: 14)),
        sizeBytes: compressedData.length,
        contentType: 'application/json',
        isCompressed: true,
        metadata: {'type': 'forms'},
      );
      
      await _cacheBox!.put(cacheKey, entry);
    } catch (e) {
      debugPrint('Error caching common forms: $e');
    }
  }
}