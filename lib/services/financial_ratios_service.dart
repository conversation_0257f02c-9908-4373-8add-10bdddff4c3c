import '../models/calculators/financial_ratios_data.dart';

class FinancialRatiosService {
  
  FinancialRatiosResult calculateRatios(FinancialRatiosInput input) {
    final liquidityRatios = calculateLiquidityRatios(input);
    final profitabilityRatios = calculateProfitabilityRatios(input);
    final activityRatios = calculateActivityRatios(input);
    final leverageRatios = calculateLeverageRatios(input);
    
    final interpretation = _interpretRatios(
      liquidityRatios,
      profitabilityRatios,
      activityRatios,
      leverageRatios,
    );
    
    final overallAnalysis = _generateOverallAnalysis(
      liquidityRatios,
      profitabilityRatios,
      activityRatios,
      leverageRatios,
      interpretation,
    );
    
    final recommendations = _generateRecommendations(
      liquidityRatios,
      profitabilityRatios,
      activityRatios,
      leverageRatios,
      interpretation,
    );
    
    return FinancialRatiosResult(
      liquidityRatios: liquidityRatios,
      profitabilityRatios: profitabilityRatios,
      activityRatios: activityRatios,
      leverageRatios: leverageRatios,
      overallAnalysis: overallAnalysis,
      recommendations: recommendations,
      interpretation: interpretation,
    );
  }

  LiquidityRatios calculateLiquidityRatios(FinancialRatiosInput input) {
    final currentRatio = input.currentLiabilities > 0 
        ? input.currentAssets / input.currentLiabilities 
        : 0.0;
    
    final quickAssets = input.currentAssets - input.inventory;
    final quickRatio = input.currentLiabilities > 0 
        ? quickAssets / input.currentLiabilities 
        : 0.0;
    
    final cashRatio = input.currentLiabilities > 0 
        ? input.cash / input.currentLiabilities 
        : 0.0;
    
    final workingCapital = input.currentAssets - input.currentLiabilities;
    
    return LiquidityRatios(
      currentRatio: currentRatio,
      quickRatio: quickRatio,
      cashRatio: cashRatio,
      workingCapital: workingCapital,
    );
  }

  ProfitabilityRatios calculateProfitabilityRatios(FinancialRatiosInput input) {
    final returnOnAssets = input.totalAssets > 0 
        ? input.netIncome / input.totalAssets 
        : 0.0;
    
    final returnOnEquity = input.totalEquity > 0 
        ? input.netIncome / input.totalEquity 
        : 0.0;
    
    final netProfitMargin = input.revenue > 0 
        ? input.netIncome / input.revenue 
        : 0.0;
    
    final grossProfitMargin = input.revenue > 0 
        ? input.grossProfit / input.revenue 
        : 0.0;
    
    final operatingMargin = input.revenue > 0 
        ? input.operatingIncome / input.revenue 
        : 0.0;
    
    return ProfitabilityRatios(
      returnOnAssets: returnOnAssets,
      returnOnEquity: returnOnEquity,
      netProfitMargin: netProfitMargin,
      grossProfitMargin: grossProfitMargin,
      operatingMargin: operatingMargin,
    );
  }

  ActivityRatios calculateActivityRatios(FinancialRatiosInput input) {
    final inventoryTurnover = input.inventory > 0 
        ? input.costOfGoodsSold / input.inventory 
        : 0.0;
    
    final receivablesTurnover = input.accountsReceivable > 0 
        ? input.revenue / input.accountsReceivable 
        : 0.0;
    
    final assetTurnover = input.totalAssets > 0 
        ? input.revenue / input.totalAssets 
        : 0.0;
    
    final inventoryDays = inventoryTurnover > 0 ? 365 / inventoryTurnover : 0.0;
    final receivablesDays = receivablesTurnover > 0 ? 365 / receivablesTurnover : 0.0;
    
    return ActivityRatios(
      inventoryTurnover: inventoryTurnover,
      receivablesTurnover: receivablesTurnover,
      assetTurnover: assetTurnover,
      inventoryDays: inventoryDays,
      receivablesDays: receivablesDays,
    );
  }

  LeverageRatios calculateLeverageRatios(FinancialRatiosInput input) {
    final debtToEquity = input.totalEquity > 0 
        ? input.totalDebt / input.totalEquity 
        : 0.0;
    
    final debtRatio = input.totalAssets > 0 
        ? input.totalDebt / input.totalAssets 
        : 0.0;
    
    final interestCoverage = input.interestExpense > 0 
        ? input.operatingIncome / input.interestExpense 
        : 0.0;
    
    final equityRatio = input.totalAssets > 0 
        ? input.totalEquity / input.totalAssets 
        : 0.0;
    
    return LeverageRatios(
      debtToEquity: debtToEquity,
      debtRatio: debtRatio,
      interestCoverage: interestCoverage,
      equityRatio: equityRatio,
    );
  }

  RatioInterpretation _interpretRatios(
    LiquidityRatios liquidity,
    ProfitabilityRatios profitability,
    ActivityRatios activity,
    LeverageRatios leverage,
  ) {
    final liquidityQuality = _assessLiquidityQuality(liquidity);
    final profitabilityQuality = _assessProfitabilityQuality(profitability);
    final activityQuality = _assessActivityQuality(activity);
    final leverageQuality = _assessLeverageQuality(leverage);
    
    final overallQuality = _calculateOverallQuality([
      liquidityQuality,
      profitabilityQuality,
      activityQuality,
      leverageQuality,
    ]);
    
    return RatioInterpretation(
      liquidityQuality: liquidityQuality,
      profitabilityQuality: profitabilityQuality,
      activityQuality: activityQuality,
      leverageQuality: leverageQuality,
      overallQuality: overallQuality,
    );
  }

  RatioQuality _assessLiquidityQuality(LiquidityRatios ratios) {
    if (ratios.currentRatio >= 2.0 && ratios.quickRatio >= 1.0) {
      return RatioQuality.excellent;
    } else if (ratios.currentRatio >= 1.5 && ratios.quickRatio >= 0.8) {
      return RatioQuality.good;
    } else if (ratios.currentRatio >= 1.0 && ratios.quickRatio >= 0.5) {
      return RatioQuality.average;
    } else if (ratios.currentRatio >= 0.8) {
      return RatioQuality.poor;
    } else {
      return RatioQuality.critical;
    }
  }

  RatioQuality _assessProfitabilityQuality(ProfitabilityRatios ratios) {
    if (ratios.returnOnAssets >= 0.15 && ratios.netProfitMargin >= 0.10) {
      return RatioQuality.excellent;
    } else if (ratios.returnOnAssets >= 0.10 && ratios.netProfitMargin >= 0.05) {
      return RatioQuality.good;
    } else if (ratios.returnOnAssets >= 0.05 && ratios.netProfitMargin >= 0.02) {
      return RatioQuality.average;
    } else if (ratios.returnOnAssets >= 0.02) {
      return RatioQuality.poor;
    } else {
      return RatioQuality.critical;
    }
  }

  RatioQuality _assessActivityQuality(ActivityRatios ratios) {
    if (ratios.inventoryTurnover >= 12 && ratios.receivablesTurnover >= 12) {
      return RatioQuality.excellent;
    } else if (ratios.inventoryTurnover >= 8 && ratios.receivablesTurnover >= 8) {
      return RatioQuality.good;
    } else if (ratios.inventoryTurnover >= 4 && ratios.receivablesTurnover >= 4) {
      return RatioQuality.average;
    } else if (ratios.inventoryTurnover >= 2) {
      return RatioQuality.poor;
    } else {
      return RatioQuality.critical;
    }
  }

  RatioQuality _assessLeverageQuality(LeverageRatios ratios) {
    if (ratios.debtToEquity <= 0.3 && ratios.interestCoverage >= 10) {
      return RatioQuality.excellent;
    } else if (ratios.debtToEquity <= 0.6 && ratios.interestCoverage >= 5) {
      return RatioQuality.good;
    } else if (ratios.debtToEquity <= 1.0 && ratios.interestCoverage >= 2.5) {
      return RatioQuality.average;
    } else if (ratios.debtToEquity <= 2.0 && ratios.interestCoverage >= 1.5) {
      return RatioQuality.poor;
    } else {
      return RatioQuality.critical;
    }
  }

  RatioQuality _calculateOverallQuality(List<RatioQuality> qualities) {
    final scores = qualities.map((q) => q.index).toList();
    final averageScore = scores.reduce((a, b) => a + b) / scores.length;
    
    if (averageScore <= 0.5) return RatioQuality.excellent;
    if (averageScore <= 1.5) return RatioQuality.good;
    if (averageScore <= 2.5) return RatioQuality.average;
    if (averageScore <= 3.5) return RatioQuality.poor;
    return RatioQuality.critical;
  }

  String _generateOverallAnalysis(
    LiquidityRatios liquidity,
    ProfitabilityRatios profitability,
    ActivityRatios activity,
    LeverageRatios leverage,
    RatioInterpretation interpretation,
  ) {
    final buffer = StringBuffer();
    
    buffer.writeln('ANALYSE GLOBALE DES RATIOS FINANCIERS\n');
    buffer.writeln('Évaluation générale: ${interpretation.overallQuality.displayName}\n');
    
    buffer.writeln('LIQUIDITÉ (${interpretation.liquidityQuality.displayName}):');
    buffer.write(liquidity.getAnalysis());
    buffer.writeln();
    
    buffer.writeln('RENTABILITÉ (${interpretation.profitabilityQuality.displayName}):');
    buffer.write(profitability.getAnalysis());
    buffer.writeln();
    
    buffer.writeln('ACTIVITÉ (${interpretation.activityQuality.displayName}):');
    buffer.write(activity.getAnalysis());
    buffer.writeln();
    
    buffer.writeln('ENDETTEMENT (${interpretation.leverageQuality.displayName}):');
    buffer.write(leverage.getAnalysis());
    
    return buffer.toString();
  }

  List<String> _generateRecommendations(
    LiquidityRatios liquidity,
    ProfitabilityRatios profitability,
    ActivityRatios activity,
    LeverageRatios leverage,
    RatioInterpretation interpretation,
  ) {
    final recommendations = <String>[];
    
    // Liquidity recommendations
    if (interpretation.liquidityQuality == RatioQuality.poor || 
        interpretation.liquidityQuality == RatioQuality.critical) {
      recommendations.add('Améliorer la gestion de trésorerie et réduire les dettes à court terme');
      recommendations.add('Négocier des délais de paiement plus longs avec les fournisseurs');
    }
    
    // Profitability recommendations
    if (interpretation.profitabilityQuality == RatioQuality.poor || 
        interpretation.profitabilityQuality == RatioQuality.critical) {
      recommendations.add('Analyser et optimiser la structure des coûts');
      recommendations.add('Revoir la stratégie de prix et améliorer l\'efficacité opérationnelle');
    }
    
    // Activity recommendations
    if (activity.inventoryDays > 90) {
      recommendations.add('Optimiser la gestion des stocks pour réduire le cycle de rotation');
    }
    
    if (activity.receivablesDays > 60) {
      recommendations.add('Améliorer le recouvrement des créances clients');
    }
    
    // Leverage recommendations
    if (leverage.debtToEquity > 1.0) {
      recommendations.add('Réduire l\'endettement ou augmenter les fonds propres');
    }
    
    if (leverage.interestCoverage < 3.0) {
      recommendations.add('Améliorer la capacité de couverture des charges financières');
    }
    
    // General recommendations
    if (interpretation.overallQuality == RatioQuality.excellent) {
      recommendations.add('Maintenir les bonnes performances et surveiller les tendances');
    } else if (interpretation.overallQuality == RatioQuality.good) {
      recommendations.add('Identifier les domaines d\'amélioration pour atteindre l\'excellence');
    }
    
    return recommendations;
  }
}
