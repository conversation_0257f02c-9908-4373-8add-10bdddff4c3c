import 'dart:developer' as developer;
import '../models/is/is_data.dart';

class IsService {
  static final IsService _instance = IsService._internal();
  factory IsService() => _instance;
  IsService._internal();

  // Cache for loaded data
  final Map<String, IsData> _cache = {};

  Future<IsData> loadIsData(String section) async {
    try {
      developer.log('Loading IS data for section: $section');

      // Remove .json extension if provided
      section = section.replaceAll('.json', '');

      if (_cache.containsKey(section)) {
        developer.log('Returning cached data for section: $section');
        return _cache[section]!;
      }

      final assetPath = 'assets/is/$section.json';
      developer.log('Loading from asset path: $assetPath');

      final data = await IsData.loadFromAsset(assetPath);
      developer.log('Successfully loaded data for section: $section');

      _cache[section] = data;
      return data;
    } catch (e, stackTrace) {
      developer.log(
        'Error loading IS data for section: $section',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // Basic sections
  Future<IsData> getPresentation() => loadIsData('presentation');
  Future<IsData> getBaseImposable() => loadIsData('base_imposable');
  Future<IsData> getObligations() => loadIsData('obligations');
  Future<IsData> getSanctions() => loadIsData('sanctions');
  Future<IsData> getExercices() => loadIsData('exercices');
  Future<IsData> getExercicesAvances() => loadIsData('exercices_avances');

  // Special sections with year-specific data
  Future<IsData> getReintegrations2025() =>
      loadIsData('is_rein_deduction_2025');
  Future<IsData> getTaux2025() => loadIsData('is_taux_2025');

  // Clear cache if needed
  void clearCache() {
    developer.log('Clearing IS data cache');
    _cache.clear();
  }
}
