import 'dart:async';
import 'package:hive/hive.dart';
import '../models/guide/note_collaboration_data.dart';

/// Service for managing note collaboration features
class NoteCollaborationService {
  static const String _commentsBoxName = 'note_comments';
  static const String _activitiesBoxName = 'collaboration_activities';
  static const String _sessionsBoxName = 'editing_sessions';
  
  static Box<NoteComment>? _commentsBox;
  static Box<CollaborationActivity>? _activitiesBox;
  static Box<EditingSession>? _sessionsBox;
  
  final StreamController<List<NoteComment>> _commentsController = 
      StreamController<List<NoteComment>>.broadcast();
  final StreamController<List<CollaborationActivity>> _activitiesController = 
      StreamController<List<CollaborationActivity>>.broadcast();
  final StreamController<List<EditingSession>> _sessionsController = 
      StreamController<List<EditingSession>>.broadcast();
  
  /// Stream of all comments
  Stream<List<NoteComment>> get commentsStream => _commentsController.stream;
  
  /// Stream of all activities
  Stream<List<CollaborationActivity>> get activitiesStream => _activitiesController.stream;
  
  /// Stream of all editing sessions
  Stream<List<EditingSession>> get sessionsStream => _sessionsController.stream;
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_commentsBox == null) {
      _commentsBox = await Hive.openBox<NoteComment>(_commentsBoxName);
      _notifyCommentsListeners();
    }
    if (_activitiesBox == null) {
      _activitiesBox = await Hive.openBox<CollaborationActivity>(_activitiesBoxName);
      _notifyActivitiesListeners();
    }
    if (_sessionsBox == null) {
      _sessionsBox = await Hive.openBox<EditingSession>(_sessionsBoxName);
      _notifySessionsListeners();
    }
  }
  
  /// Comment Management
  
  /// Add a comment to a note
  Future<NoteComment> addComment({
    required String noteId,
    required String authorId,
    required String authorName,
    required String content,
    CommentType commentType = CommentType.general,
    String? parentCommentId,
    int? lineNumber,
    int? characterStart,
    int? characterEnd,
    List<String> mentions = const [],
  }) async {
    await _ensureInitialized();
    
    final commentId = '${noteId}_${DateTime.now().millisecondsSinceEpoch}';
    final comment = NoteComment(
      id: commentId,
      noteId: noteId,
      authorId: authorId,
      authorName: authorName,
      content: content,
      commentType: commentType,
      parentCommentId: parentCommentId,
      lineNumber: lineNumber,
      characterStart: characterStart,
      characterEnd: characterEnd,
      mentions: mentions,
    );
    
    await _commentsBox!.put(commentId, comment);
    _notifyCommentsListeners();
    
    // Log activity
    await _logActivity(
      noteId: noteId,
      userId: authorId,
      userName: authorName,
      activityType: ActivityType.commentAdded,
      description: 'Commentaire ajouté: ${content.length > 50 ? '${content.substring(0, 50)}...' : content}',
    );
    
    return comment;
  }
  
  /// Get comments for a specific note
  Future<List<NoteComment>> getCommentsForNote(String noteId) async {
    await _ensureInitialized();
    final allComments = _commentsBox!.values.toList();
    return allComments.where((comment) => comment.noteId == noteId).toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }
  
  /// Get threaded comments (replies to a comment)
  Future<List<NoteComment>> getThreadedComments(String parentCommentId) async {
    await _ensureInitialized();
    final allComments = _commentsBox!.values.toList();
    return allComments.where((comment) => comment.parentCommentId == parentCommentId).toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }
  
  /// Update a comment
  Future<void> updateComment(String commentId, String newContent) async {
    await _ensureInitialized();
    final comment = _commentsBox!.get(commentId);
    if (comment != null) {
      comment.updateContent(newContent);
      _notifyCommentsListeners();
    }
  }
  
  /// Resolve a comment
  Future<void> resolveComment(String commentId, String userId, String userName) async {
    await _ensureInitialized();
    final comment = _commentsBox!.get(commentId);
    if (comment != null) {
      comment.resolve();
      _notifyCommentsListeners();
      
      // Log activity
      await _logActivity(
        noteId: comment.noteId,
        userId: userId,
        userName: userName,
        activityType: ActivityType.commentResolved,
        description: 'Commentaire résolu',
      );
    }
  }
  
  /// Delete a comment
  Future<void> deleteComment(String commentId) async {
    await _ensureInitialized();
    await _commentsBox!.delete(commentId);
    _notifyCommentsListeners();
  }
  
  /// Get unresolved comments for a note
  Future<List<NoteComment>> getUnresolvedComments(String noteId) async {
    final comments = await getCommentsForNote(noteId);
    return comments.where((comment) => !comment.isResolved).toList();
  }
  
  /// Activity Management
  
  /// Log a collaboration activity
  Future<void> _logActivity({
    required String noteId,
    required String userId,
    required String userName,
    required ActivityType activityType,
    required String description,
    Map<String, dynamic> metadata = const {},
  }) async {
    await _ensureInitialized();
    
    final activityId = '${noteId}_${DateTime.now().millisecondsSinceEpoch}';
    final activity = CollaborationActivity(
      id: activityId,
      noteId: noteId,
      userId: userId,
      userName: userName,
      activityType: activityType,
      description: description,
      metadata: metadata,
    );
    
    await _activitiesBox!.put(activityId, activity);
    _notifyActivitiesListeners();
  }
  
  /// Get activities for a specific note
  Future<List<CollaborationActivity>> getActivitiesForNote(String noteId) async {
    await _ensureInitialized();
    final allActivities = _activitiesBox!.values.toList();
    return allActivities.where((activity) => activity.noteId == noteId).toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }
  
  /// Get recent activities across all notes
  Future<List<CollaborationActivity>> getRecentActivities({int limit = 50}) async {
    await _ensureInitialized();
    final allActivities = _activitiesBox!.values.toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return allActivities.take(limit).toList();
  }
  
  /// Editing Session Management
  
  /// Start an editing session
  Future<EditingSession> startEditingSession({
    required String noteId,
    required String userId,
    required String userName,
  }) async {
    await _ensureInitialized();
    
    final sessionId = '${noteId}_${userId}_${DateTime.now().millisecondsSinceEpoch}';
    final session = EditingSession(
      id: sessionId,
      noteId: noteId,
      userId: userId,
      userName: userName,
    );
    
    await _sessionsBox!.put(sessionId, session);
    _notifySessionsListeners();
    
    // Log activity
    await _logActivity(
      noteId: noteId,
      userId: userId,
      userName: userName,
      activityType: ActivityType.userJoined,
      description: 'Session d\'édition démarrée',
    );
    
    return session;
  }
  
  /// End an editing session
  Future<void> endEditingSession(String sessionId, String userId, String userName) async {
    await _ensureInitialized();
    final session = _sessionsBox!.get(sessionId);
    if (session != null && session.isActive) {
      session.endSession();
      _notifySessionsListeners();
      
      // Log activity
      await _logActivity(
        noteId: session.noteId,
        userId: userId,
        userName: userName,
        activityType: ActivityType.userLeft,
        description: 'Session d\'édition terminée',
      );
    }
  }
  
  /// Get active editing sessions for a note
  Future<List<EditingSession>> getActiveSessionsForNote(String noteId) async {
    await _ensureInitialized();
    final allSessions = _sessionsBox!.values.toList();
    return allSessions.where((session) => 
        session.noteId == noteId && session.isActive).toList();
  }
  
  /// Check if a note is being edited by others
  Future<bool> isNoteBeingEdited(String noteId, String currentUserId) async {
    final activeSessions = await getActiveSessionsForNote(noteId);
    return activeSessions.any((session) => session.userId != currentUserId);
  }
  
  /// Get all active users editing a note
  Future<List<String>> getActiveEditorsForNote(String noteId) async {
    final activeSessions = await getActiveSessionsForNote(noteId);
    return activeSessions.map((session) => session.userName).toList();
  }
  
  /// Collaboration Statistics
  
  /// Get collaboration statistics for a note
  Future<CollaborationStatistics> getCollaborationStatistics(String noteId) async {
    final comments = await getCommentsForNote(noteId);
    final activities = await getActivitiesForNote(noteId);
    final sessions = await getActiveSessionsForNote(noteId);
    
    final uniqueCollaborators = <String>{};
    for (final comment in comments) {
      uniqueCollaborators.add(comment.authorId);
    }
    for (final activity in activities) {
      uniqueCollaborators.add(activity.userId);
    }
    
    final unresolvedComments = comments.where((c) => !c.isResolved).length;
    final resolvedComments = comments.where((c) => c.isResolved).length;
    
    return CollaborationStatistics(
      totalComments: comments.length,
      unresolvedComments: unresolvedComments,
      resolvedComments: resolvedComments,
      totalActivities: activities.length,
      uniqueCollaborators: uniqueCollaborators.length,
      activeEditors: sessions.length,
      lastActivity: activities.isNotEmpty ? activities.first.timestamp : null,
    );
  }
  
  /// Export collaboration data
  Future<Map<String, dynamic>> exportCollaborationData(String noteId) async {
    final comments = await getCommentsForNote(noteId);
    final activities = await getActivitiesForNote(noteId);
    final statistics = await getCollaborationStatistics(noteId);
    
    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'noteId': noteId,
      'statistics': {
        'totalComments': statistics.totalComments,
        'unresolvedComments': statistics.unresolvedComments,
        'resolvedComments': statistics.resolvedComments,
        'totalActivities': statistics.totalActivities,
        'uniqueCollaborators': statistics.uniqueCollaborators,
        'activeEditors': statistics.activeEditors,
        'lastActivity': statistics.lastActivity?.toIso8601String(),
      },
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'activities': activities.map((activity) => activity.toJson()).toList(),
    };
  }
  
  /// Private helper methods
  Future<void> _ensureInitialized() async {
    if (_commentsBox == null || _activitiesBox == null || _sessionsBox == null) {
      await initialize();
    }
  }
  
  void _notifyCommentsListeners() {
    if (_commentsBox != null) {
      _commentsController.add(_commentsBox!.values.toList());
    }
  }
  
  void _notifyActivitiesListeners() {
    if (_activitiesBox != null) {
      _activitiesController.add(_activitiesBox!.values.toList());
    }
  }
  
  void _notifySessionsListeners() {
    if (_sessionsBox != null) {
      _sessionsController.add(_sessionsBox!.values.toList());
    }
  }
  
  /// Dispose of resources
  void dispose() {
    _commentsController.close();
    _activitiesController.close();
    _sessionsController.close();
  }
}

/// Statistics about collaboration on a note
class CollaborationStatistics {
  final int totalComments;
  final int unresolvedComments;
  final int resolvedComments;
  final int totalActivities;
  final int uniqueCollaborators;
  final int activeEditors;
  final DateTime? lastActivity;
  
  const CollaborationStatistics({
    required this.totalComments,
    required this.unresolvedComments,
    required this.resolvedComments,
    required this.totalActivities,
    required this.uniqueCollaborators,
    required this.activeEditors,
    this.lastActivity,
  });
  
  /// Get collaboration score (0-100)
  int get collaborationScore {
    if (totalComments == 0 && totalActivities == 0) return 0;
    
    int score = 0;
    score += (totalComments * 10).clamp(0, 30);
    score += (uniqueCollaborators * 15).clamp(0, 30);
    score += (resolvedComments * 5).clamp(0, 20);
    score += (activeEditors * 10).clamp(0, 20);
    
    return score.clamp(0, 100);
  }
  
  /// Check if the note is actively collaborated on
  bool get isActivelyCollaborated {
    if (lastActivity == null) return false;
    final daysSinceLastActivity = DateTime.now().difference(lastActivity!).inDays;
    return daysSinceLastActivity <= 7 && (totalComments > 0 || activeEditors > 0);
  }
}
