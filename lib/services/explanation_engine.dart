abstract class ExplanationEngine {
  Future<String> generateExplanation({
    required String topic,
    required String context,
    String? userLevel,
  });

  Future<String> generateHint({
    required String question,
    required String context,
  });

  Future<List<String>> generateRelatedTopics(String topic);
}

class StaticExplanationEngine implements ExplanationEngine {
  static final StaticExplanationEngine _instance = StaticExplanationEngine._internal();
  factory StaticExplanationEngine() => _instance;
  StaticExplanationEngine._internal();

  // Static explanations database
  final Map<String, Map<String, String>> _explanations = {
    'tva_agricole': {
      'general': '''
La TVA agricole au Maroc bénéficie d'un régime spécial avec plusieurs avantages :

• **Produits non transformés** : Exonération totale (0%) avec droit à déduction
• **Services agricoles** : Taux réduit de 10%
• **Coopératives** : Régime préférentiel pour les opérations avec adhérents
• **Exportations** : Exonération avec remboursement de TVA

Ce régime encourage le développement du secteur agricole.
      ''',
      'beginner': '''
**TVA Agricole - Les Bases**

La TVA (Taxe sur la Valeur Ajoutée) pour l'agriculture est plus avantageuse :

1. **Produits de base** (blé, lait, fruits) = 0% de TVA
2. **Services** (labour, récolte) = 10% de TVA
3. **Coopératives** = Avantages spéciaux

Pourquoi ? Pour soutenir l'agriculture marocaine !
      ''',
    },
    'is_cooperatives': {
      'general': '''
Les coopératives agricoles bénéficient d'un taux préférentiel d'IS de **17,5%** au lieu du taux normal.

**Conditions requises :**
• Statut de coopérative agricole agréée
• Activité principale agricole
• Membres exclusivement agriculteurs
• Respect des principes coopératifs

**Avantages supplémentaires :**
• Cotisation minimale réduite (0,25%)
• Exonération sur les opérations avec adhérents
• Report déficitaire illimité
      ''',
    },
    'ir_agricole': {
      'general': '''
L'IR agricole présente plusieurs spécificités :

**Méthodes d'évaluation :**
• Bénéfice réel (comptabilité complète)
• Régime forfaitaire (surfaces × rendements)
• Moyenne triennale (revenus irréguliers)

**Abattements spéciaux :**
• Agriculture biologique : 50% pendant 10 ans
• Zone de montagne : 25%
• Petites exploitations : 24 000 MAD

**Étalement possible sur 4 ans** pour les revenus irréguliers.
      ''',
    },
  };

  final Map<String, List<String>> _hints = {
    'tva_rate_agricole': [
      'Les produits agricoles non transformés sont généralement exonérés',
      'Pensez au taux de 0% pour les produits de base',
      'Les céréales, fruits et légumes frais bénéficient d\'une exonération',
    ],
    'cooperative_tax': [
      'Les coopératives ont un taux préférentiel',
      'Le taux est de 17,5% pour les coopératives agricoles',
      'C\'est plus avantageux que le taux normal de 22,75%',
    ],
  };

  final Map<String, List<String>> _relatedTopics = {
    'tva_agricole': [
      'IS coopératives agricoles',
      'IR revenus agricoles',
      'Comptabilité agricole',
      'Subventions agricoles',
    ],
    'is_cooperatives': [
      'TVA coopératives',
      'Comptabilité coopératives',
      'Statut coopératif',
      'Cotisation minimale',
    ],
    'ir_agricole': [
      'TVA agricole',
      'Revenus fonciers ruraux',
      'Amortissements agricoles',
      'Provisions agricoles',
    ],
  };

  @override
  Future<String> generateExplanation({
    required String topic,
    required String context,
    String? userLevel,
  }) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));

    final topicKey = topic.toLowerCase().replaceAll(' ', '_');
    final levelKey = userLevel?.toLowerCase() ?? 'general';
    
    final explanation = _explanations[topicKey]?[levelKey] ?? 
                       _explanations[topicKey]?['general'] ??
                       _getDefaultExplanation(topic);

    return explanation;
  }

  @override
  Future<String> generateHint({
    required String question,
    required String context,
  }) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 300));

    // Extract topic from question context
    final topicKey = _extractTopicFromContext(context);
    final hints = _hints[topicKey] ?? _getDefaultHints();
    
    // Return a random hint
    if (hints.isNotEmpty) {
      final randomIndex = DateTime.now().millisecondsSinceEpoch % hints.length;
      return hints[randomIndex];
    }

    return "Relisez attentivement la question et les options proposées.";
  }

  @override
  Future<List<String>> generateRelatedTopics(String topic) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 200));

    final topicKey = topic.toLowerCase().replaceAll(' ', '_');
    return _relatedTopics[topicKey] ?? ['Fiscalité générale', 'Comptabilité', 'Droit fiscal'];
  }

  String _getDefaultExplanation(String topic) {
    return '''
**$topic**

Cette notion fait partie des concepts importants de la fiscalité marocaine. 
Pour une explication détaillée, consultez les ressources de formation ou 
contactez un expert fiscal.

Les règles fiscales évoluent régulièrement, il est important de se tenir 
informé des dernières dispositions légales.
    ''';
  }

  List<String> _getDefaultHints() {
    return [
      'Lisez attentivement chaque option',
      'Éliminez les réponses évidemment incorrectes',
      'Pensez aux règles générales de la fiscalité marocaine',
      'Considérez les spécificités du secteur concerné',
    ];
  }

  String _extractTopicFromContext(String context) {
    final lowerContext = context.toLowerCase();
    
    if (lowerContext.contains('tva') && lowerContext.contains('agricole')) {
      return 'tva_rate_agricole';
    }
    if (lowerContext.contains('coopérative') || lowerContext.contains('cooperative')) {
      return 'cooperative_tax';
    }
    if (lowerContext.contains('ir') && lowerContext.contains('agricole')) {
      return 'ir_agricole';
    }
    
    return 'general';
  }
}

// Future AI-powered implementation interface
abstract class AIExplanationEngine implements ExplanationEngine {
  // This would integrate with local AI models or external APIs
  // when available and configured
  
  @override
  Future<String> generateExplanation({
    required String topic,
    required String context,
    String? userLevel,
  });

  @override
  Future<String> generateHint({
    required String question,
    required String context,
  });

  @override
  Future<List<String>> generateRelatedTopics(String topic);
}

// Factory for explanation engines
class ExplanationEngineFactory {
  static ExplanationEngine createEngine({bool useAI = false}) {
    if (useAI) {
      // For future AI implementation
      throw UnimplementedError('AI explanation engine not yet implemented');
    }
    return StaticExplanationEngine();
  }
}