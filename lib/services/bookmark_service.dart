import 'dart:async';
import 'package:hive/hive.dart';
import '../models/guide/bookmark_data.dart';

/// Service for managing bookmarks with local storage
class BookmarkService {
  static const String _boxName = 'bookmarks';
  static Box<BookmarkData>? _box;
  
  final StreamController<List<BookmarkData>> _bookmarksController = 
      StreamController<List<BookmarkData>>.broadcast();
  
  /// Stream of all bookmarks
  Stream<List<BookmarkData>> get bookmarksStream => _bookmarksController.stream;
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_box == null) {
      _box = await Hive.openBox<BookmarkData>(_boxName);
      _notifyListeners();
    }
  }
  
  /// Get all bookmarks
  Future<List<BookmarkData>> getAllBookmarks() async {
    await _ensureInitialized();
    return _box!.values.toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get bookmarks for a specific guide
  Future<List<BookmarkData>> getBookmarksByGuide(String guideId) async {
    await _ensureInitialized();
    return _box!.values
        .where((bookmark) => bookmark.guideId == guideId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get bookmarks for a specific section
  Future<List<BookmarkData>> getBookmarksBySection(String guideId, String sectionId) async {
    await _ensureInitialized();
    return _box!.values
        .where((bookmark) => 
            bookmark.guideId == guideId && 
            bookmark.sectionId == sectionId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get bookmarks by type
  Future<List<BookmarkData>> getBookmarksByType(BookmarkType type) async {
    await _ensureInitialized();
    return _box!.values
        .where((bookmark) => bookmark.bookmarkType == type)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get bookmarks by tags
  Future<List<BookmarkData>> getBookmarksByTags(List<String> tags) async {
    await _ensureInitialized();
    return _box!.values
        .where((bookmark) => 
            bookmark.tags.any((tag) => tags.contains(tag)))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Check if a section is bookmarked
  Future<bool> isBookmarked(String guideId, String sectionId) async {
    await _ensureInitialized();
    return _box!.values.any((bookmark) => 
        bookmark.guideId == guideId && 
        bookmark.sectionId == sectionId);
  }
  
  /// Get a specific bookmark by ID
  Future<BookmarkData?> getBookmark(String id) async {
    await _ensureInitialized();
    return _box!.get(id);
  }
  
  /// Add a new bookmark
  Future<void> addBookmark(BookmarkData bookmark) async {
    await _ensureInitialized();
    await _box!.put(bookmark.id, bookmark);
    _notifyListeners();
  }
  
  /// Update an existing bookmark
  Future<void> updateBookmark(BookmarkData bookmark) async {
    await _ensureInitialized();
    if (_box!.containsKey(bookmark.id)) {
      await _box!.put(bookmark.id, bookmark);
      _notifyListeners();
    }
  }
  
  /// Delete a bookmark by ID
  Future<void> deleteBookmark(String id) async {
    await _ensureInitialized();
    await _box!.delete(id);
    _notifyListeners();
  }
  
  /// Delete bookmarks for a specific section
  Future<void> deleteBookmarksBySection(String guideId, String sectionId) async {
    await _ensureInitialized();
    final bookmarksToDelete = _box!.values
        .where((bookmark) => 
            bookmark.guideId == guideId && 
            bookmark.sectionId == sectionId)
        .map((bookmark) => bookmark.id)
        .toList();
    
    for (final id in bookmarksToDelete) {
      await _box!.delete(id);
    }
    
    if (bookmarksToDelete.isNotEmpty) {
      _notifyListeners();
    }
  }
  
  /// Clear all bookmarks
  Future<void> clearAllBookmarks() async {
    await _ensureInitialized();
    await _box!.clear();
    _notifyListeners();
  }
  
  /// Search bookmarks
  Future<List<BookmarkData>> searchBookmarks(String query) async {
    await _ensureInitialized();
    final lowerQuery = query.toLowerCase();
    
    return _box!.values
        .where((bookmark) =>
            bookmark.title.toLowerCase().contains(lowerQuery) ||
            (bookmark.description.toLowerCase().contains(lowerQuery) ?? false) ||
            bookmark.tags.any((tag) => tag.toLowerCase().contains(lowerQuery)))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get bookmark statistics
  Future<BookmarkStatistics> getStatistics() async {
    await _ensureInitialized();
    final bookmarks = _box!.values.toList();
    
    final Map<String, int> guideCount = {};
    final Map<BookmarkType, int> typeCount = {};
    final Set<String> uniqueTags = {};
    
    for (final bookmark in bookmarks) {
      // Count by guide
      guideCount[bookmark.guideId] = (guideCount[bookmark.guideId] ?? 0) + 1;
      
      // Count by type
      typeCount[bookmark.bookmarkType] = (typeCount[bookmark.bookmarkType] ?? 0) + 1;
      
      // Collect unique tags
      uniqueTags.addAll(bookmark.tags);
    }
    
    return BookmarkStatistics(
      totalBookmarks: bookmarks.length,
      bookmarksByGuide: guideCount,
      bookmarksByType: typeCount,
      uniqueTags: uniqueTags.toList()..sort(),
      oldestBookmark: bookmarks.isEmpty 
          ? null 
          : bookmarks.reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b).createdAt,
      newestBookmark: bookmarks.isEmpty 
          ? null 
          : bookmarks.reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b).createdAt,
    );
  }
  
  /// Export bookmarks to JSON
  Future<Map<String, dynamic>> exportBookmarks() async {
    await _ensureInitialized();
    final bookmarks = await getAllBookmarks();
    
    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'bookmarks': bookmarks.map((bookmark) => bookmark.toJson()).toList(),
    };
  }
  
  /// Import bookmarks from JSON
  Future<ImportResult> importBookmarks(Map<String, dynamic> data, {bool merge = true}) async {
    await _ensureInitialized();
    
    try {
      final bookmarksList = data['bookmarks'] as List<dynamic>?;
      if (bookmarksList == null) {
        return ImportResult(success: false, message: 'Invalid data format');
      }
      
      final bookmarks = bookmarksList
          .map((json) => BookmarkData.fromJson(json as Map<String, dynamic>))
          .toList();
      
      int imported = 0;
      int skipped = 0;
      
      if (!merge) {
        await clearAllBookmarks();
      }
      
      for (final bookmark in bookmarks) {
        if (merge && _box!.containsKey(bookmark.id)) {
          skipped++;
        } else {
          await _box!.put(bookmark.id, bookmark);
          imported++;
        }
      }
      
      _notifyListeners();
      
      return ImportResult(
        success: true,
        message: 'Import completed: $imported imported, $skipped skipped',
        importedCount: imported,
        skippedCount: skipped,
      );
    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Import failed: $e',
      );
    }
  }
  
  /// Get recent bookmarks
  Future<List<BookmarkData>> getRecentBookmarks({int limit = 10}) async {
    await _ensureInitialized();
    final bookmarks = await getAllBookmarks();
    return bookmarks.take(limit).toList();
  }
  
  /// Get bookmarks created in the last N days
  Future<List<BookmarkData>> getRecentBookmarksByDays(int days) async {
    await _ensureInitialized();
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    
    return _box!.values
        .where((bookmark) => bookmark.createdAt.isAfter(cutoffDate))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Private helper methods
  Future<void> _ensureInitialized() async {
    if (_box == null) {
      await initialize();
    }
  }
  
  void _notifyListeners() {
    if (_box != null) {
      _bookmarksController.add(_box!.values.toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt)));
    }
  }
  
  /// Dispose of resources
  void dispose() {
    _bookmarksController.close();
  }
}

/// Statistics about bookmarks
class BookmarkStatistics {
  final int totalBookmarks;
  final Map<String, int> bookmarksByGuide;
  final Map<BookmarkType, int> bookmarksByType;
  final List<String> uniqueTags;
  final DateTime? oldestBookmark;
  final DateTime? newestBookmark;
  
  const BookmarkStatistics({
    required this.totalBookmarks,
    required this.bookmarksByGuide,
    required this.bookmarksByType,
    required this.uniqueTags,
    this.oldestBookmark,
    this.newestBookmark,
  });
}

/// Result of import operation
class ImportResult {
  final bool success;
  final String message;
  final int importedCount;
  final int skippedCount;
  
  const ImportResult({
    required this.success,
    required this.message,
    this.importedCount = 0,
    this.skippedCount = 0,
  });
}