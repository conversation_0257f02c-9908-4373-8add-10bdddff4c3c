import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';
import '../models/liasse_fiscale/guide_liasse_data.dart';
import '../models/liasse_fiscale/checklist_documents_data.dart';
import '../models/liasse_fiscale/obligations_fiscales_data.dart';

class LiasseFiscaleService {
  static final LiasseFiscaleService _instance = LiasseFiscaleService._internal();
  factory LiasseFiscaleService() => _instance;
  LiasseFiscaleService._internal();

  // Cache for loaded data
  GuideLiasseData? _cachedGuideData;
  ChecklistDocumentsData? _cachedChecklistData;
  ObligationsFiscalesData? _cachedObligationsData;

  Future<GuideLiasseData> getGuideData() async {
    try {
      developer.log('Loading Liasse Fiscale guide data');

      if (_cachedGuideData != null) {
        developer.log('Returning cached guide data');
        return _cachedGuideData!;
      }

      final String jsonString = await rootBundle.loadString('assets/liasse_fiscale/guide_liasse_2025.json');
      final data = json.decode(jsonString);
      _cachedGuideData = GuideLiasseData.fromJson(data);
      developer.log('Successfully loaded guide data');
      return _cachedGuideData!;
    } catch (e, stackTrace) {
      developer.log(
        'Error loading Liasse Fiscale guide data',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<ChecklistDocumentsData> getChecklistData() async {
    try {
      developer.log('Loading Liasse Fiscale checklist data');

      if (_cachedChecklistData != null) {
        developer.log('Returning cached checklist data');
        return _cachedChecklistData!;
      }

      final String jsonString = await rootBundle.loadString('assets/liasse_fiscale/checklist_documents_2025.json');
      final data = json.decode(jsonString);
      _cachedChecklistData = ChecklistDocumentsData.fromJson(data);
      developer.log('Successfully loaded checklist data');
      return _cachedChecklistData!;
    } catch (e, stackTrace) {
      developer.log(
        'Error loading Liasse Fiscale checklist data',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<ObligationsFiscalesData> getObligationsData() async {
    try {
      developer.log('Loading Liasse Fiscale obligations data');

      if (_cachedObligationsData != null) {
        developer.log('Returning cached obligations data');
        return _cachedObligationsData!;
      }

      final String jsonString = await rootBundle.loadString('assets/liasse_fiscale/obligations_fiscales_2025.json');
      final data = json.decode(jsonString);
      _cachedObligationsData = ObligationsFiscalesData.fromJson(data);
      developer.log('Successfully loaded obligations data');
      return _cachedObligationsData!;
    } catch (e, stackTrace) {
      developer.log(
        'Error loading Liasse Fiscale obligations data',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // Clear cache if needed
  void clearCache() {
    developer.log('Clearing Liasse Fiscale data cache');
    _cachedGuideData = null;
    _cachedChecklistData = null;
    _cachedObligationsData = null;
  }
}