import 'dart:async';
import 'package:hive/hive.dart';
import '../models/adaptive_learning/spaced_repetition_item.dart';
import '../models/quiz_model.dart';

// Service for managing spaced repetition using Hive storage
class SpacedRepetitionService {
  static const String _boxName = 'spaced_repetition';
  Box<SpacedRepetitionItem>? _box;

  // Initialize the service
  Future<void> initialize() async {
    _box = await Hive.openBox<SpacedRepetitionItem>(_boxName);
  }

  // Get the box, ensuring it's initialized
  Box<SpacedRepetitionItem> get _getBox {
    if (_box == null) {
      throw Exception('SpacedRepetitionService not initialized. Call initialize() first.');
    }
    return _box!;
  }

  // Add an incorrect answer to spaced repetition system
  Future<void> addIncorrectAnswer(QuizQuestion question, String categoryName) async {
    final questionId = _generateQuestionId(question, categoryName);
    
    // Check if item already exists
    final existingItem = _getBox.get(questionId);
    
    if (existingItem != null) {
      // Update existing item with new attempt
      existingItem.updateAfterAnswer(false);
      await existingItem.save();
    } else {
      // Create new spaced repetition item
      final newItem = SpacedRepetitionItem.fromIncorrectAnswer(
        questionId: questionId,
        categoryName: categoryName,
        topic: question.topic,
      );
      
      await _getBox.put(questionId, newItem);
    }
  }

  // Update item after review using SM-2 algorithm
  Future<void> updateAfterReview(String questionId, bool isCorrect) async {
    final item = _getBox.get(questionId);
    
    if (item != null) {
      item.updateAfterAnswer(isCorrect);
      
      // Remove item if it should graduate (mastered)
      if (item.shouldGraduate()) {
        await _getBox.delete(questionId);
      } else {
        await item.save();
      }
    }
  }

  // Get items that are due for review
  List<SpacedRepetitionItem> getDueItems() {
    return _getBox.values
        .where((item) => item.isDue())
        .toList()
        ..sort((a, b) => a.nextReviewDate.compareTo(b.nextReviewDate));
  }

  // Get items due today
  List<SpacedRepetitionItem> getDueItemsToday() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(Duration(days: 1));
    
    return _getBox.values
        .where((item) => 
            item.nextReviewDate.isAfter(startOfDay.subtract(Duration(days: 1))) &&
            item.nextReviewDate.isBefore(endOfDay))
        .toList()
        ..sort((a, b) => a.nextReviewDate.compareTo(b.nextReviewDate));
  }

  // Get overdue items
  List<SpacedRepetitionItem> getOverdueItems() {
    final now = DateTime.now();
    
    return _getBox.values
        .where((item) => item.nextReviewDate.isBefore(now))
        .toList()
        ..sort((a, b) => a.nextReviewDate.compareTo(b.nextReviewDate));
  }

  // Get upcoming reviews for next N days
  List<SpacedRepetitionItem> getUpcomingReviews(int days) {
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: days));
    
    return _getBox.values
        .where((item) => 
            item.nextReviewDate.isAfter(now) &&
            item.nextReviewDate.isBefore(futureDate))
        .toList()
        ..sort((a, b) => a.nextReviewDate.compareTo(b.nextReviewDate));
  }

  // Get review statistics
  ReviewStatistics getReviewStatistics() {
    final allItems = _getBox.values.toList();
    
    if (allItems.isEmpty) {
      return ReviewStatistics(
        totalItems: 0,
        dueToday: 0,
        overdue: 0,
        averageInterval: 0.0,
        averageEasinessFactor: 2.5,
        masteryLevels: {},
        topicBreakdown: {},
      );
    }

    final dueToday = getDueItemsToday().length;
    final overdue = getOverdueItems().length;
    final averageInterval = allItems.map((item) => item.interval).reduce((a, b) => a + b) / allItems.length;
    final averageEasinessFactor = allItems.map((item) => item.easinessFactor).reduce((a, b) => a + b) / allItems.length;

    // Calculate mastery levels
    Map<String, int> masteryLevels = {
      'Beginner': 0,
      'Learning': 0,
      'Proficient': 0,
      'Mastered': 0,
    };

    // Calculate topic breakdown
    Map<String, int> topicBreakdown = {};

    for (final item in allItems) {
      // Mastery level calculation
      final masteryLevel = item.getMasteryLevel();
      if (masteryLevel < 0.25) {
        masteryLevels['Beginner'] = masteryLevels['Beginner']! + 1;
      } else if (masteryLevel < 0.5) {
        masteryLevels['Learning'] = masteryLevels['Learning']! + 1;
      } else if (masteryLevel < 0.8) {
        masteryLevels['Proficient'] = masteryLevels['Proficient']! + 1;
      } else {
        masteryLevels['Mastered'] = masteryLevels['Mastered']! + 1;
      }

      // Topic breakdown
      topicBreakdown[item.topic] = (topicBreakdown[item.topic] ?? 0) + 1;
    }

    return ReviewStatistics(
      totalItems: allItems.length,
      dueToday: dueToday,
      overdue: overdue,
      averageInterval: averageInterval,
      averageEasinessFactor: averageEasinessFactor,
      masteryLevels: masteryLevels,
      topicBreakdown: topicBreakdown,
    );
  }

  // Schedule next review for a specific item
  Future<void> scheduleNextReview(SpacedRepetitionItem item) async {
    final nextInterval = item.calculateNextInterval();
    final updatedItem = item.copyWith(
      interval: nextInterval,
      nextReviewDate: DateTime.now().add(Duration(days: nextInterval)),
    );
    
    await _getBox.put(item.questionId, updatedItem);
  }

  // Get items by topic
  List<SpacedRepetitionItem> getItemsByTopic(String topic) {
    return _getBox.values
        .where((item) => item.topic == topic)
        .toList()
        ..sort((a, b) => a.nextReviewDate.compareTo(b.nextReviewDate));
  }

  // Get items by category
  List<SpacedRepetitionItem> getItemsByCategory(String category) {
    return _getBox.values
        .where((item) => item.categoryName == category)
        .toList()
        ..sort((a, b) => a.nextReviewDate.compareTo(b.nextReviewDate));
  }

  // Batch update multiple items
  Future<void> batchUpdateItems(List<String> questionIds, List<bool> results) async {
    if (questionIds.length != results.length) {
      throw ArgumentError('Question IDs and results lists must have the same length');
    }

    for (int i = 0; i < questionIds.length; i++) {
      await updateAfterReview(questionIds[i], results[i]);
    }
  }

  // Reset progress for a specific item
  Future<void> resetItemProgress(String questionId) async {
    final item = _getBox.get(questionId);
    if (item != null) {
      item.resetProgress();
      await item.save();
    }
  }

  // Delete a specific item
  Future<void> deleteItem(String questionId) async {
    await _getBox.delete(questionId);
  }

  // Clear all spaced repetition data
  Future<void> clearAllData() async {
    await _getBox.clear();
  }

  // Get review session recommendations
  ReviewSessionRecommendation getReviewSessionRecommendation() {
    final dueItems = getDueItems();
    final overdueItems = getOverdueItems();
    
    if (overdueItems.isNotEmpty) {
      return ReviewSessionRecommendation(
        type: SessionType.overdue,
        itemCount: overdueItems.length,
        estimatedDuration: overdueItems.length * 30, // 30 seconds per item
        priority: Priority.high,
        description: 'Vous avez ${overdueItems.length} révision(s) en retard',
      );
    }
    
    if (dueItems.isNotEmpty) {
      return ReviewSessionRecommendation(
        type: SessionType.due,
        itemCount: dueItems.length,
        estimatedDuration: dueItems.length * 30,
        priority: Priority.medium,
        description: 'Vous avez ${dueItems.length} révision(s) prête(s)',
      );
    }
    
    final upcomingItems = getUpcomingReviews(7);
    if (upcomingItems.isNotEmpty) {
      return ReviewSessionRecommendation(
        type: SessionType.upcoming,
        itemCount: upcomingItems.length,
        estimatedDuration: upcomingItems.length * 30,
        priority: Priority.low,
        description: 'Vous avez ${upcomingItems.length} révision(s) cette semaine',
      );
    }
    
    return ReviewSessionRecommendation(
      type: SessionType.none,
      itemCount: 0,
      estimatedDuration: 0,
      priority: Priority.low,
      description: 'Aucune révision en attente',
    );
  }

  // Generate a unique question ID
  String _generateQuestionId(QuizQuestion question, String categoryName) {
    // Create a simple hash of the question content for ID
    final content = '${categoryName}_${question.question}_${question.correct}';
    return content.hashCode.toString();
  }

  // Get total review time for statistics
  Duration getTotalReviewTime() {
    final allItems = _getBox.values.toList();
    final totalAttempts = allItems.fold(0, (sum, item) => sum + item.totalAttempts);
    
    // Estimate 30 seconds per attempt on average
    return Duration(seconds: totalAttempts * 30);
  }

  // Get success rate across all reviews
  double getOverallSuccessRate() {
    final allItems = _getBox.values.toList();
    
    if (allItems.isEmpty) return 0.0;
    
    int totalCorrect = 0;
    int totalAttempts = 0;
    
    for (final item in allItems) {
      totalCorrect += item.consecutiveCorrect;
      totalAttempts += item.totalAttempts;
    }
    
    return totalAttempts > 0 ? totalCorrect / totalAttempts : 0.0;
  }

  // Dispose resources
  Future<void> dispose() async {
    await _box?.close();
    _box = null;
  }
}

// Statistics class for review data
class ReviewStatistics {
  final int totalItems;
  final int dueToday;
  final int overdue;
  final double averageInterval;
  final double averageEasinessFactor;
  final Map<String, int> masteryLevels;
  final Map<String, int> topicBreakdown;

  ReviewStatistics({
    required this.totalItems,
    required this.dueToday,
    required this.overdue,
    required this.averageInterval,
    required this.averageEasinessFactor,
    required this.masteryLevels,
    required this.topicBreakdown,
  });

  // Get the percentage of items that are due or overdue
  double get urgencyRate {
    if (totalItems == 0) return 0.0;
    return (dueToday + overdue) / totalItems;
  }

  // Get average mastery level
  double get averageMasteryLevel {
    if (masteryLevels.isEmpty) return 0.0;
    
    double totalScore = 0.0;
    int totalItems = 0;
    
    masteryLevels.forEach((level, count) {
      double score = 0.0;
      switch (level) {
        case 'Beginner':
          score = 0.25;
          break;
        case 'Learning':
          score = 0.5;
          break;
        case 'Proficient':
          score = 0.75;
          break;
        case 'Mastered':
          score = 1.0;
          break;
      }
      totalScore += score * count;
      totalItems += count;
    });
    
    return totalItems > 0 ? totalScore / totalItems : 0.0;
  }
}

// Recommendation for review sessions
class ReviewSessionRecommendation {
  final SessionType type;
  final int itemCount;
  final int estimatedDuration; // in seconds
  final Priority priority;
  final String description;

  ReviewSessionRecommendation({
    required this.type,
    required this.itemCount,
    required this.estimatedDuration,
    required this.priority,
    required this.description,
  });

  // Get duration in minutes
  int get durationInMinutes => (estimatedDuration / 60).ceil();
}

enum SessionType {
  overdue,
  due,
  upcoming,
  none,
}

enum Priority {
  high,
  medium,
  low,
}
