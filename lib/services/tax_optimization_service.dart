import '../models/calculators/tax_optimization_data.dart';

class TaxOptimizationService {
  
  TaxOptimizationResult analyzeAndOptimize(TaxOptimizationInput input) {
    final currentAnalysis = analyzeCurrentSituation(input);
    final strategies = generateOptimizationStrategies(input);
    final scenarios = calculateOptimizedScenarios(input, strategies);
    final implementationPlan = createImplementationPlan(strategies);
    final riskAssessment = assessRisks(strategies, input);
    final projectedSavings = calculateProjectedSavings(scenarios);
    
    return TaxOptimizationResult(
      currentAnalysis: currentAnalysis,
      recommendedStrategies: strategies,
      scenarios: scenarios,
      implementationPlan: implementationPlan,
      riskAssessment: riskAssessment,
      projectedSavings: projectedSavings,
    );
  }

  CurrentTaxAnalysis analyzeCurrentSituation(TaxOptimizationInput input) {
    final effectiveTaxRate = input.currentTaxSituation.currentISTax / 
        input.currentTaxSituation.accountingResult;
    
    final marginalTaxRate = _calculateMarginalRate(input.companyProfile.annualRevenue);
    
    final issues = _identifyTaxIssues(input);
    final opportunities = _identifyMissedOpportunities(input);
    final benchmark = _calculateBenchmarkComparison(input);
    
    return CurrentTaxAnalysis(
      effectiveTaxRate: effectiveTaxRate,
      marginalTaxRate: marginalTaxRate,
      identifiedIssues: issues,
      missedOpportunities: opportunities,
      benchmarkComparison: benchmark,
    );
  }

  List<OptimizationStrategy> generateOptimizationStrategies(TaxOptimizationInput input) {
    final strategies = <OptimizationStrategy>[];
    
    // Depreciation optimization
    if (input.currentTaxSituation.depreciationAmount > 0) {
      strategies.add(_createDepreciationStrategy(input));
    }
    
    // Provision strategies
    strategies.add(_createProvisionStrategy(input));
    
    // Investment timing
    if (input.optimizationGoals.objectives.contains(OptimizationObjective.planInvestments)) {
      strategies.add(_createInvestmentTimingStrategy(input));
    }
    
    // Expense timing
    strategies.add(_createExpenseTimingStrategy(input));
    
    // Regime optimization
    if (input.companyProfile.annualRevenue < 3000000) {
      strategies.add(_createRegimeOptimizationStrategy(input));
    }
    
    // Export incentives
    if (input.companyProfile.isExporter) {
      strategies.add(_createExportIncentiveStrategy(input));
    }
    
    // R&D incentives
    if (input.companyProfile.hasRnD) {
      strategies.add(_createRnDIncentiveStrategy(input));
    }
    
    // Filter strategies based on constraints
    return _filterStrategiesByConstraints(strategies, input.constraints);
  }

  List<OptimizationScenario> calculateOptimizedScenarios(
    TaxOptimizationInput input, 
    List<OptimizationStrategy> strategies,
  ) {
    final scenarios = <OptimizationScenario>[];
    
    // Conservative scenario
    final conservativeStrategies = strategies
        .where((s) => s.riskLevel == RiskLevel.low)
        .take(3)
        .toList();
    scenarios.add(_createScenario('Conservateur', conservativeStrategies, input));
    
    // Balanced scenario
    final balancedStrategies = strategies
        .where((s) => s.riskLevel == RiskLevel.low || s.riskLevel == RiskLevel.medium)
        .take(5)
        .toList();
    scenarios.add(_createScenario('Équilibré', balancedStrategies, input));
    
    // Aggressive scenario
    final aggressiveStrategies = strategies.take(7).toList();
    scenarios.add(_createScenario('Agressif', aggressiveStrategies, input));
    
    return scenarios;
  }

  ImplementationPlan createImplementationPlan(List<OptimizationStrategy> strategies) {
    final phases = <ImplementationPhase>[];
    final milestones = <String, DateTime>{};
    
    // Phase 1: Immediate actions (0-3 months)
    final immediateStrategies = strategies
        .where((s) => s.timeToImplement <= 3)
        .toList();
    
    if (immediateStrategies.isNotEmpty) {
      phases.add(ImplementationPhase(
        name: 'Actions Immédiates',
        description: 'Stratégies à mettre en place rapidement',
        orderIndex: 1,
        durationMonths: 3,
        tasks: immediateStrategies.map((s) => s.name).toList(),
        dependencies: [],
        cost: immediateStrategies.fold(0.0, (sum, s) => sum + s.implementationCost),
      ));
    }
    
    // Phase 2: Medium-term actions (3-12 months)
    final mediumTermStrategies = strategies
        .where((s) => s.timeToImplement > 3 && s.timeToImplement <= 12)
        .toList();
    
    if (mediumTermStrategies.isNotEmpty) {
      phases.add(ImplementationPhase(
        name: 'Actions à Moyen Terme',
        description: 'Stratégies nécessitant plus de préparation',
        orderIndex: 2,
        durationMonths: 9,
        tasks: mediumTermStrategies.map((s) => s.name).toList(),
        dependencies: immediateStrategies.map((s) => s.name).toList(),
        cost: mediumTermStrategies.fold(0.0, (sum, s) => sum + s.implementationCost),
      ));
    }
    
    // Phase 3: Long-term actions (12+ months)
    final longTermStrategies = strategies
        .where((s) => s.timeToImplement > 12)
        .toList();
    
    if (longTermStrategies.isNotEmpty) {
      phases.add(ImplementationPhase(
        name: 'Actions à Long Terme',
        description: 'Stratégies structurelles',
        orderIndex: 3,
        durationMonths: 12,
        tasks: longTermStrategies.map((s) => s.name).toList(),
        dependencies: mediumTermStrategies.map((s) => s.name).toList(),
        cost: longTermStrategies.fold(0.0, (sum, s) => sum + s.implementationCost),
      ));
    }
    
    // Set milestones
    DateTime currentDate = DateTime.now();
    for (final phase in phases) {
      milestones['Fin ${phase.name}'] = currentDate.add(Duration(days: phase.durationMonths * 30));
      currentDate = currentDate.add(Duration(days: phase.durationMonths * 30));
    }
    
    final totalDuration = phases.fold(0.0, (sum, phase) => sum + phase.durationMonths);
    
    return ImplementationPlan(
      phases: phases,
      milestones: milestones,
      criticalPath: phases.map((p) => p.name).toList(),
      totalDuration: totalDuration,
    );
  }

  RiskAssessment assessRisks(List<OptimizationStrategy> strategies, TaxOptimizationInput input) {
    final riskFactors = <RiskFactor>[];
    
    // Compliance risk
    final highRiskStrategies = strategies.where((s) => s.riskLevel == RiskLevel.high).length;
    if (highRiskStrategies > 0) {
      riskFactors.add(const RiskFactor(
        name: 'Risque de conformité',
        description: 'Certaines stratégies présentent un risque de non-conformité',
        level: RiskLevel.medium,
        probability: 0.3,
        impact: 0.8,
      ));
    }
    
    // Implementation risk
    final complexStrategies = strategies.where((s) => s.timeToImplement > 6).length;
    if (complexStrategies > 2) {
      riskFactors.add(const RiskFactor(
        name: 'Risque d\'implémentation',
        description: 'Complexité de mise en œuvre de multiples stratégies',
        level: RiskLevel.medium,
        probability: 0.4,
        impact: 0.6,
      ));
    }
    
    // Cash flow risk
    final totalCost = strategies.fold(0.0, (sum, s) => sum + s.implementationCost);
    if (totalCost > input.constraints.maxBudget * 0.8) {
      riskFactors.add(const RiskFactor(
        name: 'Risque de trésorerie',
        description: 'Coût d\'implémentation élevé par rapport au budget',
        level: RiskLevel.high,
        probability: 0.6,
        impact: 0.7,
      ));
    }
    
    final overallRisk = _calculateOverallRisk(riskFactors);
    final confidenceLevel = _calculateConfidenceLevel(strategies, riskFactors);
    
    return RiskAssessment(
      overallRisk: overallRisk,
      riskFactors: riskFactors,
      mitigationStrategies: _generateMitigationStrategies(riskFactors),
      confidenceLevel: confidenceLevel,
    );
  }

  ProjectedSavings calculateProjectedSavings(List<OptimizationScenario> scenarios) {
    if (scenarios.isEmpty) {
      return const ProjectedSavings(
        yearlyTaxSavings: {},
        totalSavings: 0,
        netPresentValue: 0,
        internalRateOfReturn: 0,
        paybackPeriodMonths: 0,
      );
    }
    
    // Use the balanced scenario for projections
    final balancedScenario = scenarios.length > 1 ? scenarios[1] : scenarios[0];
    
    final yearlyTaxSavings = balancedScenario.yearlyProjections;
    final totalSavings = balancedScenario.totalSavings;
    
    // Calculate NPV with 8% discount rate
    double npv = 0;
    for (final entry in yearlyTaxSavings.entries) {
      npv += entry.value / (1 + 0.08) * entry.key;
    }
    npv -= balancedScenario.totalCost;
    
    // Simple IRR calculation (approximation)
    final irr = totalSavings > balancedScenario.totalCost 
        ? (totalSavings - balancedScenario.totalCost) / balancedScenario.totalCost 
        : 0.0;
    
    // Payback period calculation
    double cumulativeSavings = 0;
    int paybackMonths = 0;
    for (final entry in yearlyTaxSavings.entries) {
      cumulativeSavings += entry.value;
      if (cumulativeSavings >= balancedScenario.totalCost) {
        paybackMonths = entry.key * 12;
        break;
      }
    }
    
    return ProjectedSavings(
      yearlyTaxSavings: yearlyTaxSavings,
      totalSavings: totalSavings,
      netPresentValue: npv,
      internalRateOfReturn: irr,
      paybackPeriodMonths: paybackMonths,
    );
  }

  double _calculateMarginalRate(double revenue) {
    if (revenue <= 300000) return 0.10;
    if (revenue <= 1000000) return 0.20;
    if (revenue <= 5000000) return 0.31;
    return 0.31;
  }

  List<String> _identifyTaxIssues(TaxOptimizationInput input) {
    final issues = <String>[];
    
    final effectiveRate = input.currentTaxSituation.currentISTax / 
        input.currentTaxSituation.accountingResult;
    
    if (effectiveRate > 0.35) {
      issues.add('Taux d\'imposition effectif élevé (${(effectiveRate * 100).toStringAsFixed(1)}%)');
    }
    
    if (input.currentTaxSituation.provisionsAmount == 0) {
      issues.add('Aucune provision constituée pour optimiser la charge fiscale');
    }
    
    if (input.currentTaxSituation.existingDeductions.isEmpty) {
      issues.add('Déductions fiscales non exploitées');
    }
    
    return issues;
  }

  List<String> _identifyMissedOpportunities(TaxOptimizationInput input) {
    final opportunities = <String>[];
    
    if (input.companyProfile.isExporter && 
        !input.currentTaxSituation.existingDeductions.contains('export')) {
      opportunities.add('Avantages fiscaux à l\'exportation non utilisés');
    }
    
    if (input.companyProfile.hasRnD && 
        !input.currentTaxSituation.existingDeductions.contains('rnd')) {
      opportunities.add('Crédit d\'impôt recherche et développement');
    }
    
    if (input.companyProfile.annualRevenue < 3000000 && 
        input.currentTaxSituation.currentRegime != TaxRegime.simplified) {
      opportunities.add('Éligibilité au régime simplifié');
    }
    
    return opportunities;
  }

  double _calculateBenchmarkComparison(TaxOptimizationInput input) {
    // Simplified benchmark calculation
    final sectorAverageRate = _getSectorAverageRate(input.companyProfile.sector);
    final currentRate = input.currentTaxSituation.currentISTax / 
        input.currentTaxSituation.accountingResult;
    
    return (currentRate - sectorAverageRate) / sectorAverageRate;
  }

  double _getSectorAverageRate(BusinessSector sector) {
    switch (sector) {
      case BusinessSector.manufacturing:
        return 0.28;
      case BusinessSector.services:
        return 0.30;
      case BusinessSector.retail:
        return 0.25;
      case BusinessSector.technology:
        return 0.20;
      case BusinessSector.agriculture:
        return 0.15;
      default:
        return 0.31;
    }
  }

  OptimizationStrategy _createDepreciationStrategy(TaxOptimizationInput input) {
    return const OptimizationStrategy(
      name: 'Optimisation de l\'amortissement',
      description: 'Révision de la méthode d\'amortissement pour maximiser les déductions',
      type: StrategyType.deduction,
      estimatedSavings: 50000,
      implementationCost: 5000,
      timeToImplement: 2,
      riskLevel: RiskLevel.low,
      requirements: ['Révision des politiques comptables', 'Validation expert-comptable'],
      benefits: ['Réduction immédiate de l\'IS', 'Amélioration de la trésorerie'],
      legalBasis: 'Code Général des Impôts - Article 10',
    );
  }

  OptimizationStrategy _createProvisionStrategy(TaxOptimizationInput input) {
    return const OptimizationStrategy(
      name: 'Constitution de provisions',
      description: 'Mise en place de provisions déductibles fiscalement',
      type: StrategyType.deduction,
      estimatedSavings: 30000,
      implementationCost: 3000,
      timeToImplement: 1,
      riskLevel: RiskLevel.low,
      requirements: ['Justification économique', 'Documentation appropriée'],
      benefits: ['Lissage de la charge fiscale', 'Gestion des risques'],
      legalBasis: 'Code Général des Impôts - Article 7',
    );
  }

  OptimizationStrategy _createInvestmentTimingStrategy(TaxOptimizationInput input) {
    return const OptimizationStrategy(
      name: 'Optimisation du timing d\'investissement',
      description: 'Planification des investissements pour maximiser les avantages fiscaux',
      type: StrategyType.timing,
      estimatedSavings: 75000,
      implementationCost: 10000,
      timeToImplement: 6,
      riskLevel: RiskLevel.medium,
      requirements: ['Plan d\'investissement', 'Analyse de rentabilité'],
      benefits: ['Crédit d\'impôt investissement', 'Amortissement accéléré'],
      legalBasis: 'Loi de Finances - Incitations à l\'investissement',
    );
  }

  OptimizationStrategy _createExpenseTimingStrategy(TaxOptimizationInput input) {
    return const OptimizationStrategy(
      name: 'Optimisation du timing des charges',
      description: 'Planification des dépenses pour optimiser la déductibilité',
      type: StrategyType.timing,
      estimatedSavings: 25000,
      implementationCost: 2000,
      timeToImplement: 1,
      riskLevel: RiskLevel.low,
      requirements: ['Révision des procédures', 'Formation équipe comptable'],
      benefits: ['Optimisation de la base imposable', 'Meilleure gestion fiscale'],
      legalBasis: 'Principe de déductibilité des charges',
    );
  }

  OptimizationStrategy _createRegimeOptimizationStrategy(TaxOptimizationInput input) {
    return const OptimizationStrategy(
      name: 'Changement de régime fiscal',
      description: 'Passage au régime simplifié pour réduire les obligations',
      type: StrategyType.regime,
      estimatedSavings: 40000,
      implementationCost: 8000,
      timeToImplement: 4,
      riskLevel: RiskLevel.medium,
      requirements: ['Éligibilité confirmée', 'Demande administrative'],
      benefits: ['Réduction du taux d\'IS', 'Simplification administrative'],
      legalBasis: 'Code Général des Impôts - Régime simplifié',
    );
  }

  OptimizationStrategy _createExportIncentiveStrategy(TaxOptimizationInput input) {
    return const OptimizationStrategy(
      name: 'Avantages fiscaux à l\'exportation',
      description: 'Optimisation des incitations fiscales pour les exportateurs',
      type: StrategyType.deduction,
      estimatedSavings: 60000,
      implementationCost: 5000,
      timeToImplement: 3,
      riskLevel: RiskLevel.low,
      requirements: ['Certification exportateur', 'Documentation des ventes'],
      benefits: ['Exonération partielle d\'IS', 'Avantages TVA'],
      legalBasis: 'Code des Investissements - Incitations export',
    );
  }

  OptimizationStrategy _createRnDIncentiveStrategy(TaxOptimizationInput input) {
    return const OptimizationStrategy(
      name: 'Crédit d\'impôt R&D',
      description: 'Optimisation du crédit d\'impôt recherche et développement',
      type: StrategyType.deduction,
      estimatedSavings: 80000,
      implementationCost: 12000,
      timeToImplement: 6,
      riskLevel: RiskLevel.medium,
      requirements: ['Certification R&D', 'Documentation projets'],
      benefits: ['Crédit d\'impôt 30%', 'Report sur 5 ans'],
      legalBasis: 'Loi de Finances - Crédit impôt R&D',
    );
  }

  List<OptimizationStrategy> _filterStrategiesByConstraints(
    List<OptimizationStrategy> strategies, 
    OptimizationConstraints constraints,
  ) {
    return strategies.where((strategy) {
      // Filter by budget
      if (strategy.implementationCost > constraints.maxBudget) {
        return false;
      }
      
      // Filter by risk tolerance
      if (constraints.riskTolerance == RiskTolerance.conservative && 
          strategy.riskLevel != RiskLevel.low) {
        return false;
      }
      
      // Filter excluded strategies
      if (constraints.excludedStrategies.contains(strategy.name)) {
        return false;
      }
      
      return true;
    }).toList();
  }

  OptimizationScenario _createScenario(
    String name, 
    List<OptimizationStrategy> strategies, 
    TaxOptimizationInput input,
  ) {
    final totalSavings = strategies.fold(0.0, (sum, s) => sum + s.estimatedSavings);
    final totalCost = strategies.fold(0.0, (sum, s) => sum + s.implementationCost);
    final netBenefit = totalSavings - totalCost;
    
    final maxImplementationTime = strategies.isNotEmpty 
        ? strategies.map((s) => s.timeToImplement).reduce((a, b) => a > b ? a : b)
        : 0;
    
    final overallRisk = _calculateScenarioRisk(strategies);
    
    // Simple yearly projections
    final yearlyProjections = <int, double>{};
    for (int year = 1; year <= input.optimizationGoals.timeHorizonYears; year++) {
      yearlyProjections[year] = totalSavings / input.optimizationGoals.timeHorizonYears;
    }
    
    return OptimizationScenario(
      name: name,
      strategiesIncluded: strategies.map((s) => s.name).toList(),
      totalSavings: totalSavings,
      totalCost: totalCost,
      netBenefit: netBenefit,
      implementationTimeMonths: maxImplementationTime,
      overallRisk: overallRisk,
      yearlyProjections: yearlyProjections,
    );
  }

  RiskLevel _calculateOverallRisk(List<RiskFactor> riskFactors) {
    if (riskFactors.isEmpty) return RiskLevel.low;
    
    final averageRisk = riskFactors.map((r) => r.level.index).reduce((a, b) => a + b) / riskFactors.length;
    
    if (averageRisk <= 0.5) return RiskLevel.low;
    if (averageRisk <= 1.5) return RiskLevel.medium;
    if (averageRisk <= 2.5) return RiskLevel.high;
    return RiskLevel.critical;
  }

  RiskLevel _calculateScenarioRisk(List<OptimizationStrategy> strategies) {
    if (strategies.isEmpty) return RiskLevel.low;
    
    final averageRisk = strategies.map((s) => s.riskLevel.index).reduce((a, b) => a + b) / strategies.length;
    
    if (averageRisk <= 0.5) return RiskLevel.low;
    if (averageRisk <= 1.5) return RiskLevel.medium;
    if (averageRisk <= 2.5) return RiskLevel.high;
    return RiskLevel.critical;
  }

  double _calculateConfidenceLevel(List<OptimizationStrategy> strategies, List<RiskFactor> riskFactors) {
    double confidence = 0.9; // Start with high confidence
    
    // Reduce confidence based on risk factors
    for (final risk in riskFactors) {
      confidence -= (risk.probability * risk.impact * 0.1);
    }
    
    // Reduce confidence for complex strategies
    final complexStrategies = strategies.where((s) => s.timeToImplement > 6).length;
    confidence -= (complexStrategies * 0.05);
    
    return confidence.clamp(0.0, 1.0);
  }

  List<String> _generateMitigationStrategies(List<RiskFactor> riskFactors) {
    final mitigations = <String>[];
    
    for (final risk in riskFactors) {
      switch (risk.name) {
        case 'Risque de conformité':
          mitigations.add('Validation par expert-comptable avant implémentation');
          mitigations.add('Mise en place d\'un suivi de conformité renforcé');
          break;
        case 'Risque d\'implémentation':
          mitigations.add('Planification détaillée avec jalons intermédiaires');
          mitigations.add('Formation des équipes et accompagnement externe');
          break;
        case 'Risque de trésorerie':
          mitigations.add('Échelonnement des investissements sur plusieurs exercices');
          mitigations.add('Recherche de financements ou subventions');
          break;
      }
    }
    
    return mitigations;
  }
}
