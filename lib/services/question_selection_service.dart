import 'dart:math';
import '../models/quiz/custom_quiz_config.dart';
import '../models/quiz_model.dart';
import '../models/adaptive_learning_models.dart';
import 'spaced_repetition_service.dart';
import 'adaptive_difficulty_service.dart';

// Service for intelligent question selection that replaces static level-based question lists
class QuestionSelectionService {
  final SpacedRepetitionService _spacedRepetitionService;
  final AdaptiveDifficultyService? _adaptiveDifficultyService;
  final Random _random = Random();

  // Cache for recently used questions to avoid repetition
  final Set<String> _recentlyUsedQuestions = <String>{};
  static const int _recentQueueSize = 20;

  QuestionSelectionService({
    required SpacedRepetitionService spacedRepetitionService,
    AdaptiveDifficultyService? adaptiveDifficultyService,
  }) : _spacedRepetitionService = spacedRepetitionService,
       _adaptiveDifficultyService = adaptiveDifficultyService;

  // Generate dynamic question queue based on custom quiz configuration
  Future<List<QuizQuestion>> generateQuestionQueue(
    CustomQuizConfig config,
    List<QuizQuestion> availableQuestions,
    {UserPerformance? userPerformance}
  ) async {
    final List<QuizQuestion> questionQueue = [];
    final List<QuizQuestion> candidateQuestions = [];

    // Filter questions based on configuration
    candidateQuestions.addAll(_filterQuestionsByConfig(availableQuestions, config));

    if (candidateQuestions.isEmpty) {
      // Fallback to all available questions if filters are too restrictive
      candidateQuestions.addAll(availableQuestions);
    }

    // Include spaced repetition questions if enabled
    if (config.includeSpacedRepetition) {
      final spacedRepQuestions = await _getSpacedRepetitionQuestions(
        candidateQuestions,
        maxCount: (config.questionCount * 0.3).round(), // Up to 30% from spaced repetition
      );
      questionQueue.addAll(spacedRepQuestions);
    }

    // Fill remaining slots with adaptive selection
    final remainingCount = config.questionCount - questionQueue.length;
    if (remainingCount > 0) {
      final adaptiveQuestions = await _selectAdaptiveQuestions(
        candidateQuestions,
        remainingCount,
        config,
        userPerformance: userPerformance,
        excludeQuestions: questionQueue,
      );
      questionQueue.addAll(adaptiveQuestions);
    }

    // Balance difficulty distribution for optimal learning curve
    final balancedQueue = _balanceDifficultyDistribution(questionQueue, config);

    // Shuffle to avoid predictable patterns while maintaining difficulty balance
    return _shuffleWithConstraints(balancedQueue);
  }

  // Select questions by specific topics
  List<QuizQuestion> selectQuestionsByTopic(
    List<QuizQuestion> availableQuestions,
    List<String> topics,
    int count,
    {int? preferredDifficulty}
  ) {
    // Filter by topics
    final topicQuestions = availableQuestions
        .where((q) => topics.contains(q.topic))
        .toList();

    if (topicQuestions.isEmpty) {
      return [];
    }

    // Apply difficulty preference if specified
    final filteredQuestions = preferredDifficulty != null
        ? topicQuestions.where((q) => 
            (q.difficulty - preferredDifficulty).abs() <= 1).toList()
        : topicQuestions;

    // Distribute questions evenly across topics
    return _distributedSelection(filteredQuestions, topics, count);
  }

  // Include spaced repetition questions in the queue
  Future<List<QuizQuestion>> includeSpacedRepetitionQuestions(
    List<QuizQuestion> availableQuestions,
    int maxCount
  ) async {
    return await _getSpacedRepetitionQuestions(availableQuestions, maxCount: maxCount);
  }

  // Balance difficulty distribution for optimal challenge curve
  List<QuizQuestion> balanceDifficultyDistribution(
    List<QuizQuestion> questions,
    {DifficultyDistribution? targetDistribution}
  ) {
    if (questions.isEmpty) return questions;

    final distribution = targetDistribution ?? DifficultyDistribution.balanced();
    return _applyDifficultyDistribution(questions, distribution);
  }

  // Filter questions based on performance history to avoid staleness
  List<QuizQuestion> filterByPerformanceHistory(
    List<QuizQuestion> questions,
    UserPerformance? performance,
    {double weaknessThreshold = 0.6}
  ) {
    if (performance == null) return questions;

    final List<QuizQuestion> priorityQuestions = [];
    final List<QuizQuestion> regularQuestions = [];

    for (final question in questions) {
      // Check if this topic is a weakness area
      final topicAccuracy = _getTopicAccuracy(performance, question.topic);
      
      if (topicAccuracy > 0 && topicAccuracy < weaknessThreshold) {
        priorityQuestions.add(question);
      } else {
        regularQuestions.add(question);
      }
    }

    // Combine with priority on weakness areas (70% priority, 30% regular)
    final int priorityCount = (questions.length * 0.7).round();
    final int regularCount = questions.length - priorityCount;

    final List<QuizQuestion> result = [];
    
    // Add priority questions first
    if (priorityQuestions.isNotEmpty) {
      priorityQuestions.shuffle(_random);
      result.addAll(priorityQuestions.take(priorityCount));
    }

    // Fill remaining with regular questions
    if (regularQuestions.isNotEmpty && result.length < questions.length) {
      regularQuestions.shuffle(_random);
      result.addAll(regularQuestions.take(regularCount));
    }

    return result;
  }

  // Private helper methods

  // Filter questions based on custom quiz configuration
  List<QuizQuestion> _filterQuestionsByConfig(
    List<QuizQuestion> questions,
    CustomQuizConfig config
  ) {
    return questions.where((question) {
      // Topic filter
      if (config.selectedTopics.isNotEmpty && 
          !config.selectedTopics.contains(question.topic)) {
        return false;
      }

      // Difficulty range filter
      if (question.difficulty < config.minDifficulty || 
          question.difficulty > config.maxDifficulty) {
        return false;
      }

      // Avoid recently used questions
      final questionId = _generateQuestionId(question);
      if (_recentlyUsedQuestions.contains(questionId)) {
        return false;
      }

      return true;
    }).toList();
  }

  // Get spaced repetition questions that are due for review
  Future<List<QuizQuestion>> _getSpacedRepetitionQuestions(
    List<QuizQuestion> availableQuestions,
    {required int maxCount}
  ) async {
    final dueItems = _spacedRepetitionService.getDueItems();
    final List<QuizQuestion> spacedRepQuestions = [];

    for (final item in dueItems.take(maxCount)) {
      // Find matching question from available questions
      final matchingQuestion = availableQuestions.firstWhere(
        (q) => _generateQuestionId(q) == item.questionId,
        orElse: () => null as QuizQuestion,
      );

      spacedRepQuestions.add(matchingQuestion);
        }

    return spacedRepQuestions;
  }

  // Select questions using adaptive difficulty algorithms
  Future<List<QuizQuestion>> _selectAdaptiveQuestions(
    List<QuizQuestion> candidateQuestions,
    int count,
    CustomQuizConfig config,
    {UserPerformance? userPerformance,
    List<QuizQuestion> excludeQuestions = const []}
  ) async {
    final availableQuestions = candidateQuestions
        .where((q) => !excludeQuestions.contains(q))
        .toList();

    if (availableQuestions.isEmpty) {
      return [];
    }

    List<QuizQuestion> selectedQuestions = [];

    if (config.adaptiveDifficulty && 
        _adaptiveDifficultyService != null && 
        userPerformance != null) {
      // Use adaptive difficulty service for intelligent selection
      selectedQuestions = _adaptiveDifficultyService!
          .adjustQuestionSelection(availableQuestions, userPerformance);
    } else if (config.focusOnWeakAreas && userPerformance != null) {
      // Focus on weakness areas
      selectedQuestions = filterByPerformanceHistory(
        availableQuestions, 
        userPerformance
      );
    } else {
      // Standard random selection within difficulty range
      selectedQuestions = availableQuestions;
    }

    // Ensure we don't exceed the requested count
    if (selectedQuestions.length > count) {
      selectedQuestions.shuffle(_random);
      selectedQuestions = selectedQuestions.take(count).toList();
    }

    return selectedQuestions;
  }

  // Balance difficulty distribution for optimal learning progression
  List<QuizQuestion> _balanceDifficultyDistribution(
    List<QuizQuestion> questions,
    CustomQuizConfig config
  ) {
    if (questions.length <= 3) {
      return questions; // Too few questions to balance effectively
    }

    // Group questions by difficulty
    final Map<int, List<QuizQuestion>> difficultyGroups = {};
    for (final question in questions) {
      difficultyGroups.putIfAbsent(question.difficulty, () => []).add(question);
    }

    // Create a balanced distribution
    final List<QuizQuestion> balancedQuestions = [];
    final targetDistribution = _calculateTargetDistribution(
      questions.length, 
      config.minDifficulty, 
      config.maxDifficulty
    );

    for (final entry in targetDistribution.entries) {
      final difficulty = entry.key;
      final targetCount = entry.value;
      final availableQuestions = difficultyGroups[difficulty] ?? [];

      if (availableQuestions.isNotEmpty) {
        availableQuestions.shuffle(_random);
        balancedQuestions.addAll(
          availableQuestions.take(targetCount)
        );
      }
    }

    return balancedQuestions;
  }

  // Shuffle questions while maintaining some constraints for better user experience
  List<QuizQuestion> _shuffleWithConstraints(List<QuizQuestion> questions) {
    if (questions.length <= 2) {
      return questions;
    }

    final shuffled = List<QuizQuestion>.from(questions);
    shuffled.shuffle(_random);

    // Avoid having consecutive questions of same difficulty at the start
    for (int i = 0; i < min(3, shuffled.length - 1); i++) {
      if (shuffled[i].difficulty == shuffled[i + 1].difficulty) {
        // Find a different difficulty question to swap with
        for (int j = i + 2; j < shuffled.length; j++) {
          if (shuffled[j].difficulty != shuffled[i].difficulty) {
            final temp = shuffled[i + 1];
            shuffled[i + 1] = shuffled[j];
            shuffled[j] = temp;
            break;
          }
        }
      }
    }

    return shuffled;
  }

  // Distribute questions evenly across topics
  List<QuizQuestion> _distributedSelection(
    List<QuizQuestion> questions,
    List<String> topics,
    int count
  ) {
    final Map<String, List<QuizQuestion>> topicGroups = {};
    
    // Group questions by topic
    for (final question in questions) {
      topicGroups.putIfAbsent(question.topic, () => []).add(question);
    }

    final List<QuizQuestion> selected = [];
    final int questionsPerTopic = count ~/ topics.length;
    final int extraQuestions = count % topics.length;

    // Distribute questions evenly
    for (int i = 0; i < topics.length; i++) {
      final topic = topics[i];
      final topicQuestions = topicGroups[topic] ?? [];
      
      if (topicQuestions.isNotEmpty) {
        topicQuestions.shuffle(_random);
        final takeCount = questionsPerTopic + (i < extraQuestions ? 1 : 0);
        selected.addAll(topicQuestions.take(takeCount));
      }
    }

    return selected.take(count).toList();
  }

  // Apply specific difficulty distribution
  List<QuizQuestion> _applyDifficultyDistribution(
    List<QuizQuestion> questions,
    DifficultyDistribution distribution
  ) {
    final Map<int, List<QuizQuestion>> difficultyGroups = {};
    
    for (final question in questions) {
      difficultyGroups.putIfAbsent(question.difficulty, () => []).add(question);
    }

    final List<QuizQuestion> result = [];
    final totalQuestions = questions.length;

    for (final entry in distribution.percentages.entries) {
      final difficulty = entry.key;
      final percentage = entry.value;
      final targetCount = (totalQuestions * percentage).round();
      final availableQuestions = difficultyGroups[difficulty] ?? [];

      if (availableQuestions.isNotEmpty) {
        availableQuestions.shuffle(_random);
        result.addAll(availableQuestions.take(targetCount));
      }
    }

    return result;
  }

  // Calculate optimal target distribution based on quiz length and difficulty range
  Map<int, int> _calculateTargetDistribution(
    int totalQuestions,
    int minDifficulty,
    int maxDifficulty
  ) {
    final Map<int, int> distribution = {};
    final difficultyRange = maxDifficulty - minDifficulty + 1;

    if (difficultyRange == 1) {
      distribution[minDifficulty] = totalQuestions;
      return distribution;
    }

    // Use a bell curve-like distribution favoring middle difficulties
    final List<double> weights = [];
    for (int i = 0; i < difficultyRange; i++) {
      final position = i / (difficultyRange - 1); // 0 to 1
      final weight = exp(-pow(position - 0.5, 2) * 8); // Bell curve
      weights.add(weight);
    }

    final totalWeight = weights.reduce((a, b) => a + b);
    
    for (int i = 0; i < difficultyRange; i++) {
      final difficulty = minDifficulty + i;
      final targetCount = (totalQuestions * weights[i] / totalWeight).round();
      if (targetCount > 0) {
        distribution[difficulty] = targetCount;
      }
    }

    return distribution;
  }

  // Generate unique question identifier
  String _generateQuestionId(QuizQuestion question) {
    return '${question.question}_${question.correct}'.hashCode.toString();
  }

  // Get topic accuracy from user performance
  double _getTopicAccuracy(UserPerformance performance, String topic) {
    final topicCorrect = performance.topicAccuracy[topic];
    if (topicCorrect == null) return 0.0;

    // Estimate total attempts (this should be tracked more precisely)
    final estimatedAttempts = max(1, (performance.questionsAnswered * 0.2).round());
    return topicCorrect / estimatedAttempts;
  }

  // Update recently used questions cache
  void _updateRecentlyUsedCache(List<QuizQuestion> questions) {
    for (final question in questions) {
      final questionId = _generateQuestionId(question);
      _recentlyUsedQuestions.add(questionId);
    }

    // Maintain cache size
    while (_recentlyUsedQuestions.length > _recentQueueSize) {
      _recentlyUsedQuestions.remove(_recentlyUsedQuestions.first);
    }
  }

  // Clear the recently used questions cache
  void clearRecentlyUsedCache() {
    _recentlyUsedQuestions.clear();
  }

  // Get statistics about question selection
  QuestionSelectionStats getSelectionStats(List<QuizQuestion> questions) {
    if (questions.isEmpty) {
      return QuestionSelectionStats.empty();
    }

    final Map<int, int> difficultyCount = {};
    final Map<String, int> topicCount = {};

    for (final question in questions) {
      difficultyCount[question.difficulty] = 
          (difficultyCount[question.difficulty] ?? 0) + 1;
      topicCount[question.topic] = 
          (topicCount[question.topic] ?? 0) + 1;
    }

    final avgDifficulty = questions
        .map((q) => q.difficulty)
        .reduce((a, b) => a + b) / questions.length;

    return QuestionSelectionStats(
      totalQuestions: questions.length,
      averageDifficulty: avgDifficulty,
      difficultyDistribution: difficultyCount,
      topicDistribution: topicCount,
      uniqueTopics: topicCount.keys.length,
    );
  }
}

// Class for difficulty distribution configuration
class DifficultyDistribution {
  final Map<int, double> percentages; // difficulty -> percentage (0.0 to 1.0)

  DifficultyDistribution(this.percentages);

  // Balanced distribution across all difficulties
  factory DifficultyDistribution.balanced() {
    return DifficultyDistribution({
      1: 0.15,
      2: 0.25,
      3: 0.30,
      4: 0.20,
      5: 0.10,
    });
  }

  // Easy-focused distribution for beginners
  factory DifficultyDistribution.easyFocus() {
    return DifficultyDistribution({
      1: 0.40,
      2: 0.35,
      3: 0.20,
      4: 0.05,
      5: 0.00,
    });
  }

  // Hard-focused distribution for advanced users
  factory DifficultyDistribution.hardFocus() {
    return DifficultyDistribution({
      1: 0.05,
      2: 0.15,
      3: 0.25,
      4: 0.35,
      5: 0.20,
    });
  }

  // Progressive distribution that gradually increases difficulty
  factory DifficultyDistribution.progressive() {
    return DifficultyDistribution({
      1: 0.30,
      2: 0.25,
      3: 0.20,
      4: 0.15,
      5: 0.10,
    });
  }
}

// Statistics class for question selection analysis
class QuestionSelectionStats {
  final int totalQuestions;
  final double averageDifficulty;
  final Map<int, int> difficultyDistribution;
  final Map<String, int> topicDistribution;
  final int uniqueTopics;

  QuestionSelectionStats({
    required this.totalQuestions,
    required this.averageDifficulty,
    required this.difficultyDistribution,
    required this.topicDistribution,
    required this.uniqueTopics,
  });

  factory QuestionSelectionStats.empty() {
    return QuestionSelectionStats(
      totalQuestions: 0,
      averageDifficulty: 0.0,
      difficultyDistribution: {},
      topicDistribution: {},
      uniqueTopics: 0,
    );
  }

  // Get the most represented difficulty level
  int get dominantDifficulty {
    if (difficultyDistribution.isEmpty) return 0;
    
    return difficultyDistribution.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  // Get the most represented topic
  String get dominantTopic {
    if (topicDistribution.isEmpty) return '';
    
    return topicDistribution.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  // Check if distribution is balanced
  bool get isBalanced {
    if (difficultyDistribution.isEmpty) return false;
    
    final values = difficultyDistribution.values.toList();
    final max = values.reduce((a, b) => a > b ? a : b);
    final min = values.reduce((a, b) => a < b ? a : b);
    
    // Consider balanced if max is not more than 2x min
    return max <= min * 2;
  }
}
