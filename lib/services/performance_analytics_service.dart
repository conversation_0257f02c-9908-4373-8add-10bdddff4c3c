import 'dart:math';
import '../models/analytics/performance_analytics.dart';
import '../models/adaptive_learning_models.dart';
import 'adaptive_learning_service.dart';

// Comprehensive analytics service that processes user performance data into actionable insights
class PerformanceAnalyticsService {
  final AdaptiveLearningService _adaptiveLearningService;

  PerformanceAnalyticsService({
    required AdaptiveLearningService adaptiveLearningService,
  }) : _adaptiveLearningService = adaptiveLearningService;

  // Generate comprehensive performance report for a category and date range
  Future<PerformanceAnalytics> generatePerformanceReport(
    String categoryName,
    DateRange range
  ) async {
    final performances = _adaptiveLearningService.getUserPerformance(categoryName);
    final weaknesses = _adaptiveLearningService.getWeaknessAreas(categoryName);
    
    // Filter performances by date range
    final filteredPerformances = performances
        .where((p) => _isWithinDateRange(p.lastUpdated, range))
        .toList();

    if (filteredPerformances.isEmpty) {
      return PerformanceAnalytics(
        overallAccuracy: 0.0,
        lastUpdated: DateTime.now(),
      );
    }

    // Calculate overall accuracy
    final overallAccuracy = _calculateOverallAccuracy(filteredPerformances);

    // Calculate topic performance
    final topicPerformance = _calculateTopicPerformance(filteredPerformances);

    // Calculate difficulty progression
    final difficultyProgression = _calculateDifficultyProgression(filteredPerformances);

    // Generate weekly progress
    final weeklyProgress = _generateWeeklyProgress(filteredPerformances, range);

    // Calculate streak data
    final streakData = _calculateStreakData(filteredPerformances);

    // Generate time analytics
    final timeAnalytics = _generateTimeAnalytics(filteredPerformances);

    // Identify weakest topics
    final weakestTopics = _identifyWeakestTopics(filteredPerformances, topicPerformance);

    // Generate improvement areas
    final improvementAreas = _generateImprovementAreas(filteredPerformances, weaknesses);

    // Calculate learning velocity
    final learningVelocity = _calculateLearningVelocity(filteredPerformances, range);

    return PerformanceAnalytics(
      overallAccuracy: overallAccuracy,
      topicPerformance: topicPerformance,
      difficultyProgression: difficultyProgression,
      weeklyProgress: weeklyProgress,
      streakData: streakData,
      timeAnalytics: timeAnalytics,
      weakestTopics: weakestTopics,
      improvementAreas: improvementAreas,
      learningVelocity: learningVelocity,
      lastUpdated: DateTime.now(),
    );
  }

  // Identify weakest topics with detailed analysis
  List<TopicWeakness> identifyWeakestTopics(int limit) {
    final List<TopicWeakness> allWeaknesses = [];
    
    // Get all categories' weaknesses
    final categories = ['TVA', 'IS', 'IR', 'CNSS']; // This should be dynamic
    
    for (final category in categories) {
      final performances = _adaptiveLearningService.getUserPerformance(category);
      if (performances.isEmpty) continue;

      // Calculate topic accuracies for this category
      final Map<String, List<double>> topicAccuracies = {};
      final Map<String, int> topicAttempts = {};

      for (final performance in performances) {
        for (final entry in performance.topicAccuracy.entries) {
          final topic = entry.key;
          final correct = entry.value;
          final total = max(1, (performance.questionsAnswered * 0.2).round());
          final accuracy = correct / total;

          topicAccuracies.putIfAbsent(topic, () => []).add(accuracy);
          topicAttempts[topic] = (topicAttempts[topic] ?? 0) + total;
        }
      }

      // Convert to TopicWeakness objects
      for (final entry in topicAccuracies.entries) {
        final topic = entry.key;
        final accuracies = entry.value;
        final avgAccuracy = accuracies.reduce((a, b) => a + b) / accuracies.length;
        final attemptCount = topicAttempts[topic] ?? 0;

        if (avgAccuracy < 0.8 && attemptCount >= 3) {
          final improvementTrend = _calculateTopicImprovementTrend(accuracies);
          final commonMistakes = _identifyCommonMistakes(topic, category);

          allWeaknesses.add(TopicWeakness(
            topic: topic,
            accuracy: avgAccuracy,
            attemptCount: attemptCount,
            commonMistakes: commonMistakes,
            improvementTrend: improvementTrend,
          ));
        }
      }
    }

    // Sort by accuracy (worst first) and return top N
    allWeaknesses.sort((a, b) => a.accuracy.compareTo(b.accuracy));
    return allWeaknesses.take(limit).toList();
  }

  // Calculate learning velocity metrics
  LearningVelocity calculateLearningVelocity() {
    final List<UserPerformance> allPerformances = [];
    final categories = ['TVA', 'IS', 'IR', 'CNSS'];
    
    for (final category in categories) {
      allPerformances.addAll(_adaptiveLearningService.getUserPerformance(category));
    }

    if (allPerformances.isEmpty) {
      return LearningVelocity();
    }

    // Sort by date
    allPerformances.sort((a, b) => a.lastUpdated.compareTo(b.lastUpdated));

    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(Duration(days: 30));
    final sevenDaysAgo = now.subtract(Duration(days: 7));

    // Calculate questions per day
    final recentPerformances = allPerformances
        .where((p) => p.lastUpdated.isAfter(thirtyDaysAgo))
        .toList();

    final totalQuestions = recentPerformances
        .fold(0, (sum, p) => sum + p.questionsAnswered);
    final questionsPerDay = totalQuestions / 30.0;

    // Calculate questions per week
    final weeklyPerformances = allPerformances
        .where((p) => p.lastUpdated.isAfter(sevenDaysAgo))
        .toList();

    final weeklyQuestions = weeklyPerformances
        .fold(0, (sum, p) => sum + p.questionsAnswered);
    final questionsPerWeek = weeklyQuestions.toDouble();

    // Calculate accuracy trend
    final accuracyTrend = _calculateAccuracyTrend(allPerformances);

    // Calculate active days
    final activeDays = _calculateActiveDays(allPerformances, thirtyDaysAgo);

    return LearningVelocity(
      questionsPerDay: questionsPerDay,
      questionsPerWeek: questionsPerWeek,
      accuracyTrend: accuracyTrend,
      activeDays: activeDays,
    );
  }

  // Get accuracy trends over specified number of weeks
  List<TrendPoint> getAccuracyTrends(int weeks) {
    final List<UserPerformance> allPerformances = [];
    final categories = ['TVA', 'IS', 'IR', 'CNSS'];
    
    for (final category in categories) {
      allPerformances.addAll(_adaptiveLearningService.getUserPerformance(category));
    }

    final trends = <TrendPoint>[];
    final now = DateTime.now();

    for (int i = weeks - 1; i >= 0; i--) {
      final weekStart = now.subtract(Duration(days: i * 7));
      final weekEnd = weekStart.add(Duration(days: 6));

      final weekPerformances = allPerformances
          .where((p) => p.lastUpdated.isAfter(weekStart) && 
                       p.lastUpdated.isBefore(weekEnd))
          .toList();

      if (weekPerformances.isNotEmpty) {
        final weekAccuracy = weekPerformances
            .map((p) => p.accuracy)
            .reduce((a, b) => a + b) / weekPerformances.length;

        trends.add(TrendPoint(
          date: weekStart,
          value: weekAccuracy,
          label: 'Semaine ${weeks - i}',
        ));
      }
    }

    return trends;
  }

  // Generate personalized recommendations based on analytics
  List<String> generatePersonalizedRecommendations() {
    final recommendations = <String>[];
    final weakestTopics = identifyWeakestTopics(3);
    final learningVelocity = calculateLearningVelocity();

    // Recommendations based on weak topics
    if (weakestTopics.isNotEmpty) {
      final weakest = weakestTopics.first;
      recommendations.add(
        'Concentrez-vous sur ${weakest.topic} - seulement ${(weakest.accuracy * 100).toStringAsFixed(1)}% de réussite'
      );

      if (weakest.improvementTrend > 0.1) {
        recommendations.add('Bonne nouvelle : vos performances en ${weakest.topic} s\'améliorent !');
      } else if (weakest.improvementTrend < -0.1) {
        recommendations.add('Attention : vos performances en ${weakest.topic} déclinent. Révision nécessaire.');
      }
    }

    // Recommendations based on learning velocity
    if (learningVelocity.questionsPerDay < 5) {
      recommendations.add('Augmentez votre rythme : visez au moins 5 questions par jour');
    } else if (learningVelocity.questionsPerDay > 20) {
      recommendations.add('Excellent rythme ! Assurez-vous de bien comprendre chaque concept');
    }

    // Recommendations based on accuracy trend
    if (learningVelocity.accuracyTrend > 0.1) {
      recommendations.add('Félicitations ! Vos performances s\'améliorent constamment');
    } else if (learningVelocity.accuracyTrend < -0.1) {
      recommendations.add('Vos performances diminuent. Prenez une pause ou révisez les bases');
    }

    // Recommendations based on active days
    if (learningVelocity.activeDays < 10) {
      recommendations.add('Essayez d\'étudier plus régulièrement pour une meilleure rétention');
    }

    // General recommendations
    if (recommendations.isEmpty) {
      recommendations.add('Continuez votre excellent travail !');
      recommendations.add('Essayez des questions plus difficiles pour vous challenger');
    }

    return recommendations;
  }

  // Get comparative analytics against benchmarks or personal bests
  Map<String, dynamic> getComparativeAnalytics() {
    final List<UserPerformance> allPerformances = [];
    final categories = ['TVA', 'IS', 'IR', 'CNSS'];
    
    for (final category in categories) {
      allPerformances.addAll(_adaptiveLearningService.getUserPerformance(category));
    }

    if (allPerformances.isEmpty) {
      return {
        'currentAccuracy': 0.0,
        'personalBest': 0.0,
        'improvement': 0.0,
        'ranking': 'Débutant',
      };
    }

    final currentAccuracy = _calculateOverallAccuracy(allPerformances);
    final personalBest = allPerformances
        .map((p) => p.accuracy)
        .reduce((a, b) => a > b ? a : b);

    final improvement = currentAccuracy - personalBest;

    // Simple ranking system based on overall accuracy
    final ranking = _calculateUserRanking(currentAccuracy);

    return {
      'currentAccuracy': currentAccuracy,
      'personalBest': personalBest,
      'improvement': improvement,
      'ranking': ranking,
      'totalQuestions': allPerformances.fold(0, (sum, p) => sum + p.questionsAnswered),
      'longestStreak': allPerformances.fold(0, (max, p) => p.streakCount > max ? p.streakCount : max),
    };
  }

  // Private helper methods

  bool _isWithinDateRange(DateTime date, DateRange range) {
    return date.isAfter(range.start.subtract(Duration(days: 1))) &&
           date.isBefore(range.end.add(Duration(days: 1)));
  }

  double _calculateOverallAccuracy(List<UserPerformance> performances) {
    if (performances.isEmpty) return 0.0;
    
    return performances
        .map((p) => p.accuracy)
        .reduce((a, b) => a + b) / performances.length;
  }

  Map<String, double> _calculateTopicPerformance(List<UserPerformance> performances) {
    final Map<String, List<double>> topicAccuracies = {};

    for (final performance in performances) {
      for (final entry in performance.topicAccuracy.entries) {
        final topic = entry.key;
        final correct = entry.value;
        final total = max(1, (performance.questionsAnswered * 0.2).round());
        final accuracy = correct / total;

        topicAccuracies.putIfAbsent(topic, () => []).add(accuracy);
      }
    }

    final Map<String, double> result = {};
    for (final entry in topicAccuracies.entries) {
      final accuracies = entry.value;
      result[entry.key] = accuracies.reduce((a, b) => a + b) / accuracies.length;
    }

    return result;
  }

  Map<int, double> _calculateDifficultyProgression(List<UserPerformance> performances) {
    final Map<int, List<double>> difficultyAccuracies = {};

    // This is a simplified version - in reality, we'd need to track
    // difficulty-specific performance more precisely
    for (final performance in performances) {
      // Estimate difficulty distribution based on accuracy
      final accuracy = performance.accuracy;
      int estimatedDifficulty;
      
      if (accuracy > 0.8) {
        estimatedDifficulty = 4;
      } else if (accuracy > 0.6) estimatedDifficulty = 3;
      else if (accuracy > 0.4) estimatedDifficulty = 2;
      else estimatedDifficulty = 1;

      difficultyAccuracies.putIfAbsent(estimatedDifficulty, () => []).add(accuracy);
    }

    final Map<int, double> result = {};
    for (final entry in difficultyAccuracies.entries) {
      final accuracies = entry.value;
      result[entry.key] = accuracies.reduce((a, b) => a + b) / accuracies.length;
    }

    return result;
  }

  List<WeeklyStats> _generateWeeklyProgress(List<UserPerformance> performances, DateRange range) {
    final weeklyStats = <WeeklyStats>[];
    final weeksInRange = range.end.difference(range.start).inDays ~/ 7;

    for (int i = 0; i < weeksInRange; i++) {
      final weekStart = range.start.add(Duration(days: i * 7));
      final weekEnd = weekStart.add(Duration(days: 6));

      final weekPerformances = performances
          .where((p) => p.lastUpdated.isAfter(weekStart) && 
                       p.lastUpdated.isBefore(weekEnd))
          .toList();

      if (weekPerformances.isNotEmpty) {
        final questionsAnswered = weekPerformances
            .fold(0, (sum, p) => sum + p.questionsAnswered);
        final accuracy = weekPerformances
            .map((p) => p.accuracy)
            .reduce((a, b) => a + b) / weekPerformances.length;
        final timeSpent = weekPerformances
            .fold(0.0, (sum, p) => sum + p.averageTimePerQuestion * p.questionsAnswered)
            .round();

        // Calculate topic distribution for the week
        final Map<String, int> topicDistribution = {};
        for (final performance in weekPerformances) {
          for (final topic in performance.topicAccuracy.keys) {
            topicDistribution[topic] = (topicDistribution[topic] ?? 0) + 1;
          }
        }

        weeklyStats.add(WeeklyStats(
          weekStart: weekStart,
          questionsAnswered: questionsAnswered,
          accuracy: accuracy,
          timeSpent: timeSpent,
          topicDistribution: topicDistribution,
        ));
      }
    }

    return weeklyStats;
  }

  StreakData _calculateStreakData(List<UserPerformance> performances) {
    if (performances.isEmpty) return StreakData();

    performances.sort((a, b) => a.lastUpdated.compareTo(b.lastUpdated));

    int currentStreak = 0;
    int bestStreak = 0;
    DateTime? lastActivity;
    final List<DateTime> streakDates = [];

    for (final performance in performances) {
      if (performance.streakCount > 0) {
        currentStreak = performance.streakCount;
        bestStreak = max(bestStreak, currentStreak);
        lastActivity = performance.lastUpdated;
        streakDates.add(performance.lastUpdated);
      }
    }

    return StreakData(
      currentStreak: currentStreak,
      bestStreak: bestStreak,
      lastActivity: lastActivity,
      streakDates: streakDates,
    );
  }

  TimeAnalytics _generateTimeAnalytics(List<UserPerformance> performances) {
    if (performances.isEmpty) return TimeAnalytics();

    final averageTimePerQuestion = performances
        .map((p) => p.averageTimePerQuestion)
        .reduce((a, b) => a + b) / performances.length;

    // Calculate total time spent
    final totalTimeSpent = performances
        .fold(0.0, (sum, p) => sum + p.averageTimePerQuestion * p.questionsAnswered)
        .round();

    // Generate mock difficulty time map and hourly performance
    // In a real implementation, this would be tracked more precisely
    final Map<int, double> difficultyTimeMap = {
      1: averageTimePerQuestion * 0.7,
      2: averageTimePerQuestion * 0.8,
      3: averageTimePerQuestion,
      4: averageTimePerQuestion * 1.2,
      5: averageTimePerQuestion * 1.5,
    };

    final Map<int, double> hourlyPerformance = {};
    for (int hour = 8; hour <= 22; hour++) {
      // Mock data - in reality, this would be tracked based on actual usage
      final performance = 0.6 + (sin((hour - 14) * pi / 8) * 0.2);
      hourlyPerformance[hour] = performance.clamp(0.0, 1.0);
    }

    return TimeAnalytics(
      averageTimePerQuestion: averageTimePerQuestion,
      difficultyTimeMap: difficultyTimeMap,
      hourlyPerformance: hourlyPerformance,
      totalTimeSpent: totalTimeSpent,
    );
  }

  List<TopicWeakness> _identifyWeakestTopics(
    List<UserPerformance> performances,
    Map<String, double> topicPerformance
  ) {
    final weaknesses = <TopicWeakness>[];

    for (final entry in topicPerformance.entries) {
      final topic = entry.key;
      final accuracy = entry.value;

      if (accuracy < 0.7) {
        final attemptCount = _calculateTopicAttemptCount(performances, topic);
        final improvementTrend = _calculateTopicImprovementTrend(
          performances.map((p) => p.topicAccuracy[topic]?.toDouble() ?? 0.0).toList()
        );
        final commonMistakes = _identifyCommonMistakes(topic, '');

        weaknesses.add(TopicWeakness(
          topic: topic,
          accuracy: accuracy,
          attemptCount: attemptCount,
          commonMistakes: commonMistakes,
          improvementTrend: improvementTrend,
        ));
      }
    }

    weaknesses.sort((a, b) => a.accuracy.compareTo(b.accuracy));
    return weaknesses;
  }

  List<String> _generateImprovementAreas(
    List<UserPerformance> performances,
    List<WeaknessArea> weaknesses
  ) {
    final areas = <String>[];

    // Add areas based on weakness analysis
    for (final weakness in weaknesses.take(3)) {
      areas.add(weakness.topicName);
    }

    // Add areas based on low accuracy
    final lowAccuracyPerformances = performances
        .where((p) => p.accuracy < 0.6)
        .toList();

    for (final performance in lowAccuracyPerformances.take(2)) {
      areas.add('${performance.categoryName} - ${performance.levelName}');
    }

    return areas.toSet().toList(); // Remove duplicates
  }

  LearningVelocity _calculateLearningVelocity(List<UserPerformance> performances, DateRange range) {
    if (performances.isEmpty) return LearningVelocity();

    final totalQuestions = performances.fold(0, (sum, p) => sum + p.questionsAnswered);
    final daysInRange = range.end.difference(range.start).inDays;
    final questionsPerDay = totalQuestions / max(1, daysInRange).toDouble();
    final questionsPerWeek = questionsPerDay * 7;

    final accuracyTrend = _calculateAccuracyTrend(performances);
    final activeDays = _calculateActiveDays(performances, range.start);

    return LearningVelocity(
      questionsPerDay: questionsPerDay,
      questionsPerWeek: questionsPerWeek,
      accuracyTrend: accuracyTrend,
      activeDays: activeDays,
    );
  }

  double _calculateTopicImprovementTrend(List<double> accuracies) {
    if (accuracies.length < 2) return 0.0;

    final firstHalf = accuracies.take(accuracies.length ~/ 2).toList();
    final secondHalf = accuracies.skip(accuracies.length ~/ 2).toList();

    final firstAvg = firstHalf.reduce((a, b) => a + b) / firstHalf.length;
    final secondAvg = secondHalf.reduce((a, b) => a + b) / secondHalf.length;

    return secondAvg - firstAvg;
  }

  List<String> _identifyCommonMistakes(String topic, String category) {
    // This would be populated from actual error analysis
    // For now, return some example mistakes based on topic
    final Map<String, List<String>> commonMistakes = {
      'TVA': ['Confusion entre taux réduit et normal', 'Oubli des exonérations'],
      'IS': ['Calcul incorrect des amortissements', 'Base imposable mal déterminée'],
      'IR': ['Barème non appliqué', 'Déductions oubliées'],
    };

    return commonMistakes[topic] ?? ['Erreurs de calcul', 'Concepts mal compris'];
  }

  int _calculateTopicAttemptCount(List<UserPerformance> performances, String topic) {
    return performances
        .where((p) => p.topicAccuracy.containsKey(topic))
        .fold(0, (sum, p) => sum + max(1, (p.questionsAnswered * 0.2).round()));
  }

  double _calculateAccuracyTrend(List<UserPerformance> performances) {
    if (performances.length < 2) return 0.0;

    performances.sort((a, b) => a.lastUpdated.compareTo(b.lastUpdated));

    final firstQuarter = performances.take(performances.length ~/ 4).toList();
    final lastQuarter = performances.skip((performances.length * 3) ~/ 4).toList();

    if (firstQuarter.isEmpty || lastQuarter.isEmpty) return 0.0;

    final firstAccuracy = firstQuarter
        .map((p) => p.accuracy)
        .reduce((a, b) => a + b) / firstQuarter.length;
    final lastAccuracy = lastQuarter
        .map((p) => p.accuracy)
        .reduce((a, b) => a + b) / lastQuarter.length;

    return lastAccuracy - firstAccuracy;
  }

  int _calculateActiveDays(List<UserPerformance> performances, DateTime since) {
    final activeDates = performances
        .where((p) => p.lastUpdated.isAfter(since))
        .map((p) => DateTime(p.lastUpdated.year, p.lastUpdated.month, p.lastUpdated.day))
        .toSet();

    return activeDates.length;
  }

  String _calculateUserRanking(double accuracy) {
    if (accuracy >= 0.9) return 'Expert';
    if (accuracy >= 0.8) return 'Avancé';
    if (accuracy >= 0.7) return 'Intermédiaire';
    if (accuracy >= 0.6) return 'Débutant avancé';
    return 'Débutant';
  }
}

// Date range helper class
class DateRange {
  final DateTime start;
  final DateTime end;

  DateRange({required this.start, required this.end});

  factory DateRange.lastWeek() {
    final now = DateTime.now();
    return DateRange(
      start: now.subtract(Duration(days: 7)),
      end: now,
    );
  }

  factory DateRange.lastMonth() {
    final now = DateTime.now();
    return DateRange(
      start: now.subtract(Duration(days: 30)),
      end: now,
    );
  }

  factory DateRange.lastThreeMonths() {
    final now = DateTime.now();
    return DateRange(
      start: now.subtract(Duration(days: 90)),
      end: now,
    );
  }

  factory DateRange.allTime() {
    return DateRange(
      start: DateTime(2020), // Far enough in the past
      end: DateTime.now(),
    );
  }
}
