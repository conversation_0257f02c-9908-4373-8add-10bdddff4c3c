import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

import '../models/accessibility/accessibility_preferences.dart';
import 'theme_service.dart';

/// Comprehensive accessibility service that manages user accessibility preferences,
/// provides semantic label generation, screen reader announcements, and keyboard navigation utilities.
class AccessibilityService extends ChangeNotifier {
  static const String _boxName = 'accessibility_preferences';
  static const String _preferencesKey = 'user_accessibility_preferences';
  
  late Box<AccessibilityPreferences> _box;
  AccessibilityPreferences _preferences = AccessibilityPreferences.defaultSettings();
  
  // Text-to-speech engine
  late FlutterTts _flutterTts;
  bool _ttsInitialized = false;
  
  // Device information
  late DeviceInfoPlugin _deviceInfo;
  bool _isTablet = false;
  bool _supportsHaptics = false;
  
  // Focus management
  final Map<String, FocusNode> _registeredFocusNodes = {};
  final Map<String, VoidCallback> _keyboardShortcuts = {};
  
  // Theme service reference for integration
  ThemeService? _themeService;

  /// Current accessibility preferences
  AccessibilityPreferences get preferences => _preferences;
  
  /// Whether screen reader is enabled
  bool get isScreenReaderEnabled => _preferences.screenReaderEnabled;
  
  /// Whether high contrast mode is enabled
  bool get isHighContrastEnabled => _preferences.highContrastMode;
  
  /// Whether keyboard navigation is enabled
  bool get isKeyboardNavigationEnabled => _preferences.keyboardNavigationEnabled;
  
  /// Current font scale factor
  double get fontScale => _preferences.fontSize;
  
  /// Whether reduced motion is enabled
  bool get isReducedMotionEnabled => _preferences.reducedMotion;
  
  /// Whether TTS is available and initialized
  bool get isTtsAvailable => _ttsInitialized;
  
  /// Whether device is a tablet (affects touch targets)
  bool get isTablet => _isTablet;

  /// Initialize the accessibility service
  Future<void> initialize() async {
    try {
      // Initialize Hive box
      await _initializeHiveBox();
      
      // Initialize device info
      await _initializeDeviceInfo();
      
      // Initialize TTS
      await _initializeTts();
      
      // Load preferences
      await _loadPreferences();
      
      // Setup platform-specific accessibility features
      await _setupPlatformAccessibility();
      
      debugPrint('AccessibilityService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing AccessibilityService: $e');
      // Use default preferences if initialization fails
      _preferences = AccessibilityPreferences.defaultSettings();
    }
  }

  /// Initialize Hive box for storing preferences
  Future<void> _initializeHiveBox() async {
    if (!Hive.isBoxOpen(_boxName)) {
      _box = await Hive.openBox<AccessibilityPreferences>(_boxName);
    } else {
      _box = Hive.box<AccessibilityPreferences>(_boxName);
    }
  }

  /// Initialize device information
  Future<void> _initializeDeviceInfo() async {
    _deviceInfo = DeviceInfoPlugin();
    
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        _isTablet = androidInfo.isPhysicalDevice && 
                   (androidInfo.displayMetrics.widthPx > 1000 || 
                    androidInfo.displayMetrics.heightPx > 1000);
        _supportsHaptics = androidInfo.version.sdkInt >= 26;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        _isTablet = iosInfo.model.toLowerCase().contains('ipad');
        _supportsHaptics = true; // Most iOS devices support haptics
      } else {
        _isTablet = false;
        _supportsHaptics = false;
      }
    } catch (e) {
      debugPrint('Error getting device info: $e');
      _isTablet = false;
      _supportsHaptics = false;
    }
  }

  /// Initialize text-to-speech
  Future<void> _initializeTts() async {
    try {
      _flutterTts = FlutterTts();
      
      // Configure TTS settings
      await _flutterTts.setLanguage('fr-FR'); // French for Moroccan accounting
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(0.8);
      await _flutterTts.setPitch(1.0);
      
      // Test TTS availability
      final languages = await _flutterTts.getLanguages;
      _ttsInitialized = languages != null && languages.isNotEmpty;
      
      if (_ttsInitialized) {
        debugPrint('TTS initialized successfully with ${languages?.length} languages');
      }
    } catch (e) {
      debugPrint('Error initializing TTS: $e');
      _ttsInitialized = false;
    }
  }

  /// Setup platform-specific accessibility features
  Future<void> _setupPlatformAccessibility() async {
    // Enable accessibility services on the platform
    if (Platform.isAndroid || Platform.isIOS) {
      // Platform-specific accessibility setup would go here
      // This might include registering for accessibility events
    }
  }

  /// Load accessibility preferences from storage
  Future<void> _loadPreferences() async {
    try {
      final stored = _box.get(_preferencesKey);
      if (stored != null) {
        _preferences = stored;
      } else {
        // Save default preferences
        await _savePreferences();
      }
    } catch (e) {
      debugPrint('Error loading accessibility preferences: $e');
      _preferences = AccessibilityPreferences.defaultSettings();
    }
  }

  /// Save accessibility preferences to storage
  Future<void> _savePreferences() async {
    try {
      await _box.put(_preferencesKey, _preferences);
    } catch (e) {
      debugPrint('Error saving accessibility preferences: $e');
    }
  }

  /// Set theme service reference for integration
  void setThemeService(ThemeService themeService) {
    _themeService = themeService;
  }

  /// Update font scale factor
  Future<void> updateFontScale(double scale) async {
    if (scale < 0.8 || scale > 2.0) {
      throw ArgumentError('Font scale must be between 0.8 and 2.0');
    }
    
    _preferences = _preferences.copyWith(fontSize: scale);
    await _savePreferences();
    
    // Trigger theme update if theme service is available
    _themeService?.notifyListeners();
    
    // Announce change to screen reader
    if (_preferences.screenReaderEnabled) {
      await announceToScreenReader(
        'Taille de police mise à jour: ${(scale * 100).round()}%'
      );
    }
    
    notifyListeners();
  }

  /// Toggle high contrast mode
  Future<void> toggleHighContrast() async {
    _preferences = _preferences.copyWith(
      highContrastMode: !_preferences.highContrastMode
    );
    await _savePreferences();
    
    // Trigger theme update
    _themeService?.notifyListeners();
    
    // Announce change to screen reader
    if (_preferences.screenReaderEnabled) {
      await announceToScreenReader(
        _preferences.highContrastMode 
          ? 'Mode contraste élevé activé'
          : 'Mode contraste élevé désactivé'
      );
    }
    
    notifyListeners();
  }

  /// Enable or disable screen reader support
  Future<void> enableScreenReader(bool enabled) async {
    _preferences = _preferences.copyWith(screenReaderEnabled: enabled);
    await _savePreferences();
    
    if (enabled) {
      await announceToScreenReader('Lecteur d\'écran activé');
    }
    
    notifyListeners();
  }

  /// Update keyboard navigation preference
  Future<void> updateKeyboardNavigation(bool enabled) async {
    _preferences = _preferences.copyWith(keyboardNavigationEnabled: enabled);
    await _savePreferences();
    
    if (_preferences.screenReaderEnabled) {
      await announceToScreenReader(
        enabled 
          ? 'Navigation clavier activée'
          : 'Navigation clavier désactivée'
      );
    }
    
    notifyListeners();
  }

  /// Update reduced motion preference
  Future<void> updateReducedMotion(bool enabled) async {
    _preferences = _preferences.copyWith(reducedMotion: enabled);
    await _savePreferences();
    
    if (_preferences.screenReaderEnabled) {
      await announceToScreenReader(
        enabled 
          ? 'Mouvement réduit activé'
          : 'Mouvement réduit désactivé'
      );
    }
    
    notifyListeners();
  }

  /// Update preferred navigation mode
  Future<void> updateNavigationMode(PreferredNavigationMode mode) async {
    _preferences = _preferences.copyWith(preferredNavigationMode: mode);
    await _savePreferences();
    notifyListeners();
  }

  /// Get current accessibility preferences
  AccessibilityPreferences getAccessibilityPreferences() {
    return _preferences;
  }

  /// Reset preferences to defaults
  Future<void> resetToDefaults() async {
    _preferences = AccessibilityPreferences.defaultSettings();
    await _savePreferences();
    
    // Trigger theme update
    _themeService?.notifyListeners();
    
    if (_preferences.screenReaderEnabled) {
      await announceToScreenReader('Paramètres d\'accessibilité réinitialisés');
    }
    
    notifyListeners();
  }

  // SEMANTIC LABEL GENERATION METHODS

  /// Generate semantic label for buttons
  String generateButtonLabel(String text, String context) {
    final baseLabel = text.trim();
    
    if (context.isNotEmpty) {
      return '$baseLabel, bouton, $context';
    }
    
    return '$baseLabel, bouton';
  }

  /// Generate semantic label for navigation items
  String generateNavigationLabel(String destination) {
    return 'Naviguer vers $destination';
  }

  /// Generate semantic label for content
  String generateContentLabel(String content, String type) {
    switch (type.toLowerCase()) {
      case 'header':
      case 'titre':
        return '$content, titre';
      case 'text':
      case 'texte':
        return content;
      case 'image':
        return content.isEmpty ? 'Image' : 'Image: $content';
      case 'link':
      case 'lien':
        return '$content, lien';
      case 'input':
      case 'saisie':
        return '$content, champ de saisie';
      case 'checkbox':
        return '$content, case à cocher';
      case 'radio':
        return '$content, bouton radio';
      case 'slider':
        return '$content, curseur';
      case 'progress':
        return '$content, barre de progression';
      default:
        return content;
    }
  }

  /// Generate label for quiz options
  String generateQuizOptionLabel(String option, int index, bool isSelected, bool isCorrect) {
    final letter = String.fromCharCode(65 + index); // A, B, C, D
    String label = 'Option $letter: $option';
    
    if (isSelected) {
      label += ', sélectionné';
    }
    
    if (isCorrect) {
      label += ', correct';
    }
    
    return label;
  }

  /// Generate label for form fields with validation
  String generateFormFieldLabel(String label, bool isRequired, String? error, String? hint) {
    String semanticLabel = label;
    
    if (isRequired) {
      semanticLabel += ', requis';
    }
    
    if (hint != null && hint.isNotEmpty) {
      semanticLabel += ', $hint';
    }
    
    if (error != null && error.isNotEmpty) {
      semanticLabel += ', erreur: $error';
    }
    
    return semanticLabel;
  }

  // SCREEN READER ANNOUNCEMENT METHODS

  /// Announce message to screen reader
  Future<void> announceToScreenReader(String message) async {
    if (!_preferences.screenReaderEnabled || message.isEmpty) return;
    
    try {
      // Use Flutter's SemanticsService for announcements
      await SemanticsService.announce(message, TextDirection.ltr);
      
      // Also use TTS if available and enabled
      if (_ttsInitialized && _preferences.screenReaderEnabled) {
        await _flutterTts.speak(message);
      }
    } catch (e) {
      debugPrint('Error announcing to screen reader: $e');
    }
  }

  /// Announce navigation change
  Future<void> announceNavigationChange(String destination) async {
    await announceToScreenReader('Navigation vers $destination');
  }

  /// Announce content loading
  Future<void> announceContentLoading() async {
    await announceToScreenReader('Chargement du contenu');
  }

  /// Announce content loaded
  Future<void> announceContentLoaded(String contentType) async {
    await announceToScreenReader('$contentType chargé');
  }

  /// Announce error
  Future<void> announceError(String error) async {
    await announceToScreenReader('Erreur: $error');
  }

  /// Announce success
  Future<void> announceSuccess(String message) async {
    await announceToScreenReader('Succès: $message');
  }

  // KEYBOARD SHORTCUT MANAGEMENT

  /// Register a keyboard shortcut
  void registerKeyboardShortcut(String shortcutId, VoidCallback callback) {
    _keyboardShortcuts[shortcutId] = callback;
  }

  /// Unregister a keyboard shortcut
  void unregisterKeyboardShortcut(String shortcutId) {
    _keyboardShortcuts.remove(shortcutId);
  }

  /// Execute keyboard shortcut
  bool executeKeyboardShortcut(String shortcutId) {
    final callback = _keyboardShortcuts[shortcutId];
    if (callback != null) {
      callback();
      return true;
    }
    return false;
  }

  /// Get all registered shortcuts
  Map<String, VoidCallback> get registeredShortcuts => Map.unmodifiable(_keyboardShortcuts);

  // FOCUS MANAGEMENT UTILITIES

  /// Register a focus node
  void registerFocusNode(String nodeId, FocusNode focusNode) {
    _registeredFocusNodes[nodeId] = focusNode;
  }

  /// Unregister a focus node
  void unregisterFocusNode(String nodeId) {
    _registeredFocusNodes.remove(nodeId);
  }

  /// Request focus for a specific node
  void requestFocus(String nodeId) {
    final focusNode = _registeredFocusNodes[nodeId];
    if (focusNode != null && focusNode.canRequestFocus) {
      focusNode.requestFocus();
    }
  }

  /// Get next focusable node in tab order
  FocusNode? getNextFocusNode(String currentNodeId) {
    final keys = _registeredFocusNodes.keys.toList();
    final currentIndex = keys.indexOf(currentNodeId);
    
    if (currentIndex >= 0 && currentIndex < keys.length - 1) {
      return _registeredFocusNodes[keys[currentIndex + 1]];
    }
    
    return null;
  }

  /// Get previous focusable node in tab order
  FocusNode? getPreviousFocusNode(String currentNodeId) {
    final keys = _registeredFocusNodes.keys.toList();
    final currentIndex = keys.indexOf(currentNodeId);
    
    if (currentIndex > 0) {
      return _registeredFocusNodes[keys[currentIndex - 1]];
    }
    
    return null;
  }

  // UTILITY METHODS

  /// Get scaled font size based on user preference
  double getScaledFontSize(double baseSize) {
    return _preferences.getScaledFontSize(baseSize);
  }

  /// Check if high contrast should be used
  bool shouldUseHighContrast() {
    return _preferences.shouldUseHighContrast();
  }

  /// Get minimum touch target size based on device and preferences
  double getMinimumTouchTargetSize() {
    double baseSize = _isTablet ? 48.0 : 44.0;
    return baseSize * _preferences.fontSize;
  }

  /// Provide haptic feedback if supported and enabled
  Future<void> provideHapticFeedback() async {
    if (_supportsHaptics && !_preferences.reducedMotion) {
      try {
        await HapticFeedback.vibrate();
      } catch (e) {
        debugPrint('Error providing haptic feedback: $e');
      }
    }
  }

  /// Get accessibility context from BuildContext
  static AccessibilityService? of(BuildContext context) {
    try {
      return Provider.of<AccessibilityService>(context, listen: false);
    } catch (e) {
      debugPrint('AccessibilityService not found in context: $e');
      return null;
    }
  }

  /// Dispose resources
  @override
  void dispose() {
    _flutterTts.stop();
    _registeredFocusNodes.clear();
    _keyboardShortcuts.clear();
    super.dispose();
  }
}

/// Extension to provide accessibility helpers for BuildContext
extension AccessibilityContextExtension on BuildContext {
  /// Get accessibility service from context
  AccessibilityService? get accessibility => AccessibilityService.of(this);
  
  /// Check if screen reader is enabled
  bool get isScreenReaderEnabled => accessibility?.isScreenReaderEnabled ?? false;
  
  /// Check if high contrast is enabled
  bool get isHighContrastEnabled => accessibility?.isHighContrastEnabled ?? false;
  
  /// Get current font scale
  double get fontScale => accessibility?.fontScale ?? 1.0;
  
  /// Get scaled font size
  double getScaledFontSize(double baseSize) {
    return accessibility?.getScaledFontSize(baseSize) ?? baseSize;
  }
}