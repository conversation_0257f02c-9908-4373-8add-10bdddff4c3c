import 'dart:convert';
import 'package:flutter/services.dart';

class AgricultureService {
  static final AgricultureService _instance = AgricultureService._internal();
  factory AgricultureService() => _instance;
  AgricultureService._internal();

  Map<String, dynamic>? _tvaAgricoleData;
  Map<String, dynamic>? _isAgricoleData;
  Map<String, dynamic>? _irAgricoleData;
  Map<String, dynamic>? _comptabiliteAgricoleData;
  Map<String, dynamic>? _pratiquesAgricolesData;
  Map<String, dynamic>? _exercicesAgricolesData;
  Map<String, dynamic>? _calculateursAgricolesData;
  Map<String, dynamic>? _agricultureTaxRates;

  Future<void> initialize() async {
    await _loadAllData();
  }

  Future<void> _loadAllData() async {
    try {
      // Load TVA agricole data
      final tvaString = await rootBundle.loadString('assets/agriculture/tva_agricole_2025.json');
      _tvaAgricoleData = json.decode(tvaString);

      // Load IS agricole data
      final isString = await rootBundle.loadString('assets/agriculture/is_agricole_2025.json');
      _isAgricoleData = json.decode(isString);

      // Load IR agricole data
      final irString = await rootBundle.loadString('assets/agriculture/ir_agricole_2025.json');
      _irAgricoleData = json.decode(irString);

      // Load comptabilité agricole data
      final comptaString = await rootBundle.loadString('assets/agriculture/comptabilite_agricole.json');
      _comptabiliteAgricoleData = json.decode(comptaString);

      // Load pratiques agricoles data
      final pratiquesString = await rootBundle.loadString('assets/agriculture/pratiques_agricoles_2025.json');
      _pratiquesAgricolesData = json.decode(pratiquesString);

      // Load exercices agricoles data
      final exercicesString = await rootBundle.loadString('assets/agriculture/exercices_agricoles_2025.json');
      _exercicesAgricolesData = json.decode(exercicesString);

      // Load calculateurs agricoles data
      final calculateursString = await rootBundle.loadString('assets/agriculture/calculateurs_agricoles_2025.json');
      _calculateursAgricolesData = json.decode(calculateursString);

      // Load agriculture tax rates
      final taxRatesString = await rootBundle.loadString('assets/agriculture_tax_rates.json');
      _agricultureTaxRates = json.decode(taxRatesString);
    } catch (e) {
      // Handle loading errors gracefully
      print('Error loading agriculture data: $e');
    }
  }

  // TVA Agricole methods
  Map<String, dynamic>? getTvaAgricoleData() => _tvaAgricoleData;

  List<dynamic> getTvaAgricoleCategories() {
    return _tvaAgricoleData?['TVA_Agriculture_Maroc_2025']?['categories'] ?? [];
  }

  Map<String, dynamic>? getTvaAgricoleCategory(String categoryName) {
    final categories = getTvaAgricoleCategories();
    try {
      return categories.firstWhere(
        (category) => category['categorie'] == categoryName,
        orElse: () => null,
      );
    } catch (e) {
      return null;
    }
  }

  // IS Agricole methods
  Map<String, dynamic>? getIsAgricoleData() => _isAgricoleData;

  List<dynamic> getIsAgricoleRegimes() {
    return _isAgricoleData?['regimes_agricoles'] ?? [];
  }

  Map<String, dynamic>? getIsAgricoleRegime(String regimeName) {
    final regimes = getIsAgricoleRegimes();
    try {
      return regimes.firstWhere(
        (regime) => regime['name'] == regimeName,
        orElse: () => null,
      );
    } catch (e) {
      return null;
    }
  }

  double getCooperativeTax() => 17.5;
  double getMinimumTax() => 0.25;

  // IR Agricole methods
  Map<String, dynamic>? getIrAgricoleData() => _irAgricoleData;

  List<dynamic> getIrAgricoleMethodes() {
    return _irAgricoleData?['determination_revenu']?['methodes_evaluation'] ?? [];
  }

  List<dynamic> getIrAgricoleExonerations() {
    return _irAgricoleData?['exonerations_reductions']?['exonerations_temporaires'] ?? [];
  }

  // Calculate IR based on agricultural income
  double calculateAgricultureIR(double revenue, {int dependents = 0, bool isOrganic = false}) {
    if (revenue <= 0) return 0.0;
    if (_agricultureTaxRates == null) return 0.0;

    final irRates = _agricultureTaxRates!['agriculture_ir_rates'];

    // Apply family allowance using configurable values
    final familyAllowanceBase = irRates['family_allowance_base'].toDouble();
    final familyAllowancePerDependent = irRates['family_allowance_per_dependent'].toDouble();
    final familyAllowance = familyAllowanceBase + (dependents * familyAllowancePerDependent);
    double taxableIncome = revenue - familyAllowance;

    // Apply organic farming reduction if applicable
    if (isOrganic) {
      final organicReduction = irRates['organic_farming_reduction'].toDouble();
      taxableIncome *= organicReduction;
    }

    if (taxableIncome <= 0) return 0.0;

    // Apply progressive tax rates using configurable brackets
    double tax = 0.0;
    final brackets = irRates['tax_brackets'] as List;
    double remainingIncome = taxableIncome;

    for (final bracket in brackets) {
      final min = bracket['min'].toDouble();
      final max = bracket['max']?.toDouble();
      final rate = bracket['rate'].toDouble() / 100;

      if (remainingIncome <= 0) break;

      double taxableAmount;
      if (max == null) {
        taxableAmount = remainingIncome;
      } else {
        taxableAmount = remainingIncome.clamp(0.0, max - min).toDouble();
      }

      if (taxableAmount > 0) {
        tax += taxableAmount * rate;
        remainingIncome -= taxableAmount;
      }
    }

    return tax;
  }

  // Comptabilité Agricole methods
  Map<String, dynamic>? getComptabiliteAgricoleData() => _comptabiliteAgricoleData;

  Map<String, dynamic>? getPlanComptableAgricole() {
    return _comptabiliteAgricoleData?['plan_comptable_agricole'];
  }

  List<String> getAgricultureAccountingPrinciples() {
    final principles = _comptabiliteAgricoleData?['principes_generaux']?['specificites_secteur'];
    return principles != null ? List<String>.from(principles) : [];
  }

  // Pratiques Agricoles methods
  Map<String, dynamic>? getPratiquesAgricoles() => _pratiquesAgricolesData;

  List<dynamic> getPratiquesAgricolesCaseStudies() {
    return _pratiquesAgricolesData?['case_studies'] ?? [];
  }

  List<dynamic> getPratiquesAgricolesGuides() {
    return _pratiquesAgricolesData?['step_by_step_guides'] ?? [];
  }

  Map<String, dynamic>? getPratiquesAgricolesCaseStudy(String caseStudyId) {
    final caseStudies = getPratiquesAgricolesCaseStudies();
    try {
      return caseStudies.firstWhere(
        (caseStudy) => caseStudy['id'] == caseStudyId,
        orElse: () => null,
      );
    } catch (e) {
      return null;
    }
  }

  // Exercices Agricoles methods
  Map<String, dynamic>? getExercicesAgricoles() => _exercicesAgricolesData;

  List<dynamic> getExercicesAgricolesQuestions() {
    return _exercicesAgricolesData?['questions'] ?? [];
  }

  List<dynamic> getExercicesAgricolesByDifficulty(String difficulty) {
    final questions = getExercicesAgricolesQuestions();
    return questions.where((question) => question['difficulty'] == difficulty).toList();
  }

  List<dynamic> getExercicesAgricolesByCategory(String category) {
    final questions = getExercicesAgricolesQuestions();
    return questions.where((question) => question['category'] == category).toList();
  }

  Map<String, dynamic>? getExercicesAgricolesQuestion(String questionId) {
    final questions = getExercicesAgricolesQuestions();
    try {
      return questions.firstWhere(
        (question) => question['id'] == questionId,
        orElse: () => null,
      );
    } catch (e) {
      return null;
    }
  }

  // Calculateurs Agricoles methods
  Map<String, dynamic>? getCalculateursAgricoles() => _calculateursAgricolesData;

  List<dynamic> getCalculateursAgricolesConfigs() {
    return _calculateursAgricolesData?['calculators'] ?? [];
  }

  Map<String, dynamic>? getCalculateursAgricolesConfig(String calculatorType) {
    final calculators = getCalculateursAgricolesConfigs();
    try {
      return calculators.firstWhere(
        (calculator) => calculator['type'] == calculatorType,
        orElse: () => null,
      );
    } catch (e) {
      return null;
    }
  }

  List<String> getAvailableCalculatorTypes() {
    final calculators = getCalculateursAgricolesConfigs();
    return calculators.map<String>((calculator) => calculator['type'] as String).toList();
  }

  // Utility methods
  bool isCooperativeEligible(Map<String, dynamic> cooperativeData) {
    final requiredFields = ['statut_cooperatif', 'activite_agricole', 'membres_agriculteurs'];
    return requiredFields.every((field) => cooperativeData.containsKey(field));
  }

  double calculateCooperativeTax(double revenue) {
    if (_agricultureTaxRates == null) return 0.0;
    final isRates = _agricultureTaxRates!['agriculture_is_rates'];
    final rate = isRates['cooperative_tax_rate'].toDouble();
    return revenue * (rate / 100);
  }

  double calculateMinimumTax(double revenue) {
    if (_agricultureTaxRates == null) return 0.0;
    final isRates = _agricultureTaxRates!['agriculture_is_rates'];
    final minimumAmount = isRates['minimum_tax_amount'].toDouble();
    final rate = isRates['minimum_tax_rate'].toDouble();
    final calculatedTax = revenue * (rate / 100);
    return calculatedTax > minimumAmount ? calculatedTax : minimumAmount;
  }

  // Tax calculation helpers
  Map<String, dynamic> calculateAgricultureTaxSummary({
    required double revenue,
    required String entityType, // 'individual', 'cooperative', 'company'
    int dependents = 0,
    bool isOrganic = false,
    bool isNewCompany = false,
  }) {
    double ir = 0.0;
    double corporateTax = 0.0;
    double tva = 0.0;
    double minimumTax = 0.0;

    switch (entityType.toLowerCase()) {
      case 'individual':
        ir = calculateAgricultureIR(revenue, dependents: dependents, isOrganic: isOrganic);
        break;
      case 'cooperative':
        corporateTax = calculateCooperativeTax(revenue);
        minimumTax = calculateMinimumTax(revenue);
        break;
      case 'company':
        if (isNewCompany) {
          corporateTax = 0.0; // 5-year exemption
        } else {
          if (_agricultureTaxRates != null) {
            final isRates = _agricultureTaxRates!['agriculture_is_rates'];
            final rate = isRates['company_tax_rate'].toDouble();
            corporateTax = revenue * (rate / 100);
          }
        }
        minimumTax = calculateMinimumTax(revenue);
        break;
    }

    return {
      'revenue': revenue,
      'entity_type': entityType,
      'ir': ir,
      'is': corporateTax,
      'tva': tva,
      'minimum_tax': minimumTax,
      'total_tax': ir + corporateTax + tva + minimumTax,
      'effective_rate': revenue > 0 ? ((ir + corporateTax + tva + minimumTax) / revenue * 100) : 0.0,
    };
  }

  // Cache management
  void clearCache() {
    _tvaAgricoleData = null;
    _isAgricoleData = null;
    _irAgricoleData = null;
    _comptabiliteAgricoleData = null;
    _pratiquesAgricolesData = null;
    _exercicesAgricolesData = null;
    _calculateursAgricolesData = null;
  }

  // Reload all data
  Future<void> reloadData() async {
    clearCache();
    await _loadAllData();
  }
}
