import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/is/is_input_data.dart';
import '../models/is/is_calculation_result.dart';
import '../models/is/tax_bracket.dart';

class IsCalculatorService {
  // Cache for loaded data to improve performance
  static Map<String, dynamic>? _cachedTauxData;
  static Map<String, dynamic>? _cachedReintegrationData;

  static Future<Map<String, dynamic>> loadIsTaux() async {
    if (_cachedTauxData != null) {
      return _cachedTauxData!;
    }
    final String jsonString =
        await rootBundle.loadString('assets/is/is_taux_2025.json');
    _cachedTauxData = json.decode(jsonString);
    return _cachedTauxData!;
  }

  static Future<Map<String, dynamic>> loadIsReintegrations() async {
    if (_cachedReintegrationData != null) {
      return _cachedReintegrationData!;
    }
    final String jsonString =
        await rootBundle.loadString('assets/is/is_rein_deduction_2025.json');
    _cachedReintegrationData = json.decode(jsonString);
    return _cachedReintegrationData!;
  }

  static double calculateIS(
      double benefit, String sector, Map<String, dynamic> tauxData) {
    final regimes = tauxData['regimes'] as List<dynamic>? ?? [];
    final regime = regimes.firstWhere(
      (r) => r['name'] == sector,
      orElse: () => regimes.first,
    );

    final tranches = regime['tranches'] as List<dynamic>? ?? [];
    double totalTax = 0;
    double remainingBenefit = benefit;

    for (final tranche in tranches) {
      if (tranche == null) continue;

      // Safely convert numeric values
      final num minValue = tranche['min'] is num ? tranche['min'] as num : 0;
      final double min = minValue.toDouble();

      final dynamic maxValue = tranche['max'];
      final double? max = maxValue is num ? maxValue.toDouble() : null;

      final num rateValue = tranche['taux'] is num ? tranche['taux'] as num : 0;
      final double rate = rateValue.toDouble();

      if (remainingBenefit <= 0) break;

      double taxableBenefit;
      if (max == null) {
        // Last tranche
        taxableBenefit = remainingBenefit;
      } else {
        taxableBenefit = remainingBenefit.clamp(0, max - min);
      }

      totalTax += taxableBenefit * (rate / 100);
      remainingBenefit -= taxableBenefit;
    }

    return totalTax;
  }

  static double calculateCM(
    double revenue,
    String revenueType,
    Map<String, dynamic> tauxData,
    bool isExempted,
    bool isFirstYear,
  ) {
    if (isExempted || isFirstYear) return 0;

    final cm = tauxData['cotisation_minimale'] as Map<String, dynamic>? ?? {};
    final rates = cm['taux'] as Map<String, dynamic>? ?? {};

    // Safely get the rate value
    final dynamic rateValue = rates[revenueType];
    final double rate = rateValue is num ? rateValue.toDouble() : 0.0;

    double calculatedCM = revenue * (rate / 100);

    // Safely get the minimum value
    final dynamic minValue = cm['minimum'];
    final double minCM =
        minValue is num ? minValue.toDouble() : 3000.0; // Default to 3000 MAD

    return calculatedCM < minCM ? minCM : calculatedCM;
  }

  static double calculateReintegrations(
    Map<String, dynamic> values,
    Map<String, dynamic> reintegrationData,
  ) {
    double total = 0;
    final categories =
        reintegrationData['reintegrations'] as List<dynamic>? ?? [];

    for (final category in categories) {
      if (category == null) continue;
      final items = category['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item == null) continue;
        final String? id = item['id'] as String?;
        if (id != null && values.containsKey(id)) {
          final dynamic value = values[id];
          if (value == null) continue;

          // Safely convert value to double
          final double numValue = value is num
              ? value.toDouble()
              : double.tryParse(value.toString()) ?? 0.0;

          final String? formula = item['formula'] as String?;

          if (formula != null) {
            // Apply formula if exists
            switch (formula) {
              case 'excess_20000':
                if (numValue > 20000) {
                  total += numValue * 0.5; // 50% of amount exceeding 20,000
                }
                break;
              default:
                total += numValue;
            }
          } else {
            total += numValue;
          }
        }
      }
    }

    return total;
  }

  static double calculateDeductions(
    Map<String, dynamic> values,
    Map<String, dynamic> reintegrationData,
  ) {
    double total = 0;
    final deductions = reintegrationData['deductions'] as List<dynamic>? ?? [];

    for (final deduction in deductions) {
      if (deduction == null) continue;
      final items = deduction['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item == null) continue;
        final String? id = item['id'] as String?;
        if (id != null && values.containsKey(id)) {
          final dynamic value = values[id];
          if (value == null) continue;

          // Safely convert value to double
          final double numValue = value is num
              ? value.toDouble()
              : double.tryParse(value.toString()) ?? 0.0;

          total += numValue;
        }
      }
    }

    return total;
  }

  // ========== NEW REGIME INFORMATION METHODS ==========

  /// Extract regime names from JSON data
  static Future<List<String>> getAvailableRegimes() async {
    final tauxData = await loadIsTaux();
    final regimes = tauxData['regimes'] as List<dynamic>? ?? [];
    return regimes
        .where((regime) => regime != null && regime['name'] != null)
        .map((regime) => regime['name'] as String)
        .toList();
  }

  /// Get detailed regime information
  static Future<Map<String, dynamic>> getRegimeInfo(String regimeName) async {
    final tauxData = await loadIsTaux();
    final regimes = tauxData['regimes'] as List<dynamic>? ?? [];
    final regime = regimes.firstWhere(
      (r) => r != null && r['name'] == regimeName,
      orElse: () => null,
    );
    return regime as Map<String, dynamic>? ?? {};
  }

  /// Extract tax brackets for a specific regime
  static Future<List<TaxBracket>> getRegimeBrackets(String regimeName) async {
    final regimeInfo = await getRegimeInfo(regimeName);
    final tranches = regimeInfo['tranches'] as List<dynamic>? ?? [];
    
    return tranches
        .where((tranche) => tranche != null)
        .map((tranche) => TaxBracket.fromJson(tranche as Map<String, dynamic>))
        .toList();
  }

  /// Get user-friendly description for a regime
  static Future<String> getRegimeDescription(String regimeName) async {
    final regimeInfo = await getRegimeInfo(regimeName);
    return regimeInfo['description'] as String? ?? regimeName;
  }

  // ========== NEW ENHANCED CALCULATION METHODS ==========

  /// Comprehensive calculation using IsInputData
  static Future<IsCalculationResult> calculateComplete(IsInputData input) async {
    if (!input.isValid) {
      return IsCalculationResult.empty();
    }

    final tauxData = await loadIsTaux();
    final reintegrationData = await loadIsReintegrations();

    // Calculate taxable result
    final totalReintegrations = calculateReintegrations(
      input.reintegrations,
      reintegrationData,
    );
    final totalDeductions = calculateDeductions(
      input.deductions,
      reintegrationData,
    );
    final taxableResult = input.accountingResult + totalReintegrations - totalDeductions;

    // Calculate IS
    final isAmount = calculateIS(taxableResult, input.selectedRegime, tauxData);

    // Calculate CM
    final cmAmount = calculateCM(
      input.revenue,
      input.revenueType,
      tauxData,
      input.isCmExempted,
      input.isFirstYear,
    );

    // Total payable is the maximum of IS and CM
    final totalPayable = isAmount > cmAmount ? isAmount : cmAmount;

    // Get regime information
    final regimeInfo = await getRegimeInfo(input.selectedRegime);
    final brackets = await getRegimeBrackets(input.selectedRegime);

    // Calculate effective rate
    final effectiveRate = getEffectiveRate(totalPayable, taxableResult);

    // Generate analysis
    final fiscalAnalysis = generateFiscalAnalysis(IsCalculationResult(
      taxableResult: taxableResult,
      isAmount: isAmount,
      cmAmount: cmAmount,
      totalPayable: totalPayable,
      regimeName: input.selectedRegime,
      appliedRate: _getRegimeRate(regimeInfo),
      rateType: _getRateType(regimeInfo),
      appliedBrackets: brackets,
      reintegrationBreakdown: input.reintegrations,
      effectiveRate: effectiveRate,
      fiscalAnalysis: '',
      recommendations: [],
    ));

    // Generate recommendations
    final recommendations = getOptimizationRecommendations(IsCalculationResult(
      taxableResult: taxableResult,
      isAmount: isAmount,
      cmAmount: cmAmount,
      totalPayable: totalPayable,
      regimeName: input.selectedRegime,
      appliedRate: _getRegimeRate(regimeInfo),
      rateType: _getRateType(regimeInfo),
      appliedBrackets: brackets,
      reintegrationBreakdown: input.reintegrations,
      effectiveRate: effectiveRate,
      fiscalAnalysis: fiscalAnalysis,
      recommendations: [],
    ));

    return IsCalculationResult(
      taxableResult: taxableResult,
      isAmount: isAmount,
      cmAmount: cmAmount,
      totalPayable: totalPayable,
      regimeName: input.selectedRegime,
      appliedRate: _getRegimeRate(regimeInfo),
      rateType: _getRateType(regimeInfo),
      appliedBrackets: brackets,
      reintegrationBreakdown: input.reintegrations,
      effectiveRate: effectiveRate,
      fiscalAnalysis: fiscalAnalysis,
      recommendations: recommendations,
    );
  }

  /// Compare multiple regimes for the same input data
  static Future<List<IsCalculationResult>> compareRegimes(
    IsInputData input,
    List<String> regimes,
  ) async {
    final results = <IsCalculationResult>[];

    for (final regime in regimes) {
      final inputWithRegime = input.copyWith(selectedRegime: regime);
      final result = await calculateComplete(inputWithRegime);
      results.add(result);
    }

    // Sort by total payable amount (best to worst)
    results.sort((a, b) => a.totalPayable.compareTo(b.totalPayable));
    return results;
  }

  /// Calculate effective tax rate
  static double getEffectiveRate(double tax, double base) {
    if (base <= 0) return 0.0;
    return (tax / base) * 100;
  }

  // ========== NEW SECTOR ASSISTANT METHODS ==========

  /// Suggest optimal regime based on input characteristics
  static Future<String> suggestOptimalRegime(IsInputData input) async {
    final availableRegimes = await getApplicableRegimes(input);
    if (availableRegimes.isEmpty) {
      final allRegimes = await getAvailableRegimes();
      return allRegimes.isNotEmpty ? allRegimes.first : '';
    }

    // Compare all applicable regimes
    final results = await compareRegimes(input, availableRegimes);
    return results.isNotEmpty ? results.first.regimeName : availableRegimes.first;
  }

  /// Filter regimes based on input characteristics
  static Future<List<String>> getApplicableRegimes(IsInputData input) async {
    final allRegimes = await getAvailableRegimes();
    final applicableRegimes = <String>[];

    for (final regime in allRegimes) {
      final conditions = await getRegimeConditions(regime);
      if (_meetsConditions(input, conditions)) {
        applicableRegimes.add(regime);
      }
    }

    return applicableRegimes.isNotEmpty ? applicableRegimes : allRegimes;
  }

  /// Get eligibility conditions for a regime
  static Future<Map<String, dynamic>> getRegimeConditions(String regimeName) async {
    final regimeInfo = await getRegimeInfo(regimeName);
    return regimeInfo['conditions'] as Map<String, dynamic>? ?? {};
  }

  // ========== NEW ANALYSIS METHODS ==========

  /// Generate fiscal analysis for business insights
  static String generateFiscalAnalysis(IsCalculationResult result) {
    final analysis = StringBuffer();

    // Basic analysis
    analysis.writeln('Analyse fiscale pour le régime ${result.regimeName}:');
    analysis.writeln();

    // Tax burden analysis
    if (result.effectiveRate > 0) {
      analysis.writeln('• Taux effectif d\'imposition: ${result.formattedEffectiveRate}');
      
      if (result.effectiveRate < 10) {
        analysis.writeln('• Charge fiscale relativement faible');
      } else if (result.effectiveRate > 25) {
        analysis.writeln('• Charge fiscale élevée - optimisation recommandée');
      } else {
        analysis.writeln('• Charge fiscale modérée');
      }
    }

    // IS vs CM analysis
    if (result.isAmount > result.cmAmount) {
      analysis.writeln('• L\'IS est supérieur à la CM - régime normal applicable');
    } else if (result.cmAmount > 0) {
      analysis.writeln('• La CM est applicable (IS insuffisant)');
      analysis.writeln('• Considérer des stratégies d\'optimisation du résultat');
    }

    // Reintegration analysis
    final totalReintegrations = result.reintegrationBreakdown.values
        .fold(0.0, (sum, value) => sum + value);
    if (totalReintegrations > 0) {
      analysis.writeln('• Réintégrations totales: ${totalReintegrations.toStringAsFixed(2)} DH');
      if (totalReintegrations > result.taxableResult * 0.1) {
        analysis.writeln('• Réintégrations importantes - vérifier la conformité');
      }
    }

    return analysis.toString();
  }

  /// Get optimization recommendations for tax planning
  static List<String> getOptimizationRecommendations(IsCalculationResult result) {
    final recommendations = <String>[];

    // High effective rate recommendations
    if (result.effectiveRate > 25) {
      recommendations.add('Considérer des investissements déductibles pour réduire la base imposable');
      recommendations.add('Examiner les possibilités de report de charges');
    }

    // CM vs IS recommendations
    if (result.cmAmount > result.isAmount && result.cmAmount > 0) {
      recommendations.add('La cotisation minimale s\'applique - optimiser le résultat comptable');
      recommendations.add('Considérer le report de certaines charges sur l\'exercice suivant');
    }

    // First year recommendations
    if (result.regimeName.contains('première') || result.regimeName.contains('nouveau')) {
      recommendations.add('Profiter des avantages de la première année d\'activité');
      recommendations.add('Planifier la transition vers le régime normal');
    }

    // High reintegrations recommendations
    final totalReintegrations = result.reintegrationBreakdown.values
        .fold(0.0, (sum, value) => sum + value);
    if (totalReintegrations > result.taxableResult * 0.15) {
      recommendations.add('Réintégrations importantes détectées - vérifier la documentation');
      recommendations.add('Considérer une révision des pratiques comptables');
    }

    // General recommendations
    if (result.totalPayable > 100000) {
      recommendations.add('Envisager un étalement du paiement si éligible');
      recommendations.add('Planifier la trésorerie pour les échéances fiscales');
    }

    return recommendations;
  }

  // ========== PRIVATE HELPER METHODS ==========

  /// Get the main tax rate for a regime
  static double _getRegimeRate(Map<String, dynamic> regimeInfo) {
    final tranches = regimeInfo['tranches'] as List<dynamic>? ?? [];
    if (tranches.isNotEmpty) {
      final firstTranche = tranches.first;
      if (firstTranche != null) {
        final num rateValue = firstTranche['taux'] is num ? firstTranche['taux'] as num : 0;
        return rateValue.toDouble();
      }
    }
    return 0.0;
  }

  /// Determine if the regime uses progressive or fixed rates
  static String _getRateType(Map<String, dynamic> regimeInfo) {
    final tranches = regimeInfo['tranches'] as List<dynamic>? ?? [];
    return tranches.length > 1 ? 'progressive' : 'fixed';
  }

  /// Check if input meets regime conditions
  static bool _meetsConditions(IsInputData input, Map<String, dynamic> conditions) {
    // Basic eligibility check - can be expanded based on actual conditions in JSON
    if (conditions.isEmpty) return true;

    // Check revenue limits
    if (conditions.containsKey('max_revenue')) {
      final maxRevenue = (conditions['max_revenue'] as num?)?.toDouble() ?? double.infinity;
      if (input.revenue > maxRevenue) return false;
    }

    if (conditions.containsKey('min_revenue')) {
      final minRevenue = (conditions['min_revenue'] as num?)?.toDouble() ?? 0.0;
      if (input.revenue < minRevenue) return false;
    }

    // Check sector restrictions
    if (conditions.containsKey('excluded_sectors')) {
      final excludedSectors = conditions['excluded_sectors'] as List<dynamic>? ?? [];
      if (excludedSectors.contains(input.revenueType)) return false;
    }

    return true;
  }

  /// Clear cached data (useful for testing or data refresh)
  static void clearCache() {
    _cachedTauxData = null;
    _cachedReintegrationData = null;
  }
}
