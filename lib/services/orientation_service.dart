import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Service to manage device orientation for different app contexts
/// Provides methods to force specific orientations and restore defaults
class OrientationService {
  static final OrientationService _instance = OrientationService._internal();
  factory OrientationService() => _instance;
  OrientationService._internal();

  // Store the original orientations to restore later
  List<DeviceOrientation>? _originalOrientations;
  bool _isExamModeActive = false;

  /// Initialize the service with default orientations
  void initialize() {
    _originalOrientations = [
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ];
  }

  /// Force landscape orientation for exam mode (Android/iOS only)
  Future<void> enableExamMode() async {
    if (!_shouldManageOrientation()) return;

    _isExamModeActive = true;
    
    try {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      
      debugPrint('OrientationService: Enabled exam mode (landscape only)');
    } catch (e) {
      debugPrint('OrientationService: Error enabling exam mode: $e');
    }
  }

  /// Restore default orientation settings
  Future<void> disableExamMode() async {
    if (!_shouldManageOrientation()) return;

    _isExamModeActive = false;
    
    try {
      await SystemChrome.setPreferredOrientations(
        _originalOrientations ?? [
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ],
      );
      
      debugPrint('OrientationService: Disabled exam mode (all orientations restored)');
    } catch (e) {
      debugPrint('OrientationService: Error disabling exam mode: $e');
    }
  }

  /// Force portrait orientation for specific screens
  Future<void> enablePortraitMode() async {
    if (!_shouldManageOrientation()) return;

    try {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      
      debugPrint('OrientationService: Enabled portrait mode');
    } catch (e) {
      debugPrint('OrientationService: Error enabling portrait mode: $e');
    }
  }

  /// Allow all orientations (default behavior)
  Future<void> enableAllOrientations() async {
    if (!_shouldManageOrientation()) return;

    try {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      
      debugPrint('OrientationService: Enabled all orientations');
    } catch (e) {
      debugPrint('OrientationService: Error enabling all orientations: $e');
    }
  }

  /// Check if currently in exam mode
  bool get isExamModeActive => _isExamModeActive;

  /// Check if orientation management should be applied
  /// Only manage orientation on mobile devices (Android/iOS)
  bool _shouldManageOrientation() {
    return Platform.isAndroid || Platform.isIOS;
  }

  /// Get current platform info for debugging
  String get platformInfo {
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    return 'Unknown';
  }

  /// Dispose and cleanup
  void dispose() {
    if (_isExamModeActive) {
      disableExamMode();
    }
  }
}

/// Extension to easily access orientation service from BuildContext
extension OrientationServiceExtension on BuildContext {
  OrientationService get orientationService => OrientationService();
}

/// Widget wrapper that automatically manages orientation for its child
class OrientationWrapper extends StatefulWidget {
  final Widget child;
  final List<DeviceOrientation>? preferredOrientations;
  final bool restoreOnDispose;

  const OrientationWrapper({
    super.key,
    required this.child,
    this.preferredOrientations,
    this.restoreOnDispose = true,
  });

  @override
  State<OrientationWrapper> createState() => _OrientationWrapperState();
}

class _OrientationWrapperState extends State<OrientationWrapper> {
  @override
  void initState() {
    super.initState();
    if (widget.preferredOrientations != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        SystemChrome.setPreferredOrientations(widget.preferredOrientations!);
      });
    }
  }

  @override
  void dispose() {
    if (widget.restoreOnDispose) {
      OrientationService().enableAllOrientations();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
