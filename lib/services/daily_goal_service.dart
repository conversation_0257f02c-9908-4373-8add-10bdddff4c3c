// DailyGoalService for Moroccan Accounting App
// Manages daily study goals, progress, streaks, notifications, and integration with UserProgressService.

import 'package:hive/hive.dart';
import '../models/gamification/daily_goal_data.dart';

class DailyGoalService {
  final Box dailyGoalBox;

  DailyGoalService(this.dailyGoalBox);

  // Sets a new daily goal
  void setDailyGoal(DailyGoalData goal) {
    dailyGoalBox.put(goal.goalDate.toIso8601String(), goal);
  }

  // Updates progress for the current daily goal
  void updateGoalProgress(DateTime date, int value) {
    final key = date.toIso8601String();
    final goal = dailyGoalBox.get(key) as DailyGoalData?;
    if (goal != null) {
      goal.addProgress(value);
      dailyGoalBox.put(key, goal);
      if (goal.isGoalMet()) {
        notifyGoalCompleted(goal);
      }
    }
  }

  // Checks if the daily goal is completed
  bool checkGoalCompletion(DateTime date) {
    final key = date.toIso8601String();
    final goal = dailyGoalBox.get(key) as DailyGoalData?;
    return goal?.isGoalMet() ?? false;
  }

  // Resets daily goals (for new day)
  void resetDailyGoals(DateTime newDate) {
    final key = newDate.toIso8601String();
    final goal = dailyGoalBox.get(key) as DailyGoalData?;
    if (goal != null) {
      goal.resetDaily(newDate);
      dailyGoalBox.put(key, goal);
    }
  }

  // Gets goal statistics
  Map<String, dynamic> getGoalStatistics() {
    int totalGoals = dailyGoalBox.length;
    int completedGoals = dailyGoalBox.values.where((g) => (g as DailyGoalData).isCompleted).length;
    int bestStreak = dailyGoalBox.values.map((g) => (g as DailyGoalData).bestStreak).fold(0, (a, b) => a > b ? a : b);
    return {
      'totalGoals': totalGoals,
      'completedGoals': completedGoals,
      'bestStreak': bestStreak,
    };
  }

  // Goal recommendation engine (placeholder)
  DailyGoalData recommendGoal(DateTime date) {
    // TODO: Implement recommendation logic based on user activity
    return DailyGoalData(
      goalType: DailyGoalType.timeBased,
      targetValue: 30,
      goalDate: date,
    );
  }

  // Notification logic (placeholder)
  void notifyGoalCompleted(DailyGoalData goal) {
    // TODO: Implement goal completion notification
    // e.g. showGoalCompletionNotification(goal);
  }
}

// End of daily_goal_service.dart
