import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/is/is_exercices_avances.dart';

class IsExercicesAvancesService {
  static final IsExercicesAvancesService _instance =
      IsExercicesAvancesService._internal();
  IsExercicesAvances? _cachedData;

  factory IsExercicesAvancesService() {
    return _instance;
  }

  IsExercicesAvancesService._internal();

  Future<IsExercicesAvances> getExercicesAvances() async {
    if (_cachedData != null) {
      return _cachedData!;
    }

    final jsonString =
        await rootBundle.loadString('assets/is/exercices_avances.json');
    final jsonData = json.decode(jsonString) as Map<String, dynamic>;
    _cachedData = IsExercicesAvances.fromJson(jsonData);
    return _cachedData!;
  }

  void clearCache() {
    _cachedData = null;
  }
}
