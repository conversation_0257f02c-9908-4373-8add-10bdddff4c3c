import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/guide/guide_section_data.dart';
import 'offline_cache_service.dart';

/// Cache status enumeration
enum CacheStatus {
  notCached,
  cached,
  expired,
  updating,
  error,
}

/// Content complexity levels for accessibility
enum ContentComplexity {
  beginner,
  intermediate,
  advanced,
  expert,
}

/// Accessibility metadata for content
class AccessibilityMetadata {
  final int estimatedReadTime;
  final ContentComplexity complexity;
  final List<String> headingHierarchy;
  final Map<String, String> alternativeTexts;
  final List<String> semanticStructure;
  final bool hasFormulas;
  final bool hasCalculators;
  final bool hasInteractiveElements;
  final String readingLevel;
  final List<String> keyTerms;

  AccessibilityMetadata({
    required this.estimatedReadTime,
    required this.complexity,
    required this.headingHierarchy,
    required this.alternativeTexts,
    required this.semanticStructure,
    required this.hasFormulas,
    required this.hasCalculators,
    required this.hasInteractiveElements,
    required this.readingLevel,
    required this.keyTerms,
  });

  Map<String, dynamic> toJson() => {
    'estimatedReadTime': estimatedReadTime,
    'complexity': complexity.name,
    'headingHierarchy': headingHierarchy,
    'alternativeTexts': alternativeTexts,
    'semanticStructure': semanticStructure,
    'hasFormulas': hasFormulas,
    'hasCalculators': hasCalculators,
    'hasInteractiveElements': hasInteractiveElements,
    'readingLevel': readingLevel,
    'keyTerms': keyTerms,
  };

  factory AccessibilityMetadata.fromJson(Map<String, dynamic> json) => AccessibilityMetadata(
    estimatedReadTime: json['estimatedReadTime'] ?? 0,
    complexity: ContentComplexity.values.firstWhere(
      (e) => e.name == json['complexity'],
      orElse: () => ContentComplexity.beginner,
    ),
    headingHierarchy: List<String>.from(json['headingHierarchy'] ?? []),
    alternativeTexts: Map<String, String>.from(json['alternativeTexts'] ?? {}),
    semanticStructure: List<String>.from(json['semanticStructure'] ?? []),
    hasFormulas: json['hasFormulas'] ?? false,
    hasCalculators: json['hasCalculators'] ?? false,
    hasInteractiveElements: json['hasInteractiveElements'] ?? false,
    readingLevel: json['readingLevel'] ?? 'intermediate',
    keyTerms: List<String>.from(json['keyTerms'] ?? []),
  );
}

/// Enhanced service for loading and parsing guide content with offline caching and accessibility features
class GuideContentService {
  static final GuideContentService _instance = GuideContentService._internal();
  factory GuideContentService() => _instance;
  GuideContentService._internal();

  // Services
  final OfflineCacheService _cacheService = OfflineCacheService();

  // Cache for loaded content
  final Map<String, List<GuideSectionData>> _contentCache = {};
  final Map<String, Map<String, List<String>>> _searchIndex = {};
  final Map<String, AccessibilityMetadata> _accessibilityMetadata = {};
  final Map<String, CacheStatus> _cacheStatus = {};
  final Map<String, DateTime> _lastUpdated = {};

  // Background sync timer
  Timer? _backgroundSyncTimer;
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _cacheService.initialize();
      _startBackgroundSync();
      _isInitialized = true;
      debugPrint('GuideContentService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing GuideContentService: $e');
    }
  }

  /// Preload content for multiple guides
  Future<void> preloadContent(List<String> guideIds) async {
    debugPrint('Preloading content for guides: $guideIds');
    
    for (final guideId in guideIds) {
      try {
        _cacheStatus[guideId] = CacheStatus.updating;
        await _cacheService.cacheGuideContent(guideId);
        _cacheStatus[guideId] = CacheStatus.cached;
        _lastUpdated[guideId] = DateTime.now();
        debugPrint('Preloaded content for guide: $guideId');
      } catch (e) {
        _cacheStatus[guideId] = CacheStatus.error;
        debugPrint('Error preloading content for $guideId: $e');
      }
    }
  }

  /// Get cache status for a guide
  CacheStatus getCacheStatus(String guideId) {
    if (_cacheStatus.containsKey(guideId)) {
      final status = _cacheStatus[guideId]!;
      
      // Check if cached content is expired
      if (status == CacheStatus.cached) {
        final cacheKey = 'guide_$guideId';
        if (!_cacheService.isCacheValid(cacheKey)) {
          _cacheStatus[guideId] = CacheStatus.expired;
          return CacheStatus.expired;
        }
      }
      
      return status;
    }
    
    // Check offline cache
    final cacheKey = 'guide_$guideId';
    if (_cacheService.isCacheValid(cacheKey)) {
      _cacheStatus[guideId] = CacheStatus.cached;
      return CacheStatus.cached;
    }
    
    return CacheStatus.notCached;
  }

  /// Update cached content for all guides
  Future<void> updateCachedContent() async {
    if (!await _cacheService.isOnline()) {
      debugPrint('Cannot update cached content: device is offline');
      return;
    }

    debugPrint('Updating cached content...');
    
    final cachedGuides = _cacheStatus.keys.where((guideId) => 
      _cacheStatus[guideId] == CacheStatus.cached || 
      _cacheStatus[guideId] == CacheStatus.expired
    ).toList();

    for (final guideId in cachedGuides) {
      try {
        _cacheStatus[guideId] = CacheStatus.updating;
        await _cacheService.cacheGuideContent(guideId);
        _cacheStatus[guideId] = CacheStatus.cached;
        _lastUpdated[guideId] = DateTime.now();
        
        // Clear memory cache to force reload with updated content
        _contentCache.remove(guideId);
        _searchIndex.remove(guideId);
        _accessibilityMetadata.remove(guideId);
        
      } catch (e) {
        _cacheStatus[guideId] = CacheStatus.error;
        debugPrint('Error updating cached content for $guideId: $e');
      }
    }
    
    debugPrint('Cached content update completed');
  }

  /// Loads guide content from JSON files and converts to GuideSectionData objects
  Future<List<GuideSectionData>> loadGuideContent(String guideId) async {
    // Check memory cache first
    if (_contentCache.containsKey(guideId)) {
      return _contentCache[guideId]!;
    }

    try {
      String? contentString;
      
      // Try to load from offline cache first
      final cacheKey = 'guide_$guideId';
      contentString = await _cacheService.getCachedContent(cacheKey);
      
      if (contentString == null) {
        // Fall back to assets
        final contentPath = _getContentPath(guideId);
        contentString = await rootBundle.loadString(contentPath);
        
        // Cache the content for future offline use
        if (await _cacheService.isOnline()) {
          unawaited(_cacheService.cacheGuideContent(guideId));
        }
      }

      final contentJson = json.decode(contentString) as Map<String, dynamic>;

      // Parse JSON to sections
      final sections = parseJsonToSections(contentJson, guideId);
      
      // Cache the result in memory
      _contentCache[guideId] = sections;
      
      // Build search index
      _buildSearchIndex(guideId, sections);
      
      // Generate accessibility metadata
      _generateAccessibilityMetadata(guideId, sections);
      
      // Update cache status
      _cacheStatus[guideId] = CacheStatus.cached;
      _lastUpdated[guideId] = DateTime.now();
      
      return sections;
    } catch (e) {
      debugPrint('Error loading guide content for $guideId: $e');
      _cacheStatus[guideId] = CacheStatus.error;
      return [];
    }
  }

  /// Parses JSON content into GuideSectionData objects
  List<GuideSectionData> parseJsonToSections(
    Map<String, dynamic> json, 
    String guideId,
  ) {
    final sections = <GuideSectionData>[];
    
    // Handle different JSON structures
    if (json.containsKey('sections')) {
      // Standard structure with sections array
      final sectionsJson = json['sections'] as List<dynamic>;
      for (int i = 0; i < sectionsJson.length; i++) {
        final sectionJson = sectionsJson[i] as Map<String, dynamic>;
        final section = _parseSingleSection(sectionJson, guideId, i);
        sections.add(section);
      }
    } else if (json.containsKey('tabs')) {
      // Tab-based structure (legacy)
      final tabsJson = json['tabs'] as List<dynamic>;
      for (int i = 0; i < tabsJson.length; i++) {
        final tabJson = tabsJson[i] as Map<String, dynamic>;
        final section = _parseTabAsSection(tabJson, guideId, i);
        sections.add(section);
      }
    } else {
      // Direct content structure
      final section = _parseSingleSection(json, guideId, 0);
      sections.add(section);
    }
    
    return sections;
  }

  /// Parses a single section from JSON with accessibility enhancements
  GuideSectionData _parseSingleSection(
    Map<String, dynamic> json, 
    String guideId, 
    int index,
  ) {
    final id = json['id'] ?? '${guideId}_section_$index';
    final title = json['title'] ?? 'Section ${index + 1}';
    final content = json['content'] ?? '';
    final items = List<String>.from(json['items'] ?? []);
    
    // Parse subsections if they exist
    final subsections = <GuideSubsection>[];
    if (json.containsKey('subsections')) {
      final subsectionsJson = json['subsections'] as List<dynamic>;
      for (final subsectionJson in subsectionsJson) {
        subsections.add(GuideSubsection.fromJson(subsectionJson));
      }
    }
    
    // Enhanced metadata with accessibility information
    final enhancedMetadata = <String, dynamic>{
      ...?json['metadata'],
      'accessibilityLevel': _determineAccessibilityLevel(content, items),
      'hasInteractiveElements': _hasInteractiveElements(json),
      'semanticStructure': _extractSemanticStructure(content, items, subsections),
      'keyTerms': _extractKeyTerms(content, items),
      'readingComplexity': _calculateReadingComplexity(content, items),
    };
    
    return GuideSectionData(
      id: id,
      title: title,
      content: content,
      items: items,
      subsections: subsections,
      type: _parseContentType(json['type']),
      estimatedReadTime: json['estimatedReadTime'] ?? _calculateReadTime(content, items),
      metadata: enhancedMetadata,
    );
  }

  /// Parses a tab structure as a section (for legacy compatibility)
  GuideSectionData _parseTabAsSection(
    Map<String, dynamic> json, 
    String guideId, 
    int index,
  ) {
    final id = json['id'] ?? '${guideId}_tab_$index';
    final title = json['title'] ?? json['label'] ?? 'Tab ${index + 1}';
    
    // Extract content from tab structure
    String content = '';
    List<String> items = [];
    
    if (json.containsKey('content')) {
      if (json['content'] is String) {
        content = json['content'];
      } else if (json['content'] is Map) {
        final contentMap = json['content'] as Map<String, dynamic>;
        content = contentMap['text'] ?? '';
        items = List<String>.from(contentMap['items'] ?? []);
      }
    }
    
    return GuideSectionData(
      id: id,
      title: title,
      content: content,
      items: items,
      type: _parseContentType(json['type']),
      estimatedReadTime: _calculateReadTime(content, items),
    );
  }

  /// Builds search index for efficient searching
  void _buildSearchIndex(String guideId, List<GuideSectionData> sections) {
    final index = <String, List<String>>{};
    
    for (final section in sections) {
      final words = _extractSearchableWords(section);
      for (final word in words) {
        if (word.length > 2) { // Only index words longer than 2 characters
          index.putIfAbsent(word, () => []).add(section.id);
        }
      }
    }
    
    _searchIndex[guideId] = index;
  }

  /// Enhanced search with offline support and accessibility features
  Future<List<GuideSectionData>> searchSections(String guideId, String query, {
    bool includeAccessibilityInfo = false,
    ContentComplexity? maxComplexity,
  }) async {
    // Ensure content is loaded
    final sections = await loadGuideContent(guideId);
    final index = _searchIndex[guideId] ?? {};
    
    if (query.isEmpty) {
      return _filterByComplexity(sections, maxComplexity);
    }
    
    final queryWords = query.toLowerCase().split(' ')
        .where((word) => word.length > 2)
        .toList();
    
    final matchingSectionIds = <String>{};
    
    // Find sections that match any query word
    for (final word in queryWords) {
      // Exact matches
      if (index.containsKey(word)) {
        matchingSectionIds.addAll(index[word]!);
      }
      
      // Partial matches (fuzzy search)
      for (final indexWord in index.keys) {
        if (indexWord.contains(word) || word.contains(indexWord)) {
          matchingSectionIds.addAll(index[indexWord]!);
        }
      }
      
      // Search in accessibility metadata
      final metadata = _accessibilityMetadata[guideId];
      if (metadata != null) {
        for (final term in metadata.keyTerms) {
          if (term.toLowerCase().contains(word)) {
            // Find sections containing this term
            for (final section in sections) {
              if (section.content.toLowerCase().contains(term.toLowerCase()) ||
                  section.title.toLowerCase().contains(term.toLowerCase())) {
                matchingSectionIds.add(section.id);
              }
            }
          }
        }
      }
    }
    
    // Filter and return matching sections
    final matchingSections = sections
        .where((section) => matchingSectionIds.contains(section.id))
        .toList();
    
    return _filterByComplexity(matchingSections, maxComplexity);
  }

  /// Search across all cached content (offline search)
  Future<Map<String, List<GuideSectionData>>> searchAllCachedContent(String query, {
    ContentComplexity? maxComplexity,
  }) async {
    final results = <String, List<GuideSectionData>>{};
    
    // Search in all cached guides
    for (final guideId in _contentCache.keys) {
      final sections = await searchSections(
        guideId, 
        query, 
        maxComplexity: maxComplexity,
      );
      if (sections.isNotEmpty) {
        results[guideId] = sections;
      }
    }
    
    // Also search in offline cache if not in memory
    final cacheStats = await _cacheService.getCacheStatistics();
    for (final guideId in ['ir', 'is', 'comptabilite_generale', 'droits_enregistrement']) {
      if (!_contentCache.containsKey(guideId) && 
          _cacheService.isCacheValid('guide_$guideId')) {
        try {
          final sections = await searchSections(
            guideId, 
            query, 
            maxComplexity: maxComplexity,
          );
          if (sections.isNotEmpty) {
            results[guideId] = sections;
          }
        } catch (e) {
          debugPrint('Error searching cached content for $guideId: $e');
        }
      }
    }
    
    return results;
  }

  /// Extracts searchable words from a section
  List<String> _extractSearchableWords(GuideSectionData section) {
    final words = <String>[];
    
    // Add words from title, content, and items
    words.addAll(_splitIntoWords(section.title));
    words.addAll(_splitIntoWords(section.content));
    
    for (final item in section.items) {
      words.addAll(_splitIntoWords(item));
    }
    
    // Add words from subsections
    for (final subsection in section.subsections) {
      words.addAll(_splitIntoWords(subsection.title));
      words.addAll(_splitIntoWords(subsection.content));
      for (final item in subsection.items) {
        words.addAll(_splitIntoWords(item));
      }
    }
    
    return words.map((word) => word.toLowerCase()).toList();
  }

  /// Splits text into individual words
  List<String> _splitIntoWords(String text) {
    return text
        .replaceAll(RegExp(r'[^\w\s]'), ' ') // Replace punctuation with spaces
        .split(RegExp(r'\s+')) // Split on whitespace
        .where((word) => word.isNotEmpty)
        .toList();
  }

  /// Gets the content file path for a guide
  String _getContentPath(String guideId) {
    // Map guide IDs to their content file paths
    switch (guideId) {
      case 'ir':
        return 'assets/ir/presentation.json';
      case 'is':
        return 'assets/is/presentation.json';
      case 'comptabilite_generale':
        return 'assets/compta_generale/introduction.json';
      case 'droits_enregistrement':
        return 'assets/fiscalite/droits_enregistrement.json';
      default:
        return 'assets/$guideId/content.json';
    }
  }

  /// Parses content type from string
  GuideSectionType _parseContentType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'list':
        return GuideSectionType.list;
      case 'calculator':
        return GuideSectionType.calculator;
      case 'example':
        return GuideSectionType.example;
      case 'definition':
        return GuideSectionType.definition;
      case 'formula':
        return GuideSectionType.formula;
      case 'table':
        return GuideSectionType.table;
      case 'widget':
        return GuideSectionType.widget;
      default:
        return GuideSectionType.text;
    }
  }

  /// Get accessibility metadata for a guide
  AccessibilityMetadata? getAccessibilityMetadata(String guideId) {
    return _accessibilityMetadata[guideId];
  }

  /// Get content freshness information
  DateTime? getLastUpdated(String guideId) {
    return _lastUpdated[guideId];
  }

  /// Check if content needs refresh
  bool needsRefresh(String guideId) {
    final lastUpdated = _lastUpdated[guideId];
    if (lastUpdated == null) return true;
    
    final age = DateTime.now().difference(lastUpdated);
    return age.inDays > 7; // Refresh content older than 7 days
  }

  /// Generate alternative text for images and interactive elements
  String generateAlternativeText(String elementType, Map<String, dynamic>? metadata) {
    switch (elementType.toLowerCase()) {
      case 'calculator':
        return 'Calculateur interactif pour ${metadata?['purpose'] ?? 'calculs fiscaux'}';
      case 'formula':
        return 'Formule mathématique: ${metadata?['description'] ?? 'calcul fiscal'}';
      case 'table':
        return 'Tableau de données: ${metadata?['title'] ?? 'informations fiscales'}';
      case 'chart':
        return 'Graphique: ${metadata?['description'] ?? 'données visuelles'}';
      case 'example':
        return 'Exemple pratique: ${metadata?['scenario'] ?? 'cas d\'usage'}';
      default:
        return 'Élément interactif: ${metadata?['description'] ?? elementType}';
    }
  }

  /// Validate heading hierarchy for screen readers
  List<String> validateHeadingHierarchy(List<GuideSectionData> sections) {
    final issues = <String>[];
    int previousLevel = 0;
    
    for (final section in sections) {
      // Assume section titles are H2 level
      const currentLevel = 2;
      
      if (previousLevel > 0 && currentLevel > previousLevel + 1) {
        issues.add('Heading level skip detected in section: ${section.title}');
      }
      
      previousLevel = currentLevel;
      
      // Check subsections
      for (final subsection in section.subsections) {
        const subsectionLevel = 3;
        if (subsectionLevel > currentLevel + 1) {
          issues.add('Heading level skip in subsection: ${subsection.title}');
        }
      }
    }
    
    return issues;
  }

  /// Calculates estimated read time based on content length with accessibility considerations
  int _calculateReadTime(String content, List<String> items) {
    final wordCount = content.split(' ').length + 
                     items.fold(0, (sum, item) => sum + item.split(' ').length);
    
    // Adjust reading speed based on content complexity
    int wordsPerMinute = 200; // Default reading speed
    
    // Slower reading for complex content
    if (content.contains(RegExp(r'\d+%|\d+\.\d+|€|\$'))) {
      wordsPerMinute = 150; // Financial content with numbers
    }
    if (content.contains(RegExp(r'[A-Z]{2,}'))) {
      wordsPerMinute = 120; // Content with many acronyms
    }
    
    return (wordCount / wordsPerMinute).ceil().clamp(1, 45);
  }

  /// Generate accessibility metadata for content
  void _generateAccessibilityMetadata(String guideId, List<GuideSectionData> sections) {
    final headingHierarchy = <String>[];
    final alternativeTexts = <String, String>{};
    final semanticStructure = <String>[];
    final keyTerms = <String>{};
    
    bool hasFormulas = false;
    bool hasCalculators = false;
    bool hasInteractiveElements = false;
    
    int totalReadTime = 0;
    int complexityScore = 0;
    
    for (final section in sections) {
      // Build heading hierarchy
      headingHierarchy.add('H2: ${section.title}');
      semanticStructure.add('section');
      
      // Check for interactive elements
      switch (section.type) {
        case GuideSectionType.calculator:
          hasCalculators = true;
          hasInteractiveElements = true;
          alternativeTexts[section.id] = generateAlternativeText('calculator', section.metadata);
          break;
        case GuideSectionType.formula:
          hasFormulas = true;
          alternativeTexts[section.id] = generateAlternativeText('formula', section.metadata);
          break;
        case GuideSectionType.table:
          alternativeTexts[section.id] = generateAlternativeText('table', section.metadata);
          break;
        case GuideSectionType.widget:
          hasInteractiveElements = true;
          break;
        default:
          break;
      }
      
      // Extract key terms
      keyTerms.addAll(_extractKeyTerms(section.content, section.items));
      
      // Add subsection headings
      for (final subsection in section.subsections) {
        headingHierarchy.add('H3: ${subsection.title}');
        semanticStructure.add('subsection');
        keyTerms.addAll(_extractKeyTerms(subsection.content, subsection.items));
      }
      
      totalReadTime += section.estimatedReadTime;
      complexityScore += _calculateSectionComplexity(section);
    }
    
    // Determine overall complexity
    final avgComplexity = complexityScore / sections.length;
    ContentComplexity complexity;
    if (avgComplexity < 2) {
      complexity = ContentComplexity.beginner;
    } else if (avgComplexity < 4) {
      complexity = ContentComplexity.intermediate;
    } else if (avgComplexity < 6) {
      complexity = ContentComplexity.advanced;
    } else {
      complexity = ContentComplexity.expert;
    }
    
    _accessibilityMetadata[guideId] = AccessibilityMetadata(
      estimatedReadTime: totalReadTime,
      complexity: complexity,
      headingHierarchy: headingHierarchy,
      alternativeTexts: alternativeTexts,
      semanticStructure: semanticStructure,
      hasFormulas: hasFormulas,
      hasCalculators: hasCalculators,
      hasInteractiveElements: hasInteractiveElements,
      readingLevel: _determineReadingLevel(sections),
      keyTerms: keyTerms.toList(),
    );
  }

  /// Extract key terms from content
  List<String> _extractKeyTerms(String content, List<String> items) {
    final terms = <String>{};
    final allText = '$content ${items.join(' ')}';
    
    // French fiscal terms
    final fiscalTerms = [
      'impôt', 'taxe', 'tva', 'ir', 'is', 'cnss', 'amo',
      'déclaration', 'déduction', 'abattement', 'exonération',
      'comptabilité', 'bilan', 'résultat', 'chiffre d\'affaires',
      'amortissement', 'provision', 'créance', 'dette',
    ];
    
    for (final term in fiscalTerms) {
      if (allText.toLowerCase().contains(term)) {
        terms.add(term);
      }
    }
    
    // Extract capitalized terms (likely important concepts)
    final capitalizedWords = RegExp(r'\b[A-Z][a-z]+\b').allMatches(allText);
    for (final match in capitalizedWords) {
      final word = match.group(0)!.toLowerCase();
      if (word.length > 3) {
        terms.add(word);
      }
    }
    
    return terms.toList();
  }

  /// Calculate section complexity score
  int _calculateSectionComplexity(GuideSectionData section) {
    int score = 1; // Base score
    
    // Add complexity based on content type
    switch (section.type) {
      case GuideSectionType.calculator:
      case GuideSectionType.formula:
        score += 3;
        break;
      case GuideSectionType.table:
        score += 2;
        break;
      case GuideSectionType.example:
        score += 1;
        break;
      default:
        break;
    }
    
    // Add complexity based on content characteristics
    if (section.content.contains(RegExp(r'\d+%|\d+\.\d+'))) score += 1; // Numbers
    if (section.content.contains(RegExp(r'[A-Z]{2,}'))) score += 1; // Acronyms
    if (section.subsections.isNotEmpty) score += 1; // Has subsections
    if (section.items.length > 5) score += 1; // Many items
    
    return score;
  }

  /// Determine reading level
  String _determineReadingLevel(List<GuideSectionData> sections) {
    int totalWords = 0;
    int totalSentences = 0;
    int totalSyllables = 0;
    
    for (final section in sections) {
      final text = '${section.content} ${section.items.join(' ')}';
      final words = text.split(RegExp(r'\s+'));
      final sentences = text.split(RegExp(r'[.!?]+'));
      
      totalWords += words.length;
      totalSentences += sentences.length;
      
      // Rough syllable estimation for French
      for (final word in words) {
        totalSyllables += _estimateSyllables(word);
      }
    }
    
    if (totalSentences == 0) return 'débutant';
    
    // Simplified readability score
    final avgWordsPerSentence = totalWords / totalSentences;
    final avgSyllablesPerWord = totalSyllables / totalWords;
    
    final score = avgWordsPerSentence + avgSyllablesPerWord;
    
    if (score < 12) return 'débutant';
    if (score < 16) return 'intermédiaire';
    if (score < 20) return 'avancé';
    return 'expert';
  }

  /// Estimate syllables in a French word
  int _estimateSyllables(String word) {
    if (word.length <= 3) return 1;
    
    final vowels = 'aeiouyàáâäèéêëìíîïòóôöùúûü';
    int syllables = 0;
    bool previousWasVowel = false;
    
    for (int i = 0; i < word.length; i++) {
      final char = word[i].toLowerCase();
      final isVowel = vowels.contains(char);
      
      if (isVowel && !previousWasVowel) {
        syllables++;
      }
      
      previousWasVowel = isVowel;
    }
    
    return syllables.clamp(1, word.length);
  }

  /// Determine accessibility level
  String _determineAccessibilityLevel(String content, List<String> items) {
    final allText = '$content ${items.join(' ')}';
    
    if (allText.length < 200) return 'simple';
    if (allText.length < 500) return 'modéré';
    if (allText.length < 1000) return 'complexe';
    return 'très complexe';
  }

  /// Check if content has interactive elements
  bool _hasInteractiveElements(Map<String, dynamic> json) {
    final type = json['type']?.toString().toLowerCase();
    return type == 'calculator' || 
           type == 'widget' || 
           json.containsKey('interactive') ||
           json.containsKey('calculator');
  }

  /// Extract semantic structure
  List<String> _extractSemanticStructure(
    String content, 
    List<String> items, 
    List<GuideSubsection> subsections,
  ) {
    final structure = <String>[];
    
    if (content.isNotEmpty) structure.add('paragraph');
    if (items.isNotEmpty) structure.add('list');
    if (subsections.isNotEmpty) structure.add('subsections');
    
    return structure;
  }

  /// Calculate reading complexity
  String _calculateReadingComplexity(String content, List<String> items) {
    final allText = '$content ${items.join(' ')}';
    final wordCount = allText.split(RegExp(r'\s+')).length;
    
    // Count complex indicators
    int complexityIndicators = 0;
    if (allText.contains(RegExp(r'\d+%'))) complexityIndicators++;
    if (allText.contains(RegExp(r'[A-Z]{2,}'))) complexityIndicators++;
    if (allText.contains(RegExp(r'€|\$'))) complexityIndicators++;
    
    final complexityRatio = complexityIndicators / (wordCount / 100);
    
    if (complexityRatio < 0.5) return 'simple';
    if (complexityRatio < 1.0) return 'modéré';
    if (complexityRatio < 2.0) return 'complexe';
    return 'très complexe';
  }

  /// Filter sections by complexity
  List<GuideSectionData> _filterByComplexity(
    List<GuideSectionData> sections, 
    ContentComplexity? maxComplexity,
  ) {
    if (maxComplexity == null) return sections;
    
    return sections.where((section) {
      final complexity = section.metadata?['readingComplexity'] as String?;
      switch (complexity) {
        case 'simple':
          return true;
        case 'modéré':
          return maxComplexity.index >= ContentComplexity.intermediate.index;
        case 'complexe':
          return maxComplexity.index >= ContentComplexity.advanced.index;
        case 'très complexe':
          return maxComplexity.index >= ContentComplexity.expert.index;
        default:
          return true;
      }
    }).toList();
  }

  /// Start background sync for content updates
  void _startBackgroundSync() {
    _backgroundSyncTimer = Timer.periodic(const Duration(hours: 6), (timer) async {
      if (await _cacheService.isOnline()) {
        await updateCachedContent();
      }
    });
  }

  /// Clears the content cache
  void clearCache() {
    _contentCache.clear();
    _searchIndex.clear();
    _accessibilityMetadata.clear();
    _cacheStatus.clear();
    _lastUpdated.clear();
  }

  /// Gets cached content without loading
  List<GuideSectionData>? getCachedContent(String guideId) {
    return _contentCache[guideId];
  }

  /// Dispose resources
  void dispose() {
    _backgroundSyncTimer?.cancel();
    clearCache();
  }
}
