import 'dart:async';
import 'package:hive/hive.dart';
import '../models/guide/personal_note_data.dart';

/// Service for managing personal notes with local storage
class NotesService {
  static const String _boxName = 'personal_notes';
  static Box<PersonalNoteData>? _box;
  
  final StreamController<List<PersonalNoteData>> _notesController = 
      StreamController<List<PersonalNoteData>>.broadcast();
  
  /// Stream of all notes
  Stream<List<PersonalNoteData>> get notesStream => _notesController.stream;
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_box == null) {
      _box = await Hive.openBox<PersonalNoteData>(_boxName);
      _notifyListeners();
    }
  }
  
  /// Get all notes
  Future<List<PersonalNoteData>> getAllNotes() async {
    await _ensureInitialized();
    return _box!.values.toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get notes for a specific guide
  Future<List<PersonalNoteData>> getNotesByGuide(String guideId) async {
    await _ensureInitialized();
    return _box!.values
        .where((note) => note.guideId == guideId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get notes for a specific section
  Future<List<PersonalNoteData>> getNotesBySection(String guideId, String sectionId) async {
    await _ensureInitialized();
    return _box!.values
        .where((note) => 
            note.guideId == guideId && 
            note.sectionId == sectionId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get notes by type
  Future<List<PersonalNoteData>> getNotesByType(NoteType type) async {
    await _ensureInitialized();
    return _box!.values
        .where((note) => note.noteType == type)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get notes by tags
  Future<List<PersonalNoteData>> getNotesByTags(List<String> tags) async {
    await _ensureInitialized();
    return _box!.values
        .where((note) => note.tags.any((tag) => tags.contains(tag)))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get private notes only
  Future<List<PersonalNoteData>> getPrivateNotes() async {
    await _ensureInitialized();
    return _box!.values
        .where((note) => note.isPrivate)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get public notes only
  Future<List<PersonalNoteData>> getPublicNotes() async {
    await _ensureInitialized();
    return _box!.values
        .where((note) => !note.isPrivate)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get notes with reminders
  Future<List<PersonalNoteData>> getNotesWithReminders() async {
    await _ensureInitialized();
    return _box!.values
        .where((note) => note.reminderDate != null)
        .toList()
      ..sort((a, b) => a.reminderDate!.compareTo(b.reminderDate!));
  }
  
  /// Get upcoming reminders
  Future<List<PersonalNoteData>> getUpcomingReminders({int daysAhead = 7}) async {
    await _ensureInitialized();
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: daysAhead));
    
    return _box!.values
        .where((note) => 
            note.reminderDate != null &&
            note.reminderDate!.isAfter(now) &&
            note.reminderDate!.isBefore(futureDate))
        .toList()
      ..sort((a, b) => a.reminderDate!.compareTo(b.reminderDate!));
  }
  
  /// Get overdue reminders
  Future<List<PersonalNoteData>> getOverdueReminders() async {
    await _ensureInitialized();
    final now = DateTime.now();
    
    return _box!.values
        .where((note) => 
            note.reminderDate != null &&
            note.reminderDate!.isBefore(now))
        .toList()
      ..sort((a, b) => a.reminderDate!.compareTo(b.reminderDate!));
  }
  
  /// Get a specific note by ID
  Future<PersonalNoteData?> getNote(String id) async {
    await _ensureInitialized();
    return _box!.get(id);
  }
  
  /// Add a new note
  Future<void> addNote(PersonalNoteData note) async {
    await _ensureInitialized();
    await _box!.put(note.id, note);
    _notifyListeners();
  }
  
  /// Update an existing note
  Future<void> updateNote(PersonalNoteData note) async {
    await _ensureInitialized();
    if (_box!.containsKey(note.id)) {
      note.lastModified = DateTime.now();
      await _box!.put(note.id, note);
      _notifyListeners();
    }
  }
  
  /// Delete a note by ID
  Future<void> deleteNote(String id) async {
    await _ensureInitialized();
    await _box!.delete(id);
    _notifyListeners();
  }
  
  /// Delete notes for a specific section
  Future<void> deleteNotesBySection(String guideId, String sectionId) async {
    await _ensureInitialized();
    final notesToDelete = _box!.values
        .where((note) => 
            note.guideId == guideId && 
            note.sectionId == sectionId)
        .map((note) => note.id)
        .toList();
    
    for (final id in notesToDelete) {
      await _box!.delete(id);
    }
    
    if (notesToDelete.isNotEmpty) {
      _notifyListeners();
    }
  }
  
  /// Clear all notes
  Future<void> clearAllNotes() async {
    await _ensureInitialized();
    await _box!.clear();
    _notifyListeners();
  }
  
  /// Search notes
  Future<List<PersonalNoteData>> searchNotes(String query) async {
    await _ensureInitialized();
    final lowerQuery = query.toLowerCase();
    
    return _box!.values
        .where((note) =>
            note.title.toLowerCase().contains(lowerQuery) ||
            note.content.toLowerCase().contains(lowerQuery) ||
            note.formattedContent.toLowerCase().contains(lowerQuery) ||
            note.tags.any((tag) => tag.toLowerCase().contains(lowerQuery)))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Get all unique tags
  Future<List<String>> getAllTags() async {
    await _ensureInitialized();
    final tags = <String>{};
    
    for (final note in _box!.values) {
      tags.addAll(note.tags);
    }
    
    return tags.toList()..sort();
  }
  
  /// Get note statistics
  Future<NoteStatistics> getStatistics() async {
    await _ensureInitialized();
    final notes = _box!.values.toList();
    
    final Map<String, int> guideCount = {};
    final Map<NoteType, int> typeCount = {};
    final Set<String> uniqueTags = {};
    int privateCount = 0;
    int reminderCount = 0;
    
    for (final note in notes) {
      // Count by guide
      guideCount[note.guideId] = (guideCount[note.guideId] ?? 0) + 1;
      
      // Count by type
      typeCount[note.noteType] = (typeCount[note.noteType] ?? 0) + 1;
      
      // Collect unique tags
      uniqueTags.addAll(note.tags);
      
      // Count private notes
      if (note.isPrivate) privateCount++;
      
      // Count notes with reminders
      if (note.reminderDate != null) reminderCount++;
    }
    
    return NoteStatistics(
      totalNotes: notes.length,
      privateNotes: privateCount,
      publicNotes: notes.length - privateCount,
      notesWithReminders: reminderCount,
      notesByGuide: guideCount,
      notesByType: typeCount,
      uniqueTags: uniqueTags.toList()..sort(),
      oldestNote: notes.isEmpty 
          ? null 
          : notes.reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b).createdAt,
      newestNote: notes.isEmpty 
          ? null 
          : notes.reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b).createdAt,
    );
  }
  
  /// Export notes to JSON
  Future<Map<String, dynamic>> exportNotes({bool includePrivate = false}) async {
    await _ensureInitialized();
    final notes = await getAllNotes();
    final exportNotes = includePrivate 
        ? notes 
        : notes.where((note) => !note.isPrivate).toList();
    
    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'includePrivate': includePrivate,
      'notes': exportNotes.map((note) => note.toJson()).toList(),
    };
  }
  
  /// Import notes from JSON
  Future<ImportResult> importNotes(Map<String, dynamic> data, {bool merge = true}) async {
    await _ensureInitialized();
    
    try {
      final notesList = data['notes'] as List<dynamic>?;
      if (notesList == null) {
        return ImportResult(success: false, message: 'Invalid data format');
      }
      
      final notes = notesList
          .map((json) => PersonalNoteData.fromJson(json as Map<String, dynamic>))
          .toList();
      
      int imported = 0;
      int skipped = 0;
      
      if (!merge) {
        await clearAllNotes();
      }
      
      for (final note in notes) {
        if (merge && _box!.containsKey(note.id)) {
          skipped++;
        } else {
          await _box!.put(note.id, note);
          imported++;
        }
      }
      
      _notifyListeners();
      
      return ImportResult(
        success: true,
        message: 'Import completed: $imported imported, $skipped skipped',
        importedCount: imported,
        skippedCount: skipped,
      );
    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Import failed: $e',
      );
    }
  }
  
  /// Get recent notes
  Future<List<PersonalNoteData>> getRecentNotes({int limit = 10}) async {
    await _ensureInitialized();
    final notes = await getAllNotes();
    return notes.take(limit).toList();
  }
  
  /// Get notes created in the last N days
  Future<List<PersonalNoteData>> getRecentNotesByDays(int days) async {
    await _ensureInitialized();
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    
    return _box!.values
        .where((note) => note.createdAt.isAfter(cutoffDate))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
  
  /// Auto-save a note (used for draft functionality)
  Future<void> autoSaveNote(PersonalNoteData note) async {
    await _ensureInitialized();
    // Create a draft version with auto-save flag
    final draftNote = PersonalNoteData(
      id: '${note.id}_draft',
      guideId: note.guideId,
      sectionId: note.sectionId,
      title: note.title,
      content: note.content,
      noteType: note.noteType,
      tags: note.tags,
      isPrivate: note.isPrivate,
      reminderDate: note.reminderDate,
      formattedContent: note.formattedContent,
    );
    draftNote.lastModified = DateTime.now();
    await _box!.put(draftNote.id, draftNote);
    // Don't notify listeners for auto-saves to avoid UI flicker
  }
  
  /// Get draft version of a note
  Future<PersonalNoteData?> getDraftNote(String originalId) async {
    await _ensureInitialized();
    return _box!.get('${originalId}_draft');
  }
  
  /// Delete draft version of a note
  Future<void> deleteDraftNote(String originalId) async {
    await _ensureInitialized();
    await _box!.delete('${originalId}_draft');
  }
  
  /// Private helper methods
  Future<void> _ensureInitialized() async {
    if (_box == null) {
      await initialize();
    }
  }
  
  void _notifyListeners() {
    if (_box != null) {
      // Filter out draft notes from regular notifications
      final regularNotes = _box!.values
          .where((note) => !note.id.endsWith('_draft'))
          .toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
      _notesController.add(regularNotes);
    }
  }
  
  /// Dispose of resources
  void dispose() {
    _notesController.close();
  }
}

/// Statistics about notes
class NoteStatistics {
  final int totalNotes;
  final int privateNotes;
  final int publicNotes;
  final int notesWithReminders;
  final Map<String, int> notesByGuide;
  final Map<NoteType, int> notesByType;
  final List<String> uniqueTags;
  final DateTime? oldestNote;
  final DateTime? newestNote;
  
  const NoteStatistics({
    required this.totalNotes,
    required this.privateNotes,
    required this.publicNotes,
    required this.notesWithReminders,
    required this.notesByGuide,
    required this.notesByType,
    required this.uniqueTags,
    this.oldestNote,
    this.newestNote,
  });
}

/// Result of import operation
class ImportResult {
  final bool success;
  final String message;
  final int importedCount;
  final int skippedCount;
  
  const ImportResult({
    required this.success,
    required this.message,
    this.importedCount = 0,
    this.skippedCount = 0,
  });
}