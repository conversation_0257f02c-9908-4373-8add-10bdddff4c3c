// LearningStreakService for Moroccan Accounting App
// Manages learning streaks, milestones, rewards, and integration with achievements.

import 'package:hive/hive.dart';
import '../models/gamification/learning_streak_data.dart';

class LearningStreakService {
  final Box streakBox;

  LearningStreakService(this.streakBox);

  // Records activity and updates streak
  void recordActivity(DateTime activityDate, StreakType streakType) {
    final key = streakType.name;
    final streak = streakBox.get(key) as LearningStreakData?;
    if (streak != null) {
      streak.incrementStreak(activityDate);
      streakBox.put(key, streak);
      if (isMilestone(streak.currentStreak)) {
        notifyStreakMilestone(streak.currentStreak);
      }
    } else {
      streakBox.put(
        key,
        LearningStreakData(
          currentStreak: 1,
          bestStreak: 1,
          lastActivityDate: activityDate,
          streakType: streakType,
          totalActiveDays: 1,
        ),
      );
    }
  }

  // Gets current streak for type
  int getCurrentStreak(StreakType streakType) {
    final streak = streakBox.get(streakType.name) as LearningStreakData?;
    return streak?.currentStreak ?? 0;
  }

  // Updates streak status (e.g. reset if missed day)
  void updateStreakStatus(StreakType streakType, DateTime today) {
    final streak = streakBox.get(streakType.name) as LearningStreakData?;
    if (streak != null && streak.getDaysUntilStreakBreak() == 0) {
      streak.resetStreak(today);
      streakBox.put(streakType.name, streak);
      notifyStreakBreak(streakType);
    }
  }

  // Gets streak history
  List<StreakPeriod> getStreakHistory(StreakType streakType) {
    final streak = streakBox.get(streakType.name) as LearningStreakData?;
    return streak?.streakHistory ?? [];
  }

  // Calculates streak rewards (bonus points)
  int calculateStreakRewards(StreakType streakType) {
    final streak = streakBox.get(streakType.name) as LearningStreakData?;
    if (streak == null) return 0;
    // Example: 5 points per day, bonus for milestones
    int points = streak.currentStreak * 5;
    for (final milestone in streak.getStreakMilestones()) {
      if (streak.currentStreak >= milestone) points += milestone * 2;
    }
    return points;
  }

  // Checks if streak is at a milestone
  bool isMilestone(int streakLength) {
    return [7, 30, 100].contains(streakLength);
  }

  // Notification logic (placeholder)
  void notifyStreakMilestone(int streakLength) {
    // TODO: Implement milestone notification
    // e.g. showStreakMilestoneNotification(streakLength);
  }

  void notifyStreakBreak(StreakType streakType) {
    // TODO: Implement streak break notification
    // e.g. showStreakBreakNotification(streakType);
  }

  // Streak recovery and freeze options (placeholder)
  void recoverStreak(StreakType streakType) {
    // TODO: Implement streak recovery logic
  }

  void freezeStreak(StreakType streakType) {
    // TODO: Implement streak freeze logic
  }
}

// End of learning_streak_service.dart
