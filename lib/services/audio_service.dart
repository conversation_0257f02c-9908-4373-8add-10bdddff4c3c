import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter/scheduler.dart';
import 'package:just_audio/just_audio.dart';
import 'audio_session_wrapper.dart';

/// Ensures that the given task runs on the main thread using a post-frame callback.
/// This is critical for just_audio operations that must run on the platform thread.
Future<T> runOnMainThread<T>(Future<T> Function() task) {
  // If we're already on the main thread, just run the task
  if (WidgetsBinding.instance.isRootWidgetAttached && !SchedulerBinding.instance.schedulerPhase.index.isEven) {
    return task();
  }
  
  final completer = Completer<T>();
  
  // Use a microtask to ensure we get onto the main thread as soon as possible
  scheduleMicrotask(() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final result = await task();
        if (!completer.isCompleted) {
          completer.complete(result);
        }
      } catch (e) {
        if (!completer.isCompleted) {
          completer.completeError(e);
        }
      }
    });
    
    // If no frames are being rendered (app in background), force a frame
    WidgetsBinding.instance.scheduleFrame();
  });
  
  return completer.future;
}

class AudioService {
  static final AudioService _instance = AudioService._internal();
  AudioPlayer? _player;
  bool _isInitialized = false;
  final AudioSessionWrapper _sessionWrapper = AudioSessionWrapper();

  factory AudioService() {
    return _instance;
  }

  AudioService._internal();

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _sessionWrapper.initialize();
      
      await runOnMainThread(() async {
        _player = AudioPlayer();
        // Initialize with a silent asset or the first sound
        if (kIsWeb) {
          // For web, we'll initialize when needed
          _isInitialized = true;
        } else {
          await _player?.setAsset('assets/sounds/correct.mp3');
          _isInitialized = true;
        }
        return null;
      });
    } catch (e) {
      debugPrint('Failed to initialize audio service: $e');
    }
  }

  Future<void> playSound(String assetPath) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      await _sessionWrapper.setActive(true);
      
      if (_player != null) {
        await _player!.stop();
        
        // Try different formats in order of preference
        List<String> formats = [
          assetPath.replaceAll('.mp3', '.webm'),  // Try WebM first
          assetPath.replaceAll('.mp3', '.ogg'),   // Then OGG
          assetPath                                // Finally MP3
        ];
        
        bool success = false;
        for (String format in formats) {
          try {
            await _player!.setAsset(format);
            await _player!.play();
            success = true;
            break;
          } catch (e) {
            debugPrint('Failed to play $format: $e');
            continue;
          }
        }
        
        if (!success) {
          throw Exception('No compatible audio format found');
        }
      }
    } catch (e) {
      debugPrint('Failed to play sound: $e');
    }
  }

  Future<void> dispose() async {
    if (_player != null) {
      await runOnMainThread(() async {
        await _player?.stop();
        await _player?.dispose();
        _player = null;
        _isInitialized = false;
        return null;
      });
      
      await _sessionWrapper.dispose();
    }
  }
}
