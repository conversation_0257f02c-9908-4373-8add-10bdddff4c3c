import 'dart:convert';
import 'package:flutter/material.dart'; // Import Material for IconData and Icons
import 'package:flutter/services.dart';
import '../models/quiz_model.dart'; 

class QuizDataService {
  QuizData? _quizData;
  bool _isInitialized = false;

  Future<void> loadQuizData() async {
    if (_isInitialized) return; // Load only once

    try {
      final String jsonString = await rootBundle.loadString('assets/quiz/quiz_data.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      _quizData = QuizData.fromJson(jsonData);
      _isInitialized = true;
      debugPrint('Quiz data loaded successfully.');
    } catch (e) {
      debugPrint('Error loading quiz data in Service: $e');
      // Handle error appropriately, maybe rethrow or set an error state
    }
  }

  QuizData? get quizData => _quizData;

  // Method to find a specific level - needed for resuming from profile
  QuizLevel? findQuizLevel(String categoryName, String levelName) {
    if (!_isInitialized || _quizData == null) {
      debugPrint('Quiz data not loaded, cannot find level.');
      return null; // Or throw an error
    }

    try {
      final category = _quizData!.categories.firstWhere(
        (cat) => cat.name == categoryName,
        orElse: () => throw Exception('Category not found: $categoryName'), // More specific error
      );
      final level = category.levels.firstWhere(
        (lvl) => lvl.level == levelName,
         orElse: () => throw Exception('Level not found: $levelName in category $categoryName'), // More specific error
      );
      return level;
    } catch (e) {
       debugPrint('Error finding quiz level ($categoryName - $levelName): $e');
       return null;
    }
  }

   // Method to get category color (needed for resuming from profile)
   Color? findCategoryColor(String categoryName) {
     if (!_isInitialized || _quizData == null) {
       debugPrint('Quiz data not loaded, cannot find category color.');
       return null; 
     }
     try {
       final category = _quizData!.categories.firstWhere(
         (cat) => cat.name == categoryName,
         orElse: () => throw Exception('Category not found: $categoryName'),
       );
       return category.getColor(); // Use the existing method in QuizCategory
     } catch (e) {
       debugPrint('Error finding category color for $categoryName: $e');
       return null;
     }
   }

   // Method to get category icon (needed for profile history)
   IconData? findCategoryIcon(String categoryName) {
     if (!_isInitialized || _quizData == null) {
       debugPrint('Quiz data not loaded, cannot find category icon.');
       return null; 
     }
     try {
       final category = _quizData!.categories.firstWhere(
         (cat) => cat.name == categoryName,
         orElse: () => throw Exception('Category not found: $categoryName'),
       );
       return category.getIconData(); // Use the existing method in QuizCategory
     } catch (e) {
       debugPrint('Error finding category icon for $categoryName: $e');
       return Icons.category; // Return a default icon on error
     }
   }
}
