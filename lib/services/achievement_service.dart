// AchievementService for Moroccan Accounting App
// Manages achievement unlocking, progress tracking, notifications, and integration with Hive and AdaptiveLearningService.
// References: adaptive_learning_service.dart, user_progress_service.dart

import 'package:hive/hive.dart';
import '../models/gamification/achievement_definitions.dart';

class AchievementService {
  final Box achievementBox;

  AchievementService(this.achievementBox);

  // Checks and unlocks achievements based on user progress
  void checkAndUnlockAchievements(Map<String, int> userProgress) {
    for (final achievement in achievementDefinitions) {
      final userValue = userProgress[achievement.id] ?? 0;
      if (isAchievementUnlocked(achievement.id, userValue)) {
        unlockAchievement(achievement.id);
        // Trigger notification and badge animation
        notifyAchievementUnlocked(achievement);
      }
    }
  }

  // Unlocks achievement and saves to Hive
  void unlockAchievement(String achievementId) {
    achievementBox.put(achievementId, true);
  }

  // Gets list of unlocked achievements
  List<AchievementDefinition> getUnlockedAchievements() {
    return achievementDefinitions.where((a) => achievementBox.get(a.id, defaultValue: false) == true).toList();
  }

  // Gets achievement progress for all achievements
  Map<String, double> getAchievementProgress(Map<String, int> userProgress) {
    final progress = <String, double>{};
    for (final achievement in achievementDefinitions) {
      final userValue = userProgress[achievement.id] ?? 0;
      progress[achievement.id] = calculateAchievementProgress(achievement.id, userValue);
    }
    return progress;
  }

  // Calculates total achievement points
  int calculateAchievementPoints() {
    return getUnlockedAchievements().fold(0, (sum, a) => sum + a.points);
  }

  // Gets achievements by category
  List<AchievementDefinition> getAchievementsByCategory(AchievementCategory category) {
    return achievementDefinitions.where((a) => a.category == category).toList();
  }

  // Notification logic (placeholder)
  void notifyAchievementUnlocked(AchievementDefinition achievement) {
    // TODO: Implement notification and badge animation triggers
    // e.g. showAchievementNotification(achievement);
  }

  // Event listener for user progress changes (to be hooked in UserProgressService)
  void onUserProgressChanged(Map<String, int> userProgress) {
    checkAndUnlockAchievements(userProgress);
  }
}

// End of achievement_service.dart
