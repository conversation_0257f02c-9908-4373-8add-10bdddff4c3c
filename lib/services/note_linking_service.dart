import 'dart:async';
import 'package:hive/hive.dart';
import '../models/guide/personal_note_data.dart';

/// Service for managing note links and relationships
class NoteLinkingService {
  static const String _linksBoxName = 'note_links';
  static const String _shareInfoBoxName = 'note_share_info';
  static Box<NoteLink>? _linksBox;
  static Box<NoteShareInfo>? _shareInfoBox;
  
  final StreamController<List<NoteLink>> _linksController = 
      StreamController<List<NoteLink>>.broadcast();
  final StreamController<List<NoteShareInfo>> _shareInfoController = 
      StreamController<List<NoteShareInfo>>.broadcast();
  
  /// Stream of all note links
  Stream<List<NoteLink>> get linksStream => _linksController.stream;
  
  /// Stream of all share info
  Stream<List<NoteShareInfo>> get shareInfoStream => _shareInfoController.stream;
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_linksBox == null) {
      _linksBox = await Hive.openBox<NoteLink>(_linksBoxName);
      _notifyLinksListeners();
    }
    if (_shareInfoBox == null) {
      _shareInfoBox = await Hive.openBox<NoteShareInfo>(_shareInfoBoxName);
      _notifyShareInfoListeners();
    }
  }
  
  /// Create a link between two notes
  Future<NoteLink> createLink({
    required String sourceNoteId,
    required String targetNoteId,
    required NoteLinkType linkType,
    String? description,
  }) async {
    await _ensureInitialized();
    
    final linkId = '${sourceNoteId}_${targetNoteId}_${DateTime.now().millisecondsSinceEpoch}';
    final link = NoteLink(
      id: linkId,
      sourceNoteId: sourceNoteId,
      targetNoteId: targetNoteId,
      linkType: linkType,
      description: description,
    );
    
    await _linksBox!.put(linkId, link);
    _notifyLinksListeners();
    
    return link;
  }
  
  /// Remove a link between notes
  Future<void> removeLink(String linkId) async {
    await _ensureInitialized();
    await _linksBox!.delete(linkId);
    _notifyLinksListeners();
  }
  
  /// Get all links for a specific note
  Future<List<NoteLink>> getLinksForNote(String noteId) async {
    await _ensureInitialized();
    final allLinks = _linksBox!.values.toList();
    return allLinks.where((link) => 
        link.sourceNoteId == noteId || link.targetNoteId == noteId).toList();
  }
  
  /// Get outgoing links from a note
  Future<List<NoteLink>> getOutgoingLinks(String noteId) async {
    await _ensureInitialized();
    final allLinks = _linksBox!.values.toList();
    return allLinks.where((link) => link.sourceNoteId == noteId).toList();
  }
  
  /// Get incoming links to a note
  Future<List<NoteLink>> getIncomingLinks(String noteId) async {
    await _ensureInitialized();
    final allLinks = _linksBox!.values.toList();
    return allLinks.where((link) => link.targetNoteId == noteId).toList();
  }
  
  /// Get links by type
  Future<List<NoteLink>> getLinksByType(NoteLinkType linkType) async {
    await _ensureInitialized();
    final allLinks = _linksBox!.values.toList();
    return allLinks.where((link) => link.linkType == linkType).toList();
  }
  
  /// Find connected notes (direct connections)
  Future<List<String>> getConnectedNoteIds(String noteId) async {
    await _ensureInitialized();
    final links = await getLinksForNote(noteId);
    final Set<String> connectedIds = {};
    
    for (final link in links) {
      if (link.sourceNoteId == noteId) {
        connectedIds.add(link.targetNoteId);
      } else {
        connectedIds.add(link.sourceNoteId);
      }
    }
    
    return connectedIds.toList();
  }
  
  /// Find notes within N degrees of separation
  Future<List<String>> getNotesWithinDegrees(String noteId, int degrees) async {
    if (degrees <= 0) return [];
    
    final Set<String> visited = {};
    final Set<String> currentLevel = {noteId};
    final Set<String> result = {};
    
    for (int i = 0; i < degrees; i++) {
      final Set<String> nextLevel = {};
      
      for (final currentNoteId in currentLevel) {
        if (visited.contains(currentNoteId)) continue;
        visited.add(currentNoteId);
        
        final connected = await getConnectedNoteIds(currentNoteId);
        for (final connectedId in connected) {
          if (!visited.contains(connectedId)) {
            nextLevel.add(connectedId);
            result.add(connectedId);
          }
        }
      }
      
      currentLevel.clear();
      currentLevel.addAll(nextLevel);
    }
    
    return result.toList();
  }
  
  /// Check if two notes are connected
  Future<bool> areNotesConnected(String noteId1, String noteId2) async {
    final links = await getLinksForNote(noteId1);
    return links.any((link) => 
        (link.sourceNoteId == noteId1 && link.targetNoteId == noteId2) ||
        (link.sourceNoteId == noteId2 && link.targetNoteId == noteId1));
  }
  
  /// Get link between two specific notes
  Future<NoteLink?> getLinkBetweenNotes(String noteId1, String noteId2) async {
    final links = await getLinksForNote(noteId1);
    try {
      return links.firstWhere((link) => 
          (link.sourceNoteId == noteId1 && link.targetNoteId == noteId2) ||
          (link.sourceNoteId == noteId2 && link.targetNoteId == noteId1));
    } catch (e) {
      return null;
    }
  }
  
  /// Create share info for a note
  Future<NoteShareInfo> createShareInfo({
    required String noteId,
    required String shareToken,
    bool isPublic = false,
    DateTime? expiresAt,
    List<String> allowedUsers = const [],
    bool allowComments = false,
    bool allowEditing = false,
  }) async {
    await _ensureInitialized();
    
    final shareInfoId = '${noteId}_${DateTime.now().millisecondsSinceEpoch}';
    final shareInfo = NoteShareInfo(
      id: shareInfoId,
      noteId: noteId,
      shareToken: shareToken,
      isPublic: isPublic,
      expiresAt: expiresAt,
      allowedUsers: allowedUsers,
      allowComments: allowComments,
      allowEditing: allowEditing,
    );
    
    await _shareInfoBox!.put(shareInfoId, shareInfo);
    _notifyShareInfoListeners();
    
    return shareInfo;
  }
  
  /// Get share info for a note
  Future<NoteShareInfo?> getShareInfoForNote(String noteId) async {
    await _ensureInitialized();
    final allShareInfo = _shareInfoBox!.values.toList();
    try {
      return allShareInfo.firstWhere((info) => info.noteId == noteId);
    } catch (e) {
      return null;
    }
  }
  
  /// Get share info by token
  Future<NoteShareInfo?> getShareInfoByToken(String token) async {
    await _ensureInitialized();
    final allShareInfo = _shareInfoBox!.values.toList();
    try {
      return allShareInfo.firstWhere((info) => info.shareToken == token);
    } catch (e) {
      return null;
    }
  }
  
  /// Remove share info
  Future<void> removeShareInfo(String shareInfoId) async {
    await _ensureInitialized();
    await _shareInfoBox!.delete(shareInfoId);
    _notifyShareInfoListeners();
  }
  
  /// Get all links
  Future<List<NoteLink>> getAllLinks() async {
    await _ensureInitialized();
    return _linksBox!.values.toList();
  }
  
  /// Get all share info
  Future<List<NoteShareInfo>> getAllShareInfo() async {
    await _ensureInitialized();
    return _shareInfoBox!.values.toList();
  }
  
  /// Private helper methods
  Future<void> _ensureInitialized() async {
    if (_linksBox == null || _shareInfoBox == null) {
      await initialize();
    }
  }
  
  void _notifyLinksListeners() {
    if (_linksBox != null) {
      _linksController.add(_linksBox!.values.toList());
    }
  }
  
  void _notifyShareInfoListeners() {
    if (_shareInfoBox != null) {
      _shareInfoController.add(_shareInfoBox!.values.toList());
    }
  }
  
  /// Get link statistics
  Future<NoteLinkStatistics> getLinkStatistics() async {
    await _ensureInitialized();
    final links = await getAllLinks();
    final shareInfo = await getAllShareInfo();

    final Map<NoteLinkType, int> linksByType = {};
    final Map<String, int> linksByNote = {};

    for (final link in links) {
      linksByType[link.linkType] = (linksByType[link.linkType] ?? 0) + 1;
      linksByNote[link.sourceNoteId] = (linksByNote[link.sourceNoteId] ?? 0) + 1;
      linksByNote[link.targetNoteId] = (linksByNote[link.targetNoteId] ?? 0) + 1;
    }

    final publicShares = shareInfo.where((info) => info.isPublic).length;
    final privateShares = shareInfo.length - publicShares;

    return NoteLinkStatistics(
      totalLinks: links.length,
      linksByType: linksByType,
      linksByNote: linksByNote,
      totalSharedNotes: shareInfo.length,
      publicShares: publicShares,
      privateShares: privateShares,
    );
  }

  /// Find orphaned notes (notes with no connections)
  Future<List<String>> findOrphanedNotes(List<String> allNoteIds) async {
    final connectedNoteIds = <String>{};
    final links = await getAllLinks();

    for (final link in links) {
      connectedNoteIds.add(link.sourceNoteId);
      connectedNoteIds.add(link.targetNoteId);
    }

    return allNoteIds.where((id) => !connectedNoteIds.contains(id)).toList();
  }

  /// Find hub notes (notes with many connections)
  Future<List<MapEntry<String, int>>> findHubNotes({int minConnections = 3}) async {
    final statistics = await getLinkStatistics();
    return statistics.linksByNote.entries
        .where((entry) => entry.value >= minConnections)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));
  }

  /// Export links to JSON
  Future<Map<String, dynamic>> exportLinks() async {
    final links = await getAllLinks();
    final shareInfo = await getAllShareInfo();

    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'links': links.map((link) => link.toJson()).toList(),
      'shareInfo': shareInfo.map((info) => info.toJson()).toList(),
    };
  }

  /// Import links from JSON
  Future<void> importLinks(Map<String, dynamic> data) async {
    await _ensureInitialized();

    if (data['links'] != null) {
      for (final linkData in data['links']) {
        final link = NoteLink.fromJson(linkData);
        await _linksBox!.put(link.id, link);
      }
    }

    if (data['shareInfo'] != null) {
      for (final shareData in data['shareInfo']) {
        final shareInfo = NoteShareInfo.fromJson(shareData);
        await _shareInfoBox!.put(shareInfo.id, shareInfo);
      }
    }

    _notifyLinksListeners();
    _notifyShareInfoListeners();
  }

  /// Generate a unique share token
  String generateShareToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 31) % 1000000;
    return 'share_${timestamp}_$random';
  }

  /// Validate share token format
  bool isValidShareToken(String token) {
    return token.startsWith('share_') && token.length > 10;
  }

  /// Clean up expired shares
  Future<void> cleanupExpiredShares() async {
    await _ensureInitialized();
    final now = DateTime.now();
    final allShareInfo = _shareInfoBox!.values.toList();

    for (final shareInfo in allShareInfo) {
      if (shareInfo.expiresAt != null && now.isAfter(shareInfo.expiresAt!)) {
        await _shareInfoBox!.delete(shareInfo.id);
      }
    }

    _notifyShareInfoListeners();
  }

  /// Dispose of resources
  void dispose() {
    _linksController.close();
    _shareInfoController.close();
  }
}

/// Statistics about note links
class NoteLinkStatistics {
  final int totalLinks;
  final Map<NoteLinkType, int> linksByType;
  final Map<String, int> linksByNote;
  final int totalSharedNotes;
  final int publicShares;
  final int privateShares;

  const NoteLinkStatistics({
    required this.totalLinks,
    required this.linksByType,
    required this.linksByNote,
    required this.totalSharedNotes,
    required this.publicShares,
    required this.privateShares,
  });

  /// Get the most connected note
  String? get mostConnectedNote {
    if (linksByNote.isEmpty) return null;
    return linksByNote.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Get average connections per note
  double get averageConnectionsPerNote {
    if (linksByNote.isEmpty) return 0.0;
    final totalConnections = linksByNote.values.fold(0, (sum, count) => sum + count);
    return totalConnections / linksByNote.length;
  }
}
