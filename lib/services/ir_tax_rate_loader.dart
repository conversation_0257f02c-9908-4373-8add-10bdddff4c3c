import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/rates/tax_rates.dart';

class IrTaxRateLoader {
  static IrTaxRateLoader? _instance;
  static IrTaxRateLoader get instance => _instance ??= IrTaxRateLoader._();
  
  IrTaxRateLoader._();

  List<IRBracket>? _monthlyBrackets;
  List<IRBracket>? _annualBrackets;
  bool _isLoaded = false;

  Future<List<IRBracket>> loadBrackets({bool isAnnual = false, int year = 2025}) async {
    try {
      await _ensureDataLoaded(year);
      return isAnnual ? _annualBrackets! : _monthlyBrackets!;
    } catch (e) {
      // Fallback to empty list on error
      return [];
    }
  }

  Future<void> _ensureDataLoaded(int year) async {
    if (_isLoaded) return;

    try {
      final String jsonString = await rootBundle.loadString('assets/ir/bareme.json');
      final Map<String, dynamic> data = json.decode(jsonString);
      
      final List<dynamic> categories = data['categories'] as List<dynamic>;
      
      // Find monthly and annual categories
      final monthlyCategory = categories.firstWhere(
        (cat) => cat['title'] == 'Barème Mensuel',
        orElse: () => null,
      );
      
      final annualCategory = categories.firstWhere(
        (cat) => cat['title'] == 'Barème Annuel',
        orElse: () => null,
      );

      if (monthlyCategory != null) {
        _monthlyBrackets = _parseTranches(monthlyCategory['tranches'] as List<dynamic>);
      }

      if (annualCategory != null) {
        _annualBrackets = _parseTranches(annualCategory['tranches'] as List<dynamic>);
      }

      _isLoaded = true;
    } catch (e) {
      // On error, initialize with empty lists
      _monthlyBrackets = [];
      _annualBrackets = [];
      _isLoaded = true;
      rethrow;
    }
  }

  List<IRBracket> _parseTranches(List<dynamic> tranches) {
    return tranches.map((tranche) {
      final Map<String, dynamic> t = tranche as Map<String, dynamic>;
      
      // Parse min value
      final double min = _parseAmount(t['min'] as String);
      
      // Parse max value (null for "Plus")
      final num? max = t['max'] == 'Plus' ? null : _parseAmount(t['max'] as String);
      
      // Parse rate (remove % and convert to decimal)
      final String rateStr = t['taux'] as String;
      final double rate = double.parse(rateStr.replaceAll('%', '')) / 100.0;
      
      // Parse deduction
      final double deduction = _parseAmount(t['deduction'] as String);

      return IRBracket(
        min: min,
        max: max?.toDouble(),
        rate: rate,
        deduction: deduction,
      );
    }).toList();
  }

  double _parseAmount(String amount) {
    // Remove spaces and convert to double
    return double.parse(amount.replaceAll(' ', ''));
  }

  Future<List<IRBracket>> getMonthlyBrackets({int year = 2025}) async {
    return loadBrackets(isAnnual: false, year: year);
  }

  Future<List<IRBracket>> getAnnualBrackets({int year = 2025}) async {
    return loadBrackets(isAnnual: true, year: year);
  }

  void clearCache() {
    _monthlyBrackets = null;
    _annualBrackets = null;
    _isLoaded = false;
  }

  bool isDataLoaded() {
    return _isLoaded;
  }
}