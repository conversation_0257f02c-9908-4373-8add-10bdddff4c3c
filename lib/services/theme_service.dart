import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';
import '../theme/accessibility_theme_extension.dart';
import '../models/accessibility/accessibility_preferences.dart';

enum ThemeType {
  light,
  dark,
  sepia,
  tokyoNight,
  solarizedDark,
  monokaiDimmed,
}

class ThemeService extends ChangeNotifier {
  static const String _themeKey = 'theme_type';
  late final SharedPreferences _prefs;
  ThemeType _currentTheme = ThemeType.light;
  
  // Accessibility preferences integration
  AccessibilityPreferences? _accessibilityPreferences;
  
  // Reference to accessibility service for integration
  // This will be set by the accessibility service when it initializes
  Function(AccessibilityPreferences)? _onAccessibilityChanged;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _currentTheme = ThemeType.values[_prefs.getInt(_themeKey) ?? 0];
    
    // Initialize with default accessibility preferences if none provided
    _accessibilityPreferences ??= AccessibilityPreferences.defaultSettings();
    
    notifyListeners();
  }

  ThemeType get currentTheme => _currentTheme;
  bool get isDarkMode =>
      _currentTheme == ThemeType.dark ||
      _currentTheme == ThemeType.tokyoNight ||
      _currentTheme == ThemeType.solarizedDark ||
      _currentTheme == ThemeType.monokaiDimmed;

  /// Get current accessibility preferences
  AccessibilityPreferences get accessibilityPreferences => 
      _accessibilityPreferences ?? AccessibilityPreferences.defaultSettings();

  /// Check if high contrast mode is enabled
  bool get isHighContrastEnabled => 
      _accessibilityPreferences?.highContrastMode ?? false;

  /// Get current font scale factor
  double get fontScale => 
      _accessibilityPreferences?.fontSize ?? 1.0;

  /// Check if reduced motion is enabled
  bool get isReducedMotionEnabled => 
      _accessibilityPreferences?.reducedMotion ?? false;

  void setTheme(ThemeType theme) {
    _currentTheme = theme;
    _prefs.setInt(_themeKey, theme.index);
    notifyListeners();
  }

  /// Set accessibility preferences and update theme accordingly
  void setAccessibilityPreferences(AccessibilityPreferences preferences) {
    _accessibilityPreferences = preferences;
    notifyListeners();
  }

  /// Update font scale factor
  Future<void> updateFontScale(double scale) async {
    if (scale < 0.8 || scale > 2.0) {
      throw ArgumentError('Font scale must be between 0.8 and 2.0');
    }
    
    _accessibilityPreferences = _accessibilityPreferences?.copyWith(fontSize: scale) ??
        AccessibilityPreferences.defaultSettings().copyWith(fontSize: scale);
    
    // Notify accessibility service if callback is set
    _onAccessibilityChanged?.call(_accessibilityPreferences!);
    
    notifyListeners();
  }

  /// Toggle high contrast mode
  Future<void> toggleHighContrast() async {
    final currentHighContrast = _accessibilityPreferences?.highContrastMode ?? false;
    
    _accessibilityPreferences = _accessibilityPreferences?.copyWith(
      highContrastMode: !currentHighContrast
    ) ?? AccessibilityPreferences.defaultSettings().copyWith(
      highContrastMode: !currentHighContrast
    );
    
    // Notify accessibility service if callback is set
    _onAccessibilityChanged?.call(_accessibilityPreferences!);
    
    notifyListeners();
  }

  /// Update reduced motion preference
  Future<void> updateReducedMotion(bool enabled) async {
    _accessibilityPreferences = _accessibilityPreferences?.copyWith(
      reducedMotion: enabled
    ) ?? AccessibilityPreferences.defaultSettings().copyWith(
      reducedMotion: enabled
    );
    
    // Notify accessibility service if callback is set
    _onAccessibilityChanged?.call(_accessibilityPreferences!);
    
    notifyListeners();
  }

  /// Set callback for accessibility changes (used by AccessibilityService)
  void setAccessibilityChangeCallback(Function(AccessibilityPreferences) callback) {
    _onAccessibilityChanged = callback;
  }

  void cycleTheme() {
    final values = ThemeType.values;
    final nextIndex = (_currentTheme.index + 1) % values.length;
    setTheme(values[nextIndex]);
  }

  String getThemeName(ThemeType type) {
    final baseName = switch (type) {
      ThemeType.light => 'Thème Clair',
      ThemeType.dark => 'Thème Sombre',
      ThemeType.sepia => 'Thème Sépia',
      ThemeType.tokyoNight => 'Tokyo Night',
      ThemeType.solarizedDark => 'Solarized Dark',
      ThemeType.monokaiDimmed => 'Monokai Dimmed',
    };
    
    // Add accessibility indicators to theme name
    final indicators = <String>[];
    if (isHighContrastEnabled) indicators.add('Contraste Élevé');
    if (fontScale != 1.0) indicators.add('Police ${(fontScale * 100).round()}%');
    if (isReducedMotionEnabled) indicators.add('Mouvement Réduit');
    
    return indicators.isEmpty 
        ? baseName 
        : '$baseName (${indicators.join(', ')})';
  }

  /// Get theme name with accessibility features description
  String getAccessibleThemeName() {
    return getThemeName(_currentTheme);
  }

  /// Check if current theme meets WCAG AAA standards
  bool isCurrentThemeAccessible() {
    final colorScheme = _getBaseColorScheme();
    return AppTheme.validateColorSchemeAccessibility(colorScheme);
  }

  /// Get accessibility summary for current theme
  Map<String, dynamic> getAccessibilitySummary() {
    final colorScheme = _getBaseColorScheme();
    final accessibility = AccessibilityThemeExtension();
    
    return {
      'themeName': getThemeName(_currentTheme),
      'isHighContrast': isHighContrastEnabled,
      'fontScale': fontScale,
      'isReducedMotion': isReducedMotionEnabled,
      'meetsWCAGAAA': AppTheme.validateColorSchemeAccessibility(colorScheme),
      'contrastRatios': {
        'surface': accessibility.calculateContrastRatio(
          colorScheme.surface, 
          colorScheme.onSurface
        ),
        'primary': accessibility.calculateContrastRatio(
          colorScheme.primary, 
          colorScheme.onPrimary
        ),
        'error': accessibility.calculateContrastRatio(
          colorScheme.error, 
          colorScheme.onError
        ),
      },
    };
  }

  /// Reset accessibility preferences to defaults
  Future<void> resetAccessibilityToDefaults() async {
    _accessibilityPreferences = AccessibilityPreferences.defaultSettings();
    
    // Notify accessibility service if callback is set
    _onAccessibilityChanged?.call(_accessibilityPreferences!);
    
    notifyListeners();
  }

  /// Apply accessibility preset (e.g., for vision impairment, motor impairment)
  Future<void> applyAccessibilityPreset(String presetName) async {
    switch (presetName.toLowerCase()) {
      case 'vision_impaired':
        _accessibilityPreferences = AccessibilityPreferences.visionOptimized();
        break;
      case 'motor_impaired':
        _accessibilityPreferences = AccessibilityPreferences.motorOptimized();
        break;
      case 'cognitive_support':
        _accessibilityPreferences = AccessibilityPreferences.cognitiveOptimized();
        break;
      case 'high_contrast':
        _accessibilityPreferences = AccessibilityPreferences.visionOptimized();
        break;
      default:
        _accessibilityPreferences = AccessibilityPreferences.defaultSettings();
    }
    
    // Notify accessibility service if callback is set
    _onAccessibilityChanged?.call(_accessibilityPreferences!);
    
    notifyListeners();
  }

  /// Get available accessibility presets
  List<Map<String, String>> getAccessibilityPresets() {
    return [
      {
        'id': 'default',
        'name': 'Paramètres par défaut',
        'description': 'Configuration standard pour tous les utilisateurs',
      },
      {
        'id': 'vision_impaired',
        'name': 'Déficience visuelle',
        'description': 'Police agrandie et contraste élevé pour une meilleure lisibilité',
      },
      {
        'id': 'motor_impaired',
        'name': 'Déficience motrice',
        'description': 'Cibles tactiles agrandies et navigation clavier améliorée',
      },
      {
        'id': 'cognitive_support',
        'name': 'Support cognitif',
        'description': 'Mouvement réduit et interface simplifiée',
      },
      {
        'id': 'high_contrast',
        'name': 'Contraste élevé',
        'description': 'Couleurs à contraste maximal pour une visibilité optimale',
      },
    ];
  }

  ThemeData get theme {
    return getAccessibleTheme();
  }

  /// Get theme with accessibility features applied
  ThemeData getAccessibleTheme() {
    final baseColorScheme = _getBaseColorScheme();
    final accessibilityExtension = _createAccessibilityExtension();
    
    return AppTheme.getThemeData(
      baseColorScheme,
      accessibilityExtension: accessibilityExtension,
    );
  }

  /// Get base color scheme for current theme
  ColorScheme _getBaseColorScheme() {
    switch (_currentTheme) {
      case ThemeType.light:
        return isHighContrastEnabled 
            ? AppTheme.getHighContrastLightColorScheme()
            : AppTheme.getLightColorScheme();
      case ThemeType.dark:
        return isHighContrastEnabled 
            ? AppTheme.getHighContrastDarkColorScheme()
            : AppTheme.getDarkColorScheme();
      case ThemeType.sepia:
        return _getSepiaColorScheme();
      case ThemeType.tokyoNight:
        return _getTokyoNightColorScheme();
      case ThemeType.solarizedDark:
        return _getSolarizedDarkColorScheme();
      case ThemeType.monokaiDimmed:
        return _getMonokaiDimmedColorScheme();
    }
  }

  /// Create accessibility theme extension with current preferences
  AccessibilityThemeExtension _createAccessibilityExtension() {
    final prefs = accessibilityPreferences;
    
    return AccessibilityThemeExtension(
      fontScale: prefs.fontSize,
      highContrastMode: prefs.highContrastMode,
      reducedMotion: prefs.reducedMotion,
      enhancedFocusIndicators: prefs.keyboardNavigationEnabled,
      semanticColorOverrides: const {}, // Can be extended for custom overrides
    );
  }

  /// Get sepia color scheme with accessibility support
  ColorScheme _getSepiaColorScheme() {
    if (isHighContrastEnabled) {
      // High contrast sepia theme
      return ColorScheme.fromSeed(
        seedColor: const Color(0xFF8B7355),
        brightness: Brightness.light,
        primary: const Color(0xFF000000), // Black for high contrast
        surface: const Color(0xFFFFFFFF), // White background
        onSurface: const Color(0xFF000000), // Black text
        surfaceVariant: const Color(0xFFF0F0F0), // Light gray
        onSurfaceVariant: const Color(0xFF000000), // Black text
        outline: const Color(0xFF000000), // Black borders
      );
    }
    
    return ColorScheme.fromSeed(
      seedColor: const Color(0xFF8B7355),
      brightness: Brightness.light,
      primary: const Color(0xFF8B7355),
      surface: const Color(0xFFF5E6D3),
      onSurface: const Color(0xFF2C1810),
      surfaceVariant: const Color(0xFFE6D5C1),
      onSurfaceVariant: const Color(0xFF4A3524),
    );
  }

  /// Get Tokyo Night color scheme with accessibility support
  ColorScheme _getTokyoNightColorScheme() {
    final baseScheme = AppTheme.getDarkColorScheme().copyWith(
      primary: const Color(0xFF7AA2F7),
      surface: const Color(0xFF1A1B26),
      surfaceContainerHighest: const Color(0xFF16161E),
    );
    
    if (isHighContrastEnabled) {
      return AppTheme.getHighContrastDarkColorScheme().copyWith(
        primary: const Color(0xFFFFFFFF), // White for high contrast
        secondary: const Color(0xFF00FFFF), // Cyan
        surface: const Color(0xFF000000), // Pure black
        surfaceContainerHighest: const Color(0xFF0F0F0F),
      );
    }
    
    return baseScheme;
  }

  /// Get Solarized Dark color scheme with accessibility support
  ColorScheme _getSolarizedDarkColorScheme() {
    final baseScheme = AppTheme.getDarkColorScheme().copyWith(
      primary: const Color(0xFF859900),
      surface: const Color(0xFF002B36),
      surfaceContainerHighest: const Color(0xFF073642),
      onSurface: const Color(0xFF93A1A1),
    );
    
    if (isHighContrastEnabled) {
      return AppTheme.getHighContrastDarkColorScheme().copyWith(
        primary: const Color(0xFFFFFFFF), // White for high contrast
        secondary: const Color(0xFF00FF00), // Bright green
        surface: const Color(0xFF000000), // Pure black
        surfaceContainerHighest: const Color(0xFF0F0F0F),
      );
    }
    
    return baseScheme;
  }

  /// Get Monokai Dimmed color scheme with accessibility support
  ColorScheme _getMonokaiDimmedColorScheme() {
    final baseScheme = AppTheme.getDarkColorScheme().copyWith(
      primary: const Color(0xFFFD971F),
      surface: const Color(0xFF1E1F1C),
      surfaceContainerHighest: const Color(0xFF252621),
      onSurface: const Color(0xFFA6E22E),
    );
    
    if (isHighContrastEnabled) {
      return AppTheme.getHighContrastDarkColorScheme().copyWith(
        primary: const Color(0xFFFFFFFF), // White for high contrast
        secondary: const Color(0xFFFFFF00), // Bright yellow
        surface: const Color(0xFF000000), // Pure black
        surfaceContainerHighest: const Color(0xFF0F0F0F),
      );
    }
    
    return baseScheme;
  }
}
