import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class QuizProgressService {
  static const String _quizProgressKey = 'quiz_progress';
  static const String _quizStatsKey = 'quiz_stats';
  
  static final QuizProgressService _instance = QuizProgressService._internal();
  factory QuizProgressService() => _instance;
  
  QuizProgressService._internal();
  
  // Overall quiz statistics
  Future<QuizStats> getQuizStats() async {
    final prefs = await SharedPreferences.getInstance();
    final String? statsJson = prefs.getString(_quizStatsKey);
    
    if (statsJson == null) {
      return QuizStats.initial();
    }
    
    try {
      return QuizStats.fromJson(json.decode(statsJson));
    } catch (e) {
      debugPrint('Error parsing quiz stats: $e');
      return QuizStats.initial();
    }
  }
  
  Future<void> updateQuizStats(QuizStats stats) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_quizStatsKey, json.encode(stats.toJson()));
  }
  
  // Update stats after completing a quiz
  Future<void> recordQuizCompletion({
    required String categoryName,
    required String levelName,
    required int score,
    required int totalQuestions,
    required int earnedPoints,
    required int totalPoints,
    required List<int> timeSpentPerQuestion,
  }) async {
    // Update overall stats
    final stats = await getQuizStats();
    final updatedStats = stats.copyWith(
      totalQuizzesCompleted: stats.totalQuizzesCompleted + 1,
      totalQuestionsAnswered: stats.totalQuestionsAnswered + totalQuestions,
      totalCorrectAnswers: stats.totalCorrectAnswers + score,
      totalPointsEarned: stats.totalPointsEarned + earnedPoints,
      bestStreak: stats.bestStreak > 0 ? stats.bestStreak : score,
      averageTimePerQuestion: _calculateAverageTime(
        stats.averageTimePerQuestion, 
        stats.totalQuestionsAnswered,
        timeSpentPerQuestion,
      ),
    );
    await updateQuizStats(updatedStats);
    
    // Save individual quiz result
    await saveQuizResult(
      categoryName: categoryName,
      levelName: levelName,
      score: score,
      totalQuestions: totalQuestions,
      earnedPoints: earnedPoints,
      totalPoints: totalPoints,
    );
  }
  
  double _calculateAverageTime(double currentAverage, int currentTotal, List<int> newTimes) {
    if (newTimes.isEmpty) return currentAverage;
    
    final int newTimeSum = newTimes.fold(0, (sum, time) => sum + time);
    final int newCount = newTimes.length;
    
    final double totalTime = (currentAverage * currentTotal) + newTimeSum;
    return totalTime / (currentTotal + newCount);
  }
  
  // Individual quiz results
  Future<Map<String, Map<String, QuizResult>>> getAllQuizResults() async {
    final prefs = await SharedPreferences.getInstance();
    final String? progressJson = prefs.getString(_quizProgressKey);
    
    if (progressJson == null) {
      return {};
    }
    
    try {
      final Map<String, dynamic> decoded = json.decode(progressJson);
      final Map<String, Map<String, QuizResult>> results = {};
      
      decoded.forEach((category, levels) {
        results[category] = {};
        (levels as Map<String, dynamic>).forEach((level, resultJson) {
          results[category]![level] = QuizResult.fromJson(resultJson);
        });
      });
      
      return results;
    } catch (e) {
      debugPrint('Error parsing quiz progress: $e');
      return {};
    }
  }
  
  Future<QuizResult?> getQuizResult({
    required String categoryName,
    required String levelName,
  }) async {
    final results = await getAllQuizResults();
    return results[categoryName]?[levelName];
  }
  
  Future<void> saveQuizResult({
    required String categoryName,
    required String levelName,
    required int score,
    required int totalQuestions,
    required int earnedPoints,
    required int totalPoints,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final allResults = await getAllQuizResults();
    
    // Create category map if it doesn't exist
    if (!allResults.containsKey(categoryName)) {
      allResults[categoryName] = {};
    }
    
    // Get existing result or create new one
    final existingResult = allResults[categoryName]![levelName];
    final newResult = QuizResult(
      bestScore: existingResult?.bestScore != null 
          ? max(existingResult!.bestScore, score) 
          : score,
      lastScore: score,
      totalQuestions: totalQuestions,
      bestPoints: existingResult?.bestPoints != null 
          ? max(existingResult!.bestPoints, earnedPoints) 
          : earnedPoints,
      lastPoints: earnedPoints,
      totalPoints: totalPoints,
      completionCount: (existingResult?.completionCount ?? 0) + 1,
      lastCompletedAt: DateTime.now(),
    );
    
    // Update the result
    allResults[categoryName]![levelName] = newResult;
    
    // Convert back to JSON and save
    final Map<String, dynamic> encoded = {};
    allResults.forEach((category, levels) {
      encoded[category] = {};
      levels.forEach((level, result) {
        encoded[category][level] = result.toJson();
      });
    });
    
    await prefs.setString(_quizProgressKey, json.encode(encoded));
  }
  
  // Clear all progress (for testing)
  Future<void> clearAllProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_quizProgressKey);
    await prefs.remove(_quizStatsKey);
  }
}

// Helper classes
@immutable
class QuizStats {
  final int totalQuizzesCompleted;
  final int totalQuestionsAnswered;
  final int totalCorrectAnswers;
  final int totalPointsEarned;
  final int bestStreak;
  final double averageTimePerQuestion;
  
  const QuizStats({
    required this.totalQuizzesCompleted,
    required this.totalQuestionsAnswered,
    required this.totalCorrectAnswers,
    required this.totalPointsEarned,
    required this.bestStreak,
    required this.averageTimePerQuestion,
  });
  
  factory QuizStats.initial() => const QuizStats(
    totalQuizzesCompleted: 0,
    totalQuestionsAnswered: 0,
    totalCorrectAnswers: 0,
    totalPointsEarned: 0,
    bestStreak: 0,
    averageTimePerQuestion: 0,
  );
  
  factory QuizStats.fromJson(Map<String, dynamic> json) {
    return QuizStats(
      totalQuizzesCompleted: json['totalQuizzesCompleted'] as int,
      totalQuestionsAnswered: json['totalQuestionsAnswered'] as int,
      totalCorrectAnswers: json['totalCorrectAnswers'] as int,
      totalPointsEarned: json['totalPointsEarned'] as int,
      bestStreak: json['bestStreak'] as int,
      averageTimePerQuestion: json['averageTimePerQuestion'] as double,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'totalQuizzesCompleted': totalQuizzesCompleted,
      'totalQuestionsAnswered': totalQuestionsAnswered,
      'totalCorrectAnswers': totalCorrectAnswers,
      'totalPointsEarned': totalPointsEarned,
      'bestStreak': bestStreak,
      'averageTimePerQuestion': averageTimePerQuestion,
    };
  }
  
  QuizStats copyWith({
    int? totalQuizzesCompleted,
    int? totalQuestionsAnswered,
    int? totalCorrectAnswers,
    int? totalPointsEarned,
    int? bestStreak,
    double? averageTimePerQuestion,
  }) {
    return QuizStats(
      totalQuizzesCompleted: totalQuizzesCompleted ?? this.totalQuizzesCompleted,
      totalQuestionsAnswered: totalQuestionsAnswered ?? this.totalQuestionsAnswered,
      totalCorrectAnswers: totalCorrectAnswers ?? this.totalCorrectAnswers,
      totalPointsEarned: totalPointsEarned ?? this.totalPointsEarned,
      bestStreak: bestStreak ?? this.bestStreak,
      averageTimePerQuestion: averageTimePerQuestion ?? this.averageTimePerQuestion,
    );
  }
  
  double get correctAnswerPercentage {
    if (totalQuestionsAnswered == 0) return 0;
    return (totalCorrectAnswers / totalQuestionsAnswered) * 100;
  }
}

@immutable
class QuizResult {
  final int bestScore;
  final int lastScore;
  final int totalQuestions;
  final int bestPoints;
  final int lastPoints;
  final int totalPoints;
  final int completionCount;
  final DateTime lastCompletedAt;
  
  const QuizResult({
    required this.bestScore,
    required this.lastScore,
    required this.totalQuestions,
    required this.bestPoints,
    required this.lastPoints,
    required this.totalPoints,
    required this.completionCount,
    required this.lastCompletedAt,
  });
  
  factory QuizResult.fromJson(Map<String, dynamic> json) {
    return QuizResult(
      bestScore: json['bestScore'] as int,
      lastScore: json['lastScore'] as int,
      totalQuestions: json['totalQuestions'] as int,
      bestPoints: json['bestPoints'] as int,
      lastPoints: json['lastPoints'] as int,
      totalPoints: json['totalPoints'] as int,
      completionCount: json['completionCount'] as int,
      lastCompletedAt: DateTime.parse(json['lastCompletedAt'] as String),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'bestScore': bestScore,
      'lastScore': lastScore,
      'totalQuestions': totalQuestions,
      'bestPoints': bestPoints,
      'lastPoints': lastPoints,
      'totalPoints': totalPoints,
      'completionCount': completionCount,
      'lastCompletedAt': lastCompletedAt.toIso8601String(),
    };
  }
  
  double get bestScorePercentage => (bestScore / totalQuestions) * 100;
  double get lastScorePercentage => (lastScore / totalQuestions) * 100;
  double get bestPointsPercentage => (bestPoints / totalPoints) * 100;
}

// Helper function
int max(int a, int b) => a > b ? a : b; 