import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppVersionService extends ChangeNotifier {
  static const String _lastVersionKey = 'last_app_version';
  static const String _firstTimeKey = 'first_time_open';
  
  String? _currentVersion;
  String? _lastVersion;
  bool _isFirstTimeOpen = false;
  bool _isNewVersion = false;
  bool _initialized = false;
  bool _shouldForceShowWhatsNew = false;

  /// Current app version
  String? get currentVersion => _currentVersion;
  
  /// Last saved app version
  String? get lastVersion => _lastVersion;
  
  /// Whether this is the first time opening the app
  bool get isFirstTimeOpen => _isFirstTimeOpen;
  
  /// Whether this is a new version of the app
  bool get isNewVersion => _isNewVersion;
  
  /// Whether the service has been initialized
  bool get initialized => _initialized;
  
  /// Force show What's New dialog on next check
  bool get shouldForceShowWhatsNew => _shouldForceShowWhatsNew;

  /// Initialize the service
  Future<void> init() async {
    try {
      // Get current app version
      final packageInfo = await PackageInfo.fromPlatform();
      _currentVersion = packageInfo.version;
      
      // Get stored version from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      _lastVersion = prefs.getString(_lastVersionKey);
      _isFirstTimeOpen = prefs.getBool(_firstTimeKey) ?? true;
      
      // Check if this is a new version
      if (_lastVersion == null) {
        // First installation
        _isNewVersion = true;
      } else if (_lastVersion != _currentVersion) {
        // Version update
        _isNewVersion = true;
      }
      
      _initialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing AppVersionService: $e');
      _initialized = false;
    }
  }

  /// Mark the current version as seen
  Future<void> markVersionSeen() async {
    try {
      if (_currentVersion == null) return;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastVersionKey, _currentVersion!);
      await prefs.setBool(_firstTimeKey, false);
      
      _isNewVersion = false;
      _isFirstTimeOpen = false;
      _shouldForceShowWhatsNew = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error marking version as seen: $e');
    }
  }
  
  /// Force show the What's New dialog next time it's checked
  void forceShowWhatsNew() {
    _shouldForceShowWhatsNew = true;
    notifyListeners();
  }
  
  /// Check if we should show the What's New dialog
  bool shouldShowWhatsNew() {
    return _shouldForceShowWhatsNew || _isNewVersion || _isFirstTimeOpen;
  }
}