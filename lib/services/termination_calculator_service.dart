import '../models/termination_calculator/termination_data.dart';

class TerminationCalculatorService {
  // Indemnité de licenciement selon l'ancienneté
  static const Map<int, (int, double)> _indemnityRates = {
    5: (96, 0.0), // < 5 ans: 96h par année, plafonné à 0 mois
    10: (144, 0.0), // 5-10 ans: 144h par année, plafonné à 0 mois
    15: (192, 0.0), // 10-15 ans: 192h par année, plafonné à 0 mois
    20: (240, 0.0), // 15-20 ans: 240h par année, plafonné à 0 mois
    0: (288, 0.0), // > 20 ans: 288h par année, plafonné à 0 mois
  };

  static TerminationResult calculate(TerminationData data) {
    // 1. Calculer le salaire moyen
    final averageSalary = _calculateAverageSalary(data);

    // 2. Calculer l'indemnité de préavis si non respecté
    final preAvisIndemnity = data.preAvisRespected
        ? 0.0
        : _calculatePreAvis(
            data.employeeType, data.yearsOfService, averageSalary);

    // 3. Calculer l'indemnité de licenciement
    final terminationIndemnity = _calculateTerminationIndemnity(
      data.yearsOfService,
      data.monthsOfService,
      averageSalary,
      data.reason,
    );

    // 4. Calculer l'indemnité de congés payés
    final leaveIndemnity = _calculateLeaveIndemnity(
      data.remainingPaidLeaves,
      averageSalary,
    );

    // 5. Générer le détail des calculs
    final details = _generateDetails(
      data,
      averageSalary,
      preAvisIndemnity,
      terminationIndemnity,
      leaveIndemnity,
    );

    return TerminationResult(
      preAvisIndemnity: preAvisIndemnity,
      terminationIndemnity: terminationIndemnity,
      leaveIndemnity: leaveIndemnity,
      totalIndemnity: preAvisIndemnity + terminationIndemnity + leaveIndemnity,
      details: details,
    );
  }

  static double _calculateAverageSalary(TerminationData data) {
    return data.lastSalary +
        data.transportAllowance +
        data.housingAllowance +
        data.otherAllowances;
  }

  static double _calculatePreAvis(String type, int years, double salary) {
    final (months, days) = switch ((type, years)) {
      ('cadre', < 1) => (1, 0),
      ('cadre', <= 5) => (2, 0),
      ('cadre', _) => (3, 0),
      ('salarie', < 1) => (0, 8),
      ('salarie', <= 5) => (1, 0),
      (_, _) => (2, 0),
    };

    return (salary * months) + (salary / 26 * days);
  }

  static double _calculateTerminationIndemnity(
    int years,
    int months,
    double salary,
    TerminationReason reason,
  ) {
    if (reason == TerminationReason.fault ||
        reason == TerminationReason.resignation) {
      return 0.0;
    }

    // Trouver le taux applicable
    var (hoursPerYear, maxMonths) = _indemnityRates.entries
        .where((e) => years < e.key || e.key == 0)
        .first
        .value;

    // Calculer pour les années complètes
    double totalHours = hoursPerYear.toDouble() * years;

    // Ajouter les mois supplémentaires
    if (months > 0) {
      totalHours += (hoursPerYear / 12) * months;
    }

    // Convertir les heures en montant
    double hourlyRate = salary / 191;
    return totalHours * hourlyRate;
  }

  static double _calculateLeaveIndemnity(int days, double salary) {
    return (salary / 26) * days;
  }

  static String _generateDetails(
    TerminationData data,
    double averageSalary,
    double preAvisIndemnity,
    double terminationIndemnity,
    double leaveIndemnity,
  ) {
    final sb = StringBuffer();

    sb.writeln('CALCUL DES INDEMNITÉS DE FIN DE SERVICE');
    sb.writeln('═══════════════════════════════════════\n');

    // 1. Base de calcul
    sb.writeln('I. BASE DE CALCUL');
    sb.writeln('────────────────');
    sb.writeln(
        '• Salaire de base                    ${_formatAmount(data.lastSalary)}');
    if (data.transportAllowance > 0) {
      sb.writeln(
          '• Indemnité de transport            ${_formatAmount(data.transportAllowance)}');
    }
    if (data.housingAllowance > 0) {
      sb.writeln(
          '• Indemnité de logement            ${_formatAmount(data.housingAllowance)}');
    }
    if (data.otherAllowances > 0) {
      sb.writeln(
          '• Autres indemnités fixes           ${_formatAmount(data.otherAllowances)}');
    }
    sb.writeln(
        '\nSalaire moyen mensuel               ${_formatAmount(averageSalary)}');

    // 2. Indemnité de préavis
    if (!data.preAvisRespected && preAvisIndemnity > 0) {
      sb.writeln('\nII. INDEMNITÉ DE PRÉAVIS');
      sb.writeln('─────────────────────────');
      final (months, days) =
          _getPreAvisLength(data.employeeType, data.yearsOfService);
      sb.writeln(
          '• Type : ${data.employeeType == 'cadre' ? 'Cadre' : 'Salarié'}');
      sb.writeln('• Ancienneté : ${data.yearsOfService} ans');
      if (months > 0) sb.writeln('• Durée : $months mois');
      if (days > 0) sb.writeln('• Durée : $days jours');
      sb.writeln(
          '\nTotal préavis                      ${_formatAmount(preAvisIndemnity)}');
    }

    // 3. Indemnité de licenciement
    if (terminationIndemnity > 0) {
      sb.writeln('\nIII. INDEMNITÉ DE LICENCIEMENT');
      sb.writeln('───────────────────────────────');
      sb.writeln(
          '• Ancienneté : ${data.yearsOfService} ans et ${data.monthsOfService} mois');
      final (hoursPerYear, _) = _getIndemnityRate(data.yearsOfService);
      sb.writeln('• Base : ${hoursPerYear}h par année de service');
      sb.writeln(
          '\nTotal licenciement                 ${_formatAmount(terminationIndemnity)}');
    }

    // 4. Indemnité de congés
    if (leaveIndemnity > 0) {
      sb.writeln('\nIV. SOLDE DE CONGÉS');
      sb.writeln('──────────────────');
      sb.writeln('• Jours restants : ${data.remainingPaidLeaves}');
      sb.writeln(
          '\nTotal congés                       ${_formatAmount(leaveIndemnity)}');
    }

    // 5. Total
    sb.writeln('\nTOTAL DES INDEMNITÉS');
    sb.writeln('═══════════════════');
    sb.writeln(
        'TOTAL                               ${_formatAmount(preAvisIndemnity + terminationIndemnity + leaveIndemnity)}');

    return sb.toString();
  }

  static String _formatAmount(double amount) {
    return '${amount.toStringAsFixed(2).padLeft(10)} DH';
  }

  static (int months, int days) _getPreAvisLength(String type, int years) {
    return switch ((type, years)) {
      ('cadre', < 1) => (1, 0),
      ('cadre', <= 5) => (2, 0),
      ('cadre', _) => (3, 0),
      ('salarie', < 1) => (0, 8),
      ('salarie', <= 5) => (1, 0),
      (_, _) => (2, 0),
    };
  }

  static (int hoursPerYear, double maxMonths) _getIndemnityRate(int years) {
    return _indemnityRates.entries
        .where((e) => years < e.key || e.key == 0)
        .first
        .value;
  }
}

class TerminationResult {
  final double preAvisIndemnity;
  final double terminationIndemnity;
  final double leaveIndemnity;
  final double totalIndemnity;
  final String details;

  const TerminationResult({
    required this.preAvisIndemnity,
    required this.terminationIndemnity,
    required this.leaveIndemnity,
    required this.totalIndemnity,
    required this.details,
  });
}
