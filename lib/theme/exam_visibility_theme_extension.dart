import 'package:flutter/material.dart';
import 'dart:ui' show lerpDouble;

/// Specialized theme extension for exam visibility improvements
/// Provides enhanced contrast, readability, and accessibility features specifically for exam interfaces
@immutable
class ExamVisibilityThemeExtension extends ThemeExtension<ExamVisibilityThemeExtension> {
  const ExamVisibilityThemeExtension({
    this.enhancedContrastMode = false,
    this.examFontScale = 1.0,
    this.useHighContrastAnswers = false,
    this.enableTextShadows = false,
    this.useAlternatingAnswerColors = false,
  });

  /// Whether enhanced contrast mode is enabled for exams
  final bool enhancedContrastMode;

  /// Font scale factor specifically for exam content (1.0 - 1.5)
  final double examFontScale;

  /// Whether to use high contrast colors for answer options
  final bool useHighContrastAnswers;

  /// Whether to enable text shadows for better visibility
  final bool enableTextShadows;

  /// Whether to use alternating colors for answer options
  final bool useAlternatingAnswerColors;

  @override
  ExamVisibilityThemeExtension copyWith({
    bool? enhancedContrastMode,
    double? examFontScale,
    bool? useHighContrastAnswers,
    bool? enableTextShadows,
    bool? useAlternatingAnswerColors,
  }) {
    return ExamVisibilityThemeExtension(
      enhancedContrastMode: enhancedContrastMode ?? this.enhancedContrastMode,
      examFontScale: examFontScale ?? this.examFontScale,
      useHighContrastAnswers: useHighContrastAnswers ?? this.useHighContrastAnswers,
      enableTextShadows: enableTextShadows ?? this.enableTextShadows,
      useAlternatingAnswerColors: useAlternatingAnswerColors ?? this.useAlternatingAnswerColors,
    );
  }

  @override
  ExamVisibilityThemeExtension lerp(ThemeExtension<ExamVisibilityThemeExtension>? other, double t) {
    if (other is! ExamVisibilityThemeExtension) {
      return this;
    }
    return ExamVisibilityThemeExtension(
      enhancedContrastMode: t < 0.5 ? enhancedContrastMode : other.enhancedContrastMode,
      examFontScale: lerpDouble(examFontScale, other.examFontScale, t) ?? examFontScale,
      useHighContrastAnswers: t < 0.5 ? useHighContrastAnswers : other.useHighContrastAnswers,
      enableTextShadows: t < 0.5 ? enableTextShadows : other.enableTextShadows,
      useAlternatingAnswerColors: t < 0.5 ? useAlternatingAnswerColors : other.useAlternatingAnswerColors,
    );
  }

  /// Get enhanced text color for exam answers
  Color getAnswerTextColor(ColorScheme colorScheme, bool isSelected, bool isDark, int answerIndex) {
    if (enhancedContrastMode || useHighContrastAnswers) {
      if (isSelected) {
        return isDark ? Colors.white : Colors.black;
      }
      
      if (useAlternatingAnswerColors) {
        // Use alternating high contrast colors for better distinction
        final colors = isDark 
            ? [Colors.white, Colors.grey.shade200, Colors.blue.shade100, Colors.green.shade100]
            : [Colors.black, Colors.grey.shade800, Colors.blue.shade900, Colors.green.shade900];
        return colors[answerIndex % colors.length];
      }
      
      return isDark ? Colors.white : Colors.black;
    }
    
    // Standard colors with enhanced contrast for better Android visibility
    return isSelected
        ? (isDark ? Colors.white : Colors.black)
        : (isDark ? Colors.white.withValues(alpha: 0.95) : Colors.black.withValues(alpha: 0.9));
  }

  /// Get enhanced background color for exam answers
  Color getAnswerBackgroundColor(ColorScheme colorScheme, bool isSelected, bool isDark, int answerIndex) {
    if (enhancedContrastMode) {
      if (isSelected) {
        return isDark 
            ? colorScheme.primary.withValues(alpha: 0.7)
            : colorScheme.primary.withValues(alpha: 0.8);
      }
      
      if (useAlternatingAnswerColors) {
        // Use alternating background colors for better distinction
        final colors = isDark 
            ? [
                colorScheme.surface.withValues(alpha: 0.9),
                colorScheme.surfaceContainerHighest.withValues(alpha: 0.8),
                Colors.blue.shade900.withValues(alpha: 0.3),
                Colors.green.shade900.withValues(alpha: 0.3),
              ]
            : [
                colorScheme.surface,
                colorScheme.surfaceContainerHighest,
                Colors.blue.shade50,
                Colors.green.shade50,
              ];
        return colors[answerIndex % colors.length];
      }
      
      return isDark 
          ? colorScheme.surfaceContainerHighest.withValues(alpha: 0.8)
          : colorScheme.surface;
    }
    
    // Enhanced colors for better Android visibility
    return isSelected
        ? (isDark
            ? colorScheme.primaryContainer.withValues(alpha: 0.7)
            : colorScheme.primaryContainer.withValues(alpha: 0.8))
        : (isDark
            ? colorScheme.surfaceContainerHighest.withValues(alpha: 0.8)
            : colorScheme.surfaceContainerHighest.withValues(alpha: 0.9));
  }

  /// Get text shadows for improved visibility
  List<Shadow>? getTextShadows(ColorScheme colorScheme, bool isDark) {
    if (!enableTextShadows && !enhancedContrastMode) return null;
    
    return [
      Shadow(
        offset: const Offset(0.5, 0.5),
        blurRadius: enhancedContrastMode ? 2.0 : 1.0,
        color: isDark 
            ? Colors.black.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.8),
      ),
      if (enhancedContrastMode)
        Shadow(
          offset: const Offset(-0.5, -0.5),
          blurRadius: 1.0,
          color: isDark 
              ? Colors.black.withValues(alpha: 0.6)
              : Colors.white.withValues(alpha: 0.6),
        ),
    ];
  }

  /// Get enhanced font size for exam content
  double getEnhancedFontSize(double baseSize) {
    final scale = examFontScale.clamp(1.0, 1.5);
    return baseSize * scale;
  }

  /// Get border color for answer options
  Color getBorderColor(ColorScheme colorScheme, bool isSelected, bool isDark) {
    if (enhancedContrastMode) {
      return isSelected 
          ? colorScheme.primary
          : (isDark ? Colors.white.withValues(alpha: 0.6) : Colors.black.withValues(alpha: 0.6));
    }
    
    return isSelected 
        ? colorScheme.primary
        : colorScheme.outline.withValues(alpha: isDark ? 0.4 : 0.3);
  }

  /// Get border width for answer options
  double getBorderWidth(bool isSelected) {
    if (enhancedContrastMode) {
      return isSelected ? 3.0 : 2.0;
    }
    
    return isSelected ? 2.0 : 1.0;
  }

  /// Factory constructor for vision-impaired users
  factory ExamVisibilityThemeExtension.visionOptimized() {
    return const ExamVisibilityThemeExtension(
      enhancedContrastMode: true,
      examFontScale: 1.3,
      useHighContrastAnswers: true,
      enableTextShadows: true,
      useAlternatingAnswerColors: true,
    );
  }

  /// Factory constructor for standard enhanced visibility
  factory ExamVisibilityThemeExtension.enhanced() {
    return const ExamVisibilityThemeExtension(
      enhancedContrastMode: true,  // Enable by default for better Android visibility
      examFontScale: 1.15,         // Slightly larger font for better readability
      useHighContrastAnswers: true, // Use high contrast by default
      enableTextShadows: true,
      useAlternatingAnswerColors: false, // Keep false to avoid confusion
    );
  }

  /// Factory constructor for maximum visibility
  factory ExamVisibilityThemeExtension.maximumVisibility() {
    return const ExamVisibilityThemeExtension(
      enhancedContrastMode: true,
      examFontScale: 1.4,
      useHighContrastAnswers: true,
      enableTextShadows: true,
      useAlternatingAnswerColors: true,
    );
  }
}

/// Extension to easily access exam visibility theme from BuildContext
extension ExamVisibilityThemeExtensionContext on BuildContext {
  ExamVisibilityThemeExtension? get examVisibility => 
      Theme.of(this).extension<ExamVisibilityThemeExtension>();
}
