import 'package:flutter/material.dart';
import 'design_tokens.dart';

/// Device type enumeration
enum DeviceType { mobile, tablet, desktop }

/// Unified responsive design system to replace ad-hoc breakpoint usage
/// throughout the app with consistent responsive behavior.
class ResponsiveBreakpoints {
  ResponsiveBreakpoints._();

  /// Get current device type based on screen width
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < DesignTokens.breakpointMobile) return DeviceType.mobile;
    if (width < DesignTokens.breakpointTablet) return DeviceType.tablet;
    return DeviceType.desktop;
  }

  /// Check if current device is mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  /// Check if current device is tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// Check if current device is desktop
  static bool isDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.desktop;
  }

  /// Check if screen is small (mobile or small tablet)
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < DesignTokens.breakpointMobile;
  }

  /// Check if screen is very small (narrow mobile)
  static bool isVerySmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 400;
  }

  /// Get responsive value based on device type
  static T getValue<T>(
    BuildContext context, {
    required T mobile,
    required T tablet,
    required T desktop,
  }) {
    switch (getDeviceType(context)) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet;
      case DeviceType.desktop:
        return desktop;
    }
  }

  /// Get responsive padding
  static EdgeInsets getPadding(
    BuildContext context, {
    required EdgeInsets mobile,
    required EdgeInsets tablet,
    required EdgeInsets desktop,
  }) {
    return getValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive margin
  static EdgeInsets getMargin(
    BuildContext context, {
    required EdgeInsets mobile,
    required EdgeInsets tablet,
    required EdgeInsets desktop,
  }) {
    return getValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive font size
  static double getFontSize(
    BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
  }) {
    return getValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive spacing
  static double getSpacing(
    BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
  }) {
    return getValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive grid columns
  static int getGridColumns(BuildContext context) {
    return getValue(
      context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
    );
  }

  /// Get responsive cross axis count for grids
  static int getCrossAxisCount(
    BuildContext context, {
    int? mobile,
    int? tablet,
    int? desktop,
  }) {
    return getValue(
      context,
      mobile: mobile ?? 1,
      tablet: tablet ?? 2,
      desktop: desktop ?? 3,
    );
  }

  /// Get responsive aspect ratio
  static double getAspectRatio(
    BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
  }) {
    return getValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive max width for content containers
  static double getMaxWidth(BuildContext context) {
    return getValue(
      context,
      mobile: double.infinity,
      tablet: 800.0,
      desktop: DesignTokens.maxContentWidth,
    );
  }

  /// Get responsive sidebar width
  static double getSidebarWidth(BuildContext context) {
    return getValue(
      context,
      mobile: 280.0,
      tablet: 320.0,
      desktop: 360.0,
    );
  }

  /// Get responsive app bar height
  static double getAppBarHeight(BuildContext context) {
    return getValue(
      context,
      mobile: kToolbarHeight,
      tablet: kToolbarHeight + 8,
      desktop: kToolbarHeight + 16,
    );
  }

  /// Get responsive navigation rail width
  static double getNavigationRailWidth(BuildContext context) {
    return getValue(
      context,
      mobile: 240.0,
      tablet: 260.0,
      desktop: 280.0,
    );
  }

  /// Get responsive card elevation
  static double getCardElevation(BuildContext context) {
    return getValue(
      context,
      mobile: DesignTokens.elevation1,
      tablet: DesignTokens.elevation2,
      desktop: DesignTokens.elevation4,
    );
  }

  /// Get responsive border radius
  static double getBorderRadius(BuildContext context) {
    return getValue(
      context,
      mobile: DesignTokens.radiusSm,
      tablet: DesignTokens.radiusBase,
      desktop: DesignTokens.radiusLg,
    );
  }
}

/// Extension methods for easy access to responsive breakpoints
extension ResponsiveBreakpointsExtension on BuildContext {
  /// Get current device type
  DeviceType get deviceType =>
      ResponsiveBreakpoints.getDeviceType(this);

  /// Check if mobile device
  bool get isMobile => ResponsiveBreakpoints.isMobile(this);

  /// Check if tablet device
  bool get isTablet => ResponsiveBreakpoints.isTablet(this);

  /// Check if desktop device
  bool get isDesktop => ResponsiveBreakpoints.isDesktop(this);

  /// Check if small screen
  bool get isSmallScreen => ResponsiveBreakpoints.isSmallScreen(this);

  /// Check if very small screen
  bool get isVerySmallScreen => ResponsiveBreakpoints.isVerySmallScreen(this);

  /// Get responsive value
  T responsive<T>({
    required T mobile,
    required T tablet,
    required T desktop,
  }) {
    return ResponsiveBreakpoints.getValue(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive padding
  EdgeInsets responsivePadding({
    required EdgeInsets mobile,
    required EdgeInsets tablet,
    required EdgeInsets desktop,
  }) {
    return ResponsiveBreakpoints.getPadding(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive margin
  EdgeInsets responsiveMargin({
    required EdgeInsets mobile,
    required EdgeInsets tablet,
    required EdgeInsets desktop,
  }) {
    return ResponsiveBreakpoints.getMargin(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive spacing
  double responsiveSpacing({
    required double mobile,
    required double tablet,
    required double desktop,
  }) {
    return ResponsiveBreakpoints.getSpacing(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive grid columns
  int get responsiveGridColumns => ResponsiveBreakpoints.getGridColumns(this);

  /// Get responsive cross axis count
  int responsiveCrossAxisCount({
    int? mobile,
    int? tablet,
    int? desktop,
  }) {
    return ResponsiveBreakpoints.getCrossAxisCount(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get responsive max width
  double get responsiveMaxWidth => ResponsiveBreakpoints.getMaxWidth(this);

  /// Get responsive sidebar width
  double get responsiveSidebarWidth =>
      ResponsiveBreakpoints.getSidebarWidth(this);

  /// Get responsive app bar height
  double get responsiveAppBarHeight =>
      ResponsiveBreakpoints.getAppBarHeight(this);

  /// Get responsive navigation rail width
  double get responsiveNavigationRailWidth =>
      ResponsiveBreakpoints.getNavigationRailWidth(this);

  /// Get responsive card elevation
  double get responsiveCardElevation =>
      ResponsiveBreakpoints.getCardElevation(this);

  /// Get responsive border radius
  double get responsiveBorderRadius =>
      ResponsiveBreakpoints.getBorderRadius(this);
}
