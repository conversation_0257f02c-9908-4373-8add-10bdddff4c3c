import 'package:flutter/material.dart';
import 'design_tokens.dart';
import 'semantic_colors.dart';
import '../utils/icon_mapper.dart';

/// Standardized icon system that defines consistent icon usage patterns
/// throughout the app with proper sizing, colors, and accessibility.
class AppIcons {
  AppIcons._();

  // Standard icon sizes from design tokens
  static const double sizeXs = DesignTokens.iconXs;
  static const double sizeSm = DesignTokens.iconSm;
  static const double sizeBase = DesignTokens.iconBase;
  static const double sizeLg = DesignTokens.iconLg;
  static const double sizeXl = DesignTokens.iconXl;

  // Icon button sizes with proper touch targets
  static const double buttonSizeSmall = DesignTokens.touchTargetMin;
  static const double buttonSizeMedium = DesignTokens.touchTargetComfortable;
  static const double buttonSizeLarge = 56.0;

  /// Get icon with standardized sizing and color
  static Widget icon(
    IconData iconData, {
    double? size,
    Color? color,
    String? semantics,
  }) {
    return Icon(
      iconData,
      size: size ?? sizeBase,
      color: color,
      semanticLabel: semantics,
    );
  }

  /// Get icon from semantic name using IconMapper
  static Widget semanticIcon(
    String iconName, {
    double? size,
    Color? color,
    String? semantics,
  }) {
    return Icon(
      IconMapper.getIcon(iconName),
      size: size ?? sizeBase,
      color: color,
      semanticLabel: semantics ?? iconName,
    );
  }

  /// Create icon button with proper touch target and accessibility
  static Widget iconButton({
    required IconData icon,
    required VoidCallback? onPressed,
    double? size,
    Color? color,
    Color? backgroundColor,
    String? tooltip,
    String? semantics,
    double? buttonSize,
    EdgeInsets? padding,
  }) {
    final effectiveButtonSize = buttonSize ?? buttonSizeMedium;
    final effectiveIconSize = size ?? sizeBase;
    final effectivePadding = padding ?? 
        EdgeInsets.all((effectiveButtonSize - effectiveIconSize) / 2);

    return Tooltip(
      message: tooltip ?? '',
      child: Material(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: BorderRadius.circular(effectiveButtonSize / 2),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(effectiveButtonSize / 2),
          child: Container(
            width: effectiveButtonSize,
            height: effectiveButtonSize,
            padding: effectivePadding,
            child: Icon(
              icon,
              size: effectiveIconSize,
              color: color,
              semanticLabel: semantics,
            ),
          ),
        ),
      ),
    );
  }

  /// Create semantic icon button using IconMapper
  static Widget semanticIconButton({
    required String iconName,
    required VoidCallback? onPressed,
    double? size,
    Color? color,
    Color? backgroundColor,
    String? tooltip,
    String? semantics,
    double? buttonSize,
    EdgeInsets? padding,
  }) {
    return iconButton(
      icon: IconMapper.getIcon(iconName),
      onPressed: onPressed,
      size: size,
      color: color,
      backgroundColor: backgroundColor,
      tooltip: tooltip,
      semantics: semantics ?? iconName,
      buttonSize: buttonSize,
      padding: padding,
    );
  }

  /// Get contextual icon color based on semantic meaning
  static Color getContextualColor(
    BuildContext context,
    IconContext iconContext,
  ) {
    final theme = Theme.of(context);
    final semanticColors = theme.extension<SemanticColors>()!;

    switch (iconContext) {
      case IconContext.primary:
        return theme.colorScheme.primary;
      case IconContext.secondary:
        return theme.colorScheme.secondary;
      case IconContext.surface:
        return theme.colorScheme.onSurface;
      case IconContext.surfaceVariant:
        return theme.colorScheme.onSurfaceVariant;
      case IconContext.disabled:
        return theme.colorScheme.onSurface.withValues(alpha: 0.38);
      case IconContext.success:
        return semanticColors.success;
      case IconContext.warning:
        return semanticColors.warning;
      case IconContext.error:
        return semanticColors.errorVariant;
      case IconContext.info:
        return semanticColors.info;
      case IconContext.positive:
        return semanticColors.positive;
      case IconContext.negative:
        return semanticColors.negative;
      case IconContext.neutral:
        return semanticColors.neutral;
    }
  }

  /// Comprehensive icon catalog mapping semantic names to Material Icons
  static const Map<String, IconData> catalog = {
    // Navigation
    'home': Icons.home_outlined,
    'menu': Icons.menu,
    'back': Icons.arrow_back,
    'forward': Icons.arrow_forward,
    'close': Icons.close,
    'search': Icons.search,
    'filter': Icons.filter_list,
    'sort': Icons.sort,
    'more': Icons.more_vert,

    // Actions
    'add': Icons.add,
    'edit': Icons.edit_outlined,
    'delete': Icons.delete_outline,
    'save': Icons.save_outlined,
    'download': Icons.download_outlined,
    'upload': Icons.upload_outlined,
    'share': Icons.share_outlined,
    'copy': Icons.copy_outlined,
    'print': Icons.print_outlined,
    'refresh': Icons.refresh,

    // Status
    'success': Icons.check_circle_outline,
    'warning': Icons.warning_amber_outlined,
    'error': Icons.error_outline,
    'info': Icons.info_outline,
    'help': Icons.help_outline,

    // Financial
    'money': Icons.attach_money,
    'calculator': Icons.calculate_outlined,
    'chart': Icons.bar_chart,
    'trend_up': Icons.trending_up,
    'trend_down': Icons.trending_down,
    'balance': Icons.account_balance_outlined,
    'receipt': Icons.receipt_outlined,
    'invoice': Icons.description_outlined,

    // Business
    'business': Icons.business_outlined,
    'work': Icons.work_outline,
    'person': Icons.person_outline,
    'group': Icons.group_outlined,
    'building': Icons.apartment_outlined,
    'factory': Icons.factory_outlined,

    // Documents
    'document': Icons.description_outlined,
    'folder': Icons.folder_outlined,
    'file': Icons.insert_drive_file_outlined,
    'pdf': Icons.picture_as_pdf_outlined,
    'excel': Icons.table_chart_outlined,

    // Settings
    'settings': Icons.settings_outlined,
    'preferences': Icons.tune,
    'theme': Icons.palette_outlined,
    'language': Icons.language,
    'notifications': Icons.notifications_outlined,

    // Time
    'calendar': Icons.calendar_month,
    'date': Icons.date_range,
    'time': Icons.access_time,
    'history': Icons.history,

    // Communication
    'email': Icons.email_outlined,
    'phone': Icons.phone_outlined,
    'message': Icons.message_outlined,
    'chat': Icons.chat_outlined,

    // Security
    'lock': Icons.lock_outline,
    'unlock': Icons.lock_open_outlined,
    'security': Icons.security_outlined,
    'visibility': Icons.visibility_outlined,
    'visibility_off': Icons.visibility_off_outlined,

    // Fallback
    'default': Icons.category_outlined,
  };

  /// Get icon from catalog
  static IconData getCatalogIcon(String name) {
    return catalog[name] ?? catalog['default']!;
  }
}

/// Enum for different icon contexts to determine appropriate colors
enum IconContext {
  primary,
  secondary,
  surface,
  surfaceVariant,
  disabled,
  success,
  warning,
  error,
  info,
  positive,
  negative,
  neutral,
}

/// Extension methods for easy access to app icons
extension AppIconsExtension on BuildContext {
  /// Get icon with theme-appropriate color
  Widget icon(
    IconData iconData, {
    double? size,
    IconContext context = IconContext.surface,
    String? semantics,
  }) {
    return AppIcons.icon(
      iconData,
      size: size,
      color: AppIcons.getContextualColor(this, context),
      semantics: semantics,
    );
  }

  /// Get semantic icon with theme-appropriate color
  Widget semanticIcon(
    String iconName, {
    double? size,
    IconContext context = IconContext.surface,
    String? semantics,
  }) {
    return AppIcons.semanticIcon(
      iconName,
      size: size,
      color: AppIcons.getContextualColor(this, context),
      semantics: semantics,
    );
  }

  /// Get icon button with theme-appropriate styling
  Widget iconButton({
    required IconData icon,
    required VoidCallback? onPressed,
    double? size,
    IconContext context = IconContext.surface,
    Color? backgroundColor,
    String? tooltip,
    String? semantics,
    double? buttonSize,
  }) {
    return AppIcons.iconButton(
      icon: icon,
      onPressed: onPressed,
      size: size,
      color: AppIcons.getContextualColor(this, context),
      backgroundColor: backgroundColor,
      tooltip: tooltip,
      semantics: semantics,
      buttonSize: buttonSize,
    );
  }

  /// Get semantic icon button with theme-appropriate styling
  Widget semanticIconButton({
    required String iconName,
    required VoidCallback? onPressed,
    double? size,
    IconContext context = IconContext.surface,
    Color? backgroundColor,
    String? tooltip,
    String? semantics,
    double? buttonSize,
  }) {
    return AppIcons.semanticIconButton(
      iconName: iconName,
      onPressed: onPressed,
      size: size,
      color: AppIcons.getContextualColor(this, context),
      backgroundColor: backgroundColor,
      tooltip: tooltip,
      semantics: semantics,
      buttonSize: buttonSize,
    );
  }

  /// Get catalog icon
  Widget catalogIcon(
    String name, {
    double? size,
    IconContext context = IconContext.surface,
    String? semantics,
  }) {
    return AppIcons.icon(
      AppIcons.getCatalogIcon(name),
      size: size,
      color: AppIcons.getContextualColor(this, context),
      semantics: semantics,
    );
  }
}
