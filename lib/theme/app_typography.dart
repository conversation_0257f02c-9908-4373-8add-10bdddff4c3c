import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'design_tokens.dart';

/// Typography system that extends Google Fonts implementation with consistent
/// text styles and improved hierarchy for the Moroccan Accounting app.
class AppTypography {
  AppTypography._();

  // Font scaling constraints
  static const double _minFontScale = 0.8;
  static const double _maxFontScale = 2.0;
  static const double _minFontSize = 10.0;
  static const double _maxFontSize = 72.0;

  // Base font family
  static String get fontFamily => GoogleFonts.inter().fontFamily!;

  /// Calculate scaled font size with constraints
  static double _getScaledFontSize(double baseSize, [double scale = 1.0]) {
    final constrainedScale = scale.clamp(_minFontScale, _maxFontScale);
    final scaledSize = baseSize * constrainedScale;
    return scaledSize.clamp(_minFontSize, _maxFontSize);
  }

  /// Adjust line height for scaled fonts to maintain readability
  static double _getAdjustedLineHeight(double baseLineHeight, double scale) {
    // Slightly reduce line height for very large fonts, increase for very small fonts
    if (scale > 1.5) {
      return (baseLineHeight - 0.1).clamp(1.1, 1.8);
    } else if (scale < 0.9) {
      return (baseLineHeight + 0.1).clamp(1.2, 1.8);
    }
    return baseLineHeight;
  }

  /// Adjust letter spacing for scaled fonts
  static double _getAdjustedLetterSpacing(double baseLetterSpacing, double scale) {
    // Reduce letter spacing for very large fonts, increase for very small fonts
    if (scale > 1.5) {
      return baseLetterSpacing - 0.2;
    } else if (scale < 0.9) {
      return baseLetterSpacing + 0.1;
    }
    return baseLetterSpacing;
  }

  // Display Styles - For large headings and hero text
  static TextStyle display1([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSize6xl, fontScale),
        fontWeight: FontWeight.w800,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightTight, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingTight, fontScale),
      );

  static TextStyle display2([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSize5xl, fontScale),
        fontWeight: FontWeight.w700,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightTight, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingTight, fontScale),
      );

  static TextStyle display3([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSize4xl, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  // Backward compatibility getters - removed to fix recursion
  // Use display1(), display2(), display3() methods instead

  // Heading Styles - For section headers and titles
  static TextStyle h1([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSize3xl, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle h2([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSize2xl, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle h3([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeXl, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle h4([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeLg, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle h5([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeBase, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle h6([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeSm, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWide, fontScale),
      );

  // Backward compatibility getters removed to fix recursion
  // Use h1(), h2(), h3(), h4(), h5(), h6() methods instead

  // Body Styles - For regular content
  static TextStyle bodyLarge([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeBase, fontScale),
        fontWeight: FontWeight.w400,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightRelaxed, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle bodyMedium([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeSm, fontScale),
        fontWeight: FontWeight.w400,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightRelaxed, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle bodySmall([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeXs, fontScale),
        fontWeight: FontWeight.w400,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  // Backward compatibility getters removed to fix recursion
  // Use bodyLarge(), bodyMedium(), bodySmall() methods instead

  // Label Styles - For form labels and UI elements
  static TextStyle labelLarge([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeSm, fontScale),
        fontWeight: FontWeight.w500,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWide, fontScale),
      );

  static TextStyle labelMedium([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeXs, fontScale),
        fontWeight: FontWeight.w500,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWide, fontScale),
      );

  static TextStyle labelSmall([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(11.0, fontScale),
        fontWeight: FontWeight.w500,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWider, fontScale),
      );

  // Backward compatibility getters removed to fix recursion
  // Use labelLarge(), labelMedium(), labelSmall() methods instead

  // Financial Data Styles - Specialized for accounting numbers
  static TextStyle financialLarge([double fontScale = 1.0]) => GoogleFonts.robotoMono(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeLg, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle financialMedium([double fontScale = 1.0]) => GoogleFonts.robotoMono(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeBase, fontScale),
        fontWeight: FontWeight.w500,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle financialSmall([double fontScale = 1.0]) => GoogleFonts.robotoMono(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeSm, fontScale),
        fontWeight: FontWeight.w400,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  // Backward compatibility getters removed to fix recursion
  // Use financialLarge(), financialMedium(), financialSmall() methods instead

  // Button Styles - For interactive elements
  static TextStyle buttonLarge([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeBase, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWide, fontScale),
      );

  static TextStyle buttonMedium([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeSm, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWide, fontScale),
      );

  static TextStyle buttonSmall([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeXs, fontScale),
        fontWeight: FontWeight.w600,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWider, fontScale),
      );

  // Backward compatibility getters removed to fix recursion
  // Use buttonLarge(), buttonMedium(), buttonSmall() methods instead

  // Caption and Overline - For supplementary text
  static TextStyle caption([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(DesignTokens.fontSizeXs, fontScale),
        fontWeight: FontWeight.w400,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingNormal, fontScale),
      );

  static TextStyle overline([double fontScale = 1.0]) => GoogleFonts.inter(
        fontSize: _getScaledFontSize(10.0, fontScale),
        fontWeight: FontWeight.w500,
        height: _getAdjustedLineHeight(DesignTokens.lineHeightNormal, fontScale),
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWider, fontScale),
      );

  // Backward compatibility getters removed to fix recursion
  // Use caption(), overline() methods instead

  // Accessibility-optimized text styles for high contrast mode
  static TextStyle highContrastDisplay1([double fontScale = 1.0]) => display1(fontScale).copyWith(
        fontWeight: FontWeight.w900,
        shadows: [
          Shadow(
            offset: const Offset(1, 1),
            blurRadius: 2,
            color: Colors.black.withOpacity(0.3),
          ),
        ],
      );

  static TextStyle highContrastH1([double fontScale = 1.0]) => h1(fontScale).copyWith(
        fontWeight: FontWeight.w700,
        shadows: [
          Shadow(
            offset: const Offset(0.5, 0.5),
            blurRadius: 1,
            color: Colors.black.withOpacity(0.2),
          ),
        ],
      );

  static TextStyle highContrastBodyLarge([double fontScale = 1.0]) => bodyLarge(fontScale).copyWith(
        fontWeight: FontWeight.w500,
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWide, fontScale),
      );

  static TextStyle highContrastButtonLarge([double fontScale = 1.0]) => buttonLarge(fontScale).copyWith(
        fontWeight: FontWeight.w700,
        letterSpacing: _getAdjustedLetterSpacing(DesignTokens.letterSpacingWider, fontScale),
      );

  /// Create a complete TextTheme for Material Design with optional font scaling
  static TextTheme createTextTheme(
    ColorScheme colorScheme, {
    double fontScale = 1.0,
    bool highContrastMode = false,
  }) {
    return TextTheme(
      displayLarge: (highContrastMode ? highContrastDisplay1(fontScale) : display1(fontScale))
          .copyWith(color: colorScheme.onSurface),
      displayMedium: display2(fontScale).copyWith(color: colorScheme.onSurface),
      displaySmall: display3(fontScale).copyWith(color: colorScheme.onSurface),
      headlineLarge: (highContrastMode ? highContrastH1(fontScale) : h1(fontScale))
          .copyWith(color: colorScheme.onSurface),
      headlineMedium: h2(fontScale).copyWith(color: colorScheme.onSurface),
      headlineSmall: h3(fontScale).copyWith(color: colorScheme.onSurface),
      titleLarge: h4(fontScale).copyWith(color: colorScheme.onSurface),
      titleMedium: h5(fontScale).copyWith(color: colorScheme.onSurface),
      titleSmall: h6(fontScale).copyWith(color: colorScheme.onSurfaceVariant),
      bodyLarge: (highContrastMode ? highContrastBodyLarge(fontScale) : bodyLarge(fontScale))
          .copyWith(color: colorScheme.onSurface),
      bodyMedium: bodyMedium(fontScale).copyWith(color: colorScheme.onSurface),
      bodySmall: bodySmall(fontScale).copyWith(color: colorScheme.onSurfaceVariant),
      labelLarge: labelLarge(fontScale).copyWith(color: colorScheme.onSurface),
      labelMedium: labelMedium(fontScale).copyWith(color: colorScheme.onSurfaceVariant),
      labelSmall: labelSmall(fontScale).copyWith(color: colorScheme.onSurfaceVariant),
    );
  }

  /// Get a scaled text style from any base text style
  static TextStyle getScaledTextStyle(TextStyle baseStyle, double fontScale) {
    final scaledFontSize = _getScaledFontSize(baseStyle.fontSize ?? DesignTokens.fontSizeBase, fontScale);
    final adjustedLineHeight = _getAdjustedLineHeight(baseStyle.height ?? DesignTokens.lineHeightNormal, fontScale);
    final adjustedLetterSpacing = _getAdjustedLetterSpacing(baseStyle.letterSpacing ?? 0.0, fontScale);

    return baseStyle.copyWith(
      fontSize: scaledFontSize,
      height: adjustedLineHeight,
      letterSpacing: adjustedLetterSpacing,
    );
  }

  /// Get responsive font scaling that considers both user preferences and screen size
  static double getResponsiveFontScale(BuildContext context, double userFontScale) {
    final width = MediaQuery.of(context).size.width;
    final screenScale = width < DesignTokens.breakpointMobile 
        ? 0.95 // Slightly smaller on mobile
        : width < DesignTokens.breakpointTablet 
            ? 1.0 // Normal on tablet
            : 1.05; // Slightly larger on desktop
    
    return (userFontScale * screenScale).clamp(_minFontScale, _maxFontScale);
  }

  /// Get responsive text style based on screen size with font scaling
  static TextStyle responsive(
    BuildContext context, {
    required TextStyle mobile,
    required TextStyle tablet,
    required TextStyle desktop,
    double fontScale = 1.0,
  }) {
    final width = MediaQuery.of(context).size.width;
    final baseStyle = width < DesignTokens.breakpointMobile 
        ? mobile 
        : width < DesignTokens.breakpointTablet 
            ? tablet 
            : desktop;
    
    return getScaledTextStyle(baseStyle, fontScale);
  }
}

/// Extension methods for easy access to typography
extension AppTypographyExtension on BuildContext {
  /// Get typography styles - returns the class itself since all methods are static
  Type get typography => AppTypography;
  
  /// Get responsive typography with font scaling
  TextStyle responsiveText({
    required TextStyle mobile,
    required TextStyle tablet,
    required TextStyle desktop,
    double fontScale = 1.0,
  }) {
    return AppTypography.responsive(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      fontScale: fontScale,
    );
  }

  /// Get scaled text style with responsive font scaling
  TextStyle scaledText(TextStyle baseStyle, double userFontScale) {
    final responsiveFontScale = AppTypography.getResponsiveFontScale(this, userFontScale);
    return AppTypography.getScaledTextStyle(baseStyle, responsiveFontScale);
  }

  /// Get accessibility-optimized text style
  TextStyle accessibleText(TextStyle baseStyle, {
    double fontScale = 1.0,
    bool highContrast = false,
  }) {
    final scaledStyle = AppTypography.getScaledTextStyle(baseStyle, fontScale);
    
    if (highContrast) {
      return scaledStyle.copyWith(
        fontWeight: FontWeight.values[
          (scaledStyle.fontWeight?.index ?? FontWeight.normal.index)
              .clamp(0, FontWeight.values.length - 2) + 1
        ],
        letterSpacing: (scaledStyle.letterSpacing ?? 0.0) + 0.5,
      );
    }
    
    return scaledStyle;
  }
}
