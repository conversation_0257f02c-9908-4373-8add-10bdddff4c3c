import 'package:flutter/material.dart';

// DesignTokens: Foundational design system constants for Moroccan Accounting App
// Includes gamification colors, typography, spacing, icon sizes, radii, elevation, durations, breakpoints, etc.

class DesignTokens {
  DesignTokens._();

  // Gamification colors for daily goal widget
  static const Color primaryColor = Color(0xFF1976D2);    // Blue
  static const Color successColor = Color(0xFF43A047);    // Green
  static const Color streakColor = Color(0xFFFFA000);     // Orange

  // Additional color properties for calculator components
  static const Color colorPrimary = primaryColor;         // Alias for backward compatibility
  static const Color colorSurface = Color(0xFFFAFAFA);    // Light surface
  static const Color colorOnSurface = Color(0xFF1C1B1F);  // Text on surface
  static const Color colorOnSurfaceVariant = Color(0xFF49454F); // Secondary text

  // Typography Scale - Standardized font sizes
  static const double fontSizeXs = 12.0;
  static const double fontSizeSm = 14.0;
  static const double fontSizeBase = 16.0;
  static const double fontSizeLg = 18.0;
  static const double fontSizeXl = 20.0;
  static const double fontSize2xl = 24.0;
  static const double fontSize3xl = 28.0;
  static const double fontSize4xl = 32.0;
  static const double fontSize5xl = 36.0;
  static const double fontSize6xl = 48.0;

  // Spacing Scale - Consistent spacing values
  static const double space1 = 4.0;
  static const double space2 = 8.0;
  static const double space3 = 12.0;
  static const double space4 = 16.0;
  static const double space5 = 20.0;
  static const double space6 = 24.0;
  static const double space8 = 32.0;
  static const double space10 = 40.0;
  static const double space12 = 48.0;
  static const double space16 = 64.0;

  // Icon Sizes - Standardized icon dimensions
  static const double iconXs = 16.0;
  static const double iconSm = 20.0;
  static const double iconBase = 24.0;
  static const double iconLg = 28.0;
  static const double iconXl = 32.0;

  // Border Radius - Consistent corner rounding
  static const double radiusXs = 4.0;
  static const double radiusSm = 8.0;
  static const double radiusBase = 12.0;
  static const double radiusLg = 16.0;
  static const double radiusXl = 20.0;
  static const double radius2xl = 24.0;

  // Legacy aliases for backward compatibility
  static const double radiusS = radiusSm;
  static const double radiusM = radiusBase;
  static const double spacingXS = space1;
  static const double spacingS = space2;
  static const double spacingM = space4;

  // Additional spacing aliases
  static const double spacing8 = space2;   // 8.0
  static const double spacing12 = space3;  // 12.0
  static const double spacing16 = space4;  // 16.0

  // Border radius aliases
  static const double borderRadius8 = radiusSm;  // 8.0

  // Elevation Levels - Material Design elevation
  static const double elevation0 = 0.0;
  static const double elevation1 = 1.0;
  static const double elevation2 = 2.0;
  static const double elevation4 = 4.0;
  static const double elevation6 = 6.0;
  static const double elevation8 = 8.0;
  static const double elevation12 = 12.0;
  static const double elevation16 = 16.0;
  static const double elevation24 = 24.0;

  // Animation Durations - Standardized timing
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationNormal = Duration(milliseconds: 200);
  static const Duration durationSlow = Duration(milliseconds: 300);
  static const Duration durationSlower = Duration(milliseconds: 500);

  // Breakpoints for responsive design
  static const double breakpointMobile = 600.0;
  static const double breakpointTablet = 1024.0;

  // Touch Targets - Accessibility minimum sizes
  static const double touchTargetMin = 44.0;
  static const double touchTargetComfortable = 48.0;

  // Line Heights - Typography line spacing
  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.4;
  static const double lineHeightRelaxed = 1.6;
  static const double lineHeightLoose = 1.8;

  // Letter Spacing - Typography character spacing
  static const double letterSpacingTight = -0.5;
  static const double letterSpacingNormal = 0.0;
  static const double letterSpacingWide = 0.5;
  static const double letterSpacingWider = 1.0;

  // Container Constraints
  static const double maxContentWidth = 1200.0;
  static const double minContentWidth = 320.0;

  // Grid System
  static const int gridColumns = 12;
  static const double gridGutter = space4;

  // Z-Index Layers
  static const int zIndexDropdown = 1000;
  static const int zIndexSticky = 1020;
  static const int zIndexFixed = 1030;
  static const int zIndexModal = 1040;
  static const int zIndexPopover = 1050;
  static const int zIndexTooltip = 1060;
}

// Extension methods for easy access to design tokens
extension DesignTokensExtension on BuildContext {
  /// Get spacing value by multiplier
  double spacing(double multiplier) => DesignTokens.space4 * multiplier;

  /// Get responsive spacing based on screen size
  double responsiveSpacing(double mobile, double tablet, double desktop) {
    final width = MediaQuery.of(this).size.width;
    if (width < DesignTokens.breakpointMobile) return mobile;
    if (width < DesignTokens.breakpointTablet) return tablet;
    return desktop;
  }
}

// End of design_tokens.dart
