class SalaryConstants {
  static const Map<String, Map<String, double>> smigSmagRates = {
    'smig': {
      'from_01_01_2025': 17.19,
      'from_01_07_2025': 17.92,
    },
    'smag': {
      'from_01_04_2025': 93.01,
      'from_01_10_2025': 97.44,
    }
  };

  static const Map<int, double> seniorityRates = {
    2: 0.05, // 5% after 2 years
    5: 0.10, // 10% after 5 years
    12: 0.15, // 15% after 12 years
    20: 0.20, // 20% after 20 years
    25: 0.25, // 25% after 25 years
  };

  static const Map<String, double> socialSecurityRates = {
    'cnss_employee': 0.0448,
    'cnss_employer': 0.0898,
    'amo_employee': 0.0226,
    'amo_employer': 0.0226,
    'cnss_ceiling': 6000.0,
  };

  // Add other constants...
}
