import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/calculators/calculation_history_item.dart';
import '../services/calculation_history_service.dart';

// Service provider
final calculationHistoryServiceProvider = Provider<CalculationHistoryService>((ref) {
  return CalculationHistoryService();
});

// State providers for filters and search
final historySearchQueryProvider = StateProvider<String>((ref) => '');
final historyCalculatorTypeFilterProvider = StateProvider<CalculatorType?>((ref) => null);
final historyShowFavoritesOnlyProvider = StateProvider<bool>((ref) => false);
final historyDateRangeFilterProvider = StateProvider<DateRange?>((ref) => null);

// All history items provider
final calculationHistoryProvider = FutureProvider<List<CalculationHistoryItem>>((ref) async {
  final service = ref.read(calculationHistoryServiceProvider);
  return await service.getHistory();
});

// Filtered history provider
final filteredHistoryProvider = Provider<AsyncValue<List<CalculationHistoryItem>>>((ref) {
  final historyAsync = ref.watch(calculationHistoryProvider);
  final searchQuery = ref.watch(historySearchQueryProvider);
  final typeFilter = ref.watch(historyCalculatorTypeFilterProvider);
  final showFavoritesOnly = ref.watch(historyShowFavoritesOnlyProvider);
  final dateRange = ref.watch(historyDateRangeFilterProvider);

  return historyAsync.when(
    data: (items) {
      var filteredItems = items;

      // Apply search filter
      if (searchQuery.isNotEmpty) {
        filteredItems = filteredItems.where((item) => item.matchesQuery(searchQuery)).toList();
      }

      // Apply calculator type filter
      if (typeFilter != null) {
        filteredItems = filteredItems.where((item) => item.calculatorType == typeFilter).toList();
      }

      // Apply favorites filter
      if (showFavoritesOnly) {
        filteredItems = filteredItems.where((item) => item.isFavorite).toList();
      }

      // Apply date range filter
      if (dateRange != null) {
        filteredItems = filteredItems.where((item) {
          return item.createdAt.isAfter(dateRange.start.subtract(const Duration(days: 1))) &&
                 item.createdAt.isBefore(dateRange.end.add(const Duration(days: 1)));
        }).toList();
      }

      // Sort by creation date (newest first)
      filteredItems.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return AsyncValue.data(filteredItems);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Favorite calculations provider
final favoriteCalculationsProvider = Provider<AsyncValue<List<CalculationHistoryItem>>>((ref) {
  final historyAsync = ref.watch(calculationHistoryProvider);

  return historyAsync.when(
    data: (items) {
      final favorites = items.where((item) => item.isFavorite).toList();
      favorites.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return AsyncValue.data(favorites);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Recent calculations provider (last 7 days)
final recentCalculationsProvider = Provider<AsyncValue<List<CalculationHistoryItem>>>((ref) {
  final historyAsync = ref.watch(calculationHistoryProvider);

  return historyAsync.when(
    data: (items) {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 7));
      final recent = items.where((item) => item.createdAt.isAfter(cutoffDate)).toList();
      recent.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return AsyncValue.data(recent);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// History statistics provider
final historyStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final service = ref.read(calculationHistoryServiceProvider);
  return await service.getStatistics();
});

// Calculator type counts provider
final calculatorTypeCountsProvider = Provider<AsyncValue<Map<CalculatorType, int>>>((ref) {
  final historyAsync = ref.watch(calculationHistoryProvider);

  return historyAsync.when(
    data: (items) {
      final counts = <CalculatorType, int>{};
      for (final item in items) {
        counts[item.calculatorType] = (counts[item.calculatorType] ?? 0) + 1;
      }
      return AsyncValue.data(counts);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Most used calculators provider
final mostUsedCalculatorsProvider = Provider<AsyncValue<List<MapEntry<CalculatorType, int>>>>((ref) {
  final countsAsync = ref.watch(calculatorTypeCountsProvider);

  return countsAsync.when(
    data: (counts) {
      final sortedEntries = counts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      return AsyncValue.data(sortedEntries);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// History actions provider
final historyActionsProvider = Provider<HistoryActions>((ref) {
  final service = ref.read(calculationHistoryServiceProvider);
  return HistoryActions(service, ref);
});

class HistoryActions {
  final CalculationHistoryService _service;
  final Ref _ref;

  HistoryActions(this._service, this._ref);

  Future<void> saveCalculation(CalculationHistoryItem item) async {
    await _service.saveCalculation(item);
    _ref.invalidate(calculationHistoryProvider);
  }

  Future<void> toggleFavorite(String id) async {
    await _service.toggleFavorite(id);
    _ref.invalidate(calculationHistoryProvider);
  }

  Future<void> deleteCalculation(String id) async {
    await _service.deleteCalculation(id);
    _ref.invalidate(calculationHistoryProvider);
  }

  Future<void> updateCalculation(CalculationHistoryItem item) async {
    await _service.updateCalculation(item);
    _ref.invalidate(calculationHistoryProvider);
  }

  Future<void> exportHistory(List<String> ids, ExportFormat format) async {
    await _service.exportHistory(ids, format);
  }

  Future<void> cleanupOldEntries({int maxAgeInDays = 365}) async {
    await _service.cleanupOldEntries(maxAgeInDays: maxAgeInDays);
    _ref.invalidate(calculationHistoryProvider);
  }

  Future<Map<String, dynamic>> exportBackup() async {
    return await _service.exportBackup();
  }

  Future<void> importBackup(Map<String, dynamic> backup) async {
    await _service.importBackup(backup);
    _ref.invalidate(calculationHistoryProvider);
  }

  void setSearchQuery(String query) {
    _ref.read(historySearchQueryProvider.notifier).state = query;
  }

  void setCalculatorTypeFilter(CalculatorType? type) {
    _ref.read(historyCalculatorTypeFilterProvider.notifier).state = type;
  }

  void setShowFavoritesOnly(bool showFavoritesOnly) {
    _ref.read(historyShowFavoritesOnlyProvider.notifier).state = showFavoritesOnly;
  }

  void setDateRangeFilter(DateRange? dateRange) {
    _ref.read(historyDateRangeFilterProvider.notifier).state = dateRange;
  }

  void clearFilters() {
    _ref.read(historySearchQueryProvider.notifier).state = '';
    _ref.read(historyCalculatorTypeFilterProvider.notifier).state = null;
    _ref.read(historyShowFavoritesOnlyProvider.notifier).state = false;
    _ref.read(historyDateRangeFilterProvider.notifier).state = null;
  }
}

// Selected items provider for bulk operations
final selectedHistoryItemsProvider = StateProvider<List<CalculationHistoryItem>>((ref) => []);

// Bulk actions provider
final historyBulkActionsProvider = Provider<HistoryBulkActions>((ref) {
  final service = ref.read(calculationHistoryServiceProvider);
  return HistoryBulkActions(service, ref);
});

class HistoryBulkActions {
  final CalculationHistoryService _service;
  final Ref _ref;

  HistoryBulkActions(this._service, this._ref);

  List<CalculationHistoryItem> get selectedItems => _ref.read(selectedHistoryItemsProvider);

  void toggleSelection(CalculationHistoryItem item) {
    final currentSelection = _ref.read(selectedHistoryItemsProvider);
    final newSelection = List<CalculationHistoryItem>.from(currentSelection);
    
    if (newSelection.contains(item)) {
      newSelection.remove(item);
    } else {
      newSelection.add(item);
    }
    
    _ref.read(selectedHistoryItemsProvider.notifier).state = newSelection;
  }

  void selectAll(List<CalculationHistoryItem> items) {
    _ref.read(selectedHistoryItemsProvider.notifier).state = List.from(items);
  }

  void clearSelection() {
    _ref.read(selectedHistoryItemsProvider.notifier).state = [];
  }

  Future<void> deleteSelected() async {
    final selected = selectedItems;
    for (final item in selected) {
      await _service.deleteCalculation(item.id);
    }
    clearSelection();
    _ref.invalidate(calculationHistoryProvider);
  }

  Future<void> toggleFavoriteSelected(bool favorite) async {
    final selected = selectedItems;
    for (final item in selected) {
      if (item.isFavorite != favorite) {
        await _service.toggleFavorite(item.id);
      }
    }
    clearSelection();
    _ref.invalidate(calculationHistoryProvider);
  }

  Future<void> exportSelected(ExportFormat format) async {
    final selected = selectedItems;
    final ids = selected.map((item) => item.id).toList();
    await _service.exportHistory(ids, format);
    clearSelection();
  }
}

// Date range helper class
class DateRange {
  final DateTime start;
  final DateTime end;

  const DateRange({required this.start, required this.end});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DateRange && other.start == start && other.end == end;
  }

  @override
  int get hashCode => start.hashCode ^ end.hashCode;

  @override
  String toString() => 'DateRange(start: $start, end: $end)';
}

// Predefined date ranges
final predefinedDateRangesProvider = Provider<List<PredefinedDateRange>>((ref) {
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  
  return [
    PredefinedDateRange(
      name: 'Aujourd\'hui',
      range: DateRange(start: today, end: today.add(const Duration(days: 1))),
    ),
    PredefinedDateRange(
      name: 'Cette semaine',
      range: DateRange(
        start: today.subtract(Duration(days: now.weekday - 1)),
        end: today.add(Duration(days: 7 - now.weekday)),
      ),
    ),
    PredefinedDateRange(
      name: 'Ce mois',
      range: DateRange(
        start: DateTime(now.year, now.month, 1),
        end: DateTime(now.year, now.month + 1, 1),
      ),
    ),
    PredefinedDateRange(
      name: 'Les 30 derniers jours',
      range: DateRange(
        start: today.subtract(const Duration(days: 30)),
        end: today.add(const Duration(days: 1)),
      ),
    ),
    PredefinedDateRange(
      name: 'Cette année',
      range: DateRange(
        start: DateTime(now.year, 1, 1),
        end: DateTime(now.year + 1, 1, 1),
      ),
    ),
  ];
});

class PredefinedDateRange {
  final String name;
  final DateRange range;

  const PredefinedDateRange({required this.name, required this.range});
}
