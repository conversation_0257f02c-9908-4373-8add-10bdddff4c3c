import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/guide/personal_note_data.dart';
import '../services/notes_service.dart';

/// Provider for the notes service instance
final notesServiceProvider = Provider<NotesService>((ref) {
  return NotesService();
});

/// Provider for all notes with reactive updates
final personalNotesProvider = StreamProvider<List<PersonalNoteData>>((ref) {
  final service = ref.read(notesServiceProvider);
  return service.notesStream;
});

/// Provider for notes by guide
final notesByGuideProvider = FutureProvider.family<List<PersonalNoteData>, String>((ref, guideId) async {
  final service = ref.read(notesServiceProvider);
  return await service.getNotesByGuide(guideId);
});

/// Provider for notes by section
final notesBySectionProvider = FutureProvider.family<List<PersonalNoteData>, NotesBySectionParams>((ref, params) async {
  final service = ref.read(notesServiceProvider);
  return await service.getNotesBySection(params.guideId, params.sectionId);
});

/// Provider for notes by type
final notesByTypeProvider = FutureProvider.family<List<PersonalNoteData>, NoteType>((ref, type) async {
  final service = ref.read(notesServiceProvider);
  return await service.getNotesByType(type);
});

/// Provider for notes by tags
final notesByTagsProvider = FutureProvider.family<List<PersonalNoteData>, List<String>>((ref, tags) async {
  final service = ref.read(notesServiceProvider);
  return await service.getNotesByTags(tags);
});

/// Provider for private notes only
final privateNotesProvider = FutureProvider<List<PersonalNoteData>>((ref) async {
  final service = ref.read(notesServiceProvider);
  return await service.getPrivateNotes();
});

/// Provider for public notes only
final publicNotesProvider = FutureProvider<List<PersonalNoteData>>((ref) async {
  final service = ref.read(notesServiceProvider);
  return await service.getPublicNotes();
});

/// Provider for notes with reminders
final notesWithRemindersProvider = FutureProvider<List<PersonalNoteData>>((ref) async {
  final service = ref.read(notesServiceProvider);
  return await service.getNotesWithReminders();
});

/// Provider for upcoming reminders
final upcomingRemindersProvider = FutureProvider.family<List<PersonalNoteData>, int>((ref, daysAhead) async {
  final service = ref.read(notesServiceProvider);
  return await service.getUpcomingReminders(daysAhead: daysAhead);
});

/// Provider for overdue reminders
final overdueRemindersProvider = FutureProvider<List<PersonalNoteData>>((ref) async {
  final service = ref.read(notesServiceProvider);
  return await service.getOverdueReminders();
});

/// Provider for a specific note by ID
final personalNoteProvider = FutureProvider.family<PersonalNoteData?, String>((ref, id) async {
  final service = ref.read(notesServiceProvider);
  return await service.getNote(id);
});

/// Provider for note search results
final noteSearchProvider = FutureProvider.family<List<PersonalNoteData>, String>((ref, query) async {
  final service = ref.read(notesServiceProvider);
  return await service.searchNotes(query);
});

/// Provider for note statistics
final noteStatisticsProvider = FutureProvider<NoteStatistics>((ref) async {
  final service = ref.read(notesServiceProvider);
  return await service.getStatistics();
});

/// Provider for recent notes
final recentNotesProvider = FutureProvider.family<List<PersonalNoteData>, int>((ref, limit) async {
  final service = ref.read(notesServiceProvider);
  return await service.getRecentNotes(limit: limit);
});

/// Provider for notes created in the last N days
final recentNotesByDaysProvider = FutureProvider.family<List<PersonalNoteData>, int>((ref, days) async {
  final service = ref.read(notesServiceProvider);
  return await service.getRecentNotesByDays(days);
});

/// Provider for draft note
final draftNoteProvider = FutureProvider.family<PersonalNoteData?, String>((ref, originalId) async {
  final service = ref.read(notesServiceProvider);
  return await service.getDraftNote(originalId);
});

/// Provider for all unique tags
final allTagsProvider = FutureProvider<List<String>>((ref) async {
  final service = ref.read(notesServiceProvider);
  return await service.getAllTags();
});

/// State notifier for managing personal note operations
class PersonalNotesNotifier extends StateNotifier<AsyncValue<void>> {
  final NotesService _service;
  final Ref _ref;

  PersonalNotesNotifier(this._service, this._ref) : super(const AsyncValue.data(null));

  /// Add a new note
  Future<void> addNote(PersonalNoteData note) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.addNote(note);
      
      // Invalidate related providers to trigger refresh
      _ref.invalidate(personalNotesProvider);
      _ref.invalidate(notesByGuideProvider);
      _ref.invalidate(notesBySectionProvider);
      _ref.invalidate(notesByTypeProvider);
      _ref.invalidate(privateNotesProvider);
      _ref.invalidate(publicNotesProvider);
      _ref.invalidate(recentNotesProvider);
      _ref.invalidate(allTagsProvider);
      _ref.invalidate(noteStatisticsProvider);
      
      if (note.reminderDate != null) {
        _ref.invalidate(notesWithRemindersProvider);
        _ref.invalidate(upcomingRemindersProvider);
      }
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update an existing note
  Future<void> updateNote(PersonalNoteData note) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.updateNote(note);
      
      // Invalidate related providers
      _ref.invalidate(personalNotesProvider);
      _ref.invalidate(notesByGuideProvider);
      _ref.invalidate(notesBySectionProvider);
      _ref.invalidate(notesByTypeProvider);
      _ref.invalidate(personalNoteProvider);
      _ref.invalidate(privateNotesProvider);
      _ref.invalidate(publicNotesProvider);
      _ref.invalidate(allTagsProvider);
      _ref.invalidate(noteStatisticsProvider);
      
      if (note.reminderDate != null) {
        _ref.invalidate(notesWithRemindersProvider);
        _ref.invalidate(upcomingRemindersProvider);
        _ref.invalidate(overdueRemindersProvider);
      }
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Delete a note by ID
  Future<void> deleteNote(String id) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.deleteNote(id);
      
      // Invalidate related providers
      _ref.invalidate(personalNotesProvider);
      _ref.invalidate(notesByGuideProvider);
      _ref.invalidate(notesBySectionProvider);
      _ref.invalidate(notesByTypeProvider);
      _ref.invalidate(personalNoteProvider);
      _ref.invalidate(privateNotesProvider);
      _ref.invalidate(publicNotesProvider);
      _ref.invalidate(notesWithRemindersProvider);
      _ref.invalidate(upcomingRemindersProvider);
      _ref.invalidate(overdueRemindersProvider);
      _ref.invalidate(recentNotesProvider);
      _ref.invalidate(allTagsProvider);
      _ref.invalidate(noteStatisticsProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Delete notes for a specific section
  Future<void> deleteNotesBySection(String guideId, String sectionId) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.deleteNotesBySection(guideId, sectionId);
      
      // Invalidate related providers
      _ref.invalidate(personalNotesProvider);
      _ref.invalidate(notesByGuideProvider);
      _ref.invalidate(notesBySectionProvider);
      _ref.invalidate(privateNotesProvider);
      _ref.invalidate(publicNotesProvider);
      _ref.invalidate(notesWithRemindersProvider);
      _ref.invalidate(allTagsProvider);
      _ref.invalidate(noteStatisticsProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Clear all notes
  Future<void> clearAllNotes() async {
    state = const AsyncValue.loading();
    
    try {
      await _service.clearAllNotes();
      
      // Invalidate all providers
      _ref.invalidate(personalNotesProvider);
      _ref.invalidate(notesByGuideProvider);
      _ref.invalidate(notesBySectionProvider);
      _ref.invalidate(notesByTypeProvider);
      _ref.invalidate(notesByTagsProvider);
      _ref.invalidate(privateNotesProvider);
      _ref.invalidate(publicNotesProvider);
      _ref.invalidate(notesWithRemindersProvider);
      _ref.invalidate(upcomingRemindersProvider);
      _ref.invalidate(overdueRemindersProvider);
      _ref.invalidate(personalNoteProvider);
      _ref.invalidate(noteSearchProvider);
      _ref.invalidate(noteStatisticsProvider);
      _ref.invalidate(recentNotesProvider);
      _ref.invalidate(recentNotesByDaysProvider);
      _ref.invalidate(draftNoteProvider);
      _ref.invalidate(allTagsProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Auto-save a note (used for draft functionality)
  Future<void> autoSaveNote(PersonalNoteData note) async {
    try {
      await _service.autoSaveNote(note);
      
      // Invalidate draft provider
      _ref.invalidate(draftNoteProvider);
    } catch (error) {
      // Handle auto-save errors silently or with minimal disruption
      // In production, you might want to log this to a proper logging service
    }
  }

  /// Delete draft version of a note
  Future<void> deleteDraftNote(String originalId) async {
    try {
      await _service.deleteDraftNote(originalId);
      
      // Invalidate draft provider
      _ref.invalidate(draftNoteProvider);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Import notes from JSON
  Future<ImportResult> importNotes(Map<String, dynamic> data, {bool merge = true}) async {
    state = const AsyncValue.loading();
    
    try {
      final result = await _service.importNotes(data, merge: merge);
      
      if (result.success) {
        // Invalidate all providers on successful import
        _ref.invalidate(personalNotesProvider);
        _ref.invalidate(notesByGuideProvider);
        _ref.invalidate(notesBySectionProvider);
        _ref.invalidate(notesByTypeProvider);
        _ref.invalidate(notesByTagsProvider);
        _ref.invalidate(privateNotesProvider);
        _ref.invalidate(publicNotesProvider);
        _ref.invalidate(notesWithRemindersProvider);
        _ref.invalidate(upcomingRemindersProvider);
        _ref.invalidate(overdueRemindersProvider);
        _ref.invalidate(personalNoteProvider);
        _ref.invalidate(noteStatisticsProvider);
        _ref.invalidate(recentNotesProvider);
        _ref.invalidate(recentNotesByDaysProvider);
        _ref.invalidate(allTagsProvider);
      }
      
      state = const AsyncValue.data(null);
      return result;
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return ImportResult(success: false, message: error.toString());
    }
  }

  /// Export notes to JSON
  Future<Map<String, dynamic>> exportNotes({bool includePrivate = false}) async {
    try {
      return await _service.exportNotes(includePrivate: includePrivate);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

/// Provider for the personal notes notifier
final personalNotesNotifierProvider = StateNotifierProvider<PersonalNotesNotifier, AsyncValue<void>>((ref) {
  final service = ref.read(notesServiceProvider);
  return PersonalNotesNotifier(service, ref);
});

/// Parameter classes for providers
class NotesBySectionParams {
  final String guideId;
  final String sectionId;

  const NotesBySectionParams({
    required this.guideId,
    required this.sectionId,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotesBySectionParams &&
        other.guideId == guideId &&
        other.sectionId == sectionId;
  }

  @override
  int get hashCode => Object.hash(guideId, sectionId);
}

/// Extension methods for easier note management
extension PersonalNotesProviderExtensions on WidgetRef {
  /// Quick method to add a note
  Future<void> addNote(PersonalNoteData note) {
    return read(personalNotesNotifierProvider.notifier).addNote(note);
  }

  /// Quick method to update a note
  Future<void> updateNote(PersonalNoteData note) {
    return read(personalNotesNotifierProvider.notifier).updateNote(note);
  }

  /// Quick method to delete a note
  Future<void> deleteNote(String id) {
    return read(personalNotesNotifierProvider.notifier).deleteNote(id);
  }

  /// Quick method to get notes for a section
  AsyncValue<List<PersonalNoteData>> getSectionNotes(String guideId, String sectionId) {
    return watch(notesBySectionProvider(NotesBySectionParams(
      guideId: guideId,
      sectionId: sectionId,
    )));
  }

  /// Quick method to get notes by guide
  AsyncValue<List<PersonalNoteData>> getGuideNotes(String guideId) {
    return watch(notesByGuideProvider(guideId));
  }

  /// Quick method to search notes
  AsyncValue<List<PersonalNoteData>> searchNotes(String query) {
    return watch(noteSearchProvider(query));
  }

  /// Quick method to get recent notes
  AsyncValue<List<PersonalNoteData>> getRecentNotes(int limit) {
    return watch(recentNotesProvider(limit));
  }

  /// Quick method to get upcoming reminders
  AsyncValue<List<PersonalNoteData>> getUpcomingReminders(int daysAhead) {
    return watch(upcomingRemindersProvider(daysAhead));
  }

  /// Quick method to get note statistics
  AsyncValue<NoteStatistics> getNoteStatistics() {
    return watch(noteStatisticsProvider);
  }

  /// Quick method to get all tags
  AsyncValue<List<String>> getAllTags() {
    return watch(allTagsProvider);
  }
}