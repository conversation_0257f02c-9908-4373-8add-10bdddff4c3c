import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/liasse_fiscale_service.dart';
import '../models/liasse_fiscale/guide_liasse_data.dart';
import '../models/liasse_fiscale/checklist_documents_data.dart';
import '../models/liasse_fiscale/obligations_fiscales_data.dart';

// Provider for Liasse Fiscale Service
final liasseFiscaleServiceProvider = Provider<LiasseFiscaleService>((ref) {
  return LiasseFiscaleService();
});

// Provider for Guide data
final guideDataProvider = FutureProvider<GuideLiasseData>((ref) async {
  final service = ref.watch(liasseFiscaleServiceProvider);
  return service.getGuideData();
});

// Provider for Checklist data
final checklistDataProvider = FutureProvider<ChecklistDocumentsData>((ref) async {
  final service = ref.watch(liasseFiscaleServiceProvider);
  return service.getChecklistData();
});

// Provider for Obligations data
final obligationsDataProvider = FutureProvider<ObligationsFiscalesData>((ref) async {
  final service = ref.watch(liasseFiscaleServiceProvider);
  return service.getObligationsData();
});

// State providers for checklist completion tracking
final checklistCompletionProvider = StateProvider.family<bool, String>((ref, itemId) {
  return false; // Default to not completed
});

// Provider for overall checklist completion status
final checklistProgressProvider = Provider<ChecklistProgress>((ref) {
  final checklistData = ref.watch(checklistDataProvider).maybeWhen(
    data: (data) => data,
    orElse: () => null,
  );

  if (checklistData == null) {
    return ChecklistProgress(
      totalItems: 0,
      completedItems: 0,
      completionPercentage: 0.0,
      isComplete: false,
    );
  }

  // Extract all checklist items from the data
  final List<String> allItemIds = [];
  
  // Get items from different categories
  final categories = checklistData.categories;
  for (final category in categories) {
    final items = category['items'] as List<dynamic>? ?? [];
    for (final item in items) {
      if (item['id'] != null) {
        allItemIds.add(item['id'].toString());
      }
    }
  }

  // Count completed items
  int completedCount = 0;
  for (final itemId in allItemIds) {
    final isCompleted = ref.watch(checklistCompletionProvider(itemId));
    if (isCompleted) {
      completedCount++;
    }
  }

  final totalItems = allItemIds.length;
  final completionPercentage = totalItems > 0 ? (completedCount / totalItems) * 100 : 0.0;

  return ChecklistProgress(
    totalItems: totalItems,
    completedItems: completedCount,
    completionPercentage: completionPercentage,
    isComplete: completedCount == totalItems && totalItems > 0,
  );
});

// Provider for checklist categories
final checklistCategoriesProvider = Provider<List<dynamic>>((ref) {
  final checklistData = ref.watch(checklistDataProvider).maybeWhen(
    data: (data) => data,
    orElse: () => null,
  );

  return checklistData?.categories ?? [];
});

// Provider for mandatory documents
final mandatoryDocumentsProvider = Provider<List<dynamic>>((ref) {
  final categories = ref.watch(checklistCategoriesProvider);
  final List<dynamic> mandatoryDocs = [];

  for (final category in categories) {
    final items = category['items'] as List<dynamic>? ?? [];
    for (final item in items) {
      if (item['mandatory'] == true) {
        mandatoryDocs.add(item);
      }
    }
  }

  return mandatoryDocs;
});

// Provider for optional documents
final optionalDocumentsProvider = Provider<List<dynamic>>((ref) {
  final categories = ref.watch(checklistCategoriesProvider);
  final List<dynamic> optionalDocs = [];

  for (final category in categories) {
    final items = category['items'] as List<dynamic>? ?? [];
    for (final item in items) {
      if (item['mandatory'] != true) {
        optionalDocs.add(item);
      }
    }
  }

  return optionalDocs;
});

// Provider for fiscal calendar data
final fiscalCalendarProvider = Provider<List<dynamic>>((ref) {
  final obligationsData = ref.watch(obligationsDataProvider).maybeWhen(
    data: (data) => data,
    orElse: () => null,
  );

  return obligationsData?.calendar ?? [];
});

// Provider for penalty information
final penaltyInfoProvider = Provider<Map<String, dynamic>?>((ref) {
  final obligationsData = ref.watch(obligationsDataProvider).maybeWhen(
    data: (data) => data,
    orElse: () => null,
  );

  return obligationsData?.penalties;
});

// Provider for compliance requirements
final complianceRequirementsProvider = Provider<List<dynamic>>((ref) {
  final obligationsData = ref.watch(obligationsDataProvider).maybeWhen(
    data: (data) => data,
    orElse: () => null,
  );

  return obligationsData?.complianceRequirements ?? [];
});

// State provider for checklist filter
final checklistFilterProvider = StateProvider<ChecklistFilter>((ref) {
  return ChecklistFilter.all;
});

// State provider for search query
final checklistSearchProvider = StateProvider<String>((ref) {
  return '';
});

// Provider for filtered checklist items
final filteredChecklistItemsProvider = Provider<List<dynamic>>((ref) {
  final categories = ref.watch(checklistCategoriesProvider);
  final filter = ref.watch(checklistFilterProvider);
  final searchQuery = ref.watch(checklistSearchProvider).toLowerCase();

  List<dynamic> allItems = [];
  
  // Collect all items from categories
  for (final category in categories) {
    final items = category['items'] as List<dynamic>? ?? [];
    for (final item in items) {
      allItems.add({
        ...item,
        'category': category['name'],
      });
    }
  }

  // Apply filter
  switch (filter) {
    case ChecklistFilter.mandatory:
      allItems = allItems.where((item) => item['mandatory'] == true).toList();
      break;
    case ChecklistFilter.optional:
      allItems = allItems.where((item) => item['mandatory'] != true).toList();
      break;
    case ChecklistFilter.completed:
      allItems = allItems.where((item) {
        final itemId = item['id']?.toString() ?? '';
        return ref.watch(checklistCompletionProvider(itemId));
      }).toList();
      break;
    case ChecklistFilter.pending:
      allItems = allItems.where((item) {
        final itemId = item['id']?.toString() ?? '';
        return !ref.watch(checklistCompletionProvider(itemId));
      }).toList();
      break;
    case ChecklistFilter.all:
      // No filtering
      break;
  }

  // Apply search
  if (searchQuery.isNotEmpty) {
    allItems = allItems.where((item) {
      final title = (item['title'] ?? '').toString().toLowerCase();
      final description = (item['description'] ?? '').toString().toLowerCase();
      final category = (item['category'] ?? '').toString().toLowerCase();
      
      return title.contains(searchQuery) || 
             description.contains(searchQuery) || 
             category.contains(searchQuery);
    }).toList();
  }

  return allItems;
});

// Data classes for type safety
class ChecklistProgress {
  final int totalItems;
  final int completedItems;
  final double completionPercentage;
  final bool isComplete;

  const ChecklistProgress({
    required this.totalItems,
    required this.completedItems,
    required this.completionPercentage,
    required this.isComplete,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChecklistProgress &&
          runtimeType == other.runtimeType &&
          totalItems == other.totalItems &&
          completedItems == other.completedItems &&
          completionPercentage == other.completionPercentage &&
          isComplete == other.isComplete;

  @override
  int get hashCode =>
      totalItems.hashCode ^
      completedItems.hashCode ^
      completionPercentage.hashCode ^
      isComplete.hashCode;
}

enum ChecklistFilter {
  all,
  mandatory,
  optional,
  completed,
  pending,
}

// Provider for resetting checklist completion
final resetChecklistProvider = Provider<void Function()>((ref) {
  return () {
    final categories = ref.read(checklistCategoriesProvider);
    
    // Reset all checklist items
    for (final category in categories) {
      final items = category['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item['id'] != null) {
          final itemId = item['id'].toString();
          ref.read(checklistCompletionProvider(itemId).notifier).state = false;
        }
      }
    }
  };
});

// Provider for bulk operations
final bulkChecklistOperationsProvider = Provider<BulkChecklistOperations>((ref) {
  return BulkChecklistOperations(ref);
});

class BulkChecklistOperations {
  final Ref _ref;

  BulkChecklistOperations(this._ref);

  void markAllCompleted() {
    final categories = _ref.read(checklistCategoriesProvider);
    
    for (final category in categories) {
      final items = category['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item['id'] != null) {
          final itemId = item['id'].toString();
          _ref.read(checklistCompletionProvider(itemId).notifier).state = true;
        }
      }
    }
  }

  void markAllPending() {
    final categories = _ref.read(checklistCategoriesProvider);
    
    for (final category in categories) {
      final items = category['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item['id'] != null) {
          final itemId = item['id'].toString();
          _ref.read(checklistCompletionProvider(itemId).notifier).state = false;
        }
      }
    }
  }

  void markMandatoryCompleted() {
    final categories = _ref.read(checklistCategoriesProvider);
    
    for (final category in categories) {
      final items = category['items'] as List<dynamic>? ?? [];
      for (final item in items) {
        if (item['id'] != null && item['mandatory'] == true) {
          final itemId = item['id'].toString();
          _ref.read(checklistCompletionProvider(itemId).notifier).state = true;
        }
      }
    }
  }
}