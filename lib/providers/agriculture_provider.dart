import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/agriculture_service.dart';

// Provider for Agriculture Service
final agricultureServiceProvider = Provider<AgricultureService>((ref) {
  return AgricultureService();
});

// Provider for TVA Agricole data
final tvaAgricoleDataProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final service = ref.watch(agricultureServiceProvider);
  await service.initialize();
  return service.getTvaAgricoleData();
});

// Provider for IS Agricole data
final isAgricoleDataProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final service = ref.watch(agricultureServiceProvider);
  await service.initialize();
  return service.getIsAgricoleData();
});

// Provider for IR Agricole data
final irAgricoleDataProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final service = ref.watch(agricultureServiceProvider);
  await service.initialize();
  return service.getIrAgricoleData();
});

// Provider for Comptabilité Agricole data
final comptabiliteAgricoleDataProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final service = ref.watch(agricultureServiceProvider);
  await service.initialize();
  return service.getComptabiliteAgricoleData();
});

// Provider for TVA Agricole categories
final tvaAgricoleCategoriesProvider = FutureProvider<List<dynamic>>((ref) async {
  final service = ref.watch(agricultureServiceProvider);
  await service.initialize();
  return service.getTvaAgricoleCategories();
});

// Provider for IS Agricole regimes
final isAgricoleRegimesProvider = FutureProvider<List<dynamic>>((ref) async {
  final service = ref.watch(agricultureServiceProvider);
  await service.initialize();
  return service.getIsAgricoleRegimes();
});

// Provider for agriculture tax calculation
final agricultureTaxCalculatorProvider = Provider.family<Map<String, dynamic>, AgricultureTaxParams>(
  (ref, params) {
    final service = ref.watch(agricultureServiceProvider);
    return service.calculateAgricultureTaxSummary(
      revenue: params.revenue,
      entityType: params.entityType,
      dependents: params.dependents,
      isOrganic: params.isOrganic,
      isNewCompany: params.isNewCompany,
    );
  },
);

// Parameters for agriculture tax calculation
class AgricultureTaxParams {
  final double revenue;
  final String entityType;
  final int dependents;
  final bool isOrganic;
  final bool isNewCompany;

  const AgricultureTaxParams({
    required this.revenue,
    required this.entityType,
    this.dependents = 0,
    this.isOrganic = false,
    this.isNewCompany = false,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AgricultureTaxParams &&
          runtimeType == other.runtimeType &&
          revenue == other.revenue &&
          entityType == other.entityType &&
          dependents == other.dependents &&
          isOrganic == other.isOrganic &&
          isNewCompany == other.isNewCompany;

  @override
  int get hashCode =>
      revenue.hashCode ^
      entityType.hashCode ^
      dependents.hashCode ^
      isOrganic.hashCode ^
      isNewCompany.hashCode;
}