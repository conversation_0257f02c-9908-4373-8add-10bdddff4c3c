// PersonalizedLearningProvider for Moroccan Accounting App
// Riverpod providers for personalized learning recommendations, analytics, and progress optimization.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/gamification/personalized_learning_path.dart';
import '../services/personalized_learning_service.dart';

// Provider for current learning path
final learningPathProvider = Provider<PersonalizedLearningPath?>((ref) {
  final service = ref.watch(personalizedLearningServiceProvider);
  // TODO: Replace with actual userId
  final userId = 'currentUserId';
  return service.learningPathBox.get(userId) as PersonalizedLearningPath?;
});

// Provider for recommended activities (sections/quizzes)
final recommendedActivitiesProvider = Provider<List<String>>((ref) {
  final path = ref.watch(learningPathProvider);
  if (path == null) return [];
  return [...path.recommendedSections, ...path.recommendedQuizzes];
});

// Provider for learning preferences (placeholder)
final learningPreferencesProvider = StateProvider<Map<String, dynamic>>((ref) => {});

// Provider for adaptive difficulty
final adaptiveDifficultyProvider = Provider<String>((ref) {
  final path = ref.watch(learningPathProvider);
  return path?.difficultyLevel ?? 'medium';
});

// Provider for learning efficiency metrics (placeholder)
final learningEfficiencyProvider = Provider<Map<String, dynamic>>((ref) {
  final service = ref.watch(personalizedLearningServiceProvider);
  // TODO: Replace with actual userId
  final userId = 'currentUserId';
  return service.getLearningEfficiency(userId);
});

// Provider for progress optimization (placeholder)
final progressOptimizationProvider = Provider<Map<String, dynamic>>((ref) {
  // TODO: Implement optimization logic
  return {};
});

// Provider for error handling
final personalizedLearningErrorProvider = StateProvider<String?>((ref) => null);

// Provider for personalized learning service (replace with actual instance)
final personalizedLearningServiceProvider = Provider<PersonalizedLearningService>((ref) {
  // TODO: Replace with actual Hive box initialization after running build_runner
  throw UnimplementedError('PersonalizedLearningService requires Hive box initialization. Run build_runner first.');
});

// End of personalized_learning_provider.dart
