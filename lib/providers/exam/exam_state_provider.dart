import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/exam/exam.dart';
import '../../models/exam/exam_question.dart';
import '../../models/exam/active_exam_state.dart';
import '../../models/exam/exam_attempt.dart';
import '../../services/user_progress_service.dart';
import '../user_progress_provider.dart'; // To access the service provider

// State class for the Exam session
class ExamSessionState {
  final Exam exam;
  final int currentQuestionIndex;
  final Map<int, int> userAnswers; // Map<questionIndex, answerIndex>
  final bool isSubmitted;
  final int? score; // Score is calculated only after submission
  final ExamAttempt? attempt; // Added to hold the attempt details after submission
  final DateTime? startTime; // Added to track exam start time
  final Duration? timeElapsed; // Added to track elapsed time

  ExamSessionState({
    required this.exam,
    this.currentQuestionIndex = 0,
    Map<int, int>? userAnswers,
    this.isSubmitted = false,
    this.score,
    this.attempt, // Added
    this.startTime,
    this.timeElapsed,
  }) : userAnswers = userAnswers ?? {};

  ExamSessionState copyWith({
    Exam? exam,
    int? currentQuestionIndex,
    Map<int, int>? userAnswers,
    bool? isSubmitted,
    int? score, // Allow updating score
    ExamAttempt? attempt, // Added
    DateTime? startTime,
    Duration? timeElapsed,
    bool clearScore = false, // Flag to explicitly nullify score
  }) {
    return ExamSessionState(
      exam: exam ?? this.exam,
      currentQuestionIndex: currentQuestionIndex ?? this.currentQuestionIndex,
      userAnswers: userAnswers ?? this.userAnswers,
      isSubmitted: isSubmitted ?? this.isSubmitted,
      score: clearScore ? null : (score ?? this.score),
      attempt: attempt ?? this.attempt, // Added
      startTime: startTime ?? this.startTime,
      timeElapsed: timeElapsed ?? this.timeElapsed,
    );
  }

  ExamQuestion get currentQuestion => exam.questions[currentQuestionIndex];
  int get totalQuestions => exam.questions.length;
  bool get isFirstQuestion => currentQuestionIndex == 0;
  bool get isLastQuestion => currentQuestionIndex == totalQuestions - 1;
}

// StateNotifier for managing the Exam session
class ExamStateNotifier extends StateNotifier<ExamSessionState?> {
  final UserProgressService _progressService;

  ExamStateNotifier(this._progressService) : super(null);

  // Start a new exam or resume an existing one
  Future<void> loadExam(Exam exam) async {
    // Check if there's an active state for this exam
    final activeState = _progressService.getActiveExamState(exam.id);
    final now = DateTime.now();

    if (activeState != null) {
      // Resume exam
      state = ExamSessionState(
        exam: exam,
        currentQuestionIndex: activeState.currentQuestionIndex,
        userAnswers: Map<int, int>.from(activeState.userAnswers), // Create mutable copy
        startTime: now, // Use current time for resumed exams (you might want to store this separately)
        timeElapsed: Duration.zero,
      );
      // Resuming exam ${exam.id} at question ${state!.currentQuestionIndex}
    } else {
      // Start new exam
      state = ExamSessionState(
        exam: exam,
        startTime: now,
        timeElapsed: Duration.zero,
      );
       // Starting new exam ${exam.id}
    }
  }

  void selectAnswer(int answerIndex) {
    if (state == null || state!.isSubmitted) return;

    final currentAnswers = Map<int, int>.from(state!.userAnswers);
    currentAnswers[state!.currentQuestionIndex] = answerIndex;

    state = state!.copyWith(userAnswers: currentAnswers);
    _saveActiveState(); // Save progress after answering
  }

  void nextQuestion() {
    if (state == null || state!.isLastQuestion || state!.isSubmitted) return;
    state = state!.copyWith(currentQuestionIndex: state!.currentQuestionIndex + 1);
     _saveActiveState(); // Save progress when changing question
  }

  void previousQuestion() {
    if (state == null || state!.isFirstQuestion || state!.isSubmitted) return;
    state = state!.copyWith(currentQuestionIndex: state!.currentQuestionIndex - 1);
     _saveActiveState(); // Save progress when changing question
  }

  void goToQuestion(int questionIndex) {
    if (state == null || state!.isSubmitted) return;
    if (questionIndex < 0 || questionIndex >= state!.totalQuestions) return;
    state = state!.copyWith(currentQuestionIndex: questionIndex);
    _saveActiveState(); // Save progress when changing question
  }

  // Submit the exam, calculate score, save attempt, and clear active state
  Future<void> submitExam() async {
    if (state == null || state!.isSubmitted) return;

    int correctAnswers = 0;
    final answersList = List<int?>.filled(state!.totalQuestions, null);

    for (int i = 0; i < state!.totalQuestions; i++) {
      final userAnswerIndex = state!.userAnswers[i];
      answersList[i] = userAnswerIndex; // Store user's answer index (or null)
      if (userAnswerIndex != null && userAnswerIndex == state!.exam.questions[i].correctAnswerIndex) {
        correctAnswers++;
      }
    }

    final attempt = ExamAttempt(
      examId: state!.exam.id,
      timestamp: DateTime.now(),
      score: correctAnswers,
      totalQuestions: state!.totalQuestions,
      userAnswers: answersList,
    );

    await _progressService.addExamAttempt(attempt);
    await _progressService.deleteActiveExamState(state!.exam.id); // Clear active state

    // Update state with submission status, score, and the attempt object
    state = state!.copyWith(
      isSubmitted: true, 
      score: correctAnswers,
      attempt: attempt, // Store the attempt in the state
    );
     // Exam ${state!.exam.id} submitted. Score: $correctAnswers/${state!.totalQuestions}
  }

  // Save the current state to Hive
  Future<void> _saveActiveState() async {
    if (state == null || state!.isSubmitted) return;

    final activeState = ActiveExamState(
      examId: state!.exam.id,
      currentQuestionIndex: state!.currentQuestionIndex,
      userAnswers: state!.userAnswers,
    );
    await _progressService.saveActiveExamState(activeState);
     // Saved active state for exam ${state!.exam.id}
  }

  // Call this when leaving the exam screen without submitting
  Future<void> pauseExam() async {
     if (state != null && !state!.isSubmitted) {
       await _saveActiveState();
       // Exam ${state!.exam.id} paused.
     }
     // Optionally clear the state notifier state if desired when pausing
     // state = null;
  }

  // Reset state notifier (e.g., when navigating away completely)
  void reset() {
    state = null;
  }
}

// Provider for the ExamStateNotifier
final examStateProvider = StateNotifierProvider<ExamStateNotifier, ExamSessionState?>((ref) {
  final progressService = ref.watch(userProgressServiceProvider);
  return ExamStateNotifier(progressService);
});
