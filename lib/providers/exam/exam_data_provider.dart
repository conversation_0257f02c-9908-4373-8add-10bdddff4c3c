import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/exam/exam.dart';
import '../../services/exam/exam_data_service.dart';

// Provider for the ExamDataService instance
// Consider initializing this service in main.dart and providing it if needed globally
final examDataServiceProvider = Provider<ExamDataService>((ref) {
  return ExamDataService();
});

// Provider to get the list of all exams
final examListProvider = FutureProvider<List<Exam>>((ref) async {
  final examService = ref.watch(examDataServiceProvider);
  // No need to explicitly call loadExams here, getExamList handles it
  return examService.getExamList();
});

// Provider to get a specific exam by its ID
// Use family modifier to pass the examId
final examByIdProvider = FutureProvider.family<Exam?, String>((ref, examId) async {
  final examService = ref.watch(examDataServiceProvider);
  // No need to explicitly call loadExams here, getExamById handles it
  return examService.getExamById(examId);
});
