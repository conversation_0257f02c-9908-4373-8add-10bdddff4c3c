import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/user_progress_service.dart';

// Provider for the UserProgressService instance
final userProgressServiceProvider = Provider<UserProgressService>((ref) {
  // This typically shouldn't be instantiated directly here if it needs async init.
  // It's better to initialize it in main and provide the instance.
  // For simplicity now, we assume it's initialized elsewhere or handle init differently.
  // A better approach might use FutureProvider or StateNotifierProvider if state changes.
  throw UnimplementedError('UserProgressService should be initialized in main and provided');
});

// Example using StateNotifierProvider if you need to react to progress changes
// final userProgressProvider = StateNotifierProvider<UserProgressNotifier, UserProgress?>((ref) {
//   final service = ref.watch(userProgressServiceProvider); // Assuming service is provided
//   return UserProgressNotifier(service);
// });

// class UserProgressNotifier extends StateNotifier<UserProgress?> {
//   final UserProgressService _service;
//   UserProgressNotifier(this._service) : super(null) {
//     loadProgress();
//   }

//   void loadProgress() {
//     state = _service.getCurrentUserProgress();
//   }

//   Future<void> updateUsername(String newName) async {
//     // ... implementation ...
//     loadProgress(); // Refresh state
//   }

//   Future<void> addAttempt(QuizAttempt attempt) async {
//     await _service.addQuizAttempt(attempt);
//     loadProgress(); // Refresh state
//   }
// }
