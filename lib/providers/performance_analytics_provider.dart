// Riverpod provider for performance analytics state management.
// Integrates with PerformanceAnalyticsService and PerformanceAnalytics model.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/analytics/performance_analytics.dart';
import '../services/performance_analytics_service.dart';
import '../services/adaptive_learning_service.dart';

/// StateNotifier to manage performance analytics.
/// Comments added for clarity as per .windsurfrules.
/// StateNotifier to manage performance analytics.
/// Comments added for clarity as per .windsurfrules.
class PerformanceAnalyticsNotifier extends StateNotifier<PerformanceAnalytics?> {
  final PerformanceAnalyticsService _service;

  PerformanceAnalyticsNotifier(this._service) : super(null);

  // Load analytics report for a category and date range.
  Future<void> loadReport(String categoryName, DateRange range) async {
    final analytics = await _service.generatePerformanceReport(categoryName, range);
    state = analytics;
  }

  // Refresh analytics for last week (default).
  Future<void> refresh(String categoryName) async {
    await loadReport(categoryName, DateRange.lastWeek());
  }
}

/// Provider for PerformanceAnalyticsNotifier.
/// Usage: ref.watch(performanceAnalyticsProvider) to get analytics data.
final performanceAnalyticsProvider = StateNotifierProvider<PerformanceAnalyticsNotifier, PerformanceAnalytics?>((ref) {
  final service = ref.watch(performanceAnalyticsServiceProvider);
  return PerformanceAnalyticsNotifier(service);
});

// Provider for PerformanceAnalyticsService (assumes service is stateless/singleton).
final adaptiveLearningServiceProvider = Provider<AdaptiveLearningService>((ref) {
  return AdaptiveLearningService();
});

final performanceAnalyticsServiceProvider = Provider<PerformanceAnalyticsService>((ref) {
  final adaptiveLearningService = ref.watch(adaptiveLearningServiceProvider);
  return PerformanceAnalyticsService(adaptiveLearningService: adaptiveLearningService);
});
