import 'package:flutter_riverpod/flutter_riverpod.dart';
    import '../services/quiz_data_service.dart';
    import '../models/quiz_model.dart';

    // Provider for the QuizDataService instance
    final quizDataServiceProvider = Provider<QuizDataService>((ref) {
      // Service instance should be created and initialized in main.dart
      throw UnimplementedError('QuizDataService should be initialized in main and provided');
    });

    // Optional: Provider to directly access the loaded QuizData
    // This depends on whether you prefer accessing via service or directly
    final quizDataProvider = Provider<QuizData?>((ref) {
      return ref.watch(quizDataServiceProvider).quizData;
    });
