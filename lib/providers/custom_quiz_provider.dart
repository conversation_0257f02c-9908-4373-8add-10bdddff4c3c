// Riverpod provider for custom quiz state management.
// Integrates with CustomQuizConfig model and QuestionSelectionService.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/quiz/custom_quiz_config.dart';
import '../models/quiz_model.dart';
import '../services/question_selection_service.dart';
import '../services/spaced_repetition_service.dart';

/// StateNotifier to manage custom quiz configuration and selected questions.
/// Comments added for clarity as per .windsurfrules.
/// StateNotifier to manage custom quiz configuration and selected questions.
/// Comments added for clarity as per .windsurfrules.
class CustomQuizNotifier extends StateNotifier<CustomQuizState> {
  final QuestionSelectionService _service;

  CustomQuizNotifier(this._service) : super(CustomQuizState.initial());

  // Update quiz configuration.
  void updateConfig(CustomQuizConfig config) {
    state = state.copyWith(config: config);
  }

  // Generate questions based on current config and available questions.
  Future<void> generateQuestions(List<QuizQuestion> availableQuestions) async {
    final questions = await _service.generateQuestionQueue(state.config, availableQuestions);
    state = state.copyWith(questions: questions);
  }

  // Reset quiz state.
  void reset() {
    state = CustomQuizState.initial();
  }
}

/// State for custom quiz: config + selected questions.
/// State for custom quiz: config + selected questions.
class CustomQuizState {
  final CustomQuizConfig config;
  final List<QuizQuestion> questions;

  CustomQuizState({required this.config, required this.questions});

  factory CustomQuizState.initial() => CustomQuizState(
    config: const CustomQuizConfig(),
    questions: [],
  );

  CustomQuizState copyWith({
    CustomQuizConfig? config,
    List<QuizQuestion>? questions,
  }) {
    return CustomQuizState(
      config: config ?? this.config,
      questions: questions ?? this.questions,
    );
  }
}

/// Provider for CustomQuizNotifier.
/// Usage: ref.watch(customQuizProvider) to get quiz state.
/// Provider for SpacedRepetitionService (for question selection).
final spacedRepetitionServiceProvider = Provider<SpacedRepetitionService>((ref) {
  return SpacedRepetitionService();
});

/// Provider for QuestionSelectionService (requires SpacedRepetitionService).
final questionSelectionServiceProvider = Provider<QuestionSelectionService>((ref) {
  final spacedRepetitionService = ref.watch(spacedRepetitionServiceProvider);
  return QuestionSelectionService(spacedRepetitionService: spacedRepetitionService);
});

/// Provider for CustomQuizNotifier.
/// Usage: ref.watch(customQuizProvider) to get quiz state.
final customQuizProvider = StateNotifierProvider<CustomQuizNotifier, CustomQuizState>((ref) {
  final service = ref.watch(questionSelectionServiceProvider);
  return CustomQuizNotifier(service);
});
