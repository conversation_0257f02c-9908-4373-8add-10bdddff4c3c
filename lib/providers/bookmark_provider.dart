import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/guide/bookmark_data.dart';
import '../services/bookmark_service.dart';

/// Provider for the bookmark service instance
final bookmarkServiceProvider = Provider<BookmarkService>((ref) {
  return BookmarkService();
});

/// Provider for all bookmarks with reactive updates
final bookmarksProvider = StreamProvider<List<BookmarkData>>((ref) {
  final service = ref.read(bookmarkServiceProvider);
  return service.bookmarksStream;
});

/// Provider for bookmarks by guide
final bookmarksByGuideProvider = FutureProvider.family<List<BookmarkData>, String>((ref, guideId) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.getBookmarksByGuide(guideId);
});

/// Provider for bookmarks by section
final bookmarksBySectionProvider = FutureProvider.family<List<BookmarkData>, BookmarkSectionParams>((ref, params) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.getBookmarksBySection(params.guideId, params.sectionId);
});

/// Provider for bookmarks by type
final bookmarksByTypeProvider = FutureProvider.family<List<BookmarkData>, BookmarkType>((ref, type) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.getBookmarksByType(type);
});

/// Provider for bookmarks by tags
final bookmarksByTagsProvider = FutureProvider.family<List<BookmarkData>, List<String>>((ref, tags) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.getBookmarksByTags(tags);
});

/// Provider to check if a section is bookmarked
final isBookmarkedProvider = FutureProvider.family<bool, BookmarkSectionParams>((ref, params) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.isBookmarked(params.guideId, params.sectionId);
});

/// Provider for a specific bookmark by ID
final bookmarkProvider = FutureProvider.family<BookmarkData?, String>((ref, id) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.getBookmark(id);
});

/// Provider for bookmark search results
final bookmarkSearchProvider = FutureProvider.family<List<BookmarkData>, String>((ref, query) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.searchBookmarks(query);
});

/// Provider for bookmark statistics
final bookmarkStatisticsProvider = FutureProvider<BookmarkStatistics>((ref) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.getStatistics();
});

/// Provider for recent bookmarks
final recentBookmarksProvider = FutureProvider.family<List<BookmarkData>, int>((ref, limit) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.getRecentBookmarks(limit: limit);
});

/// Provider for bookmarks created in the last N days
final recentBookmarksByDaysProvider = FutureProvider.family<List<BookmarkData>, int>((ref, days) async {
  final service = ref.read(bookmarkServiceProvider);
  return await service.getRecentBookmarksByDays(days);
});

/// State notifier for managing bookmark operations
class BookmarkNotifier extends StateNotifier<AsyncValue<void>> {
  final BookmarkService _service;
  final Ref _ref;

  BookmarkNotifier(this._service, this._ref) : super(const AsyncValue.data(null));

  /// Add a new bookmark
  Future<void> addBookmark(BookmarkData bookmark) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.addBookmark(bookmark);
      
      // Invalidate related providers to trigger refresh
      _ref.invalidate(bookmarksProvider);
      _ref.invalidate(bookmarksByGuideProvider);
      _ref.invalidate(bookmarksBySectionProvider);
      _ref.invalidate(bookmarksByTypeProvider);
      _ref.invalidate(isBookmarkedProvider);
      _ref.invalidate(bookmarkStatisticsProvider);
      _ref.invalidate(recentBookmarksProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update an existing bookmark
  Future<void> updateBookmark(BookmarkData bookmark) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.updateBookmark(bookmark);
      
      // Invalidate related providers
      _ref.invalidate(bookmarksProvider);
      _ref.invalidate(bookmarksByGuideProvider);
      _ref.invalidate(bookmarksBySectionProvider);
      _ref.invalidate(bookmarksByTypeProvider);
      _ref.invalidate(bookmarkProvider);
      _ref.invalidate(bookmarkStatisticsProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Delete a bookmark by ID
  Future<void> deleteBookmark(String id) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.deleteBookmark(id);
      
      // Invalidate related providers
      _ref.invalidate(bookmarksProvider);
      _ref.invalidate(bookmarksByGuideProvider);
      _ref.invalidate(bookmarksBySectionProvider);
      _ref.invalidate(bookmarksByTypeProvider);
      _ref.invalidate(isBookmarkedProvider);
      _ref.invalidate(bookmarkProvider);
      _ref.invalidate(bookmarkStatisticsProvider);
      _ref.invalidate(recentBookmarksProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Delete bookmarks for a specific section
  Future<void> deleteBookmarksBySection(String guideId, String sectionId) async {
    state = const AsyncValue.loading();
    
    try {
      await _service.deleteBookmarksBySection(guideId, sectionId);
      
      // Invalidate related providers
      _ref.invalidate(bookmarksProvider);
      _ref.invalidate(bookmarksByGuideProvider);
      _ref.invalidate(bookmarksBySectionProvider);
      _ref.invalidate(isBookmarkedProvider);
      _ref.invalidate(bookmarkStatisticsProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Clear all bookmarks
  Future<void> clearAllBookmarks() async {
    state = const AsyncValue.loading();
    
    try {
      await _service.clearAllBookmarks();
      
      // Invalidate all providers
      _ref.invalidate(bookmarksProvider);
      _ref.invalidate(bookmarksByGuideProvider);
      _ref.invalidate(bookmarksBySectionProvider);
      _ref.invalidate(bookmarksByTypeProvider);
      _ref.invalidate(bookmarksByTagsProvider);
      _ref.invalidate(isBookmarkedProvider);
      _ref.invalidate(bookmarkProvider);
      _ref.invalidate(bookmarkSearchProvider);
      _ref.invalidate(bookmarkStatisticsProvider);
      _ref.invalidate(recentBookmarksProvider);
      _ref.invalidate(recentBookmarksByDaysProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Toggle bookmark for a section
  Future<void> toggleBookmark({
    required String guideId,
    required String sectionId,
    required String title,
    String description = '',
    BookmarkType bookmarkType = BookmarkType.section,
    List<String> tags = const [],
    String? position,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final isCurrentlyBookmarked = await _service.isBookmarked(guideId, sectionId);
      
      if (isCurrentlyBookmarked) {
        // Remove existing bookmarks for this section
        await _service.deleteBookmarksBySection(guideId, sectionId);
      } else {
        // Add new bookmark
        final bookmark = BookmarkData(
          id: '${guideId}_${sectionId}_${DateTime.now().millisecondsSinceEpoch}',
          guideId: guideId,
          sectionId: sectionId,
          title: title,
          description: description,
          bookmarkType: bookmarkType,
          tags: tags,
          position: position,
        );
        await _service.addBookmark(bookmark);
      }
      
      // Invalidate related providers
      _ref.invalidate(bookmarksProvider);
      _ref.invalidate(bookmarksByGuideProvider);
      _ref.invalidate(bookmarksBySectionProvider);
      _ref.invalidate(isBookmarkedProvider);
      _ref.invalidate(bookmarkStatisticsProvider);
      _ref.invalidate(recentBookmarksProvider);
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Import bookmarks from JSON
  Future<ImportResult> importBookmarks(Map<String, dynamic> data, {bool merge = true}) async {
    state = const AsyncValue.loading();
    
    try {
      final result = await _service.importBookmarks(data, merge: merge);
      
      if (result.success) {
        // Invalidate all providers on successful import
        _ref.invalidate(bookmarksProvider);
        _ref.invalidate(bookmarksByGuideProvider);
        _ref.invalidate(bookmarksBySectionProvider);
        _ref.invalidate(bookmarksByTypeProvider);
        _ref.invalidate(bookmarksByTagsProvider);
        _ref.invalidate(isBookmarkedProvider);
        _ref.invalidate(bookmarkProvider);
        _ref.invalidate(bookmarkStatisticsProvider);
        _ref.invalidate(recentBookmarksProvider);
        _ref.invalidate(recentBookmarksByDaysProvider);
      }
      
      state = const AsyncValue.data(null);
      return result;
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return ImportResult(success: false, message: error.toString());
    }
  }

  /// Export bookmarks to JSON
  Future<Map<String, dynamic>> exportBookmarks() async {
    try {
      return await _service.exportBookmarks();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }
}

/// Provider for the bookmark notifier
final bookmarkNotifierProvider = StateNotifierProvider<BookmarkNotifier, AsyncValue<void>>((ref) {
  final service = ref.read(bookmarkServiceProvider);
  return BookmarkNotifier(service, ref);
});

/// Parameter classes for providers
class BookmarkSectionParams {
  final String guideId;
  final String sectionId;

  const BookmarkSectionParams({
    required this.guideId,
    required this.sectionId,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookmarkSectionParams &&
        other.guideId == guideId &&
        other.sectionId == sectionId;
  }

  @override
  int get hashCode => Object.hash(guideId, sectionId);
}

/// Extension methods for easier bookmark management
extension BookmarkProviderExtensions on WidgetRef {
  /// Quick method to toggle a bookmark
  Future<void> toggleBookmark({
    required String guideId,
    required String sectionId,
    required String title,
    String description = '',
    BookmarkType bookmarkType = BookmarkType.section,
    List<String> tags = const [],
    String? position,
  }) {
    return read(bookmarkNotifierProvider.notifier).toggleBookmark(
      guideId: guideId,
      sectionId: sectionId,
      title: title,
      description: description,
      bookmarkType: bookmarkType,
      tags: tags,
      position: position,
    );
  }

  /// Quick method to check if a section is bookmarked
  AsyncValue<bool> isBookmarked(String guideId, String sectionId) {
    return watch(isBookmarkedProvider(BookmarkSectionParams(
      guideId: guideId,
      sectionId: sectionId,
    )));
  }

  /// Quick method to get bookmarks for a section
  AsyncValue<List<BookmarkData>> getSectionBookmarks(String guideId, String sectionId) {
    return watch(bookmarksBySectionProvider(BookmarkSectionParams(
      guideId: guideId,
      sectionId: sectionId,
    )));
  }
}