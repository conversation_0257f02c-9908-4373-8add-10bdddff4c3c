// Riverpod provider for spaced repetition state management.
// Integrates with SpacedRepetitionService and SpacedRepetitionItem model.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/adaptive_learning/spaced_repetition_item.dart';
import '../services/spaced_repetition_service.dart';

/// StateNotifier to manage spaced repetition due items.
/// Integrates with SpacedRepetitionService.
/// Comments added for clarity as per .windsurfrules.
class SpacedRepetitionNotifier extends StateNotifier<List<SpacedRepetitionItem>> {
  final SpacedRepetitionService _service;

  SpacedRepetitionNotifier(this._service) : super([]) {
    _initialize();
  }

  // Initialize Hive box and load due items.
  Future<void> _initialize() async {
    await _service.initialize();
    _loadDueItems();
  }

  // Load due items for review.
  void _loadDueItems() {
    state = _service.getDueItems();
  }

  // Mark item as reviewed (correct/incorrect).
  Future<void> reviewItem(String questionId, bool isCorrect) async {
    await _service.updateAfterReview(questionId, isCorrect);
    _loadDueItems();
  }

  // Reset progress for an item.
  Future<void> resetItem(String questionId) async {
    await _service.resetItemProgress(questionId);
    _loadDueItems();
  }

  // Delete an item.
  Future<void> deleteItem(String questionId) async {
    await _service.deleteItem(questionId);
    _loadDueItems();
  }

  // Clear all spaced repetition data.
  Future<void> clearAll() async {
    await _service.clearAllData();
    _loadDueItems();
  }

  // Refresh due items manually.
  void refresh() {
    _loadDueItems();
  }
}

/// Provider for SpacedRepetitionNotifier.
/// Usage: ref.watch(spacedRepetitionProvider) to get due items.
final spacedRepetitionProvider = StateNotifierProvider<SpacedRepetitionNotifier, List<SpacedRepetitionItem>>((ref) {
  final service = ref.watch(spacedRepetitionServiceProvider);
  return SpacedRepetitionNotifier(service);
});

// Provider for SpacedRepetitionService (assumes service is stateless/singleton).
final spacedRepetitionServiceProvider = Provider<SpacedRepetitionService>((ref) {
  return SpacedRepetitionService();
});
