// Providers for quiz-related services (QuestionSelectionService, AdaptiveDifficultyService, SpacedRepetitionService)

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/question_selection_service.dart';
import '../services/adaptive_difficulty_service.dart';
import '../services/spaced_repetition_service.dart';
import '../services/adaptive_learning_service.dart';
import '../services/user_progress_service.dart';

// Provider for SpacedRepetitionService
final spacedRepetitionServiceProvider = Provider<SpacedRepetitionService>((ref) {
  final service = SpacedRepetitionService();
  // Optionally initialize Hive box here if needed
  // await service.initialize();
  return service;
});

// Provider for AdaptiveDifficultyService
final adaptiveDifficultyServiceProvider = Provider<AdaptiveDifficultyService>((ref) {
  final adaptiveLearningService = AdaptiveLearningService();
  final userProgressService = UserProgressService();
  return AdaptiveDifficultyService(
    adaptiveLearningService: adaptiveLearningService,
    userProgressService: userProgressService,
  );
});

// Provider for QuestionSelectionService
final questionSelectionServiceProvider = Provider<QuestionSelectionService>((ref) {
  final spacedRepetitionService = ref.read(spacedRepetitionServiceProvider);
  final adaptiveDifficultyService = ref.read(adaptiveDifficultyServiceProvider);
  return QuestionSelectionService(
    spacedRepetitionService: spacedRepetitionService,
    adaptiveDifficultyService: adaptiveDifficultyService,
  );
});
