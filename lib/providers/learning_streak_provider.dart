// LearningStreakProvider for Moroccan Accounting App
// Riverpod providers for learning streak management, milestones, rewards, and leaderboard.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/gamification/learning_streak_data.dart';
import '../services/learning_streak_service.dart';

// Provider for current streak count
final currentStreakProvider = Provider<int>((ref) {
  final streakService = ref.watch(learningStreakServiceProvider);
  // Default to dailyActivity streak type
  return streakService.getCurrentStreak(StreakType.dailyActivity);
});

// Provider for streak history
final streakHistoryProvider = Provider<List<StreakPeriod>>((ref) {
  final streakService = ref.watch(learningStreakServiceProvider);
  return streakService.getStreakHistory(StreakType.dailyActivity);
});

// Provider for streak milestones
final streakMilestonesProvider = Provider<List<int>>((ref) {
  // Milestones: 7, 30, 100 days
  return [7, 30, 100];
});

// Provider for streak rewards
final streakRewardsProvider = Provider<int>((ref) {
  final streakService = ref.watch(learningStreakServiceProvider);
  return streakService.calculateStreakRewards(StreakType.dailyActivity);
});

// Provider for streak status (active/broken)
// Provider for streak status (active/broken)
// Returns true if days until break > 0, false otherwise.
final streakStatusProvider = Provider<bool>((ref) {
  final streakService = ref.watch(learningStreakServiceProvider);
  final streak = streakService.streakBox.get(StreakType.dailyActivity.name) as LearningStreakData?;
  final days = streak?.getDaysUntilStreakBreak() ?? 0;
  return days > 0;
});

// Provider for leaderboard data (placeholder)
final streakLeaderboardProvider = Provider<List<Map<String, dynamic>>>((ref) {
  // TODO: Replace with actual leaderboard logic
  return [];
});

// Provider for streak recovery/freeze (triggered externally)
final streakRecoveryProvider = StateProvider<bool>((ref) => false);
final streakFreezeProvider = StateProvider<bool>((ref) => false);

// Provider for error handling
final streakErrorProvider = StateProvider<String?>((ref) => null);

// Provider for learning streak service (replace with actual instance)
final learningStreakServiceProvider = Provider<LearningStreakService>((ref) {
  // TODO: Replace with actual Hive box initialization after running build_runner
  throw UnimplementedError('LearningStreakService requires Hive box initialization. Run build_runner first.');
});

// End of learning_streak_provider.dart
