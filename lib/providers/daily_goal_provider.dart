// DailyGoalProvider for Moroccan Accounting App
// Riverpod providers for daily goal management, progress, streaks, and recommendations.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/gamification/daily_goal_data.dart';
import '../services/daily_goal_service.dart';

// Provider for current daily goal
final currentDailyGoalProvider = StateProvider<DailyGoalData?>((ref) => null);

// Provider for daily goal progress
final dailyGoalProgressProvider = Provider<double>((ref) {
  final goal = ref.watch(currentDailyGoalProvider);
  return goal?.getProgressPercentage() ?? 0.0;
});

// Provider for goal streak count
final goalStreakProvider = Provider<int>((ref) {
  final goal = ref.watch(currentDailyGoalProvider);
  return goal?.streakCount ?? 0;
});

// Provider for goal completion history (list of completed dates)
final goalCompletionHistoryProvider = Provider<List<DateTime>>((ref) {
  // TODO: Replace with actual history from service
  return [];
});

// Provider for goal recommendations
final goalRecommendationsProvider = Provider<DailyGoalData>((ref) {
  final dailyGoalService = ref.watch(dailyGoalServiceProvider);
  // Recommend goal for today
  return dailyGoalService.recommendGoal(DateTime.now());
});

// Provider for goal statistics
final goalStatisticsProvider = Provider<Map<String, dynamic>>((ref) {
  final dailyGoalService = ref.watch(dailyGoalServiceProvider);
  return dailyGoalService.getGoalStatistics();
});

// Provider for automatic daily reset (triggered externally)
final dailyGoalResetProvider = StateProvider<bool>((ref) => false);

// Provider for error handling
final dailyGoalErrorProvider = StateProvider<String?>((ref) => null);

// Provider for daily goal service (replace with actual instance)
final dailyGoalServiceProvider = Provider<DailyGoalService>((ref) {
  // TODO: Replace with actual Hive box initialization after running build_runner
  throw UnimplementedError('DailyGoalService requires Hive box initialization. Run build_runner first.');
});

// End of daily_goal_provider.dart
