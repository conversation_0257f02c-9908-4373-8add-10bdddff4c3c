// AchievementProvider for Moroccan Accounting App
// Riverpod providers for achievement state management, notifications, and badge animations.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/gamification/achievement_definitions.dart';
import '../services/achievement_service.dart';

// Provider for all achievements
final achievementsProvider = Provider<List<AchievementDefinition>>((ref) {
  // Replace with actual service call if needed
  return achievementDefinitions;
});

// Provider for unlocked achievements
final unlockedAchievementsProvider = Provider<List<AchievementDefinition>>((ref) {
  final achievementService = ref.watch(achievementServiceProvider);
  return achievementService.getUnlockedAchievements();
});

// Provider for achievement progress
final achievementProgressProvider = Provider<Map<String, double>>((ref) {
  final achievementService = ref.watch(achievementServiceProvider);
  // Replace with actual user progress map
  final userProgress = <String, int>{};
  return achievementService.getAchievementProgress(userProgress);
});

// Provider for recent achievements (last 5 unlocked)
final recentAchievementsProvider = Provider<List<AchievementDefinition>>((ref) {
  final unlocked = ref.watch(unlockedAchievementsProvider);
  return unlocked.length > 5 ? unlocked.sublist(unlocked.length - 5) : unlocked;
});

// Provider for total achievement points
final achievementPointsProvider = Provider<int>((ref) {
  final achievementService = ref.watch(achievementServiceProvider);
  return achievementService.calculateAchievementPoints();
});

// Provider for achievement categories
final achievementCategoriesProvider = Provider<List<AchievementCategory>>((ref) {
  return getAllAchievementCategories();
});

// Provider for achievement rarity filters
final achievementRarityProvider = Provider<List<AchievementRarity>>((ref) {
  return getAllAchievementRarities();
});

// Provider for achievement notifications (state)
final achievementNotificationProvider = StateProvider<AchievementDefinition?>((ref) => null);

// Provider for badge animation state
final badgeAnimationProvider = StateProvider<bool>((ref) => false);

// Provider for achievement service (replace with actual instance)
final achievementServiceProvider = Provider<AchievementService>((ref) {
  // Create a mock Hive box for now
  // TODO: Replace with actual Hive box after running build_runner
  throw UnimplementedError('AchievementService requires Hive box initialization. Run build_runner first.');
});

// Error handling provider
final achievementErrorProvider = StateProvider<String?>((ref) => null);

// End of achievement_provider.dart
