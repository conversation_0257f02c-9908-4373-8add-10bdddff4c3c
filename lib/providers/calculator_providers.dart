import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/calculators/financial_ratios_data.dart';
import '../models/calculators/enhanced_depreciation_data.dart';
import '../models/calculators/tax_optimization_data.dart';
import '../models/calculators/calculation_history_item.dart';
import '../services/financial_ratios_service.dart';
import '../services/enhanced_depreciation_service.dart';
import '../services/tax_optimization_service.dart';
import '../services/calculation_history_service.dart';

// Service providers
final financialRatiosServiceProvider = Provider<FinancialRatiosService>((ref) {
  return FinancialRatiosService();
});

final enhancedDepreciationServiceProvider = Provider<EnhancedDepreciationService>((ref) {
  return EnhancedDepreciationService();
});

final taxOptimizationServiceProvider = Provider<TaxOptimizationService>((ref) {
  return TaxOptimizationService();
});

final calculationHistoryServiceProvider = Provider<CalculationHistoryService>((ref) {
  return CalculationHistoryService();
});

// Financial Ratios Calculator State
final financialRatiosInputProvider = StateProvider<FinancialRatiosInput?>((ref) => null);
final financialRatiosResultProvider = StateProvider<FinancialRatiosResult?>((ref) => null);
final financialRatiosLoadingProvider = StateProvider<bool>((ref) => false);

// Enhanced Depreciation Calculator State
final depreciationInputProvider = StateProvider<EnhancedDepreciationInput?>((ref) => null);
final depreciationResultProvider = StateProvider<EnhancedDepreciationResult?>((ref) => null);
final depreciationComparisonsProvider = StateProvider<List<DepreciationComparison>?>((ref) => null);
final depreciationLoadingProvider = StateProvider<bool>((ref) => false);

// Tax Optimization Wizard State
final taxOptimizationInputProvider = StateProvider<TaxOptimizationInput?>((ref) => null);
final taxOptimizationResultProvider = StateProvider<TaxOptimizationResult?>((ref) => null);
final taxOptimizationLoadingProvider = StateProvider<bool>((ref) => false);
final taxOptimizationCurrentStepProvider = StateProvider<int>((ref) => 0);

// Calculator Actions Provider
final calculatorActionsProvider = Provider<CalculatorActions>((ref) {
  return CalculatorActions(ref);
});

class CalculatorActions {
  final Ref _ref;

  CalculatorActions(this._ref);

  // Financial Ratios Actions
  Future<void> calculateFinancialRatios(FinancialRatiosInput input) async {
    _ref.read(financialRatiosLoadingProvider.notifier).state = true;
    _ref.read(financialRatiosInputProvider.notifier).state = input;

    try {
      final service = _ref.read(financialRatiosServiceProvider);
      final result = service.calculateRatios(input);
      
      _ref.read(financialRatiosResultProvider.notifier).state = result;
      
      // Save to history
      await _saveFinancialRatiosToHistory(input, result);
    } catch (e) {
      _ref.read(financialRatiosResultProvider.notifier).state = null;
      rethrow;
    } finally {
      _ref.read(financialRatiosLoadingProvider.notifier).state = false;
    }
  }

  void clearFinancialRatiosResults() {
    _ref.read(financialRatiosInputProvider.notifier).state = null;
    _ref.read(financialRatiosResultProvider.notifier).state = null;
  }

  // Enhanced Depreciation Actions
  Future<void> calculateDepreciation(EnhancedDepreciationInput input, {bool compareAll = false}) async {
    _ref.read(depreciationLoadingProvider.notifier).state = true;
    _ref.read(depreciationInputProvider.notifier).state = input;

    try {
      final service = _ref.read(enhancedDepreciationServiceProvider);
      final result = service.calculateDepreciation(input);
      
      _ref.read(depreciationResultProvider.notifier).state = result;
      
      // Calculate comparisons if requested
      if (compareAll) {
        final comparisons = service.compareDepreciationMethods(input);
        _ref.read(depreciationComparisonsProvider.notifier).state = comparisons;
      } else {
        _ref.read(depreciationComparisonsProvider.notifier).state = null;
      }
      
      // Save to history
      await _saveDepreciationToHistory(input, result);
    } catch (e) {
      _ref.read(depreciationResultProvider.notifier).state = null;
      _ref.read(depreciationComparisonsProvider.notifier).state = null;
      rethrow;
    } finally {
      _ref.read(depreciationLoadingProvider.notifier).state = false;
    }
  }

  void clearDepreciationResults() {
    _ref.read(depreciationInputProvider.notifier).state = null;
    _ref.read(depreciationResultProvider.notifier).state = null;
    _ref.read(depreciationComparisonsProvider.notifier).state = null;
  }

  // Tax Optimization Actions
  Future<void> analyzeTaxOptimization(TaxOptimizationInput input) async {
    _ref.read(taxOptimizationLoadingProvider.notifier).state = true;
    _ref.read(taxOptimizationInputProvider.notifier).state = input;

    try {
      final service = _ref.read(taxOptimizationServiceProvider);
      final result = service.analyzeAndOptimize(input);
      
      _ref.read(taxOptimizationResultProvider.notifier).state = result;
      
      // Save to history
      await _saveTaxOptimizationToHistory(input, result);
    } catch (e) {
      _ref.read(taxOptimizationResultProvider.notifier).state = null;
      rethrow;
    } finally {
      _ref.read(taxOptimizationLoadingProvider.notifier).state = false;
    }
  }

  void clearTaxOptimizationResults() {
    _ref.read(taxOptimizationInputProvider.notifier).state = null;
    _ref.read(taxOptimizationResultProvider.notifier).state = null;
    _ref.read(taxOptimizationCurrentStepProvider.notifier).state = 0;
  }

  void setTaxOptimizationStep(int step) {
    _ref.read(taxOptimizationCurrentStepProvider.notifier).state = step;
  }

  // History saving methods
  Future<void> _saveFinancialRatiosToHistory(FinancialRatiosInput input, FinancialRatiosResult result) async {
    try {
      final historyService = _ref.read(calculationHistoryServiceProvider);
      final historyItem = CalculationHistoryItem.fromFinancialRatiosCalculation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        inputData: input.toJson(),
        resultData: result.toJson(),
        name: 'Analyse Ratios Financiers',
        tags: ['ratios', 'analyse', 'financier'],
      );
      
      await historyService.saveCalculation(historyItem);
    } catch (e) {
      // Log error but don't fail the calculation
      print('Failed to save financial ratios to history: $e');
    }
  }

  Future<void> _saveDepreciationToHistory(EnhancedDepreciationInput input, EnhancedDepreciationResult result) async {
    try {
      final historyService = _ref.read(calculationHistoryServiceProvider);
      final historyItem = CalculationHistoryItem.fromDepreciationCalculation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        inputData: input.toJson(),
        resultData: result.toJson(),
        name: 'Amortissement ${input.assetName}',
        tags: ['amortissement', input.method.name, input.assetName.toLowerCase()],
      );
      
      await historyService.saveCalculation(historyItem);
    } catch (e) {
      // Log error but don't fail the calculation
      print('Failed to save depreciation to history: $e');
    }
  }

  Future<void> _saveTaxOptimizationToHistory(TaxOptimizationInput input, TaxOptimizationResult result) async {
    try {
      final historyService = _ref.read(calculationHistoryServiceProvider);
      final historyItem = CalculationHistoryItem.fromTaxOptimizationCalculation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        inputData: input.toJson(),
        resultData: result.toJson(),
        name: 'Optimisation Fiscale ${input.companyProfile.legalForm.displayName}',
        tags: ['optimisation', 'fiscal', input.companyProfile.sector.name],
      );
      
      await historyService.saveCalculation(historyItem);
    } catch (e) {
      // Log error but don't fail the calculation
      print('Failed to save tax optimization to history: $e');
    }
  }
}

// Form validation providers
final financialRatiosFormValidProvider = Provider<bool>((ref) {
  final input = ref.watch(financialRatiosInputProvider);
  return input != null && _isValidFinancialRatiosInput(input);
});

final depreciationFormValidProvider = Provider<bool>((ref) {
  final input = ref.watch(depreciationInputProvider);
  return input != null && input.isValid;
});

final taxOptimizationFormValidProvider = Provider<bool>((ref) {
  final input = ref.watch(taxOptimizationInputProvider);
  return input != null && _isValidTaxOptimizationInput(input);
});

// Helper functions
bool _isValidFinancialRatiosInput(FinancialRatiosInput input) {
  return input.totalAssets > 0 &&
         input.revenue > 0 &&
         input.currentAssets >= 0 &&
         input.currentLiabilities >= 0 &&
         input.totalEquity >= 0;
}

bool _isValidTaxOptimizationInput(TaxOptimizationInput input) {
  return input.companyProfile.annualRevenue > 0 &&
         input.companyProfile.employeeCount >= 0 &&
         input.currentTaxSituation.accountingResult != 0 &&
         input.optimizationGoals.objectives.isNotEmpty;
}

// Calculator state management providers
final activeCalculatorProvider = StateProvider<CalculatorType?>((ref) => null);

final calculatorStateProvider = Provider<CalculatorState>((ref) {
  final activeCalculator = ref.watch(activeCalculatorProvider);
  
  switch (activeCalculator) {
    case CalculatorType.financialRatios:
      return CalculatorState(
        isLoading: ref.watch(financialRatiosLoadingProvider),
        hasInput: ref.watch(financialRatiosInputProvider) != null,
        hasResult: ref.watch(financialRatiosResultProvider) != null,
        isValid: ref.watch(financialRatiosFormValidProvider),
      );
    case CalculatorType.depreciation:
      return CalculatorState(
        isLoading: ref.watch(depreciationLoadingProvider),
        hasInput: ref.watch(depreciationInputProvider) != null,
        hasResult: ref.watch(depreciationResultProvider) != null,
        isValid: ref.watch(depreciationFormValidProvider),
      );
    case CalculatorType.taxOptimization:
      return CalculatorState(
        isLoading: ref.watch(taxOptimizationLoadingProvider),
        hasInput: ref.watch(taxOptimizationInputProvider) != null,
        hasResult: ref.watch(taxOptimizationResultProvider) != null,
        isValid: ref.watch(taxOptimizationFormValidProvider),
      );
    default:
      return const CalculatorState(
        isLoading: false,
        hasInput: false,
        hasResult: false,
        isValid: false,
      );
  }
});

class CalculatorState {
  final bool isLoading;
  final bool hasInput;
  final bool hasResult;
  final bool isValid;

  const CalculatorState({
    required this.isLoading,
    required this.hasInput,
    required this.hasResult,
    required this.isValid,
  });

  bool get canCalculate => hasInput && isValid && !isLoading;
  bool get canExport => hasResult && !isLoading;
  bool get canSave => hasResult && !isLoading;
}

// Export providers
final calculatorExportProvider = Provider<CalculatorExportActions>((ref) {
  return CalculatorExportActions(ref);
});

class CalculatorExportActions {
  final Ref _ref;

  CalculatorExportActions(this._ref);

  Future<void> exportFinancialRatios({required String format}) async {
    final input = _ref.read(financialRatiosInputProvider);
    final result = _ref.read(financialRatiosResultProvider);
    
    if (input == null || result == null) {
      throw Exception('Aucun résultat à exporter');
    }

    // TODO: Implement export logic based on format
    // This would use the PDF or Excel export utilities
  }

  Future<void> exportDepreciation({required String format}) async {
    final input = _ref.read(depreciationInputProvider);
    final result = _ref.read(depreciationResultProvider);
    
    if (input == null || result == null) {
      throw Exception('Aucun résultat à exporter');
    }

    // TODO: Implement export logic based on format
  }

  Future<void> exportTaxOptimization({required String format}) async {
    final input = _ref.read(taxOptimizationInputProvider);
    final result = _ref.read(taxOptimizationResultProvider);
    
    if (input == null || result == null) {
      throw Exception('Aucun résultat à exporter');
    }

    // TODO: Implement export logic based on format
  }
}

// Calculator presets provider
final calculatorPresetsProvider = Provider<Map<CalculatorType, List<CalculatorPreset>>>((ref) {
  return {
    CalculatorType.depreciation: [
      CalculatorPreset(
        name: 'Véhicule de tourisme',
        description: 'Configuration standard pour véhicule',
        data: {
          'usefulLife': 5,
          'method': 'degressive',
          'residualValuePercent': 10,
        },
      ),
      CalculatorPreset(
        name: 'Matériel informatique',
        description: 'Configuration pour équipement IT',
        data: {
          'usefulLife': 3,
          'method': 'degressive',
          'residualValuePercent': 5,
        },
      ),
    ],
    CalculatorType.financialRatios: [
      CalculatorPreset(
        name: 'PME Commerce',
        description: 'Ratios typiques pour PME commerciale',
        data: {
          'sector': 'retail',
          'benchmarks': 'pme_commerce',
        },
      ),
    ],
  };
});

class CalculatorPreset {
  final String name;
  final String description;
  final Map<String, dynamic> data;

  const CalculatorPreset({
    required this.name,
    required this.description,
    required this.data,
  });
}
