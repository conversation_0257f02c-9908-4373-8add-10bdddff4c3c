// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$QuizState {
  int get currentQuestionIndex => throw _privateConstructorUsedError;
  int get score => throw _privateConstructorUsedError;
  List<int> get userAnswers => throw _privateConstructorUsedError;
  bool get showExplanation => throw _privateConstructorUsedError;
  int get timeRemaining => throw _privateConstructorUsedError;
  double get progressValue => throw _privateConstructorUsedError;
  bool get isCompleted => throw _privateConstructorUsedError;
  int get totalPoints => throw _privateConstructorUsedError;
  int get earnedPoints => throw _privateConstructorUsedError;
  List<int> get timeSpentPerQuestion => throw _privateConstructorUsedError;
  int get streakCount => throw _privateConstructorUsedError;
  bool get isSavingResults => throw _privateConstructorUsedError;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QuizStateCopyWith<QuizState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizStateCopyWith<$Res> {
  factory $QuizStateCopyWith(QuizState value, $Res Function(QuizState) then) =
      _$QuizStateCopyWithImpl<$Res, QuizState>;
  @useResult
  $Res call(
      {int currentQuestionIndex,
      int score,
      List<int> userAnswers,
      bool showExplanation,
      int timeRemaining,
      double progressValue,
      bool isCompleted,
      int totalPoints,
      int earnedPoints,
      List<int> timeSpentPerQuestion,
      int streakCount,
      bool isSavingResults});
}

/// @nodoc
class _$QuizStateCopyWithImpl<$Res, $Val extends QuizState>
    implements $QuizStateCopyWith<$Res> {
  _$QuizStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentQuestionIndex = null,
    Object? score = null,
    Object? userAnswers = null,
    Object? showExplanation = null,
    Object? timeRemaining = null,
    Object? progressValue = null,
    Object? isCompleted = null,
    Object? totalPoints = null,
    Object? earnedPoints = null,
    Object? timeSpentPerQuestion = null,
    Object? streakCount = null,
    Object? isSavingResults = null,
  }) {
    return _then(_value.copyWith(
      currentQuestionIndex: null == currentQuestionIndex
          ? _value.currentQuestionIndex
          : currentQuestionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      userAnswers: null == userAnswers
          ? _value.userAnswers
          : userAnswers // ignore: cast_nullable_to_non_nullable
              as List<int>,
      showExplanation: null == showExplanation
          ? _value.showExplanation
          : showExplanation // ignore: cast_nullable_to_non_nullable
              as bool,
      timeRemaining: null == timeRemaining
          ? _value.timeRemaining
          : timeRemaining // ignore: cast_nullable_to_non_nullable
              as int,
      progressValue: null == progressValue
          ? _value.progressValue
          : progressValue // ignore: cast_nullable_to_non_nullable
              as double,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      earnedPoints: null == earnedPoints
          ? _value.earnedPoints
          : earnedPoints // ignore: cast_nullable_to_non_nullable
              as int,
      timeSpentPerQuestion: null == timeSpentPerQuestion
          ? _value.timeSpentPerQuestion
          : timeSpentPerQuestion // ignore: cast_nullable_to_non_nullable
              as List<int>,
      streakCount: null == streakCount
          ? _value.streakCount
          : streakCount // ignore: cast_nullable_to_non_nullable
              as int,
      isSavingResults: null == isSavingResults
          ? _value.isSavingResults
          : isSavingResults // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizStateImplCopyWith<$Res>
    implements $QuizStateCopyWith<$Res> {
  factory _$$QuizStateImplCopyWith(
          _$QuizStateImpl value, $Res Function(_$QuizStateImpl) then) =
      __$$QuizStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentQuestionIndex,
      int score,
      List<int> userAnswers,
      bool showExplanation,
      int timeRemaining,
      double progressValue,
      bool isCompleted,
      int totalPoints,
      int earnedPoints,
      List<int> timeSpentPerQuestion,
      int streakCount,
      bool isSavingResults});
}

/// @nodoc
class __$$QuizStateImplCopyWithImpl<$Res>
    extends _$QuizStateCopyWithImpl<$Res, _$QuizStateImpl>
    implements _$$QuizStateImplCopyWith<$Res> {
  __$$QuizStateImplCopyWithImpl(
      _$QuizStateImpl _value, $Res Function(_$QuizStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentQuestionIndex = null,
    Object? score = null,
    Object? userAnswers = null,
    Object? showExplanation = null,
    Object? timeRemaining = null,
    Object? progressValue = null,
    Object? isCompleted = null,
    Object? totalPoints = null,
    Object? earnedPoints = null,
    Object? timeSpentPerQuestion = null,
    Object? streakCount = null,
    Object? isSavingResults = null,
  }) {
    return _then(_$QuizStateImpl(
      currentQuestionIndex: null == currentQuestionIndex
          ? _value.currentQuestionIndex
          : currentQuestionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int,
      userAnswers: null == userAnswers
          ? _value._userAnswers
          : userAnswers // ignore: cast_nullable_to_non_nullable
              as List<int>,
      showExplanation: null == showExplanation
          ? _value.showExplanation
          : showExplanation // ignore: cast_nullable_to_non_nullable
              as bool,
      timeRemaining: null == timeRemaining
          ? _value.timeRemaining
          : timeRemaining // ignore: cast_nullable_to_non_nullable
              as int,
      progressValue: null == progressValue
          ? _value.progressValue
          : progressValue // ignore: cast_nullable_to_non_nullable
              as double,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      earnedPoints: null == earnedPoints
          ? _value.earnedPoints
          : earnedPoints // ignore: cast_nullable_to_non_nullable
              as int,
      timeSpentPerQuestion: null == timeSpentPerQuestion
          ? _value._timeSpentPerQuestion
          : timeSpentPerQuestion // ignore: cast_nullable_to_non_nullable
              as List<int>,
      streakCount: null == streakCount
          ? _value.streakCount
          : streakCount // ignore: cast_nullable_to_non_nullable
              as int,
      isSavingResults: null == isSavingResults
          ? _value.isSavingResults
          : isSavingResults // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$QuizStateImpl implements _QuizState {
  const _$QuizStateImpl(
      {required this.currentQuestionIndex,
      required this.score,
      required final List<int> userAnswers,
      required this.showExplanation,
      required this.timeRemaining,
      required this.progressValue,
      required this.isCompleted,
      required this.totalPoints,
      required this.earnedPoints,
      required final List<int> timeSpentPerQuestion,
      required this.streakCount,
      required this.isSavingResults})
      : _userAnswers = userAnswers,
        _timeSpentPerQuestion = timeSpentPerQuestion;

  @override
  final int currentQuestionIndex;
  @override
  final int score;
  final List<int> _userAnswers;
  @override
  List<int> get userAnswers {
    if (_userAnswers is EqualUnmodifiableListView) return _userAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_userAnswers);
  }

  @override
  final bool showExplanation;
  @override
  final int timeRemaining;
  @override
  final double progressValue;
  @override
  final bool isCompleted;
  @override
  final int totalPoints;
  @override
  final int earnedPoints;
  final List<int> _timeSpentPerQuestion;
  @override
  List<int> get timeSpentPerQuestion {
    if (_timeSpentPerQuestion is EqualUnmodifiableListView)
      return _timeSpentPerQuestion;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_timeSpentPerQuestion);
  }

  @override
  final int streakCount;
  @override
  final bool isSavingResults;

  @override
  String toString() {
    return 'QuizState(currentQuestionIndex: $currentQuestionIndex, score: $score, userAnswers: $userAnswers, showExplanation: $showExplanation, timeRemaining: $timeRemaining, progressValue: $progressValue, isCompleted: $isCompleted, totalPoints: $totalPoints, earnedPoints: $earnedPoints, timeSpentPerQuestion: $timeSpentPerQuestion, streakCount: $streakCount, isSavingResults: $isSavingResults)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizStateImpl &&
            (identical(other.currentQuestionIndex, currentQuestionIndex) ||
                other.currentQuestionIndex == currentQuestionIndex) &&
            (identical(other.score, score) || other.score == score) &&
            const DeepCollectionEquality()
                .equals(other._userAnswers, _userAnswers) &&
            (identical(other.showExplanation, showExplanation) ||
                other.showExplanation == showExplanation) &&
            (identical(other.timeRemaining, timeRemaining) ||
                other.timeRemaining == timeRemaining) &&
            (identical(other.progressValue, progressValue) ||
                other.progressValue == progressValue) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.totalPoints, totalPoints) ||
                other.totalPoints == totalPoints) &&
            (identical(other.earnedPoints, earnedPoints) ||
                other.earnedPoints == earnedPoints) &&
            const DeepCollectionEquality()
                .equals(other._timeSpentPerQuestion, _timeSpentPerQuestion) &&
            (identical(other.streakCount, streakCount) ||
                other.streakCount == streakCount) &&
            (identical(other.isSavingResults, isSavingResults) ||
                other.isSavingResults == isSavingResults));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentQuestionIndex,
      score,
      const DeepCollectionEquality().hash(_userAnswers),
      showExplanation,
      timeRemaining,
      progressValue,
      isCompleted,
      totalPoints,
      earnedPoints,
      const DeepCollectionEquality().hash(_timeSpentPerQuestion),
      streakCount,
      isSavingResults);

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizStateImplCopyWith<_$QuizStateImpl> get copyWith =>
      __$$QuizStateImplCopyWithImpl<_$QuizStateImpl>(this, _$identity);
}

abstract class _QuizState implements QuizState {
  const factory _QuizState(
      {required final int currentQuestionIndex,
      required final int score,
      required final List<int> userAnswers,
      required final bool showExplanation,
      required final int timeRemaining,
      required final double progressValue,
      required final bool isCompleted,
      required final int totalPoints,
      required final int earnedPoints,
      required final List<int> timeSpentPerQuestion,
      required final int streakCount,
      required final bool isSavingResults}) = _$QuizStateImpl;

  @override
  int get currentQuestionIndex;
  @override
  int get score;
  @override
  List<int> get userAnswers;
  @override
  bool get showExplanation;
  @override
  int get timeRemaining;
  @override
  double get progressValue;
  @override
  bool get isCompleted;
  @override
  int get totalPoints;
  @override
  int get earnedPoints;
  @override
  List<int> get timeSpentPerQuestion;
  @override
  int get streakCount;
  @override
  bool get isSavingResults;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuizStateImplCopyWith<_$QuizStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
