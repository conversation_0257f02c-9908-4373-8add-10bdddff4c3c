import 'dart:async';
// Ensure Timer is imported
// For debugPrint
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/quiz_model.dart';
import '../models/hive/active_quiz_state.dart'; // Import active state model
import '../models/quiz/custom_quiz_config.dart';
import '../services/question_selection_service.dart';
import '../services/adaptive_difficulty_service.dart';
import '../services/spaced_repetition_service.dart';
import '../providers/quiz_services_provider.dart';

part 'quiz_controller.freezed.dart';

@freezed
abstract class QuizState with _$QuizState {
  const factory QuizState({
    required int currentQuestionIndex,
    required int score,
    required List<int> userAnswers,
    required bool showExplanation,
    required int timeRemaining,
    required double progressValue,
    required bool isCompleted,
    required int totalPoints,
    required int earnedPoints,
    required List<int> timeSpentPerQuestion,
    required int streakCount,
    required bool isSavingResults,
  }) = _QuizState;

  factory QuizState.initial() => const QuizState(
        currentQuestionIndex: 0,
        score: 0,
        userAnswers: [],
        showExplanation: false,
        timeRemaining: 30,
        progressValue: 1.0,
        isCompleted: false,
        totalPoints: 0,
        earnedPoints: 0,
        timeSpentPerQuestion: [],
        streakCount: 0,
        isSavingResults: false,
      );
}

class QuizController extends StateNotifier<QuizState> {
  final QuizLevel level;
  final String categoryName;
  final QuestionSelectionService questionSelectionService;
  final AdaptiveDifficultyService adaptiveDifficultyService;
  final SpacedRepetitionService spacedRepetitionService;
  Timer? _questionTimer;
  int _startTime = 0;
  final ActiveQuizState? initialActiveState; // Optional initial state

  // Add service dependencies to constructor
  QuizController(
    this.level,
    this.categoryName,
    this.initialActiveState, {
    required this.questionSelectionService,
    required this.adaptiveDifficultyService,
    required this.spacedRepetitionService,
  }) : super(initialActiveState != null
              ? QuizState(
                  currentQuestionIndex: initialActiveState.currentQuestionIndex,
                  score: initialActiveState.score,
                  userAnswers: List.from(initialActiveState.userAnswers),
                  showExplanation: false,
                  timeRemaining: 30,
                  progressValue: 1.0,
                  isCompleted: false,
                  totalPoints: 0,
                  earnedPoints: initialActiveState.earnedPoints,
                  timeSpentPerQuestion: List.from(initialActiveState.timeSpentPerQuestion),
                  streakCount: initialActiveState.streakCount,
                  isSavingResults: false,
                )
              : QuizState.initial()) {
    // Async initialization of question queue using QuestionSelectionService
    _initializeQuestionQueue();
  }

  Future<void> _initializeQuestionQueue() async {
    // Build a basic CustomQuizConfig from level and categoryName
    final config = CustomQuizConfig(
      questionCount: level.questions.length,
      selectedTopics: [categoryName],
      minDifficulty: 1,
      maxDifficulty: 5,
      includeSpacedRepetition: false,
      adaptiveDifficulty: false,
      focusOnWeakAreas: false,
    );
    // Use all questions from level for now
    questionQueue = await questionSelectionService.generateQuestionQueue(
      config,
      level.questions,
    );

    // Calculate total possible points based on dynamic queue
    int totalPoints = 0;
    for (var question in questionQueue) {
      totalPoints += level.pointsPerQuestion * question.difficulty;
    }

    state = state.copyWith(
      totalPoints: totalPoints,
      timeRemaining: level.timePerQuestion,
    );

    _startQuestionTimer();
  }

  // Dynamic question queue from QuestionSelectionService
  late List<QuizQuestion> questionQueue;

  void _startQuestionTimer() {
    _questionTimer?.cancel();
    _startTime = level.timePerQuestion;
    
    state = state.copyWith(
      timeRemaining: level.timePerQuestion,
      progressValue: 1.0,
    );

    _questionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.timeRemaining > 0 && !state.showExplanation) {
        state = state.copyWith(
          timeRemaining: state.timeRemaining - 1,
          progressValue: (state.timeRemaining - 1) / _startTime,
        );
      } else if (state.timeRemaining == 0 && !state.showExplanation) {
        handleAnswer(-1); // Time's up, mark as incorrect
      }
    });
  }

  void handleAnswer(int selectedOption) {
    if (state.showExplanation) return;

    // Use dynamic queue instead of static list
    final currentQuestion = questionQueue[state.currentQuestionIndex];
    final bool isCorrect = selectedOption == currentQuestion.correct;
    _questionTimer?.cancel();

    // Calculate time spent on this question
    final timeSpent = level.timePerQuestion - state.timeRemaining;
    final List<int> updatedTimeSpent = [...state.timeSpentPerQuestion, timeSpent];

    // Calculate points based on difficulty and time
    int pointsEarned = 0;
    int newStreakCount = 0;

    if (isCorrect) {
      pointsEarned = level.pointsPerQuestion * currentQuestion.difficulty;
      final timeRatio = state.timeRemaining / level.timePerQuestion;
      if (timeRatio > 0.7) {
        pointsEarned = (pointsEarned * 1.5).round();
      } else if (timeRatio > 0.4) {
        pointsEarned = (pointsEarned * 1.2).round();
      }
      newStreakCount = state.streakCount + 1;
      if (newStreakCount >= 3) {
        pointsEarned = (pointsEarned * (1 + (newStreakCount * 0.1))).round();
      }
    } else {
      newStreakCount = 0;
      // Add incorrect answer to spaced repetition
      spacedRepetitionService.addIncorrectAnswer(
        currentQuestion,
        categoryName,
      );
    }

    // Adaptive difficulty adjustment after each answer
    // You may use adaptiveDifficultyService.calculateOptimalDifficulty or adjustQuestionSelection for future question selection if needed.

    state = state.copyWith(
      userAnswers: [...state.userAnswers, selectedOption],
      score: isCorrect ? state.score + 1 : state.score,
      showExplanation: true,
      earnedPoints: state.earnedPoints + pointsEarned,
      timeSpentPerQuestion: updatedTimeSpent,
      streakCount: newStreakCount,
    );
  }

  void nextQuestion() {
    // Use dynamic queue length
    if (state.currentQuestionIndex < questionQueue.length - 1) {
      state = state.copyWith(
        currentQuestionIndex: state.currentQuestionIndex + 1,
        showExplanation: false,
        userAnswers: List.from(state.userAnswers)..add(-1),
      );
      _startQuestionTimer();
    } else {
      state = state.copyWith(isCompleted: true);
    }
  }
  
  // Removed: _saveQuizResults() method

  @override
  void dispose() {
    _questionTimer?.cancel();
    super.dispose();
  }
}

// Update the family parameter type to include the optional ActiveQuizState
final quizControllerProvider = StateNotifierProvider.autoDispose.family<QuizController, QuizState, (QuizLevel, String, ActiveQuizState?)>(
  (ref, params) {
    // Inject services from providers
    final questionSelectionService = ref.read(questionSelectionServiceProvider);
    final adaptiveDifficultyService = ref.read(adaptiveDifficultyServiceProvider);
    final spacedRepetitionService = ref.read(spacedRepetitionServiceProvider);

    return QuizController(
      params.$1,
      params.$2,
      params.$3,
      questionSelectionService: questionSelectionService,
      adaptiveDifficultyService: adaptiveDifficultyService,
      spacedRepetitionService: spacedRepetitionService,
    );
  },
);
