import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/salary/salary_data.dart';
import '../models/salary/salary_result.dart';
import '../services/salary_calculator_service.dart';

class CalculatorSection extends StatefulWidget {
  const CalculatorSection({super.key});

  @override
  State<CalculatorSection> createState() => _CalculatorSectionState();
}

class _CalculatorSectionState extends State<CalculatorSection> {
  // Contrôleurs pour les champs de texte
  final _baseSalaryController = TextEditingController();
  final _transportController = TextEditingController();
  final _housingController = TextEditingController();
  final _otherAllowancesController = TextEditingController();
  final _yearsOfServiceController = TextEditingController();

  // Type d'employé
  String _employeeType = 'Salarié'; // ou 'Cadre'

  // Options
  bool _includeCNSS = true;

  // Heures supplémentaires
  final _regularOvertimeController = TextEditingController();
  final _holidayOvertimeController = TextEditingController();
  final _nightOvertimeController = TextEditingController();
  bool _showOvertimeSection = false;

  // Primes
  final Map<String, (TextEditingController, bool, bool)> _bonuses = {
    'Prime de rendement': (TextEditingController(), false, false),
    '13ème mois': (TextEditingController(), false, true),
    'Prime de bilan': (TextEditingController(), false, true),
  };

  // Situation familiale
  bool _isMarried = false;
  final List<(TextEditingController, bool)> _dependents = [];

  // Résultat
  SalaryResult? _result;
  final _calculatorService = SalaryCalculatorService();

  @override
  void dispose() {
    _baseSalaryController.dispose();
    _transportController.dispose();
    _housingController.dispose();
    _otherAllowancesController.dispose();
    _yearsOfServiceController.dispose();
    _regularOvertimeController.dispose();
    _holidayOvertimeController.dispose();
    _nightOvertimeController.dispose();

    for (final bonus in _bonuses.values) {
      bonus.$1.dispose();
    }

    for (final dependent in _dependents) {
      dependent.$1.dispose();
    }

    super.dispose();
  }

  Future<void> _calculate() async {
    if (_baseSalaryController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez saisir le salaire de base'),
        ),
      );
      return;
    }

    // 1. Construire les données
    final data = SalaryData(
      baseSalary: double.tryParse(_baseSalaryController.text) ?? 0,
      transportAllowance: double.tryParse(_transportController.text) ?? 0,
      housingAllowance: double.tryParse(_housingController.text) ?? 0,
      otherAllowances: double.tryParse(_otherAllowancesController.text) ?? 0,
      employeeType: _employeeType,
      yearsOfService: int.tryParse(_yearsOfServiceController.text) ?? 0,
      includeCNSS: _includeCNSS,
      overtime: OvertimeHours(
        regularHours: double.tryParse(_regularOvertimeController.text) ?? 0,
        holidayHours: double.tryParse(_holidayOvertimeController.text) ?? 0,
        nightHours: double.tryParse(_nightOvertimeController.text) ?? 0,
      ),
      bonuses: Map.fromEntries(
        _bonuses.entries.map((e) => MapEntry(
              e.key,
              BonusEntry(
                enabled: e.value.$2,
                amount: double.tryParse(e.value.$1.text) ?? 0,
                isAnnual: e.value.$3,
              ),
            )),
      ),
      familyStatus: FamilyStatus(
        isMarried: _isMarried,
        dependents: _dependents
            .map((e) => Dependent(
                  age: int.tryParse(e.$1.text) ?? 0,
                  isDisabled: e.$2,
                ))
            .toList(),
      ),
    );

    // 2. Calculer
    final result = await _calculatorService.calculate(data);

    // 3. Mettre à jour l'interface
    setState(() {
      _result = result;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 1. Informations de base
          _buildBasicSection(),
          const SizedBox(height: 24),

          // 2. Indemnités fixes
          _buildAllowancesSection(),
          const SizedBox(height: 24),

          // 3. Heures supplémentaires
          _buildOvertimeSection(),
          const SizedBox(height: 24),

          // 4. Primes
          _buildBonusesSection(),
          const SizedBox(height: 24),

          // 5. Situation familiale
          _buildFamilySection(),
          const SizedBox(height: 24),

          // 6. Bouton de calcul
          FilledButton.icon(
            onPressed: _calculate,
            icon: const Icon(Icons.calculate),
            label: const Text('Calculer'),
          ),
          const SizedBox(height: 24),

          // 7. Résultat
          if (_result != null) _buildResultSection(),
        ],
      ),
    );
  }

  Widget _buildBasicSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de base',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            // Type d'employé
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(
                  value: 'Salarié',
                  label: Text('Salarié'),
                ),
                ButtonSegment(
                  value: 'Cadre',
                  label: Text('Cadre'),
                ),
              ],
              selected: {_employeeType},
              onSelectionChanged: (Set<String> newSelection) {
                setState(() {
                  _employeeType = newSelection.first;
                });
              },
            ),
            const SizedBox(height: 16),
            // Salaire de base
            TextFormField(
              controller: _baseSalaryController,
              decoration: const InputDecoration(
                labelText: 'Salaire de base',
                suffixText: 'DH',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
            ),
            const SizedBox(height: 16),
            // Ancienneté
            TextFormField(
              controller: _yearsOfServiceController,
              decoration: const InputDecoration(
                labelText: 'Ancienneté',
                suffixText: 'années',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
            ),
            const SizedBox(height: 16),
            // CNSS
            SwitchListTile(
              title: const Text('Inclure CNSS et AMO'),
              subtitle: const Text('Cotisations sociales'),
              value: _includeCNSS,
              onChanged: (bool value) {
                setState(() {
                  _includeCNSS = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAllowancesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Indemnités fixes',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _transportController,
              decoration: const InputDecoration(
                labelText: 'Indemnité de transport',
                suffixText: 'DH',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _housingController,
              decoration: const InputDecoration(
                labelText: 'Indemnité de logement',
                suffixText: 'DH',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _otherAllowancesController,
              decoration: const InputDecoration(
                labelText: 'Autres indemnités',
                suffixText: 'DH',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOvertimeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Heures supplémentaires',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(_showOvertimeSection
                      ? Icons.expand_less
                      : Icons.expand_more),
                  onPressed: () {
                    setState(() {
                      _showOvertimeSection = !_showOvertimeSection;
                    });
                  },
                ),
              ],
            ),
            if (_showOvertimeSection) ...[
              const SizedBox(height: 16),
              // Tableau des taux
              Table(
                border: TableBorder.all(),
                columnWidths: const {
                  0: FlexColumnWidth(2),
                  1: FlexColumnWidth(1),
                  2: FlexColumnWidth(1),
                },
                children: [
                  const TableRow(
                    decoration: BoxDecoration(
                      color: Colors.black12,
                    ),
                    children: [
                      Padding(
                        padding: EdgeInsets.all(8),
                        child: Text('Horaire'),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8),
                        child: Text('Jours ouvrables'),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8),
                        child: Text('Jours fériés'),
                      ),
                    ],
                  ),
                  TableRow(
                    children: [
                      const Padding(
                        padding: EdgeInsets.all(8),
                        child: Text('Jour (6h-21h)'),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text('25%',
                            style: Theme.of(context).textTheme.bodySmall),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text('50%',
                            style: Theme.of(context).textTheme.bodySmall),
                      ),
                    ],
                  ),
                  TableRow(
                    children: [
                      const Padding(
                        padding: EdgeInsets.all(8),
                        child: Text('Nuit (21h-6h)'),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text('50%',
                            style: Theme.of(context).textTheme.bodySmall),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text('100%',
                            style: Theme.of(context).textTheme.bodySmall),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Champs de saisie
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _regularOvertimeController,
                      decoration: const InputDecoration(
                        labelText: 'Heures jour (25%)',
                        suffixText: 'h',
                        helperText: 'Jours ouvrables',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _holidayOvertimeController,
                      decoration: const InputDecoration(
                        labelText: 'Heures jour (50%)',
                        suffixText: 'h',
                        helperText: 'Jours fériés',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _nightOvertimeController,
                      decoration: const InputDecoration(
                        labelText: 'Heures nuit (50%)',
                        suffixText: 'h',
                        helperText: 'Jours ouvrables',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      enabled: false,
                      decoration: const InputDecoration(
                        labelText: 'Heures nuit (100%)',
                        suffixText: 'h',
                        helperText: 'Jours fériés',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBonusesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Primes',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ..._bonuses.entries.map((entry) {
              final (controller, enabled, isAnnual) = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Checkbox(
                      value: enabled,
                      onChanged: (value) {
                        setState(() {
                          _bonuses[entry.key] =
                              (controller, value ?? false, isAnnual);
                        });
                      },
                    ),
                    Expanded(
                      child: TextFormField(
                        controller: controller,
                        enabled: enabled,
                        decoration: InputDecoration(
                          labelText:
                              '${entry.key}${isAnnual ? ' (annuel)' : ''}',
                          suffixText: 'DH',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildFamilySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Situation familiale',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Marié(e)'),
              value: _isMarried,
              onChanged: (value) {
                setState(() {
                  _isMarried = value;
                });
              },
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  'Enfants à charge',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () {
                    setState(() {
                      _dependents.add((TextEditingController(), false));
                    });
                  },
                ),
              ],
            ),
            ..._dependents.asMap().entries.map((entry) {
              final index = entry.key;
              final (controller, isDisabled) = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controller,
                        decoration: InputDecoration(
                          labelText: 'Âge enfant ${index + 1}',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Checkbox(
                      value: isDisabled,
                      onChanged: (value) {
                        setState(() {
                          _dependents[index] = (controller, value ?? false);
                        });
                      },
                    ),
                    const Text('Handicapé'),
                    IconButton(
                      icon: const Icon(Icons.remove),
                      onPressed: () {
                        setState(() {
                          controller.dispose();
                          _dependents.removeAt(index);
                        });
                      },
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildResultSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résultat du calcul',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SelectableText(
                _result!.generateDetails(),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontFamily: 'monospace',
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
