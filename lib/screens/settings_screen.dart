import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../services/theme_service.dart';
import '../services/app_version_service.dart'; // Import AppVersionService
import '../widgets/whats_new_dialog.dart'; // Import WhatsNewDialog

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  IconData _getThemeIcon(ThemeType theme) {
    switch (theme) {
      case ThemeType.light:
        return Icons.light_mode;
      case ThemeType.dark:
        return Icons.dark_mode;
      case ThemeType.sepia:
        return Icons.color_lens;
      case ThemeType.tokyoNight:
        return Icons.nightlight_round;
      case ThemeType.solarizedDark:
        return Icons.wb_sunny;
      case ThemeType.monokaiDimmed:
        return Icons.contrast;
    }
  }

  // Show the "What's New" dialog
  void _showWhatsNewDialog(BuildContext context) {
    final appVersionService = Provider.of<AppVersionService>(context, listen: false);
    
    // Force the dialog to be shown
    appVersionService.forceShowWhatsNew();
    
    showDialog(
      context: context,
      barrierDismissible: false, // User must tap a button to close the dialog
      builder: (context) => WhatsNewDialog(
        appVersion: appVersionService.currentVersion ?? '2.4.1', // Use current version or fallback to a default
        onDismiss: () {
          // Mark this version as seen
          appVersionService.markVersionSeen();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  List<Widget> _buildDevInfoContent(ThemeData theme, bool isWideScreen) {
    final colorScheme = theme.colorScheme;
    return [
      Icon(
        Icons.code,
        size: 20,
        color: colorScheme.primary,
      ),
      const SizedBox(width: 12),
      Text(
        'Développé avec ',
        style: TextStyle(
          color: colorScheme.onSurface,
          fontSize: isWideScreen ? 16 : 14,
        ),
      ),
      const Icon(
        Icons.favorite,
        color: Colors.red,
        size: 20,
      ),
      Text(
        ' par ',
        style: TextStyle(
          color: colorScheme.onSurface,
          fontSize: isWideScreen ? 16 : 14,
        ),
      ),
      InkWell(
        onTap: () => launchUrl(Uri.parse('https://github.com/serhabdel')),
        child: Text(
          'SerhAbdel',
          style: TextStyle(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
            fontSize: isWideScreen ? 16 : 14,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final themeService = Provider.of<ThemeService>(context);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                colorScheme.primary,
                colorScheme.primaryContainer,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            'Paramètres',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
            ),
          ),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(24),
        children: [
          Card(
            elevation: 2,
            surfaceTintColor: colorScheme.surfaceTint,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              title: const Text('Thème'),
              subtitle: Text(
                themeService.getThemeName(themeService.currentTheme),
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              leading: Icon(
                _getThemeIcon(themeService.currentTheme),
                color: colorScheme.primary,
              ),
              trailing: PopupMenuButton<ThemeType>(
                icon: const Icon(Icons.more_vert),
                color: colorScheme.surface,
                onSelected: themeService.setTheme,
                itemBuilder: (context) => ThemeType.values.map((type) {
                  return PopupMenuItem(
                    value: type,
                    child: ListTile(
                      leading: Icon(
                        _getThemeIcon(type),
                        color: colorScheme.primary,
                      ),
                      title: Text(
                        themeService.getThemeName(type),
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                      trailing: themeService.currentTheme == type
                          ? Icon(Icons.check, color: colorScheme.primary)
                          : null,
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 2,
            surfaceTintColor: colorScheme.surfaceTint,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              title: const Text('Langue'),
              subtitle: Text(
                'Français',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              leading: Icon(
                Icons.language,
                color: colorScheme.primary,
              ),
              trailing: IconButton(
                icon: const Icon(Icons.more_vert),
                color: colorScheme.primary,
                onPressed: () {
                  // TODO: Implement language selection
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Sélection de langue à venir'),
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // New Card to display "What's New" dialog
          Card(
            elevation: 2,
            surfaceTintColor: colorScheme.surfaceTint,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              title: const Text("Quoi de neuf"),
              subtitle: const Text("Voir les dernières fonctionnalités"),
              leading: Icon(
                Icons.new_releases,
                color: colorScheme.primary,
              ),
              trailing: IconButton(
                icon: const Icon(Icons.chevron_right),
                color: colorScheme.primary,
                onPressed: () => _showWhatsNewDialog(context),
              ),
              onTap: () => _showWhatsNewDialog(context),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Palestine Mourning Card
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.3),
                width: 1.5,
              ),
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black,
                  Colors.white,
                  Colors.green,
                ],
                stops: [0.33, 0.33, 0.67],
              ),
            ),
            child: InkWell(
              onTap: () => _showWhatsNewDialog(context),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.red.withValues(alpha: 0.8),
                      ),
                      child: const Icon(
                        Icons.volunteer_activism,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'حداد على إخوتنا في غزة 🇵🇸',
                            style: textTheme.titleMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              height: 1.5,
                              shadows: [
                                const Shadow(
                                  color: Colors.black54,
                                  offset: Offset(1, 1),
                                  blurRadius: 3,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Priez pour la Palestine',
                            style: textTheme.bodyMedium?.copyWith(
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // const Spacer(), // Removed Spacer as it caused ParentDataWidget error in ListView
          const SizedBox(height: 40), // Keep or adjust spacing as needed
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                child: ElevatedButton.icon(
                  onPressed: () => launchUrl(
                    Uri.parse(
                        'https://github.com/serhabdel/hielcompta-public/issues/new/choose'),
                    mode: LaunchMode.externalApplication,
                  ),
                  icon: const Icon(Icons.bug_report),
                  label: const Text('Signaler un problème'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary.withValues(alpha: 0.3),
                      colorScheme.primaryContainer.withValues(alpha: 0.2),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: colorScheme.primary.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    bool isWideScreen = constraints.maxWidth > 600;
                    return isWideScreen
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: _buildDevInfoContent(theme, isWideScreen),
                          )
                        : Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: _buildDevInfoContent(theme, isWideScreen),
                          );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                alignment: Alignment.center,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary.withValues(alpha: 0.25),
                      colorScheme.primaryContainer.withValues(alpha: 0.2),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: colorScheme.primary.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Text(
                  'دعائكم هو أجرنا',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: colorScheme.primary,
                    fontSize: 22,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 1.5,
                    height: 1.5,
                    fontFamily: 'Amiri',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
