import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:moroccanaccounting/widgets/financial_statements/document_details_widget.dart';
import 'package:moroccanaccounting/widgets/journal_comptable_widget.dart';

class FusionCreationSection extends StatefulWidget {
  const FusionCreationSection({super.key});

  @override
  State<FusionCreationSection> createState() => _FusionCreationSectionState();
}

class _FusionCreationSectionState extends State<FusionCreationSection>
    with SingleTickerProviderStateMixin {
  Map<String, dynamic>? _fusionData;
  Map<String, dynamic>? _comptabilisationData;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final String fusionString =
          await rootBundle.loadString('assets/compta_soc/fusion.json');
      final String comptabilisationString = await rootBundle
          .loadString('assets/compta_soc/fusion_comptabilisation.json');

      setState(() {
        _fusionData = json.decode(fusionString);
        _comptabilisationData = json.decode(comptabilisationString);
      });
    } catch (e) {
      debugPrint('Error loading data: $e');
    }
  }

  Widget _buildTips(List<dynamic> tips) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Conseils Importants',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...tips.map<Widget>((tip) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.tips_and_updates,
                          size: 20, color: colorScheme.primary),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          tip,
                          style: textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildExample(Map<String, dynamic> example) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          example['title'],
          style: textTheme.titleMedium?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          example['description'],
          style: textTheme.bodyLarge,
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSocieteDetails('Société A', example['societe_a']),
                const SizedBox(height: 16),
                _buildSocieteDetails('Société B', example['societe_b']),
                const SizedBox(height: 16),
                Text(
                  'Calculs',
                  style: textTheme.titleSmall?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ...example['calculs']
                    .map<Widget>((calcul) => Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            calcul,
                            style: textTheme.bodyMedium,
                          ),
                        ))
                    .toList(),
                if (_comptabilisationData != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Écritures Comptables',
                    style: textTheme.titleSmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ..._comptabilisationData!['sections'][0]['entries']
                      .map<Widget>((entry) => Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: JournalComptableWidget(
                              title: entry['title'],
                              entries: [
                                for (final journalEntry
                                    in entry['journal_entries'])
                                  JournalEntry(
                                    date: journalEntry['date'],
                                    lines: [
                                      for (final je in journalEntry['entries'])
                                        JournalLine(
                                          account: je['account'],
                                          label: je['label'],
                                          debit: je['debit'],
                                          credit: je['credit'],
                                        ),
                                    ],
                                  ),
                              ],
                            ),
                          )),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSocieteDetails(String title, Map<String, dynamic> details) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: textTheme.titleSmall?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...details.entries.map<Widget>((entry) {
          if (entry.key == 'details_actif') {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Détails de l\'actif:',
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ...entry.value.entries.map<Widget>((detail) => Padding(
                      padding: const EdgeInsets.only(left: 24.0, bottom: 4.0),
                      child: Text(
                        '${detail.key.replaceAll('_', ' ').toUpperCase()}: ${detail.value}',
                        style: textTheme.bodyMedium,
                      ),
                    )),
              ],
            );
          }
          return Padding(
            padding: const EdgeInsets.only(left: 16.0, bottom: 4.0),
            child: Text(
              '${entry.key.replaceAll('_', ' ').toUpperCase()}: ${entry.value}',
              style: textTheme.bodyMedium,
            ),
          );
        }),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_fusionData == null || _comptabilisationData == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final fusionCreation = _fusionData!['sections'][0];
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          fusionCreation['title'],
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          fusionCreation['content'],
          style: textTheme.bodyLarge,
        ),
        const SizedBox(height: 16),
        if (fusionCreation['tips'] != null) ...[
          _buildTips(fusionCreation['tips']),
          const SizedBox(height: 16),
        ],
        TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: colorScheme.primary,
          unselectedLabelColor: colorScheme.onSurfaceVariant,
          indicatorColor: colorScheme.primary,
          dividerColor: Colors.transparent,
          tabs: const [
            Tab(text: 'Étapes'),
            Tab(text: 'Exemple'),
            Tab(text: 'Écritures'),
            Tab(text: 'Conseils'),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 500, // Fixed height for content
          child: TabBarView(
            controller: _tabController,
            children: [
              // Étapes Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Étapes de la Fusion Création',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DocumentDetailsWidget(
                          document: DocumentDetails(
                            items: [
                              for (final step in fusionCreation['steps'])
                                DocumentDetailsItem(
                                  title: '',
                                  content: step,
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Exemple Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: fusionCreation['example'] != null
                        ? _buildExample(fusionCreation['example'])
                        : const Center(child: Text('Aucun exemple disponible')),
                  ),
                ),
              ),

              // Écritures Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Écritures Comptables',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ..._comptabilisationData!['sections'][0]['entries']
                            .map<Widget>((entry) => Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      entry['title'],
                                      style: textTheme.titleSmall?.copyWith(
                                        color: colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      entry['description'],
                                      style: textTheme.bodyMedium,
                                    ),
                                    const SizedBox(height: 8),
                                    ...entry['journal_entries'].map<Widget>(
                                      (journalEntry) => Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16.0),
                                        child: JournalComptableWidget(
                                          title: journalEntry['description'],
                                          entries: [
                                            JournalEntry(
                                              date: journalEntry['date'],
                                              lines: [
                                                for (final je
                                                    in journalEntry['entries'])
                                                  JournalLine(
                                                    account: je['account'],
                                                    label: je['label'],
                                                    debit: je['debit'],
                                                    credit: je['credit'],
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                  ],
                                )),
                      ],
                    ),
                  ),
                ),
              ),

              // Conseils Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Conseils et Recommandations',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        if (fusionCreation['tips'] != null)
                          _buildTips(fusionCreation['tips']),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
