import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:moroccanaccounting/widgets/financial_statements/document_details_widget.dart';
import 'package:moroccanaccounting/widgets/journal_comptable_widget.dart';

class FusionAbsorptionSection extends StatefulWidget {
  const FusionAbsorptionSection({super.key});

  @override
  State<FusionAbsorptionSection> createState() =>
      _FusionAbsorptionSectionState();
}

class _FusionAbsorptionSectionState extends State<FusionAbsorptionSection>
    with SingleTickerProviderStateMixin {
  Map<String, dynamic>? _fusionData;
  Map<String, dynamic>? _comptabilisationData;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final String fusionString =
          await rootBundle.loadString('assets/compta_soc/fusion.json');
      final String comptabilisationString = await rootBundle
          .loadString('assets/compta_soc/fusion_comptabilisation.json');

      setState(() {
        _fusionData = json.decode(fusionString);
        _comptabilisationData = json.decode(comptabilisationString);
      });
    } catch (e) {
      debugPrint('Error loading data: $e');
    }
  }

  Widget _buildTips(List<dynamic> tips) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Conseils Importants',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...tips.map<Widget>((tip) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.tips_and_updates,
                          size: 20, color: colorScheme.primary),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          tip,
                          style: textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildJournalEntries(Map<String, dynamic> example) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (example['description'] != null) ...[
          Text(
            example['description'],
            style: textTheme.bodyLarge,
          ),
          const SizedBox(height: 16),
        ],
        if (_comptabilisationData != null) ...[
          ..._comptabilisationData!['sections'][1]['entries']
              .where((entry) => entry['type'] == example['type'])
              .map<Widget>((entry) => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      JournalComptableWidget(
                        title: entry['title'],
                        entries: [
                          for (final journalEntry in entry['journal_entries'])
                            JournalEntry(
                              date: journalEntry['date'],
                              lines: [
                                for (final je in journalEntry['entries'])
                                  JournalLine(
                                    account: je['account'],
                                    label: je['label'],
                                    debit: je['debit'],
                                    credit: je['credit'],
                                  ),
                              ],
                            ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],
                  )),
        ],
        if (example['calculs_prime_fusion'] != null) ...[
          const SizedBox(height: 16),
          _buildPrimeFusion(example['calculs_prime_fusion']),
        ],
      ],
    );
  }

  Widget _buildPrimeFusion(Map<String, dynamic> calculs) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calcul de la Prime de Fusion',
              style: textTheme.titleSmall?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Valeur d\'apport:',
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(calculs['valeur_apport']),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Augmentation capital:',
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(calculs['augmentation_capital']),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Prime de fusion:',
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(calculs['prime_fusion']),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Détail du calcul:',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(calculs['detail']),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutions(List<dynamic> solutions) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Solutions possibles',
          style: textTheme.titleSmall?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...solutions.map<Widget>((solution) => Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      solution['title'],
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      solution['description'],
                      style: textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            )),
      ],
    );
  }

  Widget _buildParticipationsCroisees(Map<String, dynamic> example) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Détails des Participations',
              style: textTheme.titleSmall?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Société A',
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...example['societe_a'].entries.map<Widget>((e) => Text(
                            '${e.key.replaceAll('_', ' ').toUpperCase()}: ${e.value}',
                            style: textTheme.bodyMedium,
                          )),
                    ],
                  ),
                ),
                const SizedBox(width: 32),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Société B',
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...example['societe_b'].entries.map<Widget>((e) => Text(
                            '${e.key.replaceAll('_', ' ').toUpperCase()}: ${e.value}',
                            style: textTheme.bodyMedium,
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubsection(Map<String, dynamic> subsection) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          subsection['title'],
          style: textTheme.titleMedium?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (subsection['content'] != null) ...[
          const SizedBox(height: 8),
          Text(
            subsection['content'],
            style: textTheme.bodyLarge,
          ),
        ],
        if (subsection['steps'] != null) ...[
          const SizedBox(height: 8),
          DocumentDetailsWidget(
            document: DocumentDetails(
              items: [
                for (final step in subsection['steps'])
                  DocumentDetailsItem(
                    title: '',
                    content: step,
                  ),
              ],
            ),
          ),
        ],
        if (subsection['solutions'] != null) ...[
          const SizedBox(height: 16),
          _buildSolutions(subsection['solutions']),
        ],
        if (subsection['example'] != null) ...[
          const SizedBox(height: 16),
          if (subsection['title'] == 'Participations croisées')
            _buildParticipationsCroisees(subsection['example'])
          else
            _buildJournalEntries(subsection['example']),
        ],
        if (subsection['subsections'] != null) ...[
          const SizedBox(height: 16),
          ...subsection['subsections'].map<Widget>((subsubsection) {
            return Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: _buildSubsection(subsubsection),
            );
          }).toList(),
        ],
        if (subsection['tips'] != null) ...[
          const SizedBox(height: 16),
          _buildTips(subsection['tips']),
        ],
      ],
    );
  }

  Widget _buildComptabilisationSection(Map<String, dynamic> section) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          section['title'],
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          section['content'],
          style: textTheme.bodyLarge,
        ),
        const SizedBox(height: 16),
        if (_comptabilisationData != null) ...[
          ..._comptabilisationData!['sections'][2]['entries']
              .map<Widget>((subsection) => Card(
                    margin: const EdgeInsets.only(bottom: 16.0),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            subsection['title'],
                            style: textTheme.titleMedium?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            subsection['description'],
                            style: textTheme.bodyLarge,
                          ),
                          if (subsection['journal_entries'] != null) ...[
                            const SizedBox(height: 16),
                            ...subsection['journal_entries']
                                .map<Widget>((entry) => Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        JournalComptableWidget(
                                          title: entry['description'],
                                          entries: [
                                            JournalEntry(
                                              date: entry['date'],
                                              lines: [
                                                for (final je
                                                    in entry['entries'])
                                                  JournalLine(
                                                    account: je['account'],
                                                    label: je['label'],
                                                    debit: je['debit'],
                                                    credit: je['credit'],
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                      ],
                                    )),
                          ],
                          if (subsection['tips'] != null) ...[
                            const SizedBox(height: 16),
                            _buildTips(subsection['tips']),
                          ],
                        ],
                      ),
                    ),
                  )),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_fusionData == null || _comptabilisationData == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final fusionAbsorption = _fusionData!['sections'][1];
    final comptabilisation = _fusionData!['sections'][2];
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          fusionAbsorption['title'],
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          fusionAbsorption['content'],
          style: textTheme.bodyLarge,
        ),
        if (fusionAbsorption['tips'] != null) ...[
          const SizedBox(height: 16),
          _buildTips(fusionAbsorption['tips']),
        ],
        const SizedBox(height: 16),
        TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: colorScheme.primary,
          unselectedLabelColor: colorScheme.onSurfaceVariant,
          indicatorColor: colorScheme.primary,
          dividerColor: Colors.transparent,
          tabs: const [
            Tab(text: 'Sociétés Indépendantes'),
            Tab(text: 'Participations Croisées'),
            Tab(text: 'Écritures'),
            Tab(text: 'Comptabilisation'),
            Tab(text: 'Conseils'),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 500, // Fixed height for content
          child: TabBarView(
            controller: _tabController,
            children: [
              // Sociétés Indépendantes Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Fusion entre Sociétés Indépendantes',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ...fusionAbsorption['subsections']
                            .where(
                                (s) => s['title'] == 'Sociétés indépendantes')
                            .map<Widget>((subsection) => _buildSubsection(subsection)),
                      ],
                    ),
                  ),
                ),
              ),

              // Participations Croisées Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Fusion avec Participations Croisées',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ...fusionAbsorption['subsections']
                            .where(
                                (s) => s['title'] == 'Participations croisées')
                            .map<Widget>((subsection) => _buildSubsection(subsection)),
                      ],
                    ),
                  ),
                ),
              ),

              // Écritures Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Écritures Comptables',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ..._comptabilisationData!['sections'][1]['entries']
                            .map<Widget>((entry) => Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      entry['title'],
                                      style: textTheme.titleSmall?.copyWith(
                                        color: colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      entry['description'],
                                      style: textTheme.bodyMedium,
                                    ),
                                    const SizedBox(height: 8),
                                    ...entry['journal_entries'].map<Widget>(
                                      (journalEntry) => Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 16.0),
                                        child: JournalComptableWidget(
                                          title: journalEntry['description'],
                                          entries: [
                                            JournalEntry(
                                              date: journalEntry['date'],
                                              lines: [
                                                for (final je
                                                    in journalEntry['entries'])
                                                  JournalLine(
                                                    account: je['account'],
                                                    label: je['label'],
                                                    debit: je['debit'],
                                                    credit: je['credit'],
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                  ],
                                )),
                      ],
                    ),
                  ),
                ),
              ),

              // Comptabilisation Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: _buildComptabilisationSection(comptabilisation),
                  ),
                ),
              ),

              // Conseils Tab
              SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Conseils et Recommandations',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        if (fusionAbsorption['tips'] != null)
                          _buildTips(fusionAbsorption['tips']),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
