import 'package:flutter/material.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_societes/constitution/constitution_screen.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_societes/modification_capital/modification_capital_screen.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_societes/affectation_resultats/affectation_resultats_screen.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_societes/dissolution/dissolution_screen.dart';

class ComptabiliteSocietesScreen extends StatelessWidget {
  const ComptabiliteSocietesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return DefaultTabController(
      length: 4,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Comptabilité des Sociétés'),
          bottom: TabBar(
            isScrollable: true,
            tabs: const [
              Tab(text: 'Constitution'),
              Tab(text: 'Modification Capital'),
              Tab(text: 'Affectation Résultats'),
              Tab(text: 'Dissolution'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            ConstitutionScreen(),
            ModificationCapitalScreen(),
            AffectationResultatsScreen(),
            DissolutionScreen(),
          ],
        ),
      ),
    );
  }
}
