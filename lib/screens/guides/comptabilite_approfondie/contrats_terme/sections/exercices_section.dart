import 'package:flutter/material.dart';
import '../../../../../../widgets/journal_comptable_widget.dart';

class ExercicesSection extends StatefulWidget {
  const ExercicesSection({super.key});

  @override
  State<ExercicesSection> createState() => _ExercicesSectionState();
}

class _ExercicesSectionState extends State<ExercicesSection> {
  bool _showSolution1 = false;
  bool _showSolution2 = false;
  bool _showSolution3 = false;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scrollbar(
      controller: _scrollController,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary,
                    colorScheme.primaryContainer,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Exercices d\'application',
                    style: textTheme.headlineMedium?.copyWith(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Mise en pratique des différentes méthodes',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Exercice 1
            Card(
              elevation: 2,
              margin: const EdgeInsets.only(bottom: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.assignment,
                              color: colorScheme.primary,
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Exercice 1 : Méthode de l\'achèvement',
                              style: textTheme.titleLarge?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _showSolution1 = !_showSolution1;
                            });
                          },
                          icon: Icon(
                            _showSolution1
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const Divider(height: 24),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Énoncé :',
                            style: textTheme.titleMedium?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'La société "Z" a conclu avec l\'État un contrat de construction d\'un barrage d\'une valeur de 3 900 000 DH. '
                            'La durée du contrat est de 2 ans. Le coût global prévu est de 3 700 000 DH. Les modalités de règlements sont :\n'
                            '1 350 000 pendant la première année et le reste pendant la deuxième année.\n\n'
                            'La situation des coûts réels et prévisionnels est la suivante :\n'
                            '• Coût prévisionnel cumulé N : 2 000 000 DH\n'
                            '• Coût prévisionnel cumulé N+1 : 1 700 000 DH\n'
                            '• Coût réel cumulé N : 2 100 000 DH\n'
                            '• Coût réel cumulé N+1 : 1 300 000 DH\n\n'
                            'Travail à faire :\n'
                            'Comptabiliser ce contrat selon la méthode de l\'achèvement.',
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withValues(alpha: 0.8),
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (_showSolution1) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: colorScheme.primary.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Solution :',
                              style: textTheme.titleMedium?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            JournalComptableWidget(
                              title: 'Solution exercice 1',
                              entries: [
                                JournalEntry(
                                  date: 'N',
                                  lines: [
                                    JournalLine(
                                      account: '5141',
                                      label: 'Banque',
                                      debit: '1 350 000,00',
                                    ),
                                    JournalLine(
                                      account: '4421',
                                      label: 'Clients - avances et acomptes',
                                      credit: '1 350 000,00',
                                    ),
                                  ],
                                ),
                                JournalEntry(
                                  date: 'N',
                                  lines: [
                                    JournalLine(
                                      account: '6',
                                      label: 'Charge',
                                      debit: '2 100 000,00',
                                    ),
                                    JournalLine(
                                      account: '5141',
                                      label: 'Banque',
                                      credit: '2 100 000,00',
                                    ),
                                  ],
                                ),
                                JournalEntry(
                                  date: '31/12/N',
                                  lines: [
                                    JournalLine(
                                      account: '313',
                                      label: 'Produit en cours',
                                      debit: '2 100 000,00',
                                    ),
                                    JournalLine(
                                      account: '7131',
                                      label: 'Variation des stoks',
                                      credit: '2 100 000,00',
                                    ),
                                  ],
                                ),
                                JournalEntry(
                                  date: 'N+1',
                                  lines: [
                                    JournalLine(
                                      account: '5141',
                                      label: 'Banque',
                                      debit: '2 550 000,00',
                                    ),
                                    JournalLine(
                                      account: '4421',
                                      label: 'Clients - avances et acomptes',
                                      credit: '2 550 000,00',
                                    ),
                                  ],
                                ),
                                JournalEntry(
                                  date: 'N+1',
                                  lines: [
                                    JournalLine(
                                      account: '6',
                                      label: 'Charge',
                                      debit: '1 300 000,00',
                                    ),
                                    JournalLine(
                                      account: '5141',
                                      label: 'Banque',
                                      credit: '1 300 000,00',
                                    ),
                                  ],
                                ),
                                JournalEntry(
                                  date: 'N+1',
                                  lines: [
                                    JournalLine(
                                      account: '7131',
                                      label: 'Variation des stoks',
                                      debit: '2 100 000,00',
                                    ),
                                    JournalLine(
                                      account: '313',
                                      label: 'Produit en cours',
                                      credit: '2 100 000,00',
                                    ),
                                  ],
                                ),
                                JournalEntry(
                                  date: 'N+1',
                                  lines: [
                                    JournalLine(
                                      account: '3421',
                                      label: 'Client',
                                      debit: '3 900 000,00',
                                    ),
                                    JournalLine(
                                      account: '7121',
                                      label: 'Ventes de biens et services',
                                      credit: '3 900 000,00',
                                    ),
                                  ],
                                ),
                                JournalEntry(
                                  date: 'N+1',
                                  lines: [
                                    JournalLine(
                                      account: '4421',
                                      label: 'Clients - avances et acomptes',
                                      debit: '3 900 000,00',
                                    ),
                                    JournalLine(
                                      account: '3421',
                                      label: 'Client',
                                      credit: '3 900 000,00',
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Exercice 2
            Card(
              elevation: 2,
              margin: const EdgeInsets.only(bottom: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.assignment,
                              color: colorScheme.primary,
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Exercice 2 : Méthode du bénéfice à l\'avancement',
                              style: textTheme.titleLarge?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _showSolution2 = !_showSolution2;
                            });
                          },
                          icon: Icon(
                            _showSolution2
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const Divider(height: 24),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Énoncé :',
                            style: textTheme.titleMedium?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Reprendre les données de l\'exercice 1 et comptabiliser selon la méthode du bénéfice à l\'avancement.',
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withValues(alpha: 0.8),
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (_showSolution2) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: colorScheme.primary.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Calculs préliminaires :',
                              style: textTheme.titleMedium?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            ...[
                              '1. Pourcentage d\'avancement :',
                              '• Coût réel cumulé / Coût prévisionnel total',
                              '• 2 100 000 / 3 700 000 = 0,57 soit 57%',
                              '',
                              '2. Bénéfice partiel :',
                              '• Prix de marché : 3 900 000 DH',
                              '• Coût prévisionnel : 3 700 000 DH',
                              '• Bénéfice total prévu : 200 000 DH',
                              '• Bénéfice partiel = 200 000 × 57% = 114 000 DH',
                            ].map<Widget>((text) => Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: Text(
                                    text,
                                    style: textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurface
                                          .withValues(alpha: 0.8),
                                      height: 1.5,
                                    ),
                                  ),
                                )),
                            const SizedBox(height: 16),
                            JournalComptableWidget(
                              title: 'Solution exercice 2',
                              entries: [
                                JournalEntry(
                                  date: 'N',
                                  lines: [
                                    JournalLine(
                                      account: '34272',
                                      label:
                                          'Créances sur travaux non encors facturable',
                                      debit: '114 000,00',
                                    ),
                                    JournalLine(
                                      account: '7121',
                                      label: 'Ventes de biens et services',
                                      credit: '114 000,00',
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Exercice 3
            Card(
              elevation: 2,
              margin: const EdgeInsets.only(bottom: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.assignment,
                              color: colorScheme.primary,
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Exercice 3 : Méthode de l\'avancement',
                              style: textTheme.titleLarge?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _showSolution3 = !_showSolution3;
                            });
                          },
                          icon: Icon(
                            _showSolution3
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const Divider(height: 24),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Énoncé :',
                            style: textTheme.titleMedium?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Reprendre les données de l\'exercice 1 et comptabiliser selon la méthode de l\'avancement.',
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withValues(alpha: 0.8),
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (_showSolution3) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: colorScheme.primary.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Calculs préliminaires :',
                              style: textTheme.titleMedium?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            ...[
                              '1. Pourcentage d\'avancement :',
                              '• 2 100 000 / 3 700 000 = 0,57 soit 57%',
                              '',
                              '2. Chiffre d\'affaires partiel :',
                              '• 3 900 000 × 57% = 2 109 000 DH',
                            ].map((text) => Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: Text(
                                    text,
                                    style: textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurface
                                          .withValues(alpha: 0.8),
                                      height: 1.5,
                                    ),
                                  ),
                                )),
                            const SizedBox(height: 16),
                            JournalComptableWidget(
                              title: 'Solution exercice 3',
                              entries: [
                                JournalEntry(
                                  date: '31/12/N',
                                  lines: [
                                    JournalLine(
                                      account: '3427',
                                      label: 'Clients-factures à établir',
                                      debit: '2 109 000,00',
                                    ),
                                    JournalLine(
                                      account: '712',
                                      label: 'Ventes de biens et services',
                                      credit: '2 109 000,00',
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
