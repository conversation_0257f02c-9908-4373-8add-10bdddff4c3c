import 'package:flutter/material.dart';
import '../../../../../../widgets/journal_comptable_widget.dart';

class AchevementSection extends StatelessWidget {
  const AchevementSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Méthode de l\'achèvement',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Traitement comptable des contrats à long terme',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Caractéristiques
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: colorScheme.primary,
                        size: 28,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Caractéristiques',
                        style: textTheme.titleLarge?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 24),
                  ...[
                    'Les dépenses relatives au contrat sont enregistrées en charge',
                    'Les travaux encours se rapportant au contrat à terme sont valorisés au coût de production et constatés à la clôture de chaque exercice',
                    'Le produit total du contrat est constaté suite à la réception des travaux ou service par le client',
                  ].map<Widget>((point) => Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              color: colorScheme.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                point,
                                style: textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                                  height: 1.5,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                ],
              ),
            ),
          ),

          // Exemple
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: colorScheme.primary,
                        size: 28,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Exemple d\'application',
                        style: textTheme.titleLarge?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 24),

                  // Données
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.outline.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Données :',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        ...{
                          'Valeur du contrat': '3 900 000 DH',
                          'Durée': '2 ans',
                          'Coût global prévu': '3 700 000 DH',
                          'Règlements':
                              '1 350 000 pendant la première année, reste en deuxième année',
                          'Coût réel cumulé N': '2 100 000 DH',
                          'Coût réel cumulé N+1': '1 300 000 DH',
                        }.entries.map(
                              (entry) => Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '• ${entry.key} : ',
                                      style: textTheme.bodyMedium?.copyWith(
                                        color: colorScheme.primary,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Expanded(
                                      child: Text(
                                        entry.value,
                                        style: textTheme.bodyMedium?.copyWith(
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Calculs
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.primary.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Année N',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        ...[
                          '• Enregistrement des charges : 2 100 000 DH',
                          '• Valorisation des travaux en cours : 2 100 000 DH',
                          '• Réception de l\'avance : 1 350 000 DH',
                        ].map((detail) => Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Text(
                                detail,
                                style: textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                                  height: 1.5,
                                ),
                              ),
                            )),
                        const SizedBox(height: 16),
                        Text(
                          'Année N+1',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        ...[
                          '• Enregistrement des charges : 1 300 000 DH',
                          '• Annulation des travaux en cours N : 2 100 000 DH',
                          '• Constatation du produit : 3 900 000 DH',
                          '• Réception du solde : 2 550 000 DH',
                        ].map((detail) => Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Text(
                                detail,
                                style: textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                                  height: 1.5,
                                ),
                              ),
                            )),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Journal comptable
                  JournalComptableWidget(
                    title: 'Écritures comptables - Méthode de l\'achèvement',
                    entries: [
                      JournalEntry(
                        date: 'N',
                        lines: [
                          JournalLine(
                            account: '5141',
                            label: 'Banque',
                            debit: '1 350 000,00',
                          ),
                          JournalLine(
                            account: '4421',
                            label: 'Clients - avances et acomptes',
                            credit: '1 350 000,00',
                          ),
                        ],
                      ),
                      JournalEntry(
                        date: 'N',
                        lines: [
                          JournalLine(
                            account: '6',
                            label: 'Charge',
                            debit: '2 100 000,00',
                          ),
                          JournalLine(
                            account: '5141',
                            label: 'Banque',
                            credit: '2 100 000,00',
                          ),
                        ],
                      ),
                      JournalEntry(
                        date: '31/12/N',
                        lines: [
                          JournalLine(
                            account: '313',
                            label: 'Produit en cours',
                            debit: '2 100 000,00',
                          ),
                          JournalLine(
                            account: '7131',
                            label: 'Variation des stoks',
                            credit: '2 100 000,00',
                          ),
                        ],
                      ),
                      JournalEntry(
                        date: 'N+1',
                        lines: [
                          JournalLine(
                            account: '5141',
                            label: 'Banque',
                            debit: '2 550 000,00',
                          ),
                          JournalLine(
                            account: '4421',
                            label: 'Clients - avances et acomptes',
                            credit: '2 550 000,00',
                          ),
                        ],
                      ),
                      JournalEntry(
                        date: 'N+1',
                        lines: [
                          JournalLine(
                            account: '6',
                            label: 'Charge',
                            debit: '1 300 000,00',
                          ),
                          JournalLine(
                            account: '5141',
                            label: 'Banque',
                            credit: '1 300 000,00',
                          ),
                        ],
                      ),
                      JournalEntry(
                        date: 'N+1',
                        lines: [
                          JournalLine(
                            account: '7131',
                            label: 'Variation des stoks',
                            debit: '2 100 000,00',
                          ),
                          JournalLine(
                            account: '313',
                            label: 'Produit en cours',
                            credit: '2 100 000,00',
                          ),
                        ],
                      ),
                      JournalEntry(
                        date: 'N+1',
                        lines: [
                          JournalLine(
                            account: '3421',
                            label: 'Client',
                            debit: '3 900 000,00',
                          ),
                          JournalLine(
                            account: '7121',
                            label: 'Ventes de biens et services',
                            credit: '3 900 000,00',
                          ),
                        ],
                      ),
                      JournalEntry(
                        date: 'N+1',
                        lines: [
                          JournalLine(
                            account: '4421',
                            label: 'Clients - avances et acomptes',
                            debit: '3 900 000,00',
                          ),
                          JournalLine(
                            account: '3421',
                            label: 'Client',
                            credit: '3 900 000,00',
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
