import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:ui' as ui;

class ExampleViewer extends StatefulWidget {
  final Map<String, dynamic> example;
  final bool showTotal;

  const ExampleViewer({
    super.key,
    required this.example,
    this.showTotal = true,
  });

  @override
  State<ExampleViewer> createState() => _ExampleViewerState();
}

class _ExampleViewerState extends State<ExampleViewer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;
  int _currentStep = 0;
  List<double> _visibleBars = [];

  Map<String, dynamic> _processExampleData() {
    // Handle different possible data structures
    if (widget.example.containsKey('data')) {
      return widget.example['data'];
    }

    // If no explicit 'data', try to extract numeric values
    Map<String, dynamic> processedData = {};
    widget.example.forEach((key, value) {
      if (value is num) {
        processedData[key] = value;
      }
    });

    // Calculate total if not present
    if (processedData.isEmpty) {
      // Fallback if no numeric data found
      processedData = {'default': 100000, 'total': 100000};
    } else if (!processedData.containsKey('total')) {
      processedData['total'] = processedData.values.reduce((a, b) => a + b);
    }

    return processedData;
  }

  List<String> _processSteps() {
    // Handle different possible step formats
    if (widget.example.containsKey('steps')) {
      return List<String>.from(widget.example['steps']);
    }

    // If no steps, generate steps from data
    final data = _processExampleData();
    if (data.length <= 1) {
      return ['Exemple de calcul'];
    }

    return data.entries
        .where((entry) => entry.key != 'total')
        .map((entry) => '${entry.key}: ${entry.value} DH')
        .toList();
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    final steps = _processSteps();
    _animations = List.generate(
      steps.length,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            0.0,
            1.0,
            curve: Curves.easeInOut,
          ),
        ),
      ),
    );

    // Initialize visible bars array
    final data = _processExampleData();
    _visibleBars = List.generate(
        data.length - (data.containsKey('total') ? 1 : 0), (index) => 0.0);

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        final steps = _processSteps();
        if (_currentStep < steps.length - 1) {
          setState(() {
            if (_currentStep < _visibleBars.length) {
              _visibleBars[_currentStep] = 1.0;
            }
            _currentStep++;
          });
          _controller.forward(from: 0.0);
        }
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final data = _processExampleData();
    final steps = _processSteps();
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (data.length > 1) ...[
          SizedBox(
            height: 200,
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return CustomPaint(
                  size: const Size(double.infinity, 200),
                  painter: BarChartPainter(
                    data: data,
                    animations: _animations,
                    visibleBars: _visibleBars,
                    currentStep: _currentStep,
                    currentAnimation: _controller,
                    showTotal: widget.showTotal,
                    colorScheme: colorScheme,
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 24),
        ],
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton.icon(
              icon: Icon(Icons.refresh, color: colorScheme.primary),
              label: Text(
                'Recommencer',
                style: TextStyle(color: colorScheme.primary),
              ),
              onPressed: () {
                setState(() {
                  _currentStep = 0;
                  _visibleBars =
                      List.generate(_visibleBars.length, (index) => 0.0);
                });
                _controller.reset();
                _controller.forward();
              },
            ),
            TextButton.icon(
              icon: Icon(Icons.play_arrow, color: colorScheme.primary),
              label: Text(
                'Suivant',
                style: TextStyle(color: colorScheme.primary),
              ),
              onPressed: _currentStep < steps.length - 1
                  ? () {
                      if (_currentStep < _visibleBars.length) {
                        _visibleBars[_currentStep] = 1.0;
                      }
                      setState(() {
                        _currentStep++;
                      });
                      _controller.forward(from: 0.0);
                    }
                  : null,
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Étape ${_currentStep + 1}/${steps.length}',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                steps[_currentStep].toString(),
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class BarChartPainter extends CustomPainter {
  final Map<String, dynamic> data;
  final List<Animation<double>> animations;
  final List<double> visibleBars;
  final int currentStep;
  final Animation<double> currentAnimation;
  final bool showTotal;
  final ColorScheme colorScheme;

  BarChartPainter({
    required this.data,
    required this.animations,
    required this.visibleBars,
    required this.currentStep,
    required this.currentAnimation,
    required this.showTotal,
    required this.colorScheme,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2.0;

    final total = data['total'] as num;
    final barWidth = size.width / (data.length + 1);
    final maxHeight = size.height - 40;

    var x = barWidth / 2;
    var index = 0;

    data.forEach((key, value) {
      if (key != 'total' || showTotal) {
        double animationValue;
        if (index < currentStep) {
          animationValue = visibleBars[index];
        } else if (index == currentStep) {
          animationValue = currentAnimation.value;
        } else {
          animationValue = 0.0;
        }

        final barHeight = (value / total) * maxHeight * animationValue;

        // Draw bar
        paint.color = colorScheme.primary.withValues(alpha: 0.7);
        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(
              x,
              size.height - barHeight - 20,
              barWidth - 10,
              barHeight,
            ),
            const Radius.circular(8),
          ),
          paint,
        );

        // Draw value if bar is visible
        if (animationValue > 0) {
          final textPainter = TextPainter(
            text: TextSpan(
              text: '${NumberFormat.compact().format(value)} DH',
              style: TextStyle(
                color: colorScheme.onSurface,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            textDirection: ui.TextDirection.ltr,
          );
          textPainter.layout();
          textPainter.paint(
            canvas,
            Offset(
              x + (barWidth - 10 - textPainter.width) / 2,
              size.height - barHeight - 40,
            ),
          );
        }

        x += barWidth;
        index++;
      }
    });
  }

  @override
  bool shouldRepaint(covariant BarChartPainter oldDelegate) =>
      currentAnimation != oldDelegate.currentAnimation ||
      currentStep != oldDelegate.currentStep ||
      visibleBars != oldDelegate.visibleBars ||
      colorScheme != oldDelegate.colorScheme;
}
