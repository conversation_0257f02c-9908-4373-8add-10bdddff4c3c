import 'package:flutter/material.dart';

class ImmobilisationCard extends StatelessWidget {
  final String title;
  final String description;
  final List<Map<String, dynamic>> elements;
  final List<String>? notes;
  final VoidCallback onTap;
  final VoidCallback onExampleTap;
  final VoidCallback onExerciseTap;
  final bool isCompleted;

  const ImmobilisationCard({
    super.key,
    required this.title,
    required this.description,
    required this.elements,
    this.notes,
    required this.onTap,
    required this.onExampleTap,
    required this.onExerciseTap,
    this.isCompleted = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isCompleted
            ? BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              )
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  if (isCompleted)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                children: elements
                    .take(3)
                    .map((element) => Chip(
                          label: Text(element['title'].toString()),
                          backgroundColor:
                              Theme.of(context).colorScheme.primaryContainer,
                          labelStyle: TextStyle(
                            color: Theme.of(context)
                                .colorScheme
                                .onPrimaryContainer,
                          ),
                        ))
                    .toList(),
              ),
              if (elements.length > 3)
                Chip(
                  label: Text('+${elements.length - 3} plus'),
                  backgroundColor:
                      Theme.of(context).colorScheme.secondaryContainer,
                  labelStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSecondaryContainer,
                  ),
                ),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    icon: const Icon(Icons.book),
                    label: const Text('Exemple'),
                    onPressed: onExampleTap,
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    icon: const Icon(Icons.assignment),
                    label: const Text('Exercice'),
                    onPressed: onExerciseTap,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
