import 'package:flutter/material.dart';
import 'sections/acquisition_onereux_section.dart';
import 'sections/production_interne_section.dart';
import 'sections/acquisition_echange_section.dart';
import 'sections/acquisition_gratuit_section.dart';
import 'sections/acquisition_apport_section.dart';
import 'sections/acquisition_subvention_section.dart';
import '../../../../widgets/guide/notes_fab.dart';

class ImmobilisationsScreen extends StatefulWidget {
  const ImmobilisationsScreen({super.key});

  @override
  State<ImmobilisationsScreen> createState() => _ImmobilisationsScreenState();
}

class _ImmobilisationsScreenState extends State<ImmobilisationsScreen> {
  int _selectedIndex = 0;

  final List<Widget> _sections = const [
    AcquisitionOnereuxSection(),
    ProductionInterneSection(),
    AcquisitionEchangeSection(),
    AcquisitionGratuitSection(),
    AcquisitionApportSection(),
    AcquisitionSubventionSection(),
  ];

  final List<({IconData icon, String title})> _navigationItems = const [
    (icon: Icons.shopping_cart_outlined, title: 'Acquisition à titre onéreux'),
    (icon: Icons.precision_manufacturing_outlined, title: 'Production interne'),
    (icon: Icons.swap_horiz_outlined, title: 'Acquisition par échange'),
    (icon: Icons.card_giftcard_outlined, title: 'Acquisition à titre gratuit'),
    (icon: Icons.account_balance_outlined, title: 'Acquisition par apport'),
    (icon: Icons.payments_outlined, title: 'Acquisition par subvention'),
  ];

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Évaluation des Immobilisations',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: TextButton.icon(
              icon: Icon(Icons.auto_awesome, color: colorScheme.primary),
              label: Text(
                'Guide complet',
                style: textTheme.labelLarge?.copyWith(
                  color: colorScheme.primary,
                ),
              ),
              onPressed: () {},
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            color: colorScheme.surface,
            height: 56,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _navigationItems.length,
              itemBuilder: (context, index) {
                final item = _navigationItems[index];
                final isSelected = _selectedIndex == index;
                return Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                  child: TextButton.icon(
                    icon: Icon(
                      item.icon,
                      color: isSelected
                          ? colorScheme.primary
                          : colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                    label: Text(
                      item.title,
                      style: textTheme.labelLarge?.copyWith(
                        color: isSelected
                            ? colorScheme.primary
                            : colorScheme.onSurfaceVariant,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      backgroundColor: isSelected
                          ? colorScheme.primaryContainer.withValues(alpha: 0.2)
                          : null,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: () => setState(() => _selectedIndex = index),
                  ),
                );
              },
            ),
          ),
          Expanded(
            child: _sections[_selectedIndex],
          ),
        ],
      ),
      floatingActionButton: const NotesFAB(
        guideId: 'immobilisations',
        sectionId: 'immobilisations_main',
        sectionTitle: 'Évaluation des Immobilisations',
      ),
    );
  }
}
