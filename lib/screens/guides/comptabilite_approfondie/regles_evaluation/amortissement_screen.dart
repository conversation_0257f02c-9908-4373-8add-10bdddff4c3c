import 'package:flutter/material.dart';
import 'sections/lineaire_section.dart';
import 'sections/degressif_section.dart';
import 'sections/derogatoire_section.dart';
import 'sections/calculateur_section.dart';
import 'sections/exercices_section.dart';

class AmortissementScreen extends StatefulWidget {
  const AmortissementScreen({super.key});

  @override
  State<AmortissementScreen> createState() => _AmortissementScreenState();
}

class _AmortissementScreenState extends State<AmortissementScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    final isWideScreen = MediaQuery.of(context).size.width >= 600;

    if (isWideScreen) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Amortissements'),
        ),
        body: Row(
          children: [
            NavigationRail(
              selectedIndex: _selectedIndex,
              onDestinationSelected: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              labelType: NavigationRailLabelType.all,
              destinations: const [
                NavigationRailDestination(
                  icon: Icon(Icons.linear_scale),
                  label: Text('Linéaire'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.show_chart),
                  label: Text('Dégressif'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.trending_up),
                  label: Text('Dérogatoire'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.calculate),
                  label: Text('Calculateur'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.assignment),
                  label: Text('Exercices'),
                ),
              ],
            ),
            Expanded(
              child: Container(
                color: Theme.of(context).colorScheme.surface,
                child: _buildSelectedSection(),
              ),
            ),
          ],
        ),
      );
    }

    return DefaultTabController(
      length: 5,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Amortissements'),
          bottom: const TabBar(
            isScrollable: true,
            tabs: [
              Tab(
                icon: Icon(Icons.linear_scale),
                text: 'Linéaire',
              ),
              Tab(
                icon: Icon(Icons.show_chart),
                text: 'Dégressif',
              ),
              Tab(
                icon: Icon(Icons.trending_up),
                text: 'Dérogatoire',
              ),
              Tab(
                icon: Icon(Icons.calculate),
                text: 'Calculateur',
              ),
              Tab(
                icon: Icon(Icons.assignment),
                text: 'Exercices',
              ),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            LineaireSection(),
            DegressifSection(),
            DerogatoireSection(),
            CalculateurSection(),
            ExercicesSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedSection() {
    switch (_selectedIndex) {
      case 0:
        return const LineaireSection();
      case 1:
        return const DegressifSection();
      case 2:
        return const DerogatoireSection();
      case 3:
        return const CalculateurSection();
      case 4:
        return const ExercicesSection();
      default:
        return const Center(child: Text('Section non trouvée'));
    }
  }
}
