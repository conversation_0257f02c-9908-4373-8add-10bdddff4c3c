import 'package:flutter/material.dart';

class SectionTitle extends StatelessWidget {
  final String title;

  const SectionTitle({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
    );
  }
}

class DefinitionCard extends StatelessWidget {
  final String title;
  final List<String> points;

  const DefinitionCard({
    super.key,
    required this.title,
    required this.points,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ...points.map((point) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.arrow_right,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          point,
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge
                              ?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}

class QuestionInput extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final bool? isCorrect;
  final String suffix;

  const QuestionInput({
    super.key,
    required this.label,
    required this.controller,
    required this.isCorrect,
    this.suffix = 'DH',
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: controller,
            decoration: InputDecoration(
              filled: true,
              fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              suffixText: suffix,
              suffixStyle: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              suffixIcon: isCorrect == null
                  ? null
                  : Icon(
                      isCorrect! ? Icons.check_circle : Icons.error,
                      color: isCorrect! ? Colors.green : Colors.red,
                    ),
            ),
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
    );
  }
}

class AmortissementRow {
  final String annee;
  final String base;
  final String annuite;
  final String cumul;
  final String vnc;

  const AmortissementRow({
    required this.annee,
    required this.base,
    required this.annuite,
    required this.cumul,
    required this.vnc,
  });
}

class AmortissementTable extends StatelessWidget {
  final List<AmortissementRow> rows;

  const AmortissementTable({super.key, required this.rows});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8),
        ),
        columns: [
          DataColumn(
            label: Text(
              'Année',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          DataColumn(
            label: Text(
              'Base',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          DataColumn(
            label: Text(
              'Annuité',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          DataColumn(
            label: Text(
              'Cumul',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          DataColumn(
            label: Text(
              'VNC',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
        rows: rows.map((row) {
          final style = TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: row.annee == 'N' || row.annee == 'N+5'
                ? FontWeight.bold
                : FontWeight.normal,
          );

          return DataRow(
            cells: [
              DataCell(Text(row.annee, style: style)),
              DataCell(Text(row.base, style: style)),
              DataCell(Text(row.annuite, style: style)),
              DataCell(Text(row.cumul, style: style)),
              DataCell(Text(row.vnc, style: style)),
            ],
          );
        }).toList(),
      ),
    );
  }
}
