import 'package:flutter/material.dart';
import '../widgets/common_widgets.dart';

class ExercicesSection extends StatefulWidget {
  const ExercicesSection({super.key});

  @override
  State<ExercicesSection> createState() => _ExercicesSectionState();
}

class _ExercicesSectionState extends State<ExercicesSection> {
  final _exercice1Controller = TextEditingController();
  final _exercice2Controller = TextEditingController();
  final _exercice3Controller = TextEditingController();

  bool? _exercice1Correct;
  bool? _exercice2Correct;
  bool? _exercice3Correct;

  @override
  void dispose() {
    _exercice1Controller.dispose();
    _exercice2Controller.dispose();
    _exercice3Controller.dispose();
    super.dispose();
  }

  void _verifierReponses() {
    setState(() {
      _exercice1Correct = _exercice1Controller.text.trim() == '75000';
      _exercice2Correct = _exercice2Controller.text.trim() == '37500';
      _exercice3Correct = _exercice3Controller.text.trim() == '37500';
    });

    showDialog(
      context: context,
      builder: (context) {
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return AlertDialog(
          title: Row(
            children: [
              Icon(
                _toutesLesReponsesCorrectes
                    ? Icons.check_circle
                    : Icons.warning,
                color: _toutesLesReponsesCorrectes
                    ? colorScheme.primary
                    : colorScheme.error,
              ),
              const SizedBox(width: 8),
              Text(
                _toutesLesReponsesCorrectes
                    ? 'Félicitations !'
                    : 'Continuez vos efforts',
                style: textTheme.titleLarge?.copyWith(
                  color: _toutesLesReponsesCorrectes
                      ? colorScheme.primary
                      : colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _toutesLesReponsesCorrectes
                    ? 'Toutes vos réponses sont correctes !'
                    : 'Certaines réponses sont incorrectes. Voici les explications :',
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              if (!_toutesLesReponsesCorrectes) ...[
                if (_exercice1Correct == false)
                  _buildExplication(
                    'Exercice 1',
                    '300 000 × 25% = 75 000 DH',
                  ),
                if (_exercice2Correct == false)
                  _buildExplication(
                    'Exercice 2',
                    '75 000 × 6/12 = 37 500 DH',
                  ),
                if (_exercice3Correct == false)
                  _buildExplication(
                    'Exercice 3',
                    '75 000 × 6/12 = 37 500 DH',
                  ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Fermer',
                style: textTheme.labelLarge?.copyWith(
                  color: colorScheme.primary,
                ),
              ),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: colorScheme.surface,
          surfaceTintColor: colorScheme.surfaceTint,
        );
      },
    );
  }

  bool get _toutesLesReponsesCorrectes =>
      _exercice1Correct == true &&
      _exercice2Correct == true &&
      _exercice3Correct == true;

  Widget _buildExplication(String titre, String detail) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            titre,
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            detail,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Exercices récapitulatifs',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Testez vos connaissances avec des cas pratiques',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          _buildExercice1(),
          const SizedBox(height: 24),
          _buildExercice2(),
          const SizedBox(height: 24),
          _buildExercice3(),
        ],
      ),
    );
  }

  Widget _buildExercice1() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exercice 1',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Énoncé :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Une entreprise acquiert un matériel le 01/01/N :\n'
                    '• Valeur d\'origine : 300 000 DH\n'
                    '• Durée d\'utilisation : 4 ans\n'
                    '• Mode d\'amortissement : Linéaire',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            QuestionInput(
              label: 'Calculez l\'annuité d\'amortissement :',
              controller: _exercice1Controller,
              isCorrect: _exercice1Correct,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExercice2() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exercice 2',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Énoncé :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Une entreprise acquiert un matériel le 01/07/N :\n'
                    '• Valeur d\'origine : 300 000 DH\n'
                    '• Durée d\'utilisation : 4 ans\n'
                    '• Mode d\'amortissement : Linéaire',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            QuestionInput(
              label: 'Calculez la première annuité (prorata temporis) :',
              controller: _exercice2Controller,
              isCorrect: _exercice2Correct,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExercice3() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exercice 3',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Énoncé :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Une entreprise acquiert un matériel le 01/07/N :\n'
                    '• Prix d\'achat : 280 000 DH\n'
                    '• Frais de transport : 20 000 DH\n'
                    '• Durée d\'utilisation : 4 ans\n'
                    '• Mode d\'amortissement : Linéaire',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            QuestionInput(
              label: 'Calculez la première annuité (prorata temporis) :',
              controller: _exercice3Controller,
              isCorrect: _exercice3Correct,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _verifierReponses,
                icon: const Icon(Icons.check_circle),
                label: Text(
                  'Vérifier mes réponses',
                  style: textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
