import 'package:flutter/material.dart';
import '../../../../../utils/calculation_utils.dart';
import '../../../../../models/immobilisations/amortization_row.dart';

class CalculateurSection extends StatefulWidget {
  const CalculateurSection({super.key});

  @override
  State<CalculateurSection> createState() => _CalculateurSectionState();
}

class _CalculateurSectionState extends State<CalculateurSection> {
  final _formKey = GlobalKey<FormState>();
  final _valueController = TextEditingController();
  final _durationController = TextEditingController();
  final _rateController = TextEditingController();

  String _selectedMethod = 'lineaire';
  DateTime? _acquisitionDate;
  List<AmortizationRow> _amortizationTable = [];
  bool _isCalculated = false;

  @override
  void dispose() {
    _valueController.dispose();
    _durationController.dispose();
    _rateController.dispose();
    super.dispose();
  }

  void _calculate() {
    if (_formKey.currentState!.validate() && _acquisitionDate != null) {
      final value = double.parse(_valueController.text.replaceAll(' ', ''));
      final duration = int.parse(_durationController.text);
      final rate = _rateController.text.isNotEmpty
          ? double.parse(_rateController.text)
          : (100 / duration);

      setState(() {
        _amortizationTable = _generateAmortizationTable(
          value: value,
          duration: duration,
          rate: rate,
          method: _selectedMethod,
          acquisitionDate: _acquisitionDate!,
        );
        _isCalculated = true;
      });
    }
  }

  List<AmortizationRow> _generateAmortizationTable({
    required double value,
    required int duration,
    required double rate,
    required String method,
    required DateTime acquisitionDate,
  }) {
    final List<AmortizationRow> table = [];
    double remainingValue = value;
    double cumulativeAnnuity = 0;

    for (int year = 1; year <= duration; year++) {
      final currentYear = acquisitionDate.year + year - 1;
      double annuity;

      if (method == 'lineaire') {
        annuity = value * (rate / 100);
        if (year == 1) {
          // Prorata temporis for first year
          final monthsRemaining = 12 - acquisitionDate.month + 1;
          annuity = annuity * (monthsRemaining / 12);
        }
      } else {
        // Degressive method
        annuity = remainingValue * (rate / 100);
      }

      if (year == duration) {
        annuity = remainingValue; // Last year takes remaining value
      }

      cumulativeAnnuity += annuity;
      remainingValue -= annuity;

      table.add(AmortizationRow(
        year: currentYear,
        yearLabel: year == 1 && acquisitionDate.month > 1
            ? '$currentYear (${12 - acquisitionDate.month + 1} mois)'
            : currentYear.toString(),
        baseAmount: value,
        rate: rate,
        annuity: annuity,
        cumulativeAnnuity: cumulativeAnnuity,
        netBookValue: value - cumulativeAnnuity,
      ));
    }

    return table;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Calculateur d\'Amortissement',
            style: textTheme.headlineMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          _buildCalculatorForm(),
          if (_isCalculated) ...[
            const SizedBox(height: 32),
            _buildAmortizationTable(),
          ],
        ],
      ),
    );
  }

  Widget _buildCalculatorForm() {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                controller: _valueController,
                decoration: const InputDecoration(
                  labelText: 'Valeur d\'acquisition (DH)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez saisir la valeur';
                  }
                  if (double.tryParse(value.replaceAll(' ', '')) == null) {
                    return 'Valeur invalide';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _durationController,
                decoration: const InputDecoration(
                  labelText: 'Durée d\'amortissement (années)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez saisir la durée';
                  }
                  final duration = int.tryParse(value);
                  if (duration == null || duration <= 0) {
                    return 'Durée invalide';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedMethod,
                decoration: const InputDecoration(
                  labelText: 'Méthode d\'amortissement',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'lineaire', child: Text('Linéaire')),
                  DropdownMenuItem(value: 'degressif', child: Text('Dégressif')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedMethod = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              ListTile(
                title: Text(_acquisitionDate == null
                    ? 'Date d\'acquisition'
                    : 'Date: ${_acquisitionDate!.day}/${_acquisitionDate!.month}/${_acquisitionDate!.year}'),
                trailing: const Icon(Icons.calendar_today),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2030),
                  );
                  if (date != null) {
                    setState(() {
                      _acquisitionDate = date;
                    });
                  }
                },
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _calculate,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Calculer'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmortizationTable() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tableau d\'Amortissement',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Année')),
                  DataColumn(label: Text('Base')),
                  DataColumn(label: Text('Taux')),
                  DataColumn(label: Text('Annuité')),
                  DataColumn(label: Text('Cumul')),
                  DataColumn(label: Text('VNC')),
                ],
                rows: _amortizationTable.map((row) {
                  return DataRow(
                    cells: [
                      DataCell(Text(row.yearLabel)),
                      DataCell(Text(CalculationUtils.formatMonetary(row.baseAmount))),
                      DataCell(Text('${row.rate.toStringAsFixed(2)}%')),
                      DataCell(Text(CalculationUtils.formatMonetary(row.annuity))),
                      DataCell(Text(CalculationUtils.formatMonetary(row.cumulativeAnnuity))),
                      DataCell(Text(CalculationUtils.formatMonetary(row.netBookValue))),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}