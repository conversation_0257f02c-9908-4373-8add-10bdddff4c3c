import 'package:flutter/material.dart';
import '../widgets/common_widgets.dart';
import '../../../../../widgets/journal_comptable_widget.dart';

class DerogatoireSection extends StatefulWidget {
  const DerogatoireSection({super.key});

  @override
  State<DerogatoireSection> createState() => _DerogatoireSectionState();
}

class _DerogatoireSectionState extends State<DerogatoireSection> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Amortissement Dérogatoire',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Comprendre le calcul de l\'amortissement dérogatoire',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          DefinitionCard(
            title: 'Caractéristiques de l\'amortissement dérogatoire :',
            points: [
              'Différence entre l\'amortissement fiscal et comptable',
              'Permet de bénéficier d\'avantages fiscaux',
              'Comptabilisé en amortissement dérogatoire',
              'Réintégré progressivement au résultat fiscal',
            ],
          ),
          const SizedBox(height: 24),
          _buildExemple(),
          const SizedBox(height: 24),
          _buildComptabilisation(),
        ],
      ),
    );
  }

  Widget _buildExemple() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exemple pratique',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Données :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Une entreprise acquiert un équipement le 01/01/N :\n'
                    '• Valeur d\'origine : 500 000 DH\n'
                    '• Durée d\'utilisation comptable : 5 ans\n'
                    '• Durée d\'amortissement fiscal : 3 ans\n'
                    '• Mode d\'amortissement fiscal : Dégressif',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Calculs :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '1. Amortissement comptable :\n'
                    '• Taux linéaire = 20% (100% / 5 ans)\n'
                    '• Annuité = 100 000 DH (500 000 × 20%)\n\n'
                    '2. Amortissement fiscal :\n'
                    '• Taux dégressif = 50% (33,33% × 1,5)\n'
                    '• Première annuité = 250 000 DH\n\n'
                    '3. Amortissement dérogatoire :\n'
                    '• Année N : 150 000 DH (250 000 - 100 000)\n'
                    '• Année N+1 : 75 000 DH\n'
                    '• Année N+2 : -25 000 DH\n'
                    '• Année N+3 : -100 000 DH\n'
                    '• Année N+4 : -100 000 DH',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComptabilisation() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Comptabilisation',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '1. Constatation de l\'amortissement dérogatoire :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  JournalComptableWidget(
                    title: 'Écritures comptables',
                    entries: [
                      JournalEntry(
                        date: '31/12/N',
                        lines: [
                          JournalLine(
                            account: '6852',
                            label: 'Dotations aux amortissements dérogatoires',
                            debit: '150 000,00',
                          ),
                          JournalLine(
                            account: '1452',
                            label: 'Amortissements dérogatoires',
                            credit: '150 000,00',
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '2. Reprise de l\'amortissement dérogatoire :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  JournalComptableWidget(
                    title: 'Écritures de régularisation',
                    entries: [
                      JournalEntry(
                        date: '31/12/N+2',
                        lines: [
                          JournalLine(
                            account: '1452',
                            label: 'Amortissements dérogatoires',
                            debit: '25 000,00',
                          ),
                          JournalLine(
                            account: '7852',
                            label: 'Reprises sur amortissements dérogatoires',
                            credit: '25 000,00',
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
