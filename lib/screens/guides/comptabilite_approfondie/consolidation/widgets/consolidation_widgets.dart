import 'package:flutter/material.dart';

class ConsolidationMethodCard extends StatelessWidget {
  final String title;
  final String description;
  final List<String> characteristics;
  final List<String> steps;
  final Widget example;

  const ConsolidationMethodCard({
    super.key,
    required this.title,
    required this.description,
    required this.characteristics,
    required this.steps,
    required this.example,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              description,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: colorScheme.onSurface,
                  ),
            ),
            const SizedBox(height: 24),
            _buildSection(
              context,
              'Caractéristiques',
              characteristics,
              Icons.check_circle_outline,
              colorScheme.primary,
            ),
            const SizedBox(height: 24),
            _buildSection(
              context,
              'Étapes de mise en œuvre',
              steps,
              Icons.arrow_right,
              colorScheme.secondary,
            ),
            const SizedBox(height: 24),
            _buildExampleSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    List<String> items,
    IconData iconData,
    Color iconColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
        ),
        const SizedBox(height: 12),
        ...items.map(
          (item) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  iconData,
                  size: 20,
                  color: iconColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    item,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExampleSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Exemple pratique',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
        ),
        const SizedBox(height: 12),
        example,
      ],
    );
  }
}

class OrganizationChart extends StatelessWidget {
  final List<CompanyNode> companies;

  const OrganizationChart({
    super.key,
    required this.companies,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: OrganizationChartPainter(
        companies: companies,
        textStyle: Theme.of(context).textTheme.bodyMedium!,
        primaryColor: Theme.of(context).colorScheme.primary,
      ),
      child: const SizedBox.expand(),
    );
  }
}

class CompanyNode {
  final String name;
  final double controlPercentage;
  final Point position;
  final IconData? icon;

  CompanyNode({
    required this.name,
    required this.controlPercentage,
    required this.position,
    this.icon,
  });
}

class Point {
  final double x;
  final double y;

  const Point(this.x, this.y);
}

class OrganizationChartPainter extends CustomPainter {
  final List<CompanyNode> companies;
  final TextStyle textStyle;
  final Color primaryColor;

  OrganizationChartPainter({
    required this.companies,
    required this.textStyle,
    required this.primaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    for (var i = 1; i < companies.length; i++) {
      final parent = companies[0];
      final child = companies[i];
      canvas.drawLine(
        Offset(parent.position.x.toDouble(), parent.position.y.toDouble() + 30),
        Offset(child.position.x.toDouble(), child.position.y.toDouble() - 30),
        paint,
      );
    }

    for (var company in companies) {
      _drawCompanyNode(canvas, company);
    }
  }

  void _drawCompanyNode(Canvas canvas, CompanyNode company) {
    final rect = Rect.fromCenter(
      center: Offset(company.position.x.toDouble(), company.position.y.toDouble()),
      width: 120,
      height: 60,
    );

    // Draw background
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(8)),
      Paint()
        ..color = primaryColor.withValues(alpha: 0.1)
        ..style = PaintingStyle.fill,
    );

    // Draw border
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(8)),
      Paint()
        ..color = primaryColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1,
    );

    // Draw icon if present
    if (company.icon != null) {
      final iconPainter = TextPainter(
        text: TextSpan(
          text: String.fromCharCode(company.icon!.codePoint),
          style: TextStyle(
            fontSize: 20,
            fontFamily: company.icon!.fontFamily,
            color: primaryColor,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      iconPainter.layout();
      iconPainter.paint(
        canvas,
        Offset(
          company.position.x.toDouble() - 50,
          company.position.y.toDouble() - 20,
        ),
      );
    }

    // Draw company name
    final namePainter = TextPainter(
      text: TextSpan(
        text: company.name,
        style: textStyle.copyWith(
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    namePainter.layout(maxWidth: 100);
    namePainter.paint(
      canvas,
      Offset(
        company.position.x.toDouble() - namePainter.width / 2,
        company.position.y.toDouble() - 10,
      ),
    );

    // Draw control percentage
    final percentagePainter = TextPainter(
      text: TextSpan(
        text: '${company.controlPercentage.toStringAsFixed(0)}%',
        style: textStyle.copyWith(
          color: primaryColor,
          fontSize: 11,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    percentagePainter.layout();
    percentagePainter.paint(
      canvas,
      Offset(
        company.position.x.toDouble() - percentagePainter.width / 2,
        company.position.y.toDouble() + 10,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class _CompanyBox extends StatelessWidget {
  final CompanyNode company;

  const _CompanyBox({
    required this.company,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            company.name,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
          ),
          const SizedBox(height: 4),
          Text(
            '${company.controlPercentage.toStringAsFixed(1)}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }
}

class PercentageCalculator extends StatefulWidget {
  final void Function(double) onPercentageChanged;
  final String title;
  final String description;

  const PercentageCalculator({
    super.key,
    required this.onPercentageChanged,
    required this.title,
    required this.description,
  });

  @override
  State<PercentageCalculator> createState() => _PercentageCalculatorState();
}

class _PercentageCalculatorState extends State<PercentageCalculator> {
  final _directController = TextEditingController();
  final _indirectController = TextEditingController();
  double _totalPercentage = 0;

  @override
  void initState() {
    super.initState();
    _directController.addListener(_calculateTotal);
    _indirectController.addListener(_calculateTotal);
  }

  void _calculateTotal() {
    final direct = double.tryParse(_directController.text) ?? 0;
    final indirect = double.tryParse(_indirectController.text) ?? 0;
    setState(() {
      _totalPercentage = direct + indirect;
      widget.onPercentageChanged(_totalPercentage);
    });
  }

  @override
  void dispose() {
    _directController.dispose();
    _indirectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface,
                  ),
            ),
            const SizedBox(height: 16),
            _buildInputField(
              context,
              'Pourcentage de contrôle direct',
              _directController,
            ),
            const SizedBox(height: 12),
            _buildInputField(
              context,
              'Pourcentage de contrôle indirect',
              _indirectController,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Pourcentage total de contrôle',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                  ),
                  Text(
                    '${_totalPercentage.toStringAsFixed(2)}%',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField(
    BuildContext context,
    String label,
    TextEditingController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: controller,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: InputDecoration(
            suffixText: '%',
            filled: true,
            fillColor:
                Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
          ),
        ),
      ],
    );
  }
}
