import 'package:flutter/material.dart';
import '../../../../../../widgets/journal_comptable_widget.dart';

class ExercicesAvancesSection extends StatefulWidget {
  const ExercicesAvancesSection({super.key});

  @override
  State<ExercicesAvancesSection> createState() =>
      _ExercicesAvancesSectionState();
}

class _ExercicesAvancesSectionState extends State<ExercicesAvancesSection> {
  final Map<String, bool> _showSolutions = {};

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Exercices Avancés de Consolidation',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 24),
          _buildExercice(
            'Exercice 1: Consolidation Multi-niveaux',
            '''Structure du groupe au 31/12/2024:
• La société HOLDING M détient:
  - 80% de F1 (acquisition en 2022)
  - 30% de F2 (acquisition en 2023)
• F1 détient 60% de F3 (acquisition en 2024)

Données comptables:
1. Société F1:
   • Capital: 2 000 000 DH
   • Réserves: 500 000 DH
   • Résultat: 300 000 DH
   • Coût d'acquisition: 2 200 000 DH

2. Société F2:
   • Capital: 1 500 000 DH
   • Réserves: 300 000 DH
   • Résultat: 200 000 DH
   • Coût d'acquisition: 600 000 DH

3. Société F3:
   • Capital: 1 000 000 DH
   • Réserves: 200 000 DH
   • Résultat: 150 000 DH
   • Coût d'acquisition par F1: 800 000 DH

Travail à faire:
1. Déterminer les pourcentages d'intérêt et de contrôle
2. Choisir les méthodes de consolidation appropriées
3. Calculer les écarts de première consolidation
4. Déterminer les intérêts minoritaires
5. Établir les écritures de consolidation''',
            [
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '1111',
                    label: 'Capital F1',
                    debit: '2000000',
                  ),
                  JournalLine(
                    account: '1140',
                    label: 'Réserves F1',
                    debit: '500000',
                  ),
                  JournalLine(
                    account: '1181',
                    label: 'Résultat F1',
                    debit: '300000',
                  ),
                  JournalLine(
                    account: '2510',
                    label: 'Titres F1',
                    credit: '2200000',
                  ),
                  JournalLine(
                    account: '1141',
                    label: 'Intérêts minoritaires F1 (20%)',
                    credit: '560000',
                  ),
                  JournalLine(
                    account: '1130',
                    label: 'Écart de première consolidation F1',
                    credit: '40000',
                  ),
                ],
              ),
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '2510',
                    label: 'Titres mis en équivalence F2',
                    debit: '600000',
                  ),
                  JournalLine(
                    account: '2510',
                    label: 'Titres F2',
                    credit: '600000',
                  ),
                  JournalLine(
                    account: '7525',
                    label: 'Quote-part résultat MEE F2',
                    credit: '60000',
                  ),
                ],
              ),
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '1111',
                    label: 'Capital F3',
                    debit: '1000000',
                  ),
                  JournalLine(
                    account: '1140',
                    label: 'Réserves F3',
                    debit: '200000',
                  ),
                  JournalLine(
                    account: '1181',
                    label: 'Résultat F3',
                    debit: '150000',
                  ),
                  JournalLine(
                    account: '2510',
                    label: 'Titres F3',
                    credit: '800000',
                  ),
                  JournalLine(
                    account: '1141',
                    label: 'Intérêts minoritaires F3 (52%)',
                    credit: '702000',
                  ),
                  JournalLine(
                    account: '1130',
                    label: 'Écart de première consolidation F3',
                    credit: '152000',
                  ),
                ],
              ),
            ],
            '''Solution détaillée:

1. Pourcentages d'intérêt et de contrôle:
   F1: • Intérêt = 80%
       • Contrôle = 80% (contrôle exclusif)
   
   F2: • Intérêt = 30%
       • Contrôle = 30% (influence notable)
   
   F3: • Intérêt = 80% × 60% = 48%
       • Contrôle = 60% (contrôle exclusif indirect)

2. Méthodes de consolidation:
   F1: Intégration globale (contrôle > 50%)
   F2: Mise en équivalence (20% < contrôle < 50%)
   F3: Intégration globale (contrôle indirect > 50%)

3. Écarts de première consolidation:
   F1: • Quote-part = (2 000 000 + 500 000) × 80% = 2 000 000
       • Coût d'acquisition = 2 200 000
       • Écart = -200 000 (écart d'acquisition)
   
   F2: • Quote-part = (1 500 000 + 300 000) × 30% = 540 000
       • Coût d'acquisition = 600 000
       • Écart = -60 000 (écart d'acquisition)
   
   F3: • Quote-part = (1 000 000 + 200 000) × 60% = 720 000
       • Coût d'acquisition = 800 000
       • Écart = -80 000 (écart d'acquisition)

4. Intérêts minoritaires:
   F1: • Capital = 2 000 000 × 20% = 400 000
       • Réserves = 500 000 × 20% = 100 000
       • Résultat = 300 000 × 20% = 60 000
       • Total = 560 000
   
   F3: • Capital = 1 000 000 × 52% = 520 000
       • Réserves = 200 000 × 52% = 104 000
       • Résultat = 150 000 × 52% = 78 000
       • Total = 702 000

5. Les écritures de consolidation sont présentées dans le journal ci-dessus''',
          ),
          const SizedBox(height: 24),
          _buildExercice(
            'Exercice 2: Élimination des Opérations Intra-groupe',
            '''La société HOLDING M détient 75% de F1 et 60% de F2.
Au cours de l'exercice 2024, les opérations suivantes ont eu lieu:

1. Ventes de marchandises:
   • F1 a vendu à F2 des marchandises pour 500 000 DH avec une marge de 25%
   • F2 a revendu 80% de ces marchandises à des tiers
   • Le stock final chez F2 représente 20% des achats auprès de F1

2. Prestations de services:
   • F2 a facturé des prestations à M pour 200 000 DH
   • M a facturé des frais de gestion à F1 pour 150 000 DH

3. Opérations financières:
   • M a accordé un prêt de 1 000 000 DH à F1 au taux de 4%
   • F1 a versé des dividendes de 300 000 DH à M

Travail à faire:
1. Calculer la marge sur stock à éliminer
2. Déterminer les prestations intra-groupe à neutraliser
3. Éliminer les opérations financières réciproques
4. Établir les écritures de consolidation''',
            [
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '1181',
                    label: 'Résultat consolidé',
                    debit: '25000',
                  ),
                  JournalLine(
                    account: '3121',
                    label: 'Stocks de marchandises',
                    credit: '25000',
                  ),
                ],
              ),
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '6124',
                    label: 'Charges externes',
                    credit: '350000',
                  ),
                  JournalLine(
                    account: '7127',
                    label: 'Ventes de services',
                    debit: '350000',
                  ),
                ],
              ),
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '2411',
                    label: 'Prêts au groupe',
                    credit: '1000000',
                  ),
                  JournalLine(
                    account: '1481',
                    label: 'Emprunts auprès du groupe',
                    debit: '1000000',
                  ),
                ],
              ),
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '7381',
                    label: 'Intérêts et produits assimilés',
                    debit: '40000',
                  ),
                  JournalLine(
                    account: '6381',
                    label: 'Intérêts et charges assimilées',
                    credit: '40000',
                  ),
                ],
              ),
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '4464',
                    label: 'Dividendes à payer',
                    debit: '300000',
                  ),
                  JournalLine(
                    account: '7321',
                    label: 'Produits des participations',
                    credit: '300000',
                  ),
                ],
              ),
            ],
            '''Solution détaillée:

1. Calcul de la marge sur stock à éliminer:
   • Montant des ventes F1 → F2 = 500 000 DH
   • Marge = 25% soit 125 000 DH
   • Stock final = 20% soit 100 000 DH
   • Marge sur stock à éliminer = 25 000 DH (20% de 125 000)

2. Prestations intra-groupe à neutraliser:
   • F2 → M : 200 000 DH
   • M → F1 : 150 000 DH
   • Total : 350 000 DH

3. Opérations financières à éliminer:
   • Prêt M → F1 : 1 000 000 DH
   • Intérêts : 40 000 DH (4% × 1 000 000)
   • Dividendes F1 → M : 300 000 DH

4. Les écritures de consolidation sont présentées dans le journal ci-dessus:
   • Élimination de la marge sur stock
   • Neutralisation des prestations intra-groupe
   • Élimination du prêt et des intérêts
   • Annulation des dividendes intra-groupe''',
          ),
        ],
      ),
    );
  }

  Widget _buildExercice(String title, String enonce, List<JournalEntry> entries,
      String solution) {
    final String exerciceId = title.split(':')[0].trim();
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.school, color: colorScheme.primary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                enonce,
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: FilledButton.icon(
                onPressed: () {
                  setState(() {
                    _showSolutions[exerciceId] =
                        !(_showSolutions[exerciceId] ?? false);
                  });
                },
                icon: Icon(
                  _showSolutions[exerciceId] ?? false
                      ? Icons.visibility_off
                      : Icons.visibility,
                ),
                label: Text(
                  _showSolutions[exerciceId] ?? false
                      ? 'Masquer la solution'
                      : 'Afficher la solution',
                ),
              ),
            ),
            if (_showSolutions[exerciceId] ?? false) ...[
              const SizedBox(height: 24),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.tertiary.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: colorScheme.tertiary.withValues(alpha: 0.2)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Solution',
                      style: textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.tertiary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      solution,
                      style: textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Écritures comptables:',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    JournalComptableWidget(
                      title: 'Journal des Opérations',
                      entries: entries,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
