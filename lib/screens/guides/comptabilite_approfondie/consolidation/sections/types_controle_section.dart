import 'package:flutter/material.dart';

class TypesControleSection extends StatelessWidget {
  const TypesControleSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Types de Contrôle',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 24),
          _buildControleExclusif(context),
          const SizedBox(height: 24),
          _buildControleConjoint(context),
          const SizedBox(height: 24),
          _buildInfluenceNotable(context),
          const SizedBox(height: 24),
          _buildExempleCalcul(context),
        ],
      ),
    );
  }

  Widget _buildControleExclusif(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.control_point, color: colorScheme.primary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Contrôle Exclusif',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Le contrôle exclusif est le pouvoir de diriger les politiques financière et opérationnelle d\'une entreprise afin de tirer avantage de ses activités.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.primary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Critères de contrôle exclusif:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context,
                      'Détention directe ou indirecte de la majorité des droits de vote (> 50%)'),
                  _buildBulletPoint(context,
                      'Pouvoir de nommer ou de révoquer la majorité des membres des organes de direction'),
                  _buildBulletPoint(context,
                      'Droit d\'exercer une influence dominante en vertu d\'un contrat ou de clauses statutaires'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.secondary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.secondary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Méthode de consolidation:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context, 'Intégration globale (IG)'),
                  _buildBulletPoint(context,
                      'Intégration à 100% des actifs, passifs, charges et produits'),
                  _buildBulletPoint(
                      context, 'Élimination des opérations intra-groupe'),
                  _buildBulletPoint(
                      context, 'Calcul des intérêts minoritaires'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControleConjoint(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.handshake, color: colorScheme.secondary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Contrôle Conjoint',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Le contrôle conjoint est le partage du contrôle d\'une entreprise exploitée en commun par un nombre limité d\'associés ou d\'actionnaires.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.secondary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.secondary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Caractéristiques:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(
                      context, 'Nécessité d\'un accord contractuel'),
                  _buildBulletPoint(context,
                      'Partage du contrôle entre un nombre limité de partenaires'),
                  _buildBulletPoint(
                      context, 'Décisions stratégiques prises à l\'unanimité'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.tertiary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.tertiary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Méthode de consolidation:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(
                      context, 'Intégration proportionnelle (IP)'),
                  _buildBulletPoint(context,
                      'Intégration des actifs, passifs, charges et produits au prorata des droits'),
                  _buildBulletPoint(
                      context, 'Pas de calcul d\'intérêts minoritaires'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfluenceNotable(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.tertiary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.trending_up, color: colorScheme.tertiary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Influence Notable',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'L\'influence notable est le pouvoir de participer aux décisions de politique financière et opérationnelle d\'une entreprise sans en avoir le contrôle.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.tertiary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.tertiary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Indicateurs d\'influence notable:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(
                      context, 'Détention de 20% à 50% des droits de vote'),
                  _buildBulletPoint(
                      context, 'Représentation dans les organes de direction'),
                  _buildBulletPoint(
                      context, 'Participation aux décisions stratégiques'),
                  _buildBulletPoint(context, 'Échanges de personnel dirigeant'),
                  _buildBulletPoint(context,
                      'Fourniture d\'informations techniques essentielles'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.error.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.error.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Méthode de consolidation:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.error,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context, 'Mise en équivalence (MEE)'),
                  _buildBulletPoint(context,
                      'Remplacement de la valeur des titres par la quote-part des capitaux propres'),
                  _buildBulletPoint(
                      context, 'Comptabilisation de la quote-part de résultat'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExempleCalcul(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.calculate, color: colorScheme.error),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Exemple de Calcul',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Structure du groupe:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• Société M détient 80% de F1\n'
                    '• F1 détient 60% de F2\n'
                    '• F2 détient 40% de F3',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.error.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.error.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Calculs:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.error,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '1. Pourcentages d\'intérêt:\n'
                    '   • Dans F1: 80%\n'
                    '   • Dans F2: 80% × 60% = 48%\n'
                    '   • Dans F3: 48% × 40% = 19,2%\n\n'
                    '2. Pourcentages de contrôle:\n'
                    '   • Sur F1: 80% (contrôle exclusif)\n'
                    '   • Sur F2: 60% (contrôle exclusif indirect)\n'
                    '   • Sur F3: 40% (influence notable)\n\n'
                    '3. Méthodes de consolidation:\n'
                    '   • F1: Intégration globale\n'
                    '   • F2: Intégration globale\n'
                    '   • F3: Mise en équivalence',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(BuildContext context, String text) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              )),
          Expanded(
            child: Text(text,
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                )),
          ),
        ],
      ),
    );
  }
}
