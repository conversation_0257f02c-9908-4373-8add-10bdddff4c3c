import 'package:flutter/material.dart';
import '../../../../../../widgets/journal_comptable_widget.dart';

class ExercicesSection extends StatefulWidget {
  const ExercicesSection({super.key});

  @override
  State<ExercicesSection> createState() => _ExercicesSectionState();
}

class _ExercicesSectionState extends State<ExercicesSection> {
  final Map<String, bool> _showSolutions = {};

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Exercices de Consolidation',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 24),
          _buildExercice(
            'Exercice 1: Intégration Globale',
            '''La société HOLDING M a pris une participation de 80% dans la société F.
            
Données au 31/12/2024:
• Capital F: 1 000 000 DH
• Réserves F: 200 000 DH
• Résultat F: 150 000 DH
• Coût d'acquisition des titres: 900 000 DH

Travail à faire:
1. Calculer l'écart de première consolidation
2. Déterminer les intérêts minoritaires
3. Établir les écritures de consolidation''',
            [
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '1111',
                    label: 'Capital social F',
                    debit: '1000000',
                  ),
                  JournalLine(
                    account: '1140',
                    label: 'Réserves F',
                    debit: '200000',
                  ),
                  JournalLine(
                    account: '1181',
                    label: 'Résultat F',
                    debit: '150000',
                  ),
                  JournalLine(
                    account: '2510',
                    label: 'Titres de participation',
                    credit: '900000',
                  ),
                  JournalLine(
                    account: '1141',
                    label: 'Intérêts minoritaires (20%)',
                    credit: '270000',
                  ),
                  JournalLine(
                    account: '1130',
                    label: 'Écart de première consolidation',
                    credit: '180000',
                  ),
                ],
              ),
            ],
            '''Solution détaillée:

1. Calcul de l'écart de première consolidation:
   • Quote-part dans les capitaux propres = (1 000 000 + 200 000) × 80% = 960 000
   • Coût d'acquisition = 900 000
   • Écart de première consolidation = 960 000 - 900 000 = 60 000

2. Calcul des intérêts minoritaires:
   • Sur capital = 1 000 000 × 20% = 200 000
   • Sur réserves = 200 000 × 20% = 40 000
   • Sur résultat = 150 000 × 20% = 30 000
   • Total intérêts minoritaires = 270 000

3. Les écritures de consolidation sont présentées dans le journal ci-dessus''',
          ),
          const SizedBox(height: 24),
          _buildExercice(
            'Exercice 2: Intégration Proportionnelle',
            '''La société M et la société X détiennent chacune 50% de la société F.
            
Données au 31/12/2024:
• Capital F: 800 000 DH
• Réserves F: 100 000 DH
• Résultat F: 80 000 DH
• Coût d'acquisition des titres M: 450 000 DH

Travail à faire:
1. Calculer la quote-part de M dans les capitaux propres de F
2. Déterminer l'écart de première consolidation
3. Établir les écritures de consolidation''',
            [
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '1111',
                    label: 'Capital F (50%)',
                    debit: '400000',
                  ),
                  JournalLine(
                    account: '1140',
                    label: 'Réserves F (50%)',
                    debit: '50000',
                  ),
                  JournalLine(
                    account: '1181',
                    label: 'Résultat F (50%)',
                    debit: '40000',
                  ),
                  JournalLine(
                    account: '2510',
                    label: 'Titres de participation',
                    credit: '450000',
                  ),
                  JournalLine(
                    account: '1130',
                    label: 'Écart de première consolidation',
                    credit: '40000',
                  ),
                ],
              ),
            ],
            '''Solution détaillée:

1. Quote-part dans les capitaux propres:
   • Capital = 800 000 × 50% = 400 000
   • Réserves = 100 000 × 50% = 50 000
   • Résultat = 80 000 × 50% = 40 000

2. Écart de première consolidation:
   • Quote-part capitaux propres = (800 000 + 100 000) × 50% = 450 000
   • Coût d'acquisition = 450 000
   • Écart = 450 000 - 450 000 = 0

3. Les écritures de consolidation sont présentées dans le journal ci-dessus''',
          ),
          const SizedBox(height: 24),
          _buildExercice(
            'Exercice 3: Mise en Équivalence',
            '''La société M détient 30% de la société F.
            
Données au 31/12/2024:
• Capitaux propres F: 1 500 000 DH
• Résultat F: 120 000 DH
• Coût d'acquisition: 400 000 DH

Travail à faire:
1. Calculer la quote-part de M dans les capitaux propres de F
2. Déterminer la quote-part dans le résultat
3. Établir les écritures de consolidation''',
            [
              JournalEntry(
                date: '2024-12-31',
                lines: [
                  JournalLine(
                    account: '2510',
                    label: 'Titres mis en équivalence',
                    debit: '450000',
                  ),
                  JournalLine(
                    account: '2510',
                    label: 'Titres de participation',
                    credit: '400000',
                  ),
                  JournalLine(
                    account: '1140',
                    label: 'Réserves consolidées',
                    credit: '50000',
                  ),
                  JournalLine(
                    account: '7525',
                    label: 'Quote-part résultat MEE',
                    credit: '36000',
                  ),
                ],
              ),
            ],
            '''Solution détaillée:

1. Quote-part dans les capitaux propres:
   • Capitaux propres = 1 500 000 × 30% = 450 000
   • Coût d'acquisition = 400 000
   • Écart = 450 000 - 400 000 = 50 000 (réserves consolidées)

2. Quote-part dans le résultat:
   • Résultat = 120 000 × 30% = 36 000

3. Les écritures de consolidation sont présentées dans le journal ci-dessus''',
          ),
        ],
      ),
    );
  }

  Widget _buildExercice(String title, String enonce, List<JournalEntry> entries,
      String solution) {
    final String exerciceId = title.split(':')[0].trim();
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.school, color: colorScheme.primary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                enonce,
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: FilledButton.icon(
                onPressed: () {
                  setState(() {
                    _showSolutions[exerciceId] =
                        !(_showSolutions[exerciceId] ?? false);
                  });
                },
                icon: Icon(
                  _showSolutions[exerciceId] ?? false
                      ? Icons.visibility_off
                      : Icons.visibility,
                ),
                label: Text(
                  _showSolutions[exerciceId] ?? false
                      ? 'Masquer la solution'
                      : 'Afficher la solution',
                ),
              ),
            ),
            if (_showSolutions[exerciceId] ?? false) ...[
              const SizedBox(height: 24),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.tertiary.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: colorScheme.tertiary.withValues(alpha: 0.2)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Solution',
                      style: textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.tertiary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      solution,
                      style: textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Écritures comptables:',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    JournalComptableWidget(
                      title: 'Journal des Opérations',
                      entries: entries,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
