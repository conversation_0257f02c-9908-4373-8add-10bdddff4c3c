import 'package:flutter/material.dart';
import '../../../../../../widgets/journal_comptable_widget.dart';

class EcartsConsolidationSection extends StatelessWidget {
  const EcartsConsolidationSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Écarts de Consolidation',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 24),
          _buildEcartPremiereConsolidation(context),
          const SizedBox(height: 24),
          _buildEcartEvaluation(context),
          const SizedBox(height: 24),
          _buildEcartAcquisition(context),
          const SizedBox(height: 24),
          _buildExempleCalcul(context),
        ],
      ),
    );
  }

  Widget _buildEcartPremiereConsolidation(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.calculate, color: colorScheme.primary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Écart de Première Consolidation',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'L\'écart de première consolidation est la différence entre le coût d\'acquisition des titres et la quote-part des capitaux propres de la filiale à la date d\'acquisition.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.primary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Composantes:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context, 'Écart d\'évaluation'),
                  _buildBulletPoint(context, 'Écart d\'acquisition (Goodwill)'),
                  _buildBulletPoint(
                      context, 'Badwill (écart d\'acquisition négatif)'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEcartEvaluation(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.trending_up, color: colorScheme.secondary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Écart d\'Évaluation',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'L\'écart d\'évaluation correspond à la différence entre la valeur comptable et la juste valeur des éléments identifiables.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.secondary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.secondary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Caractéristiques:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context,
                      'Affectation aux actifs et passifs identifiables'),
                  _buildBulletPoint(
                      context, 'Amortissement selon la nature du bien'),
                  _buildBulletPoint(context, 'Calcul des impôts différés'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.tertiary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.tertiary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Exemple:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Un terrain comptabilisé pour 500 000 DH a une valeur réelle de 800 000 DH à la date d\'acquisition.',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 16),
                  JournalComptableWidget(
                    title: 'Constatation de l\'écart d\'évaluation',
                    entries: [
                      JournalEntry(
                        date: '31/12/2024',
                        lines: [
                          JournalLine(
                            account: '2311',
                            label: 'Terrains',
                            debit: '300000',
                          ),
                          JournalLine(
                            account: '1061',
                            label: 'Écart d\'évaluation',
                            credit: '300000',
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEcartAcquisition(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.tertiary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child:
                      Icon(Icons.monetization_on, color: colorScheme.tertiary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Écart d\'Acquisition',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'L\'écart d\'acquisition (goodwill) est la différence entre l\'écart de première consolidation et l\'écart d\'évaluation.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.tertiary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.tertiary.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Traitement comptable:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(
                      context, 'Inscription à l\'actif du bilan consolidé'),
                  _buildBulletPoint(
                      context, 'Amortissement sur une durée appropriée'),
                  _buildBulletPoint(context, 'Test de dépréciation annuel'),
                  _buildBulletPoint(
                      context, 'Suivi des variations de périmètre'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExempleCalcul(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child:
                      Icon(Icons.calculate_outlined, color: colorScheme.error),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Exemple de Calcul Complet',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Données:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• Coût d\'acquisition des titres: 1 200 000 DH\n'
                    '• Capitaux propres à la date d\'acquisition: 1 000 000 DH\n'
                    '• Pourcentage de participation: 80%\n'
                    '• Plus-value sur terrain: 300 000 DH\n'
                    '• Plus-value sur construction: 200 000 DH',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.error.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.error.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Calculs:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.error,
                    ),
                  ),
                  const SizedBox(height: 16),
                  JournalComptableWidget(
                    title: 'Détermination des écarts',
                    entries: [
                      JournalEntry(
                        date: '31/12/2024',
                        lines: [
                          JournalLine(
                            account: '2311',
                            label: 'Terrains',
                            debit: '300000',
                          ),
                          JournalLine(
                            account: '2313',
                            label: 'Constructions',
                            debit: '200000',
                          ),
                          JournalLine(
                            account: '207',
                            label: 'Écart d\'acquisition',
                            debit: '100000',
                          ),
                          JournalLine(
                            account: '1061',
                            label: 'Écarts d\'évaluation',
                            credit: '400000',
                          ),
                          JournalLine(
                            account: '1344',
                            label: 'Impôts différés passifs',
                            credit: '200000',
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '1. Écart de première consolidation:\n'
                    '   • Coût d\'acquisition: 1 200 000 DH\n'
                    '   • Quote-part (80%): 800 000 DH\n'
                    '   • Écart: 400 000 DH\n\n'
                    '2. Écart d\'évaluation:\n'
                    '   • Terrain: 300 000 DH\n'
                    '   • Construction: 200 000 DH\n'
                    '   • Total: 500 000 DH × 80% = 400 000 DH\n\n'
                    '3. Écart d\'acquisition:\n'
                    '   • Écart première consolidation: 400 000 DH\n'
                    '   • Écart d\'évaluation net: 300 000 DH\n'
                    '   • Goodwill: 100 000 DH',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(BuildContext context, String text) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              )),
          Expanded(
            child: Text(text,
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                )),
          ),
        ],
      ),
    );
  }
}
