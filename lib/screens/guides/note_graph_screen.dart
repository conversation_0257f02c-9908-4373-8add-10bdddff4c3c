import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/guide/personal_note_data.dart';
import '../../services/note_linking_service.dart';
import '../../services/notes_service.dart';
import '../../widgets/guide/personal_notes/note_graph_widget.dart';

/// Screen for visualizing note connections and relationships
class NoteGraphScreen extends ConsumerStatefulWidget {
  final String? initialNoteId;

  const NoteGraphScreen({
    super.key,
    this.initialNoteId,
  });

  @override
  ConsumerState<NoteGraphScreen> createState() => _NoteGraphScreenState();
}

class _NoteGraphScreenState extends ConsumerState<NoteGraphScreen>
    with TickerProviderStateMixin {
  final NoteLinkingService _linkingService = NoteLinkingService();
  final NotesService _notesService = NotesService();
  
  List<PersonalNoteData> _notes = [];
  List<NoteLink> _links = [];
  NoteLinkStatistics? _statistics;
  String? _selectedNoteId;
  bool _isLoading = true;
  
  // Filter options
  bool _showOrphanedNotes = true;
  bool _showPrivateNotes = true;
  NoteType? _filterByType;
  String? _filterByTag;
  
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _selectedNoteId = widget.initialNoteId;
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      await _linkingService.initialize();
      await _notesService.initialize();
      
      final notes = await _notesService.getAllNotes();
      final links = await _linkingService.getAllLinks();
      final statistics = await _linkingService.getLinkStatistics();
      
      setState(() {
        _notes = notes;
        _links = links;
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  List<PersonalNoteData> get _filteredNotes {
    var filtered = _notes.where((note) {
      // Filter by privacy
      if (!_showPrivateNotes && note.isPrivate) return false;
      
      // Filter by type
      if (_filterByType != null && note.noteType != _filterByType) return false;
      
      // Filter by tag
      if (_filterByTag != null && !note.tags.contains(_filterByTag)) return false;
      
      return true;
    }).toList();
    
    // Filter orphaned notes if needed
    if (!_showOrphanedNotes) {
      final connectedNoteIds = <String>{};
      for (final link in _links) {
        connectedNoteIds.add(link.sourceNoteId);
        connectedNoteIds.add(link.targetNoteId);
      }
      filtered = filtered.where((note) => connectedNoteIds.contains(note.id)).toList();
    }
    
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildFilterBar(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildGraphView(),
                      _buildStatisticsView(),
                      _buildNetworkView(),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('Graphique des Notes'),
      bottom: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.account_tree), text: 'Graphique'),
          Tab(icon: Icon(Icons.analytics), text: 'Statistiques'),
          Tab(icon: Icon(Icons.hub), text: 'Réseau'),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadData,
        ),
      ],
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Wrap(
        spacing: 8,
        children: [
          FilterChip(
            label: Text('Notes privées (${_notes.where((n) => n.isPrivate).length})'),
            selected: _showPrivateNotes,
            onSelected: (selected) {
              setState(() => _showPrivateNotes = selected);
            },
          ),
          FilterChip(
            label: Text('Notes isolées (${_getOrphanedCount()})'),
            selected: _showOrphanedNotes,
            onSelected: (selected) {
              setState(() => _showOrphanedNotes = selected);
            },
          ),
          if (_filterByType != null)
            FilterChip(
              label: Text('Type: ${_filterByType!.name}'),
              selected: true,
              onSelected: (_) => setState(() => _filterByType = null),
              deleteIcon: const Icon(Icons.close),
              onDeleted: () => setState(() => _filterByType = null),
            ),
          if (_filterByTag != null)
            FilterChip(
              label: Text('Tag: $_filterByTag'),
              selected: true,
              onSelected: (_) => setState(() => _filterByTag = null),
              deleteIcon: const Icon(Icons.close),
              onDeleted: () => setState(() => _filterByTag = null),
            ),
        ],
      ),
    );
  }

  Widget _buildGraphView() {
    final filteredNotes = _filteredNotes;
    
    if (filteredNotes.isEmpty) {
      return _buildEmptyState('Aucune note à afficher avec les filtres actuels');
    }
    
    return NoteGraphWidget(
      notes: filteredNotes,
      highlightedNoteId: _selectedNoteId,
      onNoteSelected: (noteId) {
        setState(() => _selectedNoteId = noteId);
        _showNoteDetails(noteId);
      },
    );
  }

  Widget _buildStatisticsView() {
    if (_statistics == null) {
      return const Center(child: Text('Aucune statistique disponible'));
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCard('Aperçu général', [
            _buildStatRow('Total des notes', '${_notes.length}'),
            _buildStatRow('Total des liaisons', '${_statistics!.totalLinks}'),
            _buildStatRow('Notes connectées', '${_statistics!.linksByNote.length}'),
            _buildStatRow('Notes isolées', '${_getOrphanedCount()}'),
            _buildStatRow('Connexions moyennes', _statistics!.averageConnectionsPerNote.toStringAsFixed(1)),
          ]),
          const SizedBox(height: 16),
          _buildStatisticsCard('Répartition par type', [
            ..._statistics!.linksByType.entries.map((entry) =>
                _buildStatRow(entry.key.name, '${entry.value}')),
          ]),
          const SizedBox(height: 16),
          _buildStatisticsCard('Notes les plus connectées', [
            ..._getTopConnectedNotes().take(5).map((entry) =>
                _buildStatRow(_getNoteTitle(entry.key), '${entry.value} connexions')),
          ]),
          const SizedBox(height: 16),
          _buildStatisticsCard('Partage', [
            _buildStatRow('Notes partagées', '${_statistics!.totalSharedNotes}'),
            _buildStatRow('Partages publics', '${_statistics!.publicShares}'),
            _buildStatRow('Partages privés', '${_statistics!.privateShares}'),
          ]),
        ],
      ),
    );
  }

  Widget _buildNetworkView() {
    if (_selectedNoteId == null) {
      return _buildEmptyState('Sélectionnez une note pour voir son réseau');
    }
    
    return FutureBuilder<List<String>>(
      future: _linkingService.getNotesWithinDegrees(_selectedNoteId!, 2),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return _buildEmptyState('Cette note n\'a pas de réseau de connexions');
        }
        
        final networkNoteIds = snapshot.data!;
        final networkNotes = _notes.where((note) => 
            networkNoteIds.contains(note.id) || note.id == _selectedNoteId).toList();
        
        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Réseau de la note sélectionnée (${networkNotes.length} notes connectées)',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
            Expanded(
              child: NoteGraphWidget(
                notes: networkNotes,
                highlightedNoteId: _selectedNoteId,
                onNoteSelected: (noteId) {
                  setState(() => _selectedNoteId = noteId);
                  _showNoteDetails(noteId);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_tree_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _showCreateLinkDialog,
      tooltip: 'Créer une liaison',
      child: const Icon(Icons.add_link),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtres'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<NoteType?>(
              value: _filterByType,
              decoration: const InputDecoration(labelText: 'Type de note'),
              items: [
                const DropdownMenuItem(value: null, child: Text('Tous les types')),
                ...NoteType.values.map((type) => DropdownMenuItem(
                  value: type,
                  child: Text(type.name),
                )),
              ],
              onChanged: (value) => setState(() => _filterByType = value),
            ),
            const SizedBox(height: 16),
            // Add tag filter dropdown here
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showCreateLinkDialog() {
    // Implementation for creating new links
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fonctionnalité de création de liaison à implémenter')),
    );
  }

  void _showNoteDetails(String noteId) {
    final note = _notes.firstWhere((n) => n.id == noteId);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.3,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                note.title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Text(note.content),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _getOrphanedCount() {
    final connectedNoteIds = <String>{};
    for (final link in _links) {
      connectedNoteIds.add(link.sourceNoteId);
      connectedNoteIds.add(link.targetNoteId);
    }
    return _notes.where((note) => !connectedNoteIds.contains(note.id)).length;
  }

  List<MapEntry<String, int>> _getTopConnectedNotes() {
    if (_statistics == null) return [];
    return _statistics!.linksByNote.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
  }

  String _getNoteTitle(String noteId) {
    try {
      return _notes.firstWhere((note) => note.id == noteId).title;
    } catch (e) {
      return noteId;
    }
  }
}
