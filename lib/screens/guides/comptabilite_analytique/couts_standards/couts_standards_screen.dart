import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:intl/intl.dart';

class CoutsStandardsScreen extends StatefulWidget {
  const CoutsStandardsScreen({super.key});

  @override
  State<CoutsStandardsScreen> createState() => _CoutsStandardsScreenState();
}

class _CoutsStandardsScreenState extends State<CoutsStandardsScreen>
    with SingleTickerProviderStateMixin {
  static final _logger = Logger('CoutsStandardsScreen');
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Controllers for calculator
  final _prixBaseController = TextEditingController();
  final _fraisTransportController = TextEditingController();
  final _fraisStockageController = TextEditingController();
  final _quantiteTheoriqueController = TextEditingController();
  final _tauxPerteController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _prixBaseController.dispose();
    _fraisTransportController.dispose();
    _fraisStockageController.dispose();
    _quantiteTheoriqueController.dispose();
    _tauxPerteController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      // final jsonString = await rootBundle
      //     .loadString('assets/compta_analytique/couts_standards.json');
      // setState(() {
      //   // _data = json.decode(jsonString); // Data is not currently used
      // });
      // TODO: Load and use the data from 'assets/compta_analytique/couts_standards.json' if needed later.
      // For now, just log that the data loading step is skipped.
      _logger.info('Data loading from JSON skipped as _data is not used.');
    } catch (e) {
      _logger.severe('Error during potential data loading: $e');
    }
  }

  Widget _buildHeader(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Méthode des Coûts Standards',
            style: textTheme.headlineMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Guide complet pour comprendre, implémenter et analyser les coûts standards',
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefinitionSection(ColorScheme colorScheme, TextTheme textTheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb_outline, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'Définition',
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Les coûts standards sont des coûts prédéterminés qui servent de référence pour mesurer et contrôler la performance réelle.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 24),
            _buildKeyPoints(colorScheme, textTheme),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyPoints(ColorScheme colorScheme, TextTheme textTheme) {
    final points = [
      {
        'title': 'Planification',
        'description':
            'Établis avant la production pour servir d\'objectifs',
        'icon': Icons.timeline,
      },
      {
        'title': 'Contrôle',
        'description':
            'Permettent de mesurer les écarts et prendre des actions correctives',
        'icon': Icons.track_changes,
      },
      {
        'title': 'Décision',
        'description':
            'Aident à la prise de décision et à la fixation des prix',
        'icon': Icons.psychology,
      },
    ];

    return Column(
      children: points
          .map<Widget>((point) => _buildKeyPointCard(
                point['title'] as String,
                point['description'] as String,
                point['icon'] as IconData,
                colorScheme,
                textTheme,
              ))
          .toList(),
    );
  }

  Widget _buildKeyPointCard(
    String title,
    String description,
    IconData icon,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: colorScheme.primary),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentsSection(ColorScheme colorScheme, TextTheme textTheme) {
    final components = [
      {
        'title': 'Matières Premières',
        'elements': [
          'Prix standard des matériaux',
          'Quantités standards requises',
          'Pertes et rebuts normaux',
        ],
        'example': {
          'item': 'Acier inoxydable',
          'price': 100,
          'quantity': 1.2,
          'waste': 0.05,
        },
        'icon': Icons.inventory_2,
      },
      {
        'title': 'Main d\'Œuvre Directe',
        'elements': [
          'Taux horaire standard',
          'Temps standard d\'exécution',
          'Temps de préparation',
        ],
        'example': {
          'rate': 75,
          'time': 2.5,
          'setup': 0.5,
        },
        'icon': Icons.engineering,
      },
      {
        'title': 'Frais Généraux',
        'elements': [
          'Taux d\'imputation',
          'Base d\'allocation',
          'Capacité normale',
        ],
        'example': {
          'rate': 150,
          'basis': 'Heures machines',
          'capacity': 160,
        },
        'icon': Icons.account_balance,
      },
    ];

    return Card(
      elevation: 0,
      color: colorScheme.surface,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.category, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'Composantes',
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            ...components.map((component) => _buildComponentCard(
                  component,
                  colorScheme,
                  textTheme,
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildComponentCard(
    Map<String, dynamic> component,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(component['icon'] as IconData, color: colorScheme.secondary),
              const SizedBox(width: 8),
              Text(
                component['title'],
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.secondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: colorScheme.outline.withValues(alpha: 0.1)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...List.generate(
                  (component['elements'] as List).length,
                  (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: colorScheme.secondary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          component['elements'][index],
                          style: textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(height: 24),
                _buildExample(component['example'], colorScheme, textTheme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExample(
    Map<String, dynamic> example,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Exemple',
          style: textTheme.titleSmall?.copyWith(
            color: colorScheme.secondary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...example.entries.map(
          (entry) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  entry.key.toUpperCase(),
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  entry.value.toString(),
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCalculatorSection(ColorScheme colorScheme, TextTheme textTheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.calculate, color: colorScheme.primary),
                  const SizedBox(width: 12),
                  Text(
                    'Calculateur',
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildCalculatorForm(colorScheme, textTheme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalculatorForm(ColorScheme colorScheme, TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Prix Standard',
          style: textTheme.titleMedium?.copyWith(
            color: colorScheme.secondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 16),
        _buildInputField(
          'Prix de Base (DH)',
          _prixBaseController,
          colorScheme,
        ),
        _buildInputField(
          'Frais de Transport (DH)',
          _fraisTransportController,
          colorScheme,
        ),
        _buildInputField(
          'Frais de Stockage (DH)',
          _fraisStockageController,
          colorScheme,
        ),
        const SizedBox(height: 24),
        Text(
          'Quantité Standard',
          style: textTheme.titleMedium?.copyWith(
            color: colorScheme.secondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 16),
        _buildInputField(
          'Quantité Théorique',
          _quantiteTheoriqueController,
          colorScheme,
        ),
        _buildInputField(
          'Taux de Perte Normal (%)',
          _tauxPerteController,
          colorScheme,
        ),
        const SizedBox(height: 32),
        Center(
          child: ElevatedButton.icon(
            onPressed: _calculateStandards,
            icon: const Icon(Icons.calculate),
            label: const Text('Calculer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(
                horizontal: 48,
                vertical: 16,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInputField(
    String label,
    TextEditingController controller,
    ColorScheme colorScheme,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: controller,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        decoration: InputDecoration(
          labelText: label,
          filled: true,
          fillColor: colorScheme.surface,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: colorScheme.outline),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: colorScheme.outline.withValues(alpha: 0.5)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: colorScheme.primary, width: 2),
          ),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Ce champ est requis';
          }
          if (double.tryParse(value) == null) {
            return 'Veuillez entrer un nombre valide';
          }
          return null;
        },
      ),
    );
  }

  void _calculateStandards() {
    if (_formKey.currentState?.validate() ?? false) {
      final prixBase = double.parse(_prixBaseController.text);
      final fraisTransport = double.parse(_fraisTransportController.text);
      final fraisStockage = double.parse(_fraisStockageController.text);
      final quantiteTheorique = double.parse(_quantiteTheoriqueController.text);
      final tauxPerte = double.parse(_tauxPerteController.text) / 100;

      final prixStandard = prixBase + fraisTransport + fraisStockage;
      final quantiteStandard = quantiteTheorique * (1 + tauxPerte);
      final coutTotal = prixStandard * quantiteStandard;

      _showResults(prixStandard, quantiteStandard, coutTotal);
    }
  }

  void _showResults(
    double prixStandard,
    double quantiteStandard,
    double coutTotal,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.check_circle, color: colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                'Résultats',
                style: textTheme.titleLarge?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildResultItem(
                'Prix Standard',
                '${NumberFormat("#,##0.00", "fr_FR").format(prixStandard)} DH',
                colorScheme,
                textTheme,
              ),
              const SizedBox(height: 12),
              _buildResultItem(
                'Quantité Standard',
                NumberFormat("#,##0.000", "fr_FR").format(quantiteStandard),
                colorScheme,
                textTheme,
              ),
              const Divider(height: 24),
              _buildResultItem(
                'Coût Total Standard',
                '${NumberFormat("#,##0.00", "fr_FR").format(coutTotal)} DH',
                colorScheme,
                textTheme,
                isTotal: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Fermer',
                style: TextStyle(color: colorScheme.primary),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildResultItem(
    String label,
    String value,
    ColorScheme colorScheme,
    TextTheme textTheme, {
    bool isTotal = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isTotal
            ? colorScheme.primaryContainer
            : colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: textTheme.bodyMedium?.copyWith(
              color: isTotal
                  ? colorScheme.onPrimaryContainer
                  : colorScheme.onSurfaceVariant,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: textTheme.bodyMedium?.copyWith(
              color: isTotal
                  ? colorScheme.onPrimaryContainer
                  : colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisSection(ColorScheme colorScheme, TextTheme textTheme) {
    final variances = [
      {
        'title': 'Écart sur Prix',
        'formula': '(Prix Réel - Prix Standard) × Quantité Réelle',
        'description':
            'Mesure l\'impact des variations de prix des matières premières et de la main d\'œuvre.',
        'icon': Icons.price_change,
      },
      {
        'title': 'Écart sur Quantité',
        'formula': '(Quantité Réelle - Quantité Standard) × Prix Standard',
        'description': 'Évalue l\'efficacité de l\'utilisation des ressources.',
        'icon': Icons.scale,
      },
      {
        'title': 'Écart sur Rendement',
        'formula': '(Rendement Réel - Rendement Standard) × Taux Standard',
        'description':
            'Analyse la productivité et l\'efficience des opérations.',
        'icon': Icons.trending_up,
      },
      {
        'title': 'Écart sur Budget',
        'formula': 'Coûts Réels - Coûts Budgétés',
        'description':
            'Compare les dépenses réelles aux prévisions budgétaires.',
        'icon': Icons.account_balance_wallet,
      },
    ];

    return Card(
      elevation: 0,
      color: colorScheme.surface,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'Analyse des Écarts',
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'L\'analyse des écarts permet d\'identifier les différences entre les coûts standards et les coûts réels.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 24),
            ...variances.map((variance) => _buildVarianceCard(
                  variance['title'] as String,
                  variance['formula'] as String,
                  variance['description'] as String,
                  variance['icon'] as IconData,
                  colorScheme,
                  textTheme,
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildVarianceCard(
    String title,
    String formula,
    String description,
    IconData icon,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: colorScheme.primary),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              formula,
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.secondary,
                fontWeight: FontWeight.w500,
                fontFamily: 'monospace',
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExamplesSection(ColorScheme colorScheme, TextTheme textTheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cases_outlined, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'Exemples Pratiques',
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildExample1(colorScheme, textTheme),
            const SizedBox(height: 32),
            _buildExample2(colorScheme, textTheme),
          ],
        ),
      ),
    );
  }

  Widget _buildExample1(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Exemple 1: Production de Meubles',
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Une entreprise de fabrication de meubles établit les standards suivants pour une table:',
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          _buildDataTable(
            [
              ['Élément', 'Standard', 'Réel', 'Écart'],
              ['Bois (m²)', '2.5 × 100 DH', '2.7 × 110 DH', '+47 DH'],
              ['Main d\'œuvre', '3h × 50 DH', '3.2h × 52 DH', '+31.4 DH'],
              ['Frais généraux', '200 DH', '220 DH', '+20 DH'],
            ],
            colorScheme,
            textTheme,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Analyse:',
                  style: textTheme.titleSmall?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '• Écart défavorable sur matières de 47 DH\n'
                  '• Écart défavorable sur main d\'œuvre de 31.4 DH\n'
                  '• Écart défavorable sur frais généraux de 20 DH\n'
                  '• Écart total défavorable: 98.4 DH',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExample2(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Exemple 2: Production Textile',
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Une usine textile établit les standards suivants pour 100 mètres de tissu:',
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          _buildDataTable(
            [
              ['Élément', 'Standard', 'Réel', 'Écart'],
              ['Coton (kg)', '50 × 40 DH', '48 × 42 DH', '+16 DH'],
              ['Main d\'œuvre', '8h × 45 DH', '7.5h × 46 DH', '-15.5 DH'],
              ['Énergie', '300 DH', '280 DH', '-20 DH'],
            ],
            colorScheme,
            textTheme,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Analyse:',
                  style: textTheme.titleSmall?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '• Écart défavorable sur matières de 16 DH\n'
                  '• Écart favorable sur main d\'œuvre de 15.5 DH\n'
                  '• Écart favorable sur énergie de 20 DH\n'
                  '• Écart total favorable: 19.5 DH',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataTable(
    List<List<String>> data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Table(
      border: TableBorder.all(
        color: colorScheme.outline.withValues(alpha: 0.2),
        width: 1,
        borderRadius: BorderRadius.circular(8),
      ),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      children: data.asMap().entries.map((entry) {
        final isHeader = entry.key == 0;
        return TableRow(
          decoration: BoxDecoration(
            color: isHeader
                ? colorScheme.primary.withValues(alpha: 0.1)
                : Colors.transparent,
          ),
          children: entry.value
              .map((cell) => TableCell(
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Text(
                        cell,
                        style: (isHeader
                                ? textTheme.titleSmall
                                : textTheme.bodyMedium)
                            ?.copyWith(
                          color: isHeader
                              ? colorScheme.primary
                              : colorScheme.onSurfaceVariant,
                          fontWeight:
                              isHeader ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ))
              .toList(),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: colorScheme.primary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: colorScheme.primary,
          unselectedLabelColor: colorScheme.onSurfaceVariant,
          indicatorColor: colorScheme.primary,
          tabs: const [
            Tab(text: 'Définition'),
            Tab(text: 'Composantes'),
            Tab(text: 'Calculateur'),
            Tab(text: 'Analyse'),
            Tab(text: 'Exemples'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          ListView(
            children: [
              _buildHeader(colorScheme, textTheme),
              _buildDefinitionSection(colorScheme, textTheme),
            ],
          ),
          ListView(
            children: [
              _buildHeader(colorScheme, textTheme),
              _buildComponentsSection(colorScheme, textTheme),
            ],
          ),
          ListView(
            children: [
              _buildHeader(colorScheme, textTheme),
              _buildCalculatorSection(colorScheme, textTheme),
            ],
          ),
          ListView(
            children: [
              _buildHeader(colorScheme, textTheme),
              _buildAnalysisSection(colorScheme, textTheme),
            ],
          ),
          ListView(
            children: [
              _buildHeader(colorScheme, textTheme),
              _buildExamplesSection(colorScheme, textTheme),
            ],
          ),
        ],
      ),
    );
  }
}
