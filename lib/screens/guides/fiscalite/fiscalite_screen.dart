import 'package:flutter/material.dart';
import 'tva/tva_screen.dart';
import 'is/is_screen.dart';
import 'ir/ir_screen.dart';
import 'droits_enregistrement/droits_enregistrement_screen.dart';
import 'liasse_fiscale/liasse_fiscale_screen.dart';

class FiscaliteScreen extends StatelessWidget {
  const FiscaliteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Guide de la Fiscalité'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Guide de la Fiscalité Marocaine',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Loi de Finances 2025',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.secondary,
                  ),
            ),
            const SizedBox(height: 24),

            // Grille de navigation
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.85,
              children: [
                _buildFiscalCard(
                  context,
                  'TVA',
                  Icons.receipt_long,
                  Theme.of(context).colorScheme.primary,
                  'Taxe sur la Valeur Ajoutée',
                  'Taux, exonérations et calculs',
                  const TvaScreen(),
                ),
                _buildFiscalCard(
                  context,
                  'IS',
                  Icons.business,
                  Theme.of(context).colorScheme.secondary,
                  'Impôt sur les Sociétés',
                  'Taux et obligations fiscales',
                  const IsScreen(),
                ),
                _buildFiscalCard(
                  context,
                  'IR',
                  Icons.person,
                  Theme.of(context).colorScheme.tertiary,
                  'Impôt sur le Revenu',
                  'Barèmes et déductions',
                  const IRScreen(),
                ),
                _buildFiscalCard(
                  context,
                  'Droits d\'Enregistrement',
                  Icons.gavel,
                  Colors.amber,
                  'Droits d\'Enregistrement',
                  'Taux et nouveautés 2025',
                  const DroitsEnregistrementScreen(),
                ),
                _buildFiscalCard(
                  context,
                  'Liasse Fiscale',
                  Icons.folder_open,
                  Colors.deepOrange,
                  'Liasse Fiscale',
                  'Documents et obligations fiscales annuelles',
                  const LiasseFiscaleScreen(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiscalCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    String subtitle,
    String description,
    Widget destination,
  ) {
    return Hero(
      tag: 'fiscal_${title.toLowerCase()}',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => destination),
          ),
          child: Card(
            elevation: 4,
            child: Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    size: 40,
                    color: color,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: color,
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
