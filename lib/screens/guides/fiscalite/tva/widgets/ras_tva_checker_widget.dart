import 'package:flutter/material.dart';

class RASTVACheckerWidget extends StatefulWidget {
  const RASTVACheckerWidget({super.key});

  @override
  _RASTVACheckerWidgetState createState() => _RASTVACheckerWidgetState();
}

class _RASTVACheckerWidgetState extends State<RASTVACheckerWidget> {
  bool _isPublicEntity = false;
  String? _selectedClientType;
  String? _selectedProviderType;
  String? _selectedServiceType;
  double _montantHT = 0;
  bool _hasAttestationRegularite = false;

  final List<String> _clientTypes = [
    'État',
    'Collectivités territoriales',
    'Établissements et entreprises publics',
    'Filiales d\'établissements publics',
    'Personne morale de droit privé',
    'Personne physique (RNR/RNS)',
    'Autre'
  ];

  final List<String> _providerTypes = [
    'Personne physique',
    'Personne morale',
    'Autre'
  ];

  final List<String> _serviceTypes = [
    'Abonnements',
    'Achat d\'espaces publicitaires',
    'Assistance et conseil technique',
    'Assistance technique (logiciels/web)',
    'Conseil juridique, comptable, fiscal et audit',
    'Centres d\'appels et télémarketing',
    'Collecte et traitement de déchets',
    'Entretien et maintenance',
    'Formation et conseil',
    'Gardiennage et surveillance',
    'Impression et reproduction',
    'Location de matériel',
    'Nettoyage et jardinage',
    'Prestations informatiques',
    'Services professionnels',
    'Autre'
  ];

  double get _rasRate {
    if (_isPublicEntity) return 0.75;
    if (!_hasAttestationRegularite) return 1.0;
    return 0.75;
  }

  bool get _isEligibleForRAS {
    if (_selectedClientType == null ||
        _selectedProviderType == null ||
        _selectedServiceType == null) {
      return false;
    }
    if (_selectedClientType == 'Autre' ||
        _selectedProviderType == 'Autre' ||
        _selectedServiceType == 'Autre') {
      return false;
    }

    if (_isPublicEntity) return true;

    if (!_isPublicEntity && _selectedProviderType != 'Personne physique') {
      return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.calculate,
                      color: colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Vérification RAS TVA 2024',
                          style: textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                        Text(
                          '(Applicable à partir du 1er juillet 2024)',
                          style: textTheme.bodyMedium?.copyWith(
                            fontStyle: FontStyle.italic,
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildDropdownField(
                'Type de client (qui paie)',
                _selectedClientType,
                _clientTypes,
                (value) {
                  setState(() {
                    _selectedClientType = value;
                    _isPublicEntity = value != null &&
                        (value.contains('État') ||
                            value.contains('territoriales') ||
                            value.contains('publics'));
                  });
                },
              ),
              const SizedBox(height: 16),
              _buildDropdownField(
                'Type de prestataire (qui facture)',
                _selectedProviderType,
                _providerTypes,
                (value) {
                  setState(() {
                    _selectedProviderType = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              _buildDropdownField(
                'Type de service',
                _selectedServiceType,
                _serviceTypes,
                (value) {
                  setState(() {
                    _selectedServiceType = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                decoration: InputDecoration(
                  labelText: 'Montant HT (MAD)',
                  filled: true,
                  fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  prefixIcon:
                      Icon(Icons.attach_money, color: colorScheme.primary),
                  suffixText: ' MAD',
                  suffixStyle: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                  labelStyle: textTheme.bodyLarge?.copyWith(
                    color: colorScheme.onSurface,
                  ),
                  floatingLabelStyle: textTheme.bodyLarge?.copyWith(
                    color: colorScheme.primary,
                  ),
                ),
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  setState(() {
                    _montantHT = double.tryParse(value) ?? 0;
                  });
                },
              ),
              if (!_isPublicEntity) ...[
                const SizedBox(height: 16),
                Card(
                  elevation: 0,
                  color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SwitchListTile(
                    title: Text(
                      'Attestation de régularité fiscale (< 1 an) ?',
                      style: textTheme.titleSmall?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                    subtitle: Text(
                      'Obligatoire pour les prestataires personnes physiques',
                      style: textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    value: _hasAttestationRegularite,
                    onChanged: (bool value) {
                      setState(() {
                        _hasAttestationRegularite = value;
                      });
                    },
                    activeColor: colorScheme.primary,
                  ),
                ),
              ],
              const SizedBox(height: 24),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: _isEligibleForRAS
                      ? colorScheme.primaryContainer
                      : colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _isEligibleForRAS
                        ? colorScheme.primary.withValues(alpha: 0.2)
                        : colorScheme.error.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      _isEligibleForRAS
                          ? 'Cette opération est soumise à la RAS TVA'
                          : 'Cette opération n\'est pas soumise à la RAS TVA',
                      style: textTheme.titleMedium?.copyWith(
                        color: _isEligibleForRAS
                            ? colorScheme.onPrimaryContainer
                            : colorScheme.onErrorContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_isEligibleForRAS) ...[
                      const SizedBox(height: 12),
                      Text(
                        'Taux de retenue: ${(_rasRate * 100).toStringAsFixed(0)}% de la TVA',
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ] else ...[
                      const SizedBox(height: 12),
                      Text(
                        _getReason(),
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onErrorContainer,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField(
    String label,
    String? value,
    List<String> items,
    void Function(String?) onChanged,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return DropdownButtonFormField<String>(
      value: value,
      isExpanded: true,
      decoration: InputDecoration(
        labelText: label,
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        labelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        floatingLabelStyle: textTheme.bodyLarge?.copyWith(
          color: colorScheme.primary,
        ),
      ),
      style: textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurface,
      ),
      icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
      dropdownColor: colorScheme.surface,
      items: items
          .map((item) => DropdownMenuItem(
                value: item,
                child: Text(
                  item,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface,
                  ),
                ),
              ))
          .toList(),
      onChanged: onChanged,
    );
  }

  String _getReason() {
    if (_selectedClientType == null) {
      return 'Raison: Veuillez sélectionner le type de client';
    }
    if (_selectedProviderType == null) {
      return 'Raison: Veuillez sélectionner le type de prestataire';
    }
    if (_selectedServiceType == null) {
      return 'Raison: Veuillez sélectionner le type de service';
    }
    if (_selectedClientType == 'Autre') {
      return 'Raison: Type de client non éligible';
    }
    if (_selectedProviderType == 'Autre') {
      return 'Raison: Type de prestataire non éligible';
    }
    if (_selectedServiceType == 'Autre') {
      return 'Raison: Type de service non éligible';
    }
    if (!_isPublicEntity && _selectedProviderType != 'Personne physique') {
      return 'Raison: Les entités privées ne peuvent appliquer la RAS qu\'aux prestataires personnes physiques';
    }
    return '';
  }
}
