import 'package:flutter/material.dart';
import '../../../../../utils/calculation_utils.dart';
import '../../../../../utils/input_validators.dart';
import '../widgets/ras_tva_checker_widget.dart';
import '../../../../../widgets/tva/tva_item_form_card.dart';
import '../../../../../models/tva/tva_line_item.dart';

class CalculateurSection extends StatefulWidget {
  const CalculateurSection({super.key});

  @override
  State<CalculateurSection> createState() => _CalculateurSectionState();
}

class _CalculateurSectionState extends State<CalculateurSection> {
  final _formKey = GlobalKey<FormState>();
  final _montantController = TextEditingController();
  double _selectedTaux = 20;
  bool _isHT = true;
  double? _montantHT;
  double? _montantTVA;
  double? _montantTTC;

  // Advanced mode state
  bool _advancedMode = false;
  TvaLineItem? _advancedItem;
  double? _advHT;
  double? _advTVA;
  double? _advTTC;

  final List<double> _tauxTVA = [0, 10, 20];

  void _calculer() {
    if (_formKey.currentState!.validate()) {
      final montant =
          double.parse(_montantController.text.replaceAll(',', '.'));
      setState(() {
        if (_isHT) {
          _montantHT = CalculationUtils.roundMonetary(montant);
          _montantTVA =
              CalculationUtils.calculateVATFromHT(montant, _selectedTaux);
          _montantTTC =
              CalculationUtils.calculateTTCFromHT(montant, _selectedTaux);
        } else {
          _montantTTC = CalculationUtils.roundMonetary(montant);
          _montantHT =
              CalculationUtils.calculateHTFromTTC(montant, _selectedTaux);
          _montantTVA =
              CalculationUtils.calculateVATFromTTC(montant, _selectedTaux);
        }
      });
    }
  }

  void _reinitialiser() {
    setState(() {
      _montantController.clear();
      _selectedTaux = 20;
      _isHT = true;
      _montantHT = null;
      _montantTVA = null;
      _montantTTC = null;
      _advancedItem = null;
      _advHT = null;
      _advTVA = null;
      _advTTC = null;
    });
  }

  @override
  void dispose() {
    _montantController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Calculateur TVA',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Calculez rapidement la TVA et les montants HT/TTC',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Mode selection
          Text(
            'Mode de calcul',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          SegmentedButton<bool>(
            segments: const [
              ButtonSegment<bool>(
                value: false,
                label: Text('Simple'),
              ),
              ButtonSegment<bool>(
                value: true,
                label: Text('Avancé'),
              ),
            ],
            selected: {_advancedMode},
            onSelectionChanged: (Set<bool> newSelection) {
              setState(() {
                _advancedMode = newSelection.first;
                // Reset all results when switching mode
                _montantHT = null;
                _montantTVA = null;
                _montantTTC = null;
                _advancedItem = null;
                _advHT = null;
                _advTVA = null;
                _advTTC = null;
                _montantController.clear();
                _selectedTaux = 20;
                _isHT = true;
              });
            },
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.resolveWith<Color>(
                (states) {
                  if (states.contains(WidgetState.selected)) {
                    return colorScheme.primaryContainer;
                  }
                  return colorScheme.surface;
                },
              ),
              foregroundColor: WidgetStateProperty.resolveWith<Color>(
                (states) {
                  if (states.contains(WidgetState.selected)) {
                    return colorScheme.onPrimaryContainer;
                  }
                  return colorScheme.onSurface;
                },
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Simple Mode
          if (!_advancedMode) ...[
            Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    elevation: 2,
                    surfaceTintColor: colorScheme.surfaceTint,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Type de montant',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 8),
                          SegmentedButton<bool>(
                            segments: const [
                              ButtonSegment<bool>(
                                value: true,
                                label: Text('Montant HT'),
                              ),
                              ButtonSegment<bool>(
                                value: false,
                                label: Text('Montant TTC'),
                              ),
                            ],
                            selected: {_isHT},
                            onSelectionChanged: (Set<bool> newSelection) {
                              setState(() {
                                _isHT = newSelection.first;
                              });
                            },
                            style: ButtonStyle(
                              backgroundColor:
                                  WidgetStateProperty.resolveWith<Color>(
                                (states) {
                                  if (states.contains(WidgetState.selected)) {
                                    return colorScheme.primaryContainer;
                                  }
                                  return colorScheme.surface;
                                },
                              ),
                              foregroundColor:
                                  WidgetStateProperty.resolveWith<Color>(
                                (states) {
                                  if (states.contains(WidgetState.selected)) {
                                    return colorScheme.onPrimaryContainer;
                                  }
                                  return colorScheme.onSurface;
                                },
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Montant',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _montantController,
                            decoration: InputDecoration(
                              hintText: 'Saisissez le montant',
                              suffixText: 'DH',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: colorScheme.primary),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: colorScheme.outline),
                              ),
                              labelStyle:
                                  TextStyle(color: colorScheme.primary),
                            ),
                            keyboardType:
                                const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: InputValidators
                                .getMonetaryInputFormatters(),
                            validator:
                                InputValidators.validateMonetaryAmount,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Taux de TVA',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<double>(
                            value: _selectedTaux,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: colorScheme.primary),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: colorScheme.outline),
                              ),
                            ),
                            items: _tauxTVA.map((taux) {
                              return DropdownMenuItem<double>(
                                value: taux,
                                child: Text('$taux%'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedTaux = value!;
                              });
                            },
                          ),
                          const SizedBox(height: 24),
                          Row(
                            children: [
                              Expanded(
                                child: FilledButton.icon(
                                  onPressed: _calculer,
                                  icon: const Icon(Icons.calculate),
                                  label: const Text('Calculer'),
                                ),
                              ),
                              const SizedBox(width: 8),
                              IconButton.filledTonal(
                                onPressed: _reinitialiser,
                                icon: const Icon(Icons.refresh),
                                tooltip: 'Réinitialiser',
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (_montantHT != null) ...[
                    const SizedBox(height: 24),
                    Card(
                      elevation: 2,
                      surfaceTintColor: colorScheme.surfaceTint,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Résultat',
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 16),
                            _buildResultRow(
                              context,
                              'Montant HT:',
                              _montantHT!,
                              colorScheme.primary,
                            ),
                            Divider(color: colorScheme.outlineVariant),
                            _buildResultRow(
                              context,
                              'Montant TVA:',
                              _montantTVA!,
                              colorScheme.secondary,
                            ),
                            Divider(color: colorScheme.outlineVariant),
                            _buildResultRow(
                              context,
                              'Montant TTC:',
                              _montantTTC!,
                              colorScheme.tertiary,
                            ),
                            const SizedBox(height: 16),
                            ExpansionTile(
                              title: const Text('Détail du calcul'),
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (_isHT) ...[
                                        Text(
                                          'Calcul à partir du montant HT:',
                                          style: textTheme.titleSmall,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '• Montant HT: ${CalculationUtils.formatMonetary(_montantHT!)}',
                                        ),
                                        Text(
                                          '• TVA ($_selectedTaux%): ${CalculationUtils.formatMonetary(_montantHT!)} × $_selectedTaux% = ${CalculationUtils.formatMonetary(_montantTVA!)}',
                                        ),
                                        Text(
                                          '• Montant TTC: ${CalculationUtils.formatMonetary(_montantHT!)} + ${CalculationUtils.formatMonetary(_montantTVA!)} = ${CalculationUtils.formatMonetary(_montantTTC!)}',
                                        ),
                                      ] else ...[
                                        Text(
                                          'Calcul à partir du montant TTC:',
                                          style: textTheme.titleSmall,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '• Montant TTC: ${CalculationUtils.formatMonetary(_montantTTC!)}',
                                        ),
                                        Text(
                                          '• Montant HT: ${CalculationUtils.formatMonetary(_montantTTC!)} ÷ (1 + $_selectedTaux%) = ${CalculationUtils.formatMonetary(_montantHT!)}',
                                        ),
                                        Text(
                                          '• TVA ($_selectedTaux%): ${CalculationUtils.formatMonetary(_montantTTC!)} - ${CalculationUtils.formatMonetary(_montantHT!)} = ${CalculationUtils.formatMonetary(_montantTVA!)}',
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const RASTVACheckerWidget(),
                  ],
                ],
              ),
            ),
          ],

          // Advanced Mode
          if (_advancedMode) ...[
            TvaItemFormCard(
              initialItem: null,
              onChanged: (item) {
                setState(() {
                  _advancedItem = item;
                  _advHT = item.totalHT;
                  _advTVA = item.totalTVA;
                  _advTTC = item.totalTTC;
                });
              },
              showDeleteButton: false,
              autoFocus: false,
            ),
            if (_advHT != null) ...[
              const SizedBox(height: 24),
              Card(
                elevation: 2,
                surfaceTintColor: colorScheme.surfaceTint,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Résultat',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildResultRow(
                        context,
                        'Total HT:',
                        _advHT!,
                        colorScheme.primary,
                      ),
                      Divider(color: colorScheme.outlineVariant),
                      _buildResultRow(
                        context,
                        'Total TVA:',
                        _advTVA!,
                        colorScheme.secondary,
                      ),
                      Divider(color: colorScheme.outlineVariant),
                      _buildResultRow(
                        context,
                        'Total TTC:',
                        _advTTC!,
                        colorScheme.tertiary,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              const RASTVACheckerWidget(),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildResultRow(
    BuildContext context,
    String label,
    double montant,
    Color color,
  ) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          Text(
            '${montant.toStringAsFixed(2)} DH',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}