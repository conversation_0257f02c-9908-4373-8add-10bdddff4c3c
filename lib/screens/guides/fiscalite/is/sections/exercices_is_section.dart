import 'package:flutter/material.dart';
import '../../../../../models/is/is_data.dart';
import '../../../../../services/is_service.dart';
import '../../../../../widgets/loading_indicator.dart';
import '../../../../../widgets/error_display.dart';
import '../../../../../widgets/journal_comptable_widget.dart';

class ExercicesIsSection extends StatelessWidget {
  const ExercicesIsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<IsData>(
      future: IsService().getExercices(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }

        if (snapshot.hasError) {
          return ErrorDisplay(
            error: snapshot.error.toString(),
            onRetry: () {
              IsService().clearCache();
              (context as Element).markNeedsBuild();
            },
          );
        }

        final data = snapshot.data!;
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary,
                    colorScheme.primaryContainer,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    data.title,
                    style: textTheme.headlineMedium?.copyWith(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Exercices pratiques avec écritures comptables',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            if (data.sections != null) ...[
              ...data.sections!.map((section) => ExerciceCard(
                    section: section,
                    colorScheme: colorScheme,
                  )),
            ],
          ],
        );
      },
    );
  }
}

class ExerciceCard extends StatefulWidget {
  final IsSection section;
  final ColorScheme colorScheme;

  const ExerciceCard({
    super.key,
    required this.section,
    required this.colorScheme,
  });

  @override
  State<ExerciceCard> createState() => _ExerciceCardState();
}

class _ExerciceCardState extends State<ExerciceCard> {
  final isSolutionVisible = ValueNotifier<bool>(false);
  late final List<String> _lines;
  late final String _content;
  late final String _solution;
  late final List<JournalEntry> _entries;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  void _parseContent() {
    _lines = widget.section.formattedContent.split('\n');
    final contentParts = <String>[];
    final solutionParts = <String>[];
    final entries = <JournalEntry>[];

    bool isSolution = false;
    bool isJournalEntry = false;
    List<JournalLine> currentLines = [];
    String? currentDate;

    for (var line in _lines) {
      if (line.trim().isEmpty) continue;

      if (line.startsWith('Solution:')) {
        isSolution = true;
        continue;
      }

      if (line.startsWith('Écriture:')) {
        isJournalEntry = true;
        currentLines = [];
        currentDate = null;
        contentParts.add(line); // Add the title line
        continue;
      }

      if (line.startsWith('|')) {
        final parts = line.split('|');
        if (parts.length >= 4) {
          // Skip empty first element from split
          parts.removeAt(0);
          // Remove last empty element if exists
          if (parts.last.trim().isEmpty) {
            parts.removeLast();
          }

          if (currentLines.isEmpty) {
            currentDate = parts[0].trim();
          }

          currentLines.add(JournalLine(
            account: parts[1].trim(),
            label: parts[2].trim(),
            debit: parts.length > 3 && parts[3].trim().isNotEmpty
                ? parts[3].trim()
                : null,
            credit: parts.length > 4 && parts[4].trim().isNotEmpty
                ? parts[4].trim()
                : null,
          ));
        }
      } else {
        if (currentLines.isNotEmpty) {
          entries.add(JournalEntry(
            date: currentDate,
            lines: List.from(currentLines),
          ));
          currentLines = [];
          currentDate = null;
          isJournalEntry = false;
        }

        if (isSolution) {
          solutionParts.add(line);
        } else {
          contentParts.add(line);
        }
      }
    }

    // Add any remaining journal entries
    if (currentLines.isNotEmpty) {
      entries.add(JournalEntry(
        date: currentDate,
        lines: List.from(currentLines),
      ));
    }

    _content = contentParts.join('\n');
    _solution = solutionParts.join('\n');
    _entries = entries;
  }

  Widget _buildContentSection(String text) {
    final lines = text.split('\n');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: lines.map((line) {
        if (line.trim().isEmpty) return const SizedBox(height: 8);

        Widget contentWidget;
        if (line.startsWith('•') || line.startsWith('-')) {
          contentWidget = Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: widget.colorScheme.primary,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  line.substring(1).trim(),
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        height: 1.5,
                        color: widget.colorScheme.onSurface,
                      ),
                ),
              ),
            ],
          );
        } else if (RegExp(r'^\d+\.').hasMatch(line)) {
          contentWidget = Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              color: widget.colorScheme.primaryContainer.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${line.split('.').first}.',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: widget.colorScheme.onSurface,
                      ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    line.substring(line.indexOf('.') + 1).trim(),
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          height: 1.5,
                          color: widget.colorScheme.onSurface,
                        ),
                  ),
                ),
              ],
            ),
          );
        } else if (line.startsWith('Note:') || line.startsWith('Attention:')) {
          contentWidget = Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: line.startsWith('Attention:')
                  ? widget.colorScheme.errorContainer.withValues(alpha: 0.2)
                  : widget.colorScheme.tertiaryContainer.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: line.startsWith('Attention:')
                    ? widget.colorScheme.error.withValues(alpha: 0.2)
                    : widget.colorScheme.tertiary.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  line.startsWith('Attention:')
                      ? Icons.warning_amber_rounded
                      : Icons.info_outline,
                  color: line.startsWith('Attention:')
                      ? widget.colorScheme.error
                      : widget.colorScheme.tertiary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    line.split(':')[1].trim(),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontStyle: FontStyle.italic,
                          height: 1.5,
                          color: line.startsWith('Attention:')
                              ? widget.colorScheme.error
                              : widget.colorScheme.onSurface,
                        ),
                  ),
                ),
              ],
            ),
          );
        } else {
          contentWidget = Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: Text(
              line,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    height: 1.5,
                    color: widget.colorScheme.onSurface,
                  ),
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: contentWidget,
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      surfaceTintColor: widget.colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getExerciceIcon(widget.section.title),
                  color: widget.colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.section.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: widget.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ],
            ),
            const Divider(height: 24),

            // Exercise Content
            _buildContentSection(_content),
            const SizedBox(height: 24),

            // Solution Section
            ValueListenableBuilder(
              valueListenable: isSolutionVisible,
              builder: (context, visible, _) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: () => isSolutionVisible.value = !visible,
                        icon: Icon(
                          visible ? Icons.visibility_off : Icons.visibility,
                          color: widget.colorScheme.onPrimary,
                        ),
                        label: Text(
                          visible
                              ? 'Masquer la solution'
                              : 'Afficher la solution',
                          style: TextStyle(color: widget.colorScheme.onPrimary),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: widget.colorScheme.primary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    if (visible) ...[
                      const SizedBox(height: 24),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: widget.colorScheme.primaryContainer
                              .withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: widget.colorScheme.primary.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.lightbulb_outline,
                                  color: widget.colorScheme.primary,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Solution détaillée',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                        color: widget.colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            _buildContentSection(_solution),
                            const SizedBox(height: 16),
                            ..._entries.map((entry) => Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: JournalComptableWidget(
                                    title: 'Écritures comptables',
                                    entries: [entry],
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  IconData _getExerciceIcon(String title) {
    if (title.contains('Exercice 1')) {
      return Icons.looks_one;
    } else if (title.contains('Exercice 2')) {
      return Icons.looks_two;
    } else if (title.contains('Exercice 3')) {
      return Icons.looks_3;
    }
    return Icons.school;
  }
}
