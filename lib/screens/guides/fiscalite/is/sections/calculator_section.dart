import 'package:flutter/material.dart';
import '../../../../../models/is/is_input_data.dart';
import '../../../../../models/is/is_calculation_result.dart';
import '../../../../../widgets/is/is_wizard_progress.dart';
import '../../../../../widgets/is/sector_selection_card.dart';
import '../../../../../widgets/is/input_collection_card.dart';
import '../../../../../widgets/is/results_summary_card.dart';
import '../../../../../widgets/is/regime_comparison_table.dart';
import '../../../../../services/is_calculator_service.dart';

class IsCalculatorSection extends StatefulWidget {
  const IsCalculatorSection({super.key});

  @override
  State<IsCalculatorSection> createState() => _IsCalculatorSectionState();
}

class _IsCalculatorSectionState extends State<IsCalculatorSection>
    with TickerProviderStateMixin {
  // Wizard state management
  final PageController _pageController = PageController();
  int _currentStep = 0;
  static const int _totalSteps = 3;
  final List<String> _stepLabels = [
    'Régime Fiscal',
    'Saisie des Données',
    'Résultats & Analyse',
  ];
  final List<bool> _stepCompleted = [false, false, false];

  // Data model
  IsInputData _inputData = const IsInputData();
  IsCalculationResult _calculationResult = IsCalculationResult.empty();
  
  // Wizard state
  bool _isCalculating = false;
  bool _showComparison = false;
  List<String> _availableRegimes = [];
  List<String> _selectedRegimesForComparison = [];

  // Animation controllers
  late AnimationController _pageTransitionController;
  late Animation<double> _pageTransitionAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _pageTransitionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _pageTransitionAnimation = CurvedAnimation(
      parent: _pageTransitionController,
      curve: Curves.easeInOut,
    );
    
    // Load initial data
    _loadAvailableRegimes();
    
    // Start animation
    _pageTransitionController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _pageTransitionController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailableRegimes() async {
    try {
      final regimes = await IsCalculatorService.getAvailableRegimes();
      setState(() {
        _availableRegimes = regimes;
      });
    } catch (e) {
      debugPrint('Error loading available regimes: $e');
    }
  }

  // Navigation methods
  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      if (_canProceedToNextStep()) {
        setState(() {
          _stepCompleted[_currentStep] = true;
          _currentStep++;
        });
        
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        
        // Trigger calculation when moving to results step
        if (_currentStep == 2) {
          _performCalculation();
        }
      }
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _canProceedToNextStep() {
    switch (_currentStep) {
      case 0: // Sector selection step
        return _inputData.selectedRegime.isNotEmpty;
      case 1: // Input collection step
        return _inputData.isValid;
      case 2: // Results step
        return true;
      default:
        return false;
    }
  }

  // Data handling methods
  void _onInputDataChanged(IsInputData newData) {
    setState(() {
      _inputData = newData;
    });
  }

  void _onRegimeChanged(String regime) {
    setState(() {
      _inputData = _inputData.copyWith(selectedRegime: regime);
    });
  }

  Future<void> _performCalculation() async {
    if (!_inputData.isValid) return;

    setState(() {
      _isCalculating = true;
    });

    try {
      final result = await IsCalculatorService.calculateComplete(_inputData);
      setState(() {
        _calculationResult = result;
        _isCalculating = false;
      });
    } catch (e) {
      setState(() {
        _isCalculating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du calcul: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _toggleComparison() {
    setState(() {
      _showComparison = !_showComparison;
      if (_showComparison && _selectedRegimesForComparison.isEmpty) {
        _selectedRegimesForComparison = [_inputData.selectedRegime];
      }
    });
  }

  void _onComparisonRegimesChanged(List<String> regimes) {
    setState(() {
      _selectedRegimesForComparison = regimes;
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return FadeTransition(
      opacity: _pageTransitionAnimation,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Header Section
            _buildHeader(context),
            const SizedBox(height: 24),
            
            // Progress Indicator
            IsWizardProgress(
              currentStep: _currentStep,
              totalSteps: _totalSteps,
              stepLabels: _stepLabels,
              stepCompleted: _stepCompleted,
            ),
            const SizedBox(height: 24),
            
            // Wizard Content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // Step 1: Sector Selection
                  _buildStep1SectorSelection(),
                  
                  // Step 2: Input Collection
                  _buildStep2InputCollection(),
                  
                  // Step 3: Results and Analysis
                  _buildStep3Results(),
                ],
              ),
            ),
            
            // Navigation Controls
            _buildNavigationControls(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary,
            colorScheme.primary.withValues(alpha: 0.8),
            colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.onPrimary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.calculate,
              color: colorScheme.onPrimary,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Calculateur IS Avancé',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Assistant intelligent pour le calcul de votre Impôt sur les Sociétés',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStep1SectorSelection() {
    return SingleChildScrollView(
      child: SectorSelectionCard(
        selectedRegime: _inputData.selectedRegime.isNotEmpty 
            ? _inputData.selectedRegime 
            : null,
        onRegimeChanged: _onRegimeChanged,
        showRecommendations: true,
        businessProfile: {
          'revenue': _inputData.revenue,
          'revenueType': _inputData.revenueType,
          'accountingResult': _inputData.accountingResult,
        },
      ),
    );
  }

  Widget _buildStep2InputCollection() {
    return SingleChildScrollView(
      child: InputCollectionCard(
        inputData: _inputData,
        onDataChanged: _onInputDataChanged,
        showPreview: true,
        selectedRegime: _inputData.selectedRegime,
      ),
    );
  }

  Widget _buildStep3Results() {
    return SingleChildScrollView(
      child: Column(
        children: [
          if (_isCalculating)
            _buildCalculatingIndicator()
          else if (_calculationResult.regimeName.isNotEmpty) ...[
            ResultsSummaryCard(
              result: _calculationResult,
              inputData: _inputData,
              showBreakdown: true,
              showAnalysis: true,
            ),
            const SizedBox(height: 16),
            _buildComparisonToggle(),
            if (_showComparison) ...[
              const SizedBox(height: 16),
              RegimeComparisonTable(
                inputData: _inputData,
                availableRegimes: _availableRegimes,
                selectedRegimes: _selectedRegimesForComparison,
                onRegimeSelectionChanged: _onComparisonRegimesChanged,
                showOptimalHighlight: true,
              ),
            ],
          ] else
            _buildEmptyResults(),
        ],
      ),
    );
  }

  Widget _buildCalculatingIndicator() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),
            const SizedBox(height: 16),
            Text(
              'Calcul en cours...',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Analyse des données et application du régime fiscal',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyResults() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.calculate_outlined,
              size: 64,
              color: colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'Prêt pour le calcul',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Les résultats s\'afficheront automatiquement',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonToggle() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.compare_arrows,
              color: colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Comparaison des régimes',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  Text(
                    'Comparez ce régime avec d\'autres options',
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: _showComparison,
              onChanged: (_) => _toggleComparison(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationControls(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _previousStep,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Précédent'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: colorScheme.primary,
                  side: BorderSide(color: colorScheme.outline),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            flex: _currentStep == 0 ? 1 : 1,
            child: FilledButton.icon(
              onPressed: _canProceedToNextStep() ? _nextStep : null,
              icon: Icon(_currentStep == _totalSteps - 1 
                  ? Icons.check 
                  : Icons.arrow_forward),
              label: Text(_currentStep == _totalSteps - 1 
                  ? 'Terminer' 
                  : 'Suivant'),
              style: FilledButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

}
