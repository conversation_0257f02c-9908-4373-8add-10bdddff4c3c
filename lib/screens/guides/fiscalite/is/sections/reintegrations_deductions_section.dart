import 'package:flutter/material.dart';
import '../../../../../models/is/is_data.dart';
import '../../../../../services/is_service.dart';
import '../../../../../widgets/loading_indicator.dart';
import '../../../../../widgets/error_display.dart';

class ReintegrationsDeductionsSection extends StatelessWidget {
  const ReintegrationsDeductionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<IsData>(
      future: IsService().getReintegrations2025(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }

        if (snapshot.hasError) {
          return ErrorDisplay(
            error: snapshot.error.toString(),
            onRetry: () {
              IsService().clearCache();
              (context as Element).markNeedsBuild();
            },
          );
        }

        final data = snapshot.data!;
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primaryContainer,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.title,
                      style: textTheme.headlineMedium?.copyWith(
                        color: colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Réintégrations et déductions fiscales',
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.onPrimary.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Sections
              if (data.sections != null) ...[
                ...data.sections!.map((section) => Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      elevation: 2,
                      surfaceTintColor: colorScheme.surfaceTint,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section Header
                            Row(
                              children: [
                                Icon(
                                  _getSectionIcon(section.title),
                                  color: colorScheme.primary,
                                  size: 28,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    section.title,
                                    style: textTheme.titleLarge?.copyWith(
                                      color: colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const Divider(height: 24),

                            // Categories
                            if (section.categories != null)
                              ...section.categories!.map((category) => Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        margin:
                                            const EdgeInsets.only(bottom: 12),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 6,
                                        ),
                                        decoration: BoxDecoration(
                                          color: colorScheme.primaryContainer
                                              .withValues(alpha: 0.2),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Text(
                                          category.name,
                                          style:
                                              textTheme.titleMedium?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: colorScheme.primary,
                                          ),
                                        ),
                                      ),
                                      ...category.items.map((item) => Container(
                                            margin: const EdgeInsets.only(
                                              left: 16,
                                              bottom: 16,
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                      margin:
                                                          const EdgeInsets.only(
                                                              top: 8),
                                                      width: 6,
                                                      height: 6,
                                                      decoration: BoxDecoration(
                                                        color:
                                                            colorScheme.primary,
                                                        shape: BoxShape.circle,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 12),
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            item.title ?? '',
                                                            style: textTheme
                                                                .bodyLarge
                                                                ?.copyWith(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color: colorScheme
                                                                  .onSurface,
                                                            ),
                                                          ),
                                                          ...[
                                                          const SizedBox(
                                                              height: 4),
                                                          Text(
                                                            item.description,
                                                            style: textTheme
                                                                .bodyMedium
                                                                ?.copyWith(
                                                              height: 1.5,
                                                              color: colorScheme
                                                                  .onSurfaceVariant,
                                                            ),
                                                          ),
                                                        ],
                                                          if (item.condition !=
                                                              null) ...[
                                                            const SizedBox(
                                                                height: 8),
                                                            Container(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(8),
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: colorScheme
                                                                    .primaryContainer
                                                                    .withValues(alpha: 0.1),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            4),
                                                                border:
                                                                    Border.all(
                                                                  color: colorScheme
                                                                      .primary
                                                                      .withValues(alpha: 0.2),
                                                                ),
                                                              ),
                                                              child: Row(
                                                                children: [
                                                                  Icon(
                                                                    Icons
                                                                        .info_outline,
                                                                    size: 16,
                                                                    color: colorScheme
                                                                        .primary,
                                                                  ),
                                                                  const SizedBox(
                                                                      width: 8),
                                                                  Expanded(
                                                                    child: Text(
                                                                      item.condition!,
                                                                      style: textTheme
                                                                          .bodySmall
                                                                          ?.copyWith(
                                                                        color: colorScheme
                                                                            .primary,
                                                                        height:
                                                                            1.5,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                          if (item.example !=
                                                              null) ...[
                                                            const SizedBox(
                                                                height: 8),
                                                            Container(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(8),
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: colorScheme
                                                                    .tertiaryContainer
                                                                    .withValues(alpha: 0.1),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            4),
                                                              ),
                                                              child: Row(
                                                                children: [
                                                                  Icon(
                                                                    Icons
                                                                        .lightbulb_outline,
                                                                    size: 16,
                                                                    color: colorScheme
                                                                        .tertiary,
                                                                  ),
                                                                  const SizedBox(
                                                                      width: 8),
                                                                  Expanded(
                                                                    child: Text(
                                                                      item.example!,
                                                                      style: textTheme
                                                                          .bodySmall
                                                                          ?.copyWith(
                                                                        fontStyle:
                                                                            FontStyle.italic,
                                                                        height:
                                                                            1.5,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          )),
                                    ],
                                  )),
                          ],
                        ),
                      ),
                    )),
              ],
            ],
          ),
        );
      },
    );
  }

  IconData _getSectionIcon(String title) {
    switch (title) {
      case 'Réintégrations':
        return Icons.add_circle_outline;
      case 'Déductions':
        return Icons.remove_circle_outline;
      default:
        return Icons.article;
    }
  }
}
