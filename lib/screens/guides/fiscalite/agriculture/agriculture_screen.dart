import 'package:flutter/material.dart';
import 'sections/tva_agricole_section.dart';
import 'sections/is_agricole_section.dart';
import 'sections/ir_agricole_section.dart';
import 'sections/cooperatives_section.dart';
import 'sections/comptabilite_agricole_section.dart';
import 'sections/calculateurs_agricoles_section.dart';

class AgricultureScreen extends StatelessWidget {
  const AgricultureScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return DefaultTabController(
      length: 6,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'Fiscalité Agricole',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
          bottom: TabBar(
            isScrollable: true,
            labelColor: colorScheme.primary,
            unselectedLabelColor: colorScheme.onSurfaceVariant,
            indicatorColor: colorScheme.primary,
            dividerColor: colorScheme.outlineVariant,
            tabs: [
              Tab(
                icon: Icon(Icons.agriculture, color: colorScheme.primary),
                text: 'TVA Agricole',
              ),
              Tab(
                icon: Icon(Icons.business, color: colorScheme.primary),
                text: 'IS Agricole',
              ),
              Tab(
                icon: Icon(Icons.person, color: colorScheme.primary),
                text: 'IR Agricole',
              ),
              Tab(
                icon: Icon(Icons.group, color: colorScheme.primary),
                text: 'Coopératives',
              ),
              Tab(
                icon: Icon(Icons.account_balance_wallet, color: colorScheme.primary),
                text: 'Comptabilité',
              ),
              Tab(
                icon: Icon(Icons.calculate, color: colorScheme.primary),
                text: 'Calculateurs',
              ),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            TvaAgricoleSection(),
            IsAgricoleSection(),
            IrAgricoleSection(),
            CooperativesSection(),
            ComptabiliteAgricoleSection(),
            CalculateursAgricolesSection(),
          ],
        ),
      ),
    );
  }
}