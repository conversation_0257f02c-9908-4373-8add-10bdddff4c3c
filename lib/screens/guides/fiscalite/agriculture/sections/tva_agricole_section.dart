import 'package:flutter/material.dart';

class TvaAgricoleSection extends StatelessWidget {
  const TvaAgricoleSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionCard(
            context,
            'Produits Agricoles Non Transformés',
            Icons.grass,
            Colors.green,
            [
              _buildInfoTile('Taux TVA', '0% (Exonération avec droit à déduction)'),
              _buildInfoTile('Céréales', 'Blé, orge, maïs, avoine à l\'état brut'),
              _buildInfoTile('Légumineuses', 'Lentilles, pois chiches, haricots secs'),
              _buildInfoTile('Fruits frais', 'Agrumes, pommes, raisins non transformés'),
              _buildInfoTile('Légumes', 'Tomates, pommes de terre, oignons frais'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Services Agricoles',
            Icons.build,
            Colors.blue,
            [
              _buildInfoTile('Taux TVA', '10% avec droit à déduction'),
              _buildInfoTile('Labour et préparation', 'Services mécanisés agricoles'),
              _buildInfoTile('Semis et plantation', 'Services de mise en terre'),
              _buildInfoTile('Récolte et battage', 'Services de récolte mécanisée'),
              _buildInfoTile('Transport agricole', 'Transport spécialisé produits agricoles'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Coopératives Agricoles',
            Icons.group,
            Colors.orange,
            [
              _buildInfoTile('Ventes produits adhérents', '0% (Exonération)'),
              _buildInfoTile('Achats groupés intrants', '0% (Exonération)'),
              _buildInfoTile('Services aux adhérents', '10% (Taux réduit)'),
              _buildInfoTile('Transformation primaire', '10% (Première transformation)'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Équipements Agricoles',
            Icons.agriculture,
            Colors.brown,
            [
              _buildInfoTile('Tracteurs agricoles', '0% (< 130 CV, neufs)'),
              _buildInfoTile('Matériel travail du sol', '0% (Charrues, herses, cultivateurs)'),
              _buildInfoTile('Matériel de récolte', '0% (Moissonneuses, faucheuses)'),
              _buildInfoTile('Équipement d\'élevage', '10% (Matériel de traite, abreuvoirs)'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Exportations Agricoles',
            Icons.flight_takeoff,
            Colors.purple,
            [
              _buildInfoTile('Taux export', '0% avec droit à déduction'),
              _buildInfoTile('Remboursement TVA', 'Dans les 3 mois'),
              _buildInfoTile('Procédures', 'DEB + facture commerciale'),
              _buildInfoTile('Justification', 'Document douanier + connaissement'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color iconColor,
    List<Widget> children,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: iconColor, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              color: Colors.grey[400],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}