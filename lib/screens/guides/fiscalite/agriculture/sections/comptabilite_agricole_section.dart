import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

class ComptabiliteAgricoleSection extends StatefulWidget {
  const ComptabiliteAgricoleSection({super.key});

  @override
  State<ComptabiliteAgricoleSection> createState() => _ComptabiliteAgricoleSectionState();
}

class _ComptabiliteAgricoleSectionState extends State<ComptabiliteAgricoleSection> {
  Map<String, dynamic>? _comptabiliteData;
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';
  String _selectedAccountClass = 'all';

  @override
  void initState() {
    super.initState();
    _loadComptabiliteData();
  }

  Future<void> _loadComptabiliteData() async {
    try {
      final String jsonString = await rootBundle.loadString(
        'assets/agriculture/comptabilite_agricole.json',
      );
      final Map<String, dynamic> data = json.decode(jsonString);
      setState(() {
        _comptabiliteData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Erreur lors du chargement des données: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _loadComptabiliteData();
              },
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    if (_comptabiliteData == null) {
      return const Center(
        child: Text('Aucune donnée disponible'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          _buildPrincipesGeneraux(),
          const SizedBox(height: 24),
          _buildPlanComptable(),
          const SizedBox(height: 24),
          _buildComptabilisationSpecifique(),
          const SizedBox(height: 24),
          _buildEvaluationInventaire(),
          const SizedBox(height: 24),
          _buildDocumentsSynthese(),
          const SizedBox(height: 24),
          _buildControleCoherence(),
          const SizedBox(height: 24),
          _buildObligationsAnnexes(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _comptabiliteData!['title'] ?? 'Comptabilité Agricole',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _comptabiliteData!['description'] ?? '',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.update,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  'Mise à jour: ${_comptabiliteData!['date_mise_a_jour'] ?? ''}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrincipesGeneraux() {
    final principes = _comptabiliteData!['principes_generaux'];
    if (principes == null) return const SizedBox.shrink();

    return Card(
      child: ExpansionTile(
        leading: const Icon(Icons.info_outline),
        title: const Text('Principes Généraux'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (principes['specificites_secteur'] != null) ...[
                  Text(
                    'Spécificités du secteur agricole:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...List<String>.from(principes['specificites_secteur']).map(
                    (spec) => Padding(
                      padding: const EdgeInsets.only(left: 16.0, bottom: 4.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('• '),
                          Expanded(child: Text(spec)),
                        ],
                      ),
                    ),
                  ).cast<Widget>(),
                  const SizedBox(height: 16),
                ],
                if (principes['obligations_comptables'] != null) ...[
                  Text(
                    'Obligations comptables:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildObligationsComptables(principes['obligations_comptables']),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildObligationsComptables(Map<String, dynamic> obligations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: obligations.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 120,
                child: Text(
                  '${entry.key.replaceAll('_', ' ').toUpperCase()}:',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              Expanded(child: Text(entry.value.toString())),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPlanComptable() {
    final planComptable = _comptabiliteData!['plan_comptable_agricole'];
    if (planComptable == null) return const SizedBox.shrink();

    return Card(
      child: ExpansionTile(
        leading: const Icon(Icons.account_tree),
        title: const Text('Plan Comptable Agricole'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _buildAccountClassFilter(),
                const SizedBox(height: 16),
                _buildAccountSearch(),
                const SizedBox(height: 16),
                _buildAccountClasses(planComptable),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountClassFilter() {
    final classes = [
      {'value': 'all', 'label': 'Toutes les classes'},
      {'value': 'classe_1', 'label': 'Classe 1 - Financement'},
      {'value': 'classe_2', 'label': 'Classe 2 - Immobilisations'},
      {'value': 'classe_3', 'label': 'Classe 3 - Stocks'},
      {'value': 'classe_6', 'label': 'Classe 6 - Charges'},
    ];

    return Wrap(
      spacing: 8.0,
      children: classes.map((classInfo) {
        final isSelected = _selectedAccountClass == classInfo['value'];
        return FilterChip(
          label: Text(classInfo['label']!),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedAccountClass = classInfo['value']!;
            });
          },
        );
      }).toList(),
    );
  }

  Widget _buildAccountSearch() {
    return TextField(
      decoration: const InputDecoration(
        labelText: 'Rechercher un compte',
        prefixIcon: Icon(Icons.search),
        border: OutlineInputBorder(),
      ),
      onChanged: (value) {
        setState(() {
          _searchQuery = value.toLowerCase();
        });
      },
    );
  }

  Widget _buildAccountClasses(Map<String, dynamic> planComptable) {
    return Column(
      children: planComptable.entries
          .where((entry) => _selectedAccountClass == 'all' || entry.key == _selectedAccountClass)
          .map((classEntry) => _buildAccountClass(classEntry.key, classEntry.value))
          .toList(),
    );
  }

  Widget _buildAccountClass(String className, Map<String, dynamic> accounts) {
    final filteredAccounts = accounts.entries
        .where((entry) => 
            _searchQuery.isEmpty ||
            entry.key.toLowerCase().contains(_searchQuery) ||
            entry.value.toString().toLowerCase().contains(_searchQuery))
        .toList();

    if (filteredAccounts.isEmpty) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ExpansionTile(
        title: Text(_getClassTitle(className)),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: filteredAccounts.map((account) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        child: Text(
                          account.key,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          account.value.toString(),
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  String _getClassTitle(String className) {
    switch (className) {
      case 'classe_1_financement':
        return 'Classe 1 - Comptes de Financement Permanent';
      case 'classe_2_immobilisations':
        return 'Classe 2 - Comptes d\'Immobilisations';
      case 'classe_3_stocks':
        return 'Classe 3 - Comptes de Stocks';
      case 'classe_6_charges':
        return 'Classe 6 - Comptes de Charges';
      default:
        return className.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildComptabilisationSpecifique() {
    final comptabilisation = _comptabiliteData!['comptabilisation_specifique'];
    if (comptabilisation == null) return const SizedBox.shrink();

    return Card(
      child: ExpansionTile(
        leading: const Icon(Icons.agriculture),
        title: const Text('Comptabilisation Spécifique'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                if (comptabilisation['actifs_biologiques'] != null)
                  _buildActifsBiologiques(comptabilisation['actifs_biologiques']),
                const SizedBox(height: 16),
                if (comptabilisation['cycles_production'] != null)
                  _buildCyclesProduction(comptabilisation['cycles_production']),
                const SizedBox(height: 16),
                if (comptabilisation['subventions_agricoles'] != null)
                  _buildSubventionsAgricoles(comptabilisation['subventions_agricoles']),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActifsBiologiques(Map<String, dynamic> actifs) {
    return Card(
      child: ExpansionTile(
        title: const Text('Actifs Biologiques'),
        subtitle: Text(actifs['definition'] ?? ''),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: List<Map<String, dynamic>>.from(actifs['categories'] ?? [])
                  .map((category) => _buildActifCategory(category))
                  .cast<Widget>()
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActifCategory(Map<String, dynamic> category) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            category['type'] ?? '',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildCategoryDetails(category),
        ],
      ),
    );
  }

  Widget _buildCategoryDetails(Map<String, dynamic> category) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (category['traitement'] != null)
          _buildDetailRow('Traitement', category['traitement']),
        if (category['valorisation'] != null)
          _buildDetailRow('Valorisation', category['valorisation']),
        if (category['amortissement'] != null)
          _buildDetailRow('Amortissement', category['amortissement']),
        if (category['phases'] != null)
          _buildPhases(category['phases']),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildPhases(List<dynamic> phases) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Phases:',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        ...phases.map((phase) => Padding(
          padding: const EdgeInsets.only(left: 16.0, top: 4.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('• '),
              Expanded(child: Text(phase.toString())),
            ],
          ),
        )).cast<Widget>(),
      ],
    );
  }

  Widget _buildCyclesProduction(Map<String, dynamic> cycles) {
    return Card(
      child: ExpansionTile(
        title: const Text('Cycles de Production'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: cycles.entries.map((cycle) {
                return _buildCycleCard(cycle.key, cycle.value);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCycleCard(String cycleType, dynamic cycleData) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ExpansionTile(
        title: Text(_getCycleTitle(cycleType)),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildCycleContent(cycleData),
          ),
        ],
      ),
    );
  }

  String _getCycleTitle(String cycleType) {
    switch (cycleType) {
      case 'cultures_annuelles':
        return 'Cultures Annuelles';
      case 'cultures_perennes':
        return 'Cultures Pérennes';
      case 'elevage':
        return 'Élevage';
      default:
        return cycleType.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildCycleContent(dynamic cycleData) {
    if (cycleData is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: cycleData.entries.map((entry) {
          if (entry.value is List) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${entry.key.replaceAll('_', ' ').toUpperCase()}:',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                ...List<String>.from(entry.value).map(
                  (item) => Padding(
                    padding: const EdgeInsets.only(left: 16.0, top: 4.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• '),
                        Expanded(child: Text(item)),
                      ],
                    ),
                  ),
                ).cast<Widget>(),
                const SizedBox(height: 8),
              ],
            );
          } else {
            return _buildDetailRow(
              entry.key.replaceAll('_', ' ').toUpperCase(),
              entry.value.toString(),
            );
          }
        }).toList(),
      );
    }
    return Text(cycleData.toString());
  }

  Widget _buildSubventionsAgricoles(Map<String, dynamic> subventions) {
    return Card(
      child: ExpansionTile(
        title: const Text('Subventions Agricoles'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: subventions.entries.map((subvention) {
                return _buildSubventionCard(subvention.key, subvention.value);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubventionCard(String subventionType, Map<String, dynamic> subventionData) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ExpansionTile(
        title: Text(_getSubventionTitle(subventionType)),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: subventionData.entries.map((entry) {
                if (entry.value is List) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${entry.key.replaceAll('_', ' ').toUpperCase()}:',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      ...List<String>.from(entry.value).map(
                        (item) => Padding(
                          padding: const EdgeInsets.only(left: 16.0, top: 4.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('• '),
                              Expanded(child: Text(item)),
                            ],
                          ),
                        ),
                      ).cast<Widget>(),
                      const SizedBox(height: 8),
                    ],
                  );
                } else {
                  return _buildDetailRow(
                    entry.key.replaceAll('_', ' ').toUpperCase(),
                    entry.value.toString(),
                  );
                }
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  String _getSubventionTitle(String subventionType) {
    switch (subventionType) {
      case 'subventions_exploitation':
        return 'Subventions d\'Exploitation';
      case 'subventions_investissement':
        return 'Subventions d\'Investissement';
      case 'aides_exceptionnelles':
        return 'Aides Exceptionnelles';
      default:
        return subventionType.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildEvaluationInventaire() {
    final evaluation = _comptabiliteData!['evaluation_inventaire'];
    if (evaluation == null) return const SizedBox.shrink();

    return Card(
      child: ExpansionTile(
        leading: const Icon(Icons.inventory),
        title: const Text('Évaluation et Inventaire'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: evaluation.entries.map<Widget>((section) {
                return _buildEvaluationSection(section.key, section.value);
              }).cast<Widget>().toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEvaluationSection(String sectionType, Map<String, dynamic> sectionData) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ExpansionTile(
        title: Text(_getEvaluationTitle(sectionType)),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: sectionData.entries.map((item) {
                return _buildEvaluationItem(item.key, item.value);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  String _getEvaluationTitle(String sectionType) {
    switch (sectionType) {
      case 'stocks_produits':
        return 'Stocks de Produits';
      case 'cheptel':
        return 'Cheptel';
      case 'creances_dettes':
        return 'Créances et Dettes';
      default:
        return sectionType.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildEvaluationItem(String itemType, dynamic itemData) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            itemType.replaceAll('_', ' ').toUpperCase(),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildItemContent(itemData),
        ],
      ),
    );
  }

  Widget _buildItemContent(dynamic itemData) {
    if (itemData is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: itemData.entries.map((entry) {
          if (entry.value is List) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${entry.key.replaceAll('_', ' ').toUpperCase()}:',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                ...List<String>.from(entry.value).map(
                  (item) => Padding(
                    padding: const EdgeInsets.only(left: 16.0, top: 2.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• '),
                        Expanded(child: Text(item)),
                      ],
                    ),
                  ),
                ).cast<Widget>(),
                const SizedBox(height: 4),
              ],
            );
          } else {
            return _buildDetailRow(
              entry.key.replaceAll('_', ' ').toUpperCase(),
              entry.value.toString(),
            );
          }
        }).toList(),
      );
    }
    return Text(itemData.toString());
  }

  Widget _buildDocumentsSynthese() {
    final documents = _comptabiliteData!['documents_synthese'];
    if (documents == null) return const SizedBox.shrink();

    return Card(
      child: ExpansionTile(
        leading: const Icon(Icons.description),
        title: const Text('Documents de Synthèse'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: documents.entries.map<Widget>((document) {
                return _buildDocumentCard(document.key, document.value);
              }).cast<Widget>().toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentCard(String documentType, dynamic documentData) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ExpansionTile(
        title: Text(_getDocumentTitle(documentType)),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildDocumentContent(documentData),
          ),
        ],
      ),
    );
  }

  String _getDocumentTitle(String documentType) {
    switch (documentType) {
      case 'compte_resultat':
        return 'Compte de Résultat';
      case 'bilan':
        return 'Bilan';
      case 'annexes':
        return 'Annexes';
      default:
        return documentType.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildDocumentContent(dynamic documentData) {
    if (documentData is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: documentData.entries.map((entry) {
          if (entry.value is List) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${entry.key.replaceAll('_', ' ').toUpperCase()}:',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                ...List<String>.from(entry.value).map(
                  (item) => Padding(
                    padding: const EdgeInsets.only(left: 16.0, top: 4.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• '),
                        Expanded(child: Text(item)),
                      ],
                    ),
                  ),
                ).cast<Widget>(),
                const SizedBox(height: 8),
              ],
            );
          } else {
            return _buildDetailRow(
              entry.key.replaceAll('_', ' ').toUpperCase(),
              entry.value.toString(),
            );
          }
        }).toList(),
      );
    } else if (documentData is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List<String>.from(documentData).map(
          (item) => Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('• '),
                Expanded(child: Text(item)),
              ],
            ),
          ),
        ).cast<Widget>().toList(),
      );
    }
    return Text(documentData.toString());
  }

  Widget _buildControleCoherence() {
    final controle = _comptabiliteData!['controle_coherence'];
    if (controle == null) return const SizedBox.shrink();

    return Card(
      child: ExpansionTile(
        leading: const Icon(Icons.fact_check),
        title: const Text('Contrôle et Cohérence'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: controle.entries.map<Widget>((section) {
                return _buildControleSection(section.key, section.value);
              }).cast<Widget>().toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControleSection(String sectionType, dynamic sectionData) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ExpansionTile(
        title: Text(_getControleTitle(sectionType)),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildControleContent(sectionData),
          ),
        ],
      ),
    );
  }

  String _getControleTitle(String sectionType) {
    switch (sectionType) {
      case 'ratios_gestion':
        return 'Ratios de Gestion';
      case 'controles_coherence':
        return 'Contrôles de Cohérence';
      default:
        return sectionType.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildControleContent(dynamic sectionData) {
    if (sectionData is List) {
      if (sectionData.isNotEmpty && sectionData.first is Map) {
        // Ratios de gestion
        return Column(
          children: List<Map<String, dynamic>>.from(sectionData).map((ratio) {
            return Container(
              margin: const EdgeInsets.only(bottom: 12.0),
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    ratio['nom'] ?? '',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  if (ratio['calcul'] != null)
                    _buildDetailRow('Calcul', ratio['calcul']),
                  if (ratio['benchmark'] != null)
                    _buildDetailRow('Benchmark', ratio['benchmark']),
                  if (ratio['suivi'] != null)
                    _buildDetailRow('Suivi', ratio['suivi']),
                  if (ratio['alerte'] != null)
                    _buildDetailRow('Alerte', ratio['alerte']),
                ],
              ),
            );
          }).cast<Widget>().toList(),
        );
      } else {
        // Contrôles de cohérence
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: List<String>.from(sectionData).map(
            (item) => Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('• '),
                  Expanded(child: Text(item)),
                ],
              ),
            ),
          ).cast<Widget>().toList(),
        );
      }
    }
    return Text(sectionData.toString());
  }

  Widget _buildObligationsAnnexes() {
    final obligations = _comptabiliteData!['obligations_annexes'];
    if (obligations == null) return const SizedBox.shrink();

    return Card(
      child: ExpansionTile(
        leading: const Icon(Icons.assignment),
        title: const Text('Obligations et Annexes'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: obligations.entries.map((section) {
                return _buildObligationSection(section.key, section.value);
              }).cast<Widget>().toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildObligationSection(String sectionType, dynamic sectionData) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ExpansionTile(
        title: Text(_getObligationTitle(sectionType)),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildObligationContent(sectionData),
          ),
        ],
      ),
    );
  }

  String _getObligationTitle(String sectionType) {
    switch (sectionType) {
      case 'registres_obligatoires':
        return 'Registres Obligatoires';
      case 'declarations':
        return 'Déclarations';
      case 'conservation':
        return 'Conservation des Documents';
      default:
        return sectionType.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildObligationContent(dynamic sectionData) {
    if (sectionData is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List<String>.from(sectionData).map(
          (item) => Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('• '),
                Expanded(child: Text(item)),
              ],
            ),
          ),
        ).cast<Widget>().toList(),
      );
    } else if (sectionData is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: sectionData.entries.map((entry) {
          return _buildDetailRow(
            entry.key.replaceAll('_', ' ').toUpperCase(),
            entry.value.toString(),
          );
        }).toList(),
      );
    }
    return Text(sectionData.toString());
  }
}
