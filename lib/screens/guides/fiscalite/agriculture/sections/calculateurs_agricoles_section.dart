import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

class CalculateursAgricolesSection extends StatefulWidget {
  const CalculateursAgricolesSection({super.key});

  @override
  State<CalculateursAgricolesSection> createState() => _CalculateursAgricolesSectionState();
}

class _CalculateursAgricolesSectionState extends State<CalculateursAgricolesSection> {
  String _selectedCalculator = 'IR Agricole';
  Map<String, dynamic>? _calculatorData;
  bool _isLoading = true;
  String? _error;

  // Common controllers
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, dynamic> _calculationResults = {};

  // Calculator types
  final List<String> _calculatorTypes = [
    'IR Agricole',
    'IS Agricole',
    'TVA Agricole',
    'Amortissement',
    'Impact Subventions',
    'Coopératives',
  ];

  @override
  void initState() {
    super.initState();
    _loadCalculatorData();
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadCalculatorData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final String jsonString = await rootBundle.loadString(
        'assets/agriculture/calculateurs_agricoles_2025.json',
      );
      final data = json.decode(jsonString);

      setState(() {
        _calculatorData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Erreur lors du chargement des données: $e';
        _isLoading = false;
      });
    }
  }

  TextEditingController _getController(String key) {
    if (!_controllers.containsKey(key)) {
      _controllers[key] = TextEditingController();
    }
    return _controllers[key]!;
  }

  void _calculate() {
    final calculatorConfig = _calculatorData?['calculators']?[_selectedCalculator];
    if (calculatorConfig == null) return;

    final inputs = <String, double>{};
    bool hasError = false;

    // Collect input values
    for (final field in calculatorConfig['inputs'] ?? []) {
      final key = field['key'] as String;
      final controller = _getController(key);
      final value = double.tryParse(controller.text);
      
      if (field['required'] == true && (value == null || value < 0)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Veuillez saisir une valeur valide pour ${field['label']}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
        hasError = true;
        break;
      }
      
      inputs[key] = value ?? 0;
    }

    if (hasError) return;

    // Perform calculation based on calculator type
    Map<String, dynamic> result;
    switch (_selectedCalculator) {
      case 'IR Agricole':
        result = _calculateIRAgricole(inputs, calculatorConfig);
        break;
      case 'IS Agricole':
        result = _calculateISAgricole(inputs, calculatorConfig);
        break;
      case 'TVA Agricole':
        result = _calculateTVAAgricole(inputs, calculatorConfig);
        break;
      case 'Amortissement':
        result = _calculateAmortissement(inputs, calculatorConfig);
        break;
      case 'Impact Subventions':
        result = _calculateImpactSubventions(inputs, calculatorConfig);
        break;
      case 'Coopératives':
        result = _calculateCooperatives(inputs, calculatorConfig);
        break;
      default:
        result = {'error': 'Calculateur non implémenté'};
    }

    setState(() {
      _calculationResults[_selectedCalculator] = result;
    });
  }

  Map<String, dynamic> _calculateIRAgricole(Map<String, double> inputs, Map<String, dynamic> config) {
    final revenue = inputs['revenue'] ?? 0;
    final expenses = inputs['expenses'] ?? 0;
    final exemptions = inputs['exemptions'] ?? 0;
    
    final taxableIncome = (revenue - expenses - exemptions).clamp(0, double.infinity);
    
    // Apply progressive scale from config
    final tranches = config['calculation']?['tranches'] as List<dynamic>? ?? [];
    double totalTax = 0;
    double remainingIncome = taxableIncome.toDouble();
    
    final List<Map<String, dynamic>> breakdown = [];
    
    for (final tranche in tranches) {
      if (remainingIncome <= 0) break;
      
      final min = (tranche['min'] as num?)?.toDouble() ?? 0;
      final max = (tranche['max'] as num?)?.toDouble();
      final rate = (tranche['rate'] as num?)?.toDouble() ?? 0;
      
      final taxableAmount = max != null
        ? remainingIncome.clamp(0.0, max - min)
        : remainingIncome;
      
      final trancheTax = taxableAmount * (rate / 100);
      totalTax += trancheTax;
      remainingIncome -= taxableAmount;
      
      if (taxableAmount > 0) {
        breakdown.add({
          'tranche': '${min.toStringAsFixed(0)} - ${max?.toStringAsFixed(0) ?? '∞'} DH',
          'rate': '${rate.toStringAsFixed(1)}%',
          'taxable': taxableAmount,
          'tax': trancheTax,
        });
      }
    }
    
    return {
      'revenue': revenue,
      'expenses': expenses,
      'exemptions': exemptions,
      'taxableIncome': taxableIncome,
      'totalTax': totalTax,
      'netIncome': taxableIncome - totalTax,
      'breakdown': breakdown,
    };
  }

  Map<String, dynamic> _calculateISAgricole(Map<String, double> inputs, Map<String, dynamic> config) {
    final benefit = inputs['benefit'] ?? 0;
    final reintegrations = inputs['reintegrations'] ?? 0;
    final deductions = inputs['deductions'] ?? 0;
    
    final taxableBenefit = (benefit + reintegrations - deductions).clamp(0, double.infinity);
    
    // Standard IS rate for agriculture
    final rate = (config['calculation']?['standard_rate'] as num?)?.toDouble() ?? 30;
    final tax = taxableBenefit * (rate / 100);
    
    // Calculate minimum tax (cotisation minimale)
    final revenue = inputs['revenue'] ?? 0;
    final cmRate = (config['calculation']?['cm_rate'] as num?)?.toDouble() ?? 0.5;
    final minTax = revenue * (cmRate / 100);
    
    final finalTax = tax > minTax ? tax : minTax;
    
    return {
      'benefit': benefit,
      'reintegrations': reintegrations,
      'deductions': deductions,
      'taxableBenefit': taxableBenefit,
      'calculatedTax': tax,
      'minimumTax': minTax,
      'finalTax': finalTax,
      'rate': rate,
    };
  }

  Map<String, dynamic> _calculateTVAAgricole(Map<String, double> inputs, Map<String, dynamic> config) {
    final sales = inputs['sales'] ?? 0;
    final purchases = inputs['purchases'] ?? 0;
    final exports = inputs['exports'] ?? 0;
    
    final domesticSales = sales - exports;
    
    // VAT rates from config
    final standardRate = (config['calculation']?['standard_rate'] as num?)?.toDouble() ?? 20;
    final reducedRate = (config['calculation']?['reduced_rate'] as num?)?.toDouble() ?? 14;
    
    // Calculate VAT on sales (assuming standard rate)
    final vatOnSales = domesticSales * (standardRate / 100);
    
    // Calculate VAT on purchases (deductible)
    final vatOnPurchases = purchases * (standardRate / 100);
    
    // Net VAT to pay
    final netVAT = (vatOnSales - vatOnPurchases).clamp(0, double.infinity);
    
    return {
      'sales': sales,
      'exports': exports,
      'domesticSales': domesticSales,
      'purchases': purchases,
      'vatOnSales': vatOnSales,
      'vatOnPurchases': vatOnPurchases,
      'netVAT': netVAT,
      'standardRate': standardRate,
    };
  }

  Map<String, dynamic> _calculateAmortissement(Map<String, double> inputs, Map<String, dynamic> config) {
    final assetValue = inputs['asset_value'] ?? 0;
    final usefulLife = inputs['useful_life'] ?? 1;
    final biologicalAsset = inputs['biological_asset'] ?? 0;
    
    // Linear depreciation
    final annualDepreciation = assetValue / usefulLife;
    
    // Special rules for biological assets
    double biologicalDepreciation = 0;
    if (biologicalAsset > 0) {
      final bioRate = (config['calculation']?['biological_rate'] as num?)?.toDouble() ?? 10;
      biologicalDepreciation = biologicalAsset * (bioRate / 100);
    }
    
    final totalDepreciation = annualDepreciation + biologicalDepreciation;
    
    return {
      'assetValue': assetValue,
      'usefulLife': usefulLife,
      'biologicalAsset': biologicalAsset,
      'annualDepreciation': annualDepreciation,
      'biologicalDepreciation': biologicalDepreciation,
      'totalDepreciation': totalDepreciation,
      'remainingValue': assetValue - totalDepreciation,
    };
  }

  Map<String, dynamic> _calculateImpactSubventions(Map<String, double> inputs, Map<String, dynamic> config) {
    final investmentSubsidy = inputs['investment_subsidy'] ?? 0;
    final operatingSubsidy = inputs['operating_subsidy'] ?? 0;
    final totalIncome = inputs['total_income'] ?? 0;
    
    // Investment subsidies are generally spread over asset life
    final assetLife = inputs['asset_life'] ?? 5;
    final annualInvestmentImpact = investmentSubsidy / assetLife;
    
    // Operating subsidies are fully taxable in the year received
    final taxableSubsidies = annualInvestmentImpact + operatingSubsidy;
    
    // Calculate tax impact
    final taxRate = (config['calculation']?['tax_rate'] as num?)?.toDouble() ?? 30;
    final additionalTax = taxableSubsidies * (taxRate / 100);
    
    return {
      'investmentSubsidy': investmentSubsidy,
      'operatingSubsidy': operatingSubsidy,
      'annualInvestmentImpact': annualInvestmentImpact,
      'taxableSubsidies': taxableSubsidies,
      'additionalTax': additionalTax,
      'netBenefit': (investmentSubsidy + operatingSubsidy) - additionalTax,
    };
  }

  Map<String, dynamic> _calculateCooperatives(Map<String, double> inputs, Map<String, dynamic> config) {
    final memberSales = inputs['member_sales'] ?? 0;
    final nonMemberSales = inputs['non_member_sales'] ?? 0;
    final operatingExpenses = inputs['operating_expenses'] ?? 0;
    
    // Member transactions are often exempt or reduced rate
    final memberExemptionRate = (config['calculation']?['member_exemption'] as num?)?.toDouble() ?? 100;
    final standardRate = (config['calculation']?['standard_rate'] as num?)?.toDouble() ?? 30;
    
    // Calculate taxable income
    final totalSales = memberSales + nonMemberSales;
    final netIncome = totalSales - operatingExpenses;
    
    // Apply exemptions
    final exemptIncome = memberSales * (memberExemptionRate / 100);
    final taxableIncome = (netIncome - exemptIncome).clamp(0, double.infinity);
    
    // Calculate tax
    final tax = taxableIncome * (standardRate / 100);
    
    return {
      'memberSales': memberSales,
      'nonMemberSales': nonMemberSales,
      'totalSales': totalSales,
      'operatingExpenses': operatingExpenses,
      'netIncome': netIncome,
      'exemptIncome': exemptIncome,
      'taxableIncome': taxableIncome,
      'tax': tax,
      'memberExemptionRate': memberExemptionRate,
    };
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            FilledButton(
              onPressed: _loadCalculatorData,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          _buildCalculatorSelector(),
          const SizedBox(height: 24),
          _buildCalculatorForm(),
          const SizedBox(height: 24),
          _buildCalculateButton(),
          const SizedBox(height: 24),
          if (_calculationResults.containsKey(_selectedCalculator))
            _buildResultsSection(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate,
                color: Theme.of(context).colorScheme.onPrimary,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Calculateurs Agricoles',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Outils de calcul pour la fiscalité agricole marocaine',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context)
                      .colorScheme
                      .onPrimary
                      .withValues(alpha: 0.8),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculatorSelector() {
    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.apps,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Type de calculateur',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _calculatorTypes.map((type) {
                final isSelected = type == _selectedCalculator;
                return FilterChip(
                  label: Text(type),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedCalculator = type;
                        // Clear previous results when switching calculators
                        _calculationResults.remove(type);
                      });
                    }
                  },
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  selectedColor: Theme.of(context).colorScheme.primaryContainer,
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatorForm() {
    final calculatorConfig = _calculatorData?['calculators']?[_selectedCalculator];
    if (calculatorConfig == null) {
      return const SizedBox.shrink();
    }

    final inputs = calculatorConfig['inputs'] as List<dynamic>? ?? [];

    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.input,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Données d\'entrée',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...inputs.map((input) => _buildInputField(input)),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField(Map<String, dynamic> input) {
    final key = input['key'] as String;
    final label = input['label'] as String;
    final hint = input['hint'] as String?;
    final required = input['required'] as bool? ?? false;
    final suffix = input['suffix'] as String? ?? 'DH';

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: _getController(key),
        decoration: InputDecoration(
          labelText: required ? '$label *' : label,
          hintText: hint,
          suffixText: suffix,
          prefixIcon: Icon(
            Icons.edit_outlined,
            color: Theme.of(context).colorScheme.primary,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          helperText: hint,
        ),
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
        ],
      ),
    );
  }

  Widget _buildCalculateButton() {
    return FilledButton.icon(
      onPressed: _calculate,
      icon: const Icon(Icons.calculate),
      label: Text(
        'Calculer',
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
            ),
      ),
      style: FilledButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.primary,
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 16,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    final result = _calculationResults[_selectedCalculator];
    if (result == null) return const SizedBox.shrink();

    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Résultats du calcul',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildResultContent(result),
          ],
        ),
      ),
    );
  }

  Widget _buildResultContent(Map<String, dynamic> result) {
    switch (_selectedCalculator) {
      case 'IR Agricole':
        return _buildIRAgricoleResults(result);
      case 'IS Agricole':
        return _buildISAgricoleResults(result);
      case 'TVA Agricole':
        return _buildTVAAgricoleResults(result);
      case 'Amortissement':
        return _buildAmortissementResults(result);
      case 'Impact Subventions':
        return _buildSubventionsResults(result);
      case 'Coopératives':
        return _buildCooperativesResults(result);
      default:
        return Text('Résultats non disponibles');
    }
  }

  Widget _buildIRAgricoleResults(Map<String, dynamic> result) {
    return Column(
      children: [
        _buildResultRow('Revenus', result['revenue'], 'DH'),
        _buildResultRow('Charges déductibles', result['expenses'], 'DH'),
        _buildResultRow('Exonérations', result['exemptions'], 'DH'),
        const Divider(),
        _buildResultRow('Revenu imposable', result['taxableIncome'], 'DH', isHighlighted: true),
        _buildResultRow('Impôt calculé', result['totalTax'], 'DH', isHighlighted: true),
        _buildResultRow('Revenu net', result['netIncome'], 'DH', isHighlighted: true),
        if (result['breakdown'] != null) ...[
          const SizedBox(height: 16),
          Text(
            'Détail par tranche:',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          ...((result['breakdown'] as List).map((tranche) => 
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Expanded(child: Text(tranche['tranche'])),
                  Text('${tranche['rate']} = '),
                  Text('${(tranche['tax'] as double).toStringAsFixed(2)} DH'),
                ],
              ),
            ),
          )),
        ],
      ],
    );
  }

  Widget _buildISAgricoleResults(Map<String, dynamic> result) {
    return Column(
      children: [
        _buildResultRow('Bénéfice comptable', result['benefit'], 'DH'),
        _buildResultRow('Réintégrations', result['reintegrations'], 'DH'),
        _buildResultRow('Déductions', result['deductions'], 'DH'),
        const Divider(),
        _buildResultRow('Bénéfice imposable', result['taxableBenefit'], 'DH', isHighlighted: true),
        _buildResultRow('IS calculé (${result['rate']}%)', result['calculatedTax'], 'DH'),
        _buildResultRow('Cotisation minimale', result['minimumTax'], 'DH'),
        const Divider(),
        _buildResultRow('IS à payer', result['finalTax'], 'DH', isHighlighted: true),
      ],
    );
  }

  Widget _buildTVAAgricoleResults(Map<String, dynamic> result) {
    return Column(
      children: [
        _buildResultRow('Ventes totales', result['sales'], 'DH'),
        _buildResultRow('Exportations', result['exports'], 'DH'),
        _buildResultRow('Ventes domestiques', result['domesticSales'], 'DH'),
        _buildResultRow('Achats', result['purchases'], 'DH'),
        const Divider(),
        _buildResultRow('TVA sur ventes (${result['standardRate']}%)', result['vatOnSales'], 'DH'),
        _buildResultRow('TVA sur achats (déductible)', result['vatOnPurchases'], 'DH'),
        const Divider(),
        _buildResultRow('TVA nette à payer', result['netVAT'], 'DH', isHighlighted: true),
      ],
    );
  }

  Widget _buildAmortissementResults(Map<String, dynamic> result) {
    return Column(
      children: [
        _buildResultRow('Valeur de l\'actif', result['assetValue'], 'DH'),
        _buildResultRow('Durée d\'utilité', result['usefulLife'], 'ans'),
        _buildResultRow('Actifs biologiques', result['biologicalAsset'], 'DH'),
        const Divider(),
        _buildResultRow('Amortissement linéaire', result['annualDepreciation'], 'DH'),
        _buildResultRow('Amortissement biologique', result['biologicalDepreciation'], 'DH'),
        const Divider(),
        _buildResultRow('Amortissement total', result['totalDepreciation'], 'DH', isHighlighted: true),
        _buildResultRow('Valeur résiduelle', result['remainingValue'], 'DH', isHighlighted: true),
      ],
    );
  }

  Widget _buildSubventionsResults(Map<String, dynamic> result) {
    return Column(
      children: [
        _buildResultRow('Subvention d\'investissement', result['investmentSubsidy'], 'DH'),
        _buildResultRow('Subvention d\'exploitation', result['operatingSubsidy'], 'DH'),
        _buildResultRow('Impact annuel investissement', result['annualInvestmentImpact'], 'DH'),
        const Divider(),
        _buildResultRow('Subventions imposables', result['taxableSubsidies'], 'DH', isHighlighted: true),
        _buildResultRow('Impôt supplémentaire', result['additionalTax'], 'DH'),
        const Divider(),
        _buildResultRow('Bénéfice net', result['netBenefit'], 'DH', isHighlighted: true),
      ],
    );
  }

  Widget _buildCooperativesResults(Map<String, dynamic> result) {
    return Column(
      children: [
        _buildResultRow('Ventes aux membres', result['memberSales'], 'DH'),
        _buildResultRow('Ventes aux non-membres', result['nonMemberSales'], 'DH'),
        _buildResultRow('Charges d\'exploitation', result['operatingExpenses'], 'DH'),
        const Divider(),
        _buildResultRow('Revenus nets', result['netIncome'], 'DH'),
        _buildResultRow('Revenus exonérés (${result['memberExemptionRate']}%)', result['exemptIncome'], 'DH'),
        const Divider(),
        _buildResultRow('Revenus imposables', result['taxableIncome'], 'DH', isHighlighted: true),
        _buildResultRow('Impôt à payer', result['tax'], 'DH', isHighlighted: true),
      ],
    );
  }

  Widget _buildResultRow(String label, dynamic value, String unit, {bool isHighlighted = false}) {
    final formattedValue = value is double 
      ? value.toStringAsFixed(2)
      : value.toString();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      margin: const EdgeInsets.only(bottom: 4),
      decoration: isHighlighted ? BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ) : null,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
                    color: isHighlighted 
                      ? Theme.of(context).colorScheme.primary
                      : null,
                  ),
            ),
          ),
          Text(
            '$formattedValue $unit',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
                  color: isHighlighted 
                    ? Theme.of(context).colorScheme.primary
                    : null,
                ),
          ),
        ],
      ),
    );
  }
}
