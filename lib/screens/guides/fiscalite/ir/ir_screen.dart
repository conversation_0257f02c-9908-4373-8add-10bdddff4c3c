// IR Screen - Main screen for Income Tax (IR) information and calculations
import 'package:flutter/material.dart';
import '../../../../widgets/guide/guide_scaffold.dart';
import '../../../../widgets/guide/breadcrumb_navigation.dart';
import '../../../../widgets/guide/notes_fab.dart';

class IRScreen extends StatelessWidget {
  const IRScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final breadcrumbs = BreadcrumbBuilder.buildFiscaliteBreadcrumbs(
      fiscaliteType: 'IR',
      onFiscaliteNavigate: () {
        Navigator.of(context).popUntil((route) => route.isFirst);
      },
    );

    return GuideScaffold(
      guideId: 'ir',
      title: 'Impôt sur le Revenu',
      breadcrumbs: breadcrumbs,
      showSearch: true,
      showProgress: true,
      allowMultipleExpanded: false,
      floatingActionButton: const NotesFAB(
        guideId: 'ir',
        sectionId: 'ir_main',
        sectionTitle: 'Impôt sur le Revenu',
      ),
      onSectionCompleted: (sectionId) {
        // Handle section completion if needed
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Section "$sectionId" terminée !'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      },
      onBackPressed: () {
        Navigator.of(context).pop();
      },
    );
  }
}


