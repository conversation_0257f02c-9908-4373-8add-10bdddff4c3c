import 'package:flutter/material.dart';

class SanctionsSection extends StatelessWidget {
  const SanctionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.error,
                  colorScheme.errorContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: colorScheme.onError,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Sanctions et Pénalités',
                      style: textTheme.headlineMedium?.copyWith(
                        color: colorScheme.onError,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Les sanctions et pénalités applicables en cas de non-respect des obligations fiscales',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onError.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          _buildSanctionCategory(
            context,
            title: 'Défaut de déclaration',
            sanctions: [
              SanctionItem(
                title: 'Déclaration tardive',
                description:
                    '''• Majoration de 5% si le retard est inférieur à 1 mois
• Majoration de 15% si le retard est entre 1 et 2 mois
• Majoration de 20% si le retard dépasse 2 mois''',
              ),
              SanctionItem(
                title: 'Absence de déclaration',
                description:
                    '''• Majoration de 30% des droits dus ou qui auraient été dus
• Application d'une amende de 500 à 2.000 DH''',
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSanctionCategory(
            context,
            title: 'Paiement tardif',
            sanctions: [
              SanctionItem(
                title: 'Pénalités de retard',
                description: '''• 5% de majoration sur le montant non payé
• 0,5% par mois ou fraction de mois supplémentaire''',
              ),
              SanctionItem(
                title: 'Intérêts de retard',
                description: '• Taux des avances de Bank Al-Maghrib + 2 points',
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSanctionCategory(
            context,
            title: 'Infractions graves',
            sanctions: [
              SanctionItem(
                title: 'Dissimulation de revenus',
                description:
                    '''• Majoration de 100% des droits correspondant aux revenus dissimulés
• Possibilité de poursuites pénales''',
              ),
              SanctionItem(
                title: 'Manœuvres frauduleuses',
                description: '''• Majoration de 100% des droits éludés
• Amende pouvant aller jusqu'à 50.000 DH
• Possibilité d'emprisonnement de 1 à 3 mois''',
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSanctionCategory(
            context,
            title: 'Régularisation spontanée',
            sanctions: [
              SanctionItem(
                title: 'Déclaration rectificative',
                description:
                    '''• Réduction de 50% des majorations si la régularisation est spontanée
• Application uniquement des intérêts de retard dans certains cas''',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSanctionCategory(
    BuildContext context, {
    required String title,
    required List<SanctionItem> sanctions,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: colorScheme.errorContainer.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: colorScheme.error,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    title,
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.error,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            ...sanctions.map((sanction) => Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: colorScheme.outlineVariant,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 20,
                            color: colorScheme.error,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              sanction.title,
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: colorScheme.error,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        sanction.description,
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onSurface,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}

class SanctionItem {
  final String title;
  final String description;

  const SanctionItem({
    required this.title,
    required this.description,
  });
}
