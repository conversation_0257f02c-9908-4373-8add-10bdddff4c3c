import 'package:flutter/material.dart';

class PresentationSection extends StatelessWidget {
  const PresentationSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: <PERSON>umn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: <PERSON>umn(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Impôt sur le Revenu (IR)',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Comprendre les principes fondamentaux de l\'IR au Maroc',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            title: 'Définition',
            icon: Icons.info_outline,
            content:
                '''L'Impôt sur le Revenu (IR) est un impôt direct qui s'applique aux revenus et profits des personnes physiques. Il est calculé selon un barème progressif et tient compte de la situation personnelle du contribuable.''',
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            title: 'Caractéristiques',
            icon: Icons.featured_play_list_outlined,
            items: [
              'Impôt annuel sur l\'ensemble des revenus',
              'Barème progressif par tranches',
              'Déclaration obligatoire',
              'Prélèvement à la source pour les salariés',
              'Système de retenues et d\'acomptes',
            ],
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            title: 'Revenus Imposables',
            icon: Icons.account_balance_wallet_outlined,
            items: [
              'Revenus professionnels',
              'Revenus salariaux et assimilés',
              'Revenus et profits fonciers',
              'Revenus et profits de capitaux mobiliers',
              'Revenus et profits de source étrangère',
            ],
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            title: 'Principes de Base',
            icon: Icons.foundation_outlined,
            items: [
              'Annualité : l\'impôt est établi chaque année',
              'Territorialité : imposition des revenus de source marocaine',
              'Personnalisation : prise en compte de la situation familiale',
              'Progressivité : taux croissant selon les tranches de revenus',
              'Unicité : un seul impôt pour l\'ensemble des revenus',
            ],
          ),
          const SizedBox(height: 16),
          _buildSection(
            context,
            title: 'Objectifs',
            icon: Icons.track_changes_outlined,
            items: [
              'Équité fiscale et redistribution des richesses',
              'Financement des dépenses publiques',
              'Régulation économique et sociale',
              'Encouragement de certaines activités ou secteurs',
              'Développement de la conformité fiscale',
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.tertiaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: colorScheme.tertiary.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  size: 24,
                  color: colorScheme.tertiary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bon à savoir',
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.tertiary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'L\'IR est régi par le Code Général des Impôts (CGI) qui définit les règles d\'imposition, les exonérations et les modalités de déclaration et de paiement.',
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.tertiary,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    String? content,
    List<String>? items,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (content != null)
                    Text(
                      content,
                      style: textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurface,
                        height: 1.5,
                      ),
                    ),
                  if (items != null)
                    ...items.map(
                      (item) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(top: 8),
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                color: colorScheme.primary,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                item,
                                style: textTheme.bodyLarge?.copyWith(
                                  color: colorScheme.onSurface,
                                  height: 1.5,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
