// Liasse Fiscale Screen - Main screen for annual fiscal documentation
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Import sections
import 'sections/guide_section.dart';
import 'sections/checklist_section.dart';
import 'sections/obligations_section.dart';

class LiasseFiscaleScreen extends StatefulWidget {
  const LiasseFiscaleScreen({super.key});

  @override
  State<LiasseFiscaleScreen> createState() => _LiasseFiscaleScreenState();
}

class _LiasseFiscaleScreenState extends State<LiasseFiscaleScreen>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  List<Map<String, dynamic>> _tabs = [];
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadTabs();
  }

  Future<void> _loadTabs() async {
    try {
      final jsonString = await rootBundle.loadString('assets/liasse_fiscale/tabs.json');
      final json = jsonDecode(jsonString);
      setState(() {
        _tabs = List<Map<String, dynamic>>.from(json['tabs']);
        _tabController = TabController(length: _tabs.length, vsync: this);
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading tabs: $e');
      setState(() {
        _isLoading = false;
        _errorMessage =
            'Impossible de charger les onglets. Veuillez réessayer.';
      });
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Widget _buildTabContent(Map<String, dynamic> tab) {
    switch (tab['id']) {
      case 'guide':
        return const GuideSection();
      case 'checklist':
        return const ChecklistSection();
      case 'obligations':
        return const ObligationsSection();
      default:
        return Center(
          child: Text('Section ${tab['id']} non implémentée'),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isWideScreen = MediaQuery.of(context).size.width > 900;

    if (_isLoading) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
              ),
              const SizedBox(height: 16),
              Text(
                'Chargement des données fiscales...',
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.errorContainer.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: colorScheme.error.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: colorScheme.error,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage,
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.error,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadTabs,
                icon: Icon(
                  Icons.refresh,
                  color: colorScheme.onPrimary,
                ),
                label: Text(
                  'Réessayer',
                  style: textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Liasse Fiscale',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        bottom: !isWideScreen
            ? PreferredSize(
                preferredSize: const Size.fromHeight(kToolbarHeight),
                child: Container(
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    border: Border(
                      bottom: BorderSide(
                        color: colorScheme.outlineVariant,
                        width: 1,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.shadow.withValues(alpha: 0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TabBar(
                    controller: _tabController,
                    isScrollable: true,
                    indicatorColor: colorScheme.primary,
                    indicatorWeight: 3,
                    labelColor: colorScheme.primary,
                    unselectedLabelColor: colorScheme.onSurfaceVariant,
                    labelStyle: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    unselectedLabelStyle: textTheme.titleSmall,
                    indicatorSize: TabBarIndicatorSize.label,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    tabs: _tabs.map((tab) {
                      return Tab(
                        icon: Icon(
                          _getIconData(tab['icon']),
                          color: _getTabIconColor(context, tab['id']),
                        ),
                        text: tab['text'],
                      );
                    }).toList(),
                  ),
                ),
              )
            : null,
      ),
      body: Row(
        children: [
          if (isWideScreen && _tabs.length >= 2)
            Container(
              width: 200,
              decoration: BoxDecoration(
                color: colorScheme.surface,
                border: Border(
                  right: BorderSide(
                    color: colorScheme.outlineVariant,
                    width: 1,
                  ),
                ),
              ),
              child: NavigationRail(
                selectedIndex: _tabController?.index ?? 0,
                onDestinationSelected: (index) {
                  setState(() {
                    _tabController?.animateTo(index);
                  });
                },
                labelType: NavigationRailLabelType.all,
                backgroundColor: colorScheme.surface,
                selectedIconTheme: IconThemeData(
                  color: colorScheme.primary,
                  size: 28,
                ),
                unselectedIconTheme: IconThemeData(
                  color: colorScheme.onSurfaceVariant,
                  size: 24,
                ),
                selectedLabelTextStyle: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
                unselectedLabelTextStyle: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                useIndicator: true,
                indicatorColor: colorScheme.primaryContainer,
                destinations: _tabs.map((tab) {
                  return NavigationRailDestination(
                    icon: Icon(_getIconData(tab['icon'])),
                    selectedIcon: Icon(
                      _getIconData(tab['icon']),
                      size: 28,
                    ),
                    label: Text(tab['text']),
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  );
                }).toList(),
              ),
            ),
          Expanded(
            child: Container(
              color: colorScheme.surface,
              child: TabBarView(
                controller: _tabController,
                children: _tabs.map<Widget>((tab) => _buildTabContent(tab)).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTabIconColor(BuildContext context, String tabId) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (tabId) {
      case 'guide':
        return Colors.blue;
      case 'checklist':
        return Colors.green;
      case 'obligations':
        return Colors.orange;
      default:
        return colorScheme.onSurfaceVariant;
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'book':
        return Icons.book_outlined;
      case 'checklist':
        return Icons.checklist_outlined;
      case 'assignment':
        return Icons.assignment_outlined;
      case 'folder_open':
        return Icons.folder_open_outlined;
      case 'description':
        return Icons.description_outlined;
      case 'calendar_today':
        return Icons.calendar_today_outlined;
      default:
        return Icons.error_outline;
    }
  }
}