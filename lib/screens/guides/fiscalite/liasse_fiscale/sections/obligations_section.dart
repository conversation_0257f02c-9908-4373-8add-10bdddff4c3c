import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../providers/liasse_fiscale_provider.dart';

class ObligationsSection extends ConsumerStatefulWidget {
  const ObligationsSection({super.key});

  @override
  ConsumerState<ObligationsSection> createState() => _ObligationsSectionState();
}

class _ObligationsSectionState extends ConsumerState<ObligationsSection>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedMonth = DateTime.now().month;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final obligationsAsync = ref.watch(obligationsDataProvider);
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return obligationsAsync.when(
      loading: () => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: colorScheme.primary),
            const SizedBox(height: 16),
            Text(
              'Chargement des obligations fiscales...',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Erreur de chargement',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            FilledButton.icon(
              onPressed: () => ref.refresh(obligationsDataProvider),
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
            ),
          ],
        ),
      ),
      data: (data) => Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Obligations Fiscales',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Calendrier, pénalités et procédures de conformité',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),

          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              labelColor: colorScheme.primary,
              unselectedLabelColor: colorScheme.onSurfaceVariant,
              indicatorColor: colorScheme.primary,
              indicatorWeight: 3,
              tabs: const [
                Tab(
                  icon: Icon(Icons.calendar_month),
                  text: 'Calendrier',
                ),
                Tab(
                  icon: Icon(Icons.warning_amber),
                  text: 'Pénalités',
                ),
                Tab(
                  icon: Icon(Icons.checklist),
                  text: 'Conformité',
                ),
                Tab(
                  icon: Icon(Icons.folder_outlined),
                  text: 'Archives',
                ),
                Tab(
                  icon: Icon(Icons.edit_document),
                  text: 'Rectifications',
                ),
                Tab(
                  icon: Icon(Icons.support_agent),
                  text: 'Support',
                ),
              ],
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCalendarTab(data, colorScheme, textTheme),
                _buildPenaltiesTab(data, colorScheme, textTheme),
                _buildComplianceTab(data, colorScheme, textTheme),
                _buildRecordKeepingTab(data, colorScheme, textTheme),
                _buildAmendmentTab(data, colorScheme, textTheme),
                _buildSupportTab(data, colorScheme, textTheme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarTab(
    dynamic data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    final months = [
      'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
      'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Month Selector
          Container(
            height: 60,
            margin: const EdgeInsets.only(bottom: 20),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 12,
              itemBuilder: (context, index) {
                final isSelected = _selectedMonth == index + 1;
                return GestureDetector(
                  onTap: () => setState(() => _selectedMonth = index + 1),
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? colorScheme.primary
                          : colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? colorScheme.primary
                            : colorScheme.outlineVariant,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        months[index],
                        style: textTheme.labelLarge?.copyWith(
                          color: isSelected
                              ? colorScheme.onPrimary
                              : colorScheme.onSurfaceVariant,
                          fontWeight: isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Calendar Events for Selected Month
          _buildMonthObligations(_selectedMonth, colorScheme, textTheme),

          const SizedBox(height: 20),

          // Annual Overview
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.event_note,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Échéances Annuelles Principales',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildAnnualDeadline(
                    'Liasse Fiscale',
                    '31 mars',
                    'Dépôt des états financiers annuels',
                    Icons.description,
                    colorScheme,
                    textTheme,
                  ),
                  _buildAnnualDeadline(
                    'Déclaration IS',
                    '31 mars',
                    'Déclaration annuelle de l\'impôt sur les sociétés',
                    Icons.account_balance,
                    colorScheme,
                    textTheme,
                  ),
                  _buildAnnualDeadline(
                    'Assemblée Générale',
                    '30 juin',
                    'Approbation des comptes annuels',
                    Icons.groups,
                    colorScheme,
                    textTheme,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthObligations(
    int month,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    // Sample obligations data - in real implementation, this would come from JSON
    final obligations = _getObligationsForMonth(month);

    return Column(
      children: obligations.map((obligation) {
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 1,
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getPriorityColor(obligation['priority'], colorScheme),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getObligationIcon(obligation['type']),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(
              obligation['title'],
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(obligation['description']),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Échéance: ${obligation['deadline']}',
                      style: textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: colorScheme.onSurfaceVariant,
            ),
            onTap: () => _showObligationDetails(obligation),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAnnualDeadline(
    String title,
    String deadline,
    String description,
    IconData icon,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.outlineVariant,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              deadline,
              style: textTheme.labelMedium?.copyWith(
                color: colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPenaltiesTab(
    dynamic data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Penalty Calculator
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.calculate,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Calculateur de Pénalités',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPenaltyCalculator(colorScheme, textTheme),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Penalty Structure
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Structure des Pénalités',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildPenaltyStructure(colorScheme, textTheme),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Amnesty Programs
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.policy,
                        color: colorScheme.tertiary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Programmes d\'Amnistie',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.tertiary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildAmnestyPrograms(colorScheme, textTheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComplianceTab(
    dynamic data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Compliance Checklist
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.checklist_rtl,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Liste de Conformité',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildComplianceChecklist(colorScheme, textTheme),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Ongoing Obligations
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Obligations Permanentes',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildOngoingObligations(colorScheme, textTheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordKeepingTab(
    dynamic data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Document Retention
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.archive,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Conservation des Documents',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildRetentionRequirements(colorScheme, textTheme),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Organization Tips
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.tips_and_updates,
                        color: colorScheme.tertiary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Conseils d\'Organisation',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.tertiary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildOrganizationTips(colorScheme, textTheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmendmentTab(
    dynamic data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Amendment Process
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.edit_document,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Procédure de Rectification',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildAmendmentProcess(colorScheme, textTheme),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Special Situations
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Situations Particulières',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildSpecialSituations(colorScheme, textTheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSupportTab(
    dynamic data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // When to Seek Help
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.support_agent,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Quand Faire Appel à un Professionnel',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildProfessionalSupport(colorScheme, textTheme),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Cost Considerations
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.monetization_on,
                        color: colorScheme.tertiary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Considérations de Coût',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.tertiary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildCostConsiderations(colorScheme, textTheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for building specific content sections
  Widget _buildPenaltyCalculator(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorScheme.outlineVariant),
      ),
      child: Column(
        children: [
          Text(
            'Calculez vos pénalités de retard',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    labelText: 'Montant dû (DH)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    labelText: 'Jours de retard',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          FilledButton(
            onPressed: () {
              // Calculate penalty logic
            },
            child: const Text('Calculer'),
          ),
        ],
      ),
    );
  }

  Widget _buildPenaltyStructure(ColorScheme colorScheme, TextTheme textTheme) {
    final penalties = [
      {
        'type': 'Retard de déclaration',
        'rate': '15% minimum 500 DH',
        'description': 'Pénalité pour dépôt tardif de la liasse fiscale',
      },
      {
        'type': 'Défaut de déclaration',
        'rate': '25% minimum 1000 DH',
        'description': 'Absence totale de déclaration',
      },
      {
        'type': 'Insuffisance de déclaration',
        'rate': '15%',
        'description': 'Sous-estimation du montant dû',
      },
    ];

    return Column(
      children: penalties.map((penalty) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorScheme.errorContainer.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: colorScheme.error.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    penalty['type']!,
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: colorScheme.error,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      penalty['rate']!,
                      style: textTheme.labelSmall?.copyWith(
                        color: colorScheme.onError,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                penalty['description']!,
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAmnestyPrograms(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.tertiaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.tertiary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: colorScheme.tertiary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Programme d\'Amnistie 2025',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.tertiary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Remise des pénalités et majorations pour les déclarations spontanées avant le 31 décembre 2025.',
            style: textTheme.bodyMedium,
          ),
          const SizedBox(height: 12),
          FilledButton.tonal(
            onPressed: () {
              // Show amnesty details
            },
            child: const Text('En savoir plus'),
          ),
        ],
      ),
    );
  }

  Widget _buildComplianceChecklist(ColorScheme colorScheme, TextTheme textTheme) {
    final checklistItems = [
      'Tenue régulière de la comptabilité',
      'Archivage des pièces justificatives',
      'Déclarations mensuelles TVA à jour',
      'Versements des acomptes IS',
      'Mise à jour du registre du commerce',
      'Conservation des contrats et factures',
    ];

    return Column(
      children: checklistItems.map((item) {
        return CheckboxListTile(
          title: Text(item),
          value: false, // In real implementation, this would be managed by state
          onChanged: (value) {
            // Handle checkbox state change
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        );
      }).toList(),
    );
  }

  Widget _buildOngoingObligations(ColorScheme colorScheme, TextTheme textTheme) {
    final obligations = [
      {
        'title': 'Comptabilité',
        'frequency': 'Quotidienne',
        'description': 'Enregistrement des opérations',
      },
      {
        'title': 'TVA',
        'frequency': 'Mensuelle',
        'description': 'Déclaration et paiement',
      },
      {
        'title': 'Acomptes IS',
        'frequency': 'Trimestrielle',
        'description': 'Versements provisionnels',
      },
    ];

    return Column(
      children: obligations.map((obligation) {
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: colorScheme.primaryContainer,
            child: Icon(
              Icons.schedule,
              color: colorScheme.onPrimaryContainer,
              size: 20,
            ),
          ),
          title: Text(
            obligation['title']!,
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(obligation['description']!),
          trailing: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: colorScheme.secondaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              obligation['frequency']!,
              style: textTheme.labelSmall?.copyWith(
                color: colorScheme.onSecondaryContainer,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRetentionRequirements(ColorScheme colorScheme, TextTheme textTheme) {
    final requirements = [
      {
        'document': 'Livres comptables',
        'period': '10 ans',
        'note': 'À partir de la clôture de l\'exercice',
      },
      {
        'document': 'Pièces justificatives',
        'period': '10 ans',
        'note': 'Factures, contrats, relevés bancaires',
      },
      {
        'document': 'Déclarations fiscales',
        'period': '4 ans',
        'note': 'Délai de prescription fiscale',
      },
    ];

    return Column(
      children: requirements.map((req) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: colorScheme.outlineVariant),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  req['document']!,
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      req['period']!,
                      style: textTheme.labelMedium?.copyWith(
                        color: colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  req['note']!,
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildOrganizationTips(ColorScheme colorScheme, TextTheme textTheme) {
    final tips = [
      'Classement chronologique des documents',
      'Numération séquentielle des pièces',
      'Sauvegarde numérique sécurisée',
      'Index des documents par catégorie',
      'Vérification périodique de l\'archivage',
    ];

    return Column(
      children: tips.map((tip) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: colorScheme.tertiary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  tip,
                  style: textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAmendmentProcess(ColorScheme colorScheme, TextTheme textTheme) {
    final steps = [
      'Identification de l\'erreur',
      'Préparation des documents rectificatifs',
      'Dépôt de la déclaration rectificative',
      'Paiement des différences éventuelles',
      'Suivi de la procédure',
    ];

    return Column(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        final isLast = index == steps.length - 1;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                CircleAvatar(
                  radius: 12,
                  backgroundColor: colorScheme.primary,
                  child: Text(
                    '${index + 1}',
                    style: textTheme.labelSmall?.copyWith(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 40,
                    color: colorScheme.outlineVariant,
                  ),
              ],
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(bottom: isLast ? 0 : 24),
                child: Text(
                  step,
                  style: textTheme.bodyLarge,
                ),
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildSpecialSituations(ColorScheme colorScheme, TextTheme textTheme) {
    final situations = [
      {
        'title': 'Fusion/Acquisition',
        'description': 'Procédures spéciales pour les restructurations',
        'icon': Icons.merge_type,
      },
      {
        'title': 'Cessation d\'activité',
        'description': 'Obligations lors de la fermeture',
        'icon': Icons.business_center_outlined,
      },
      {
        'title': 'Changement d\'exercice',
        'description': 'Modification de la date de clôture',
        'icon': Icons.calendar_month,
      },
    ];

    return Column(
      children: situations.map((situation) {
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Icon(
              situation['icon'] as IconData,
              color: colorScheme.primary,
            ),
            title: Text(
              situation['title']! as String,
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(situation['description']! as String),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: colorScheme.onSurfaceVariant,
            ),
            onTap: () {
              // Show detailed information
            },
          ),
        );
      }).toList(),
    );
  }

  Widget _buildProfessionalSupport(ColorScheme colorScheme, TextTheme textTheme) {
    final scenarios = [
      'Première année d\'activité',
      'Opérations complexes (fusion, scission)',
      'Contrôle fiscal en cours',
      'Redressement fiscal',
      'Changement de régime fiscal',
      'Activités internationales',
    ];

    return Column(
      children: scenarios.map((scenario) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.check_circle_outline,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  scenario,
                  style: textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCostConsiderations(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.tertiaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.tertiary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Fourchettes de prix indicatives:',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildCostRange('Liasse fiscale simple', '2 000 - 5 000 DH', colorScheme, textTheme),
          _buildCostRange('Liasse fiscale complexe', '5 000 - 15 000 DH', colorScheme, textTheme),
          _buildCostRange('Conseil fiscal annuel', '10 000 - 30 000 DH', colorScheme, textTheme),
          const SizedBox(height: 12),
          Text(
            'Les tarifs varient selon la taille de l\'entreprise, la complexité des opérations et la région.',
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostRange(String service, String range, ColorScheme colorScheme, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            service,
            style: textTheme.bodyMedium,
          ),
          Text(
            range,
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.tertiary,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  List<Map<String, dynamic>> _getObligationsForMonth(int month) {
    // Sample data - in real implementation, this would come from JSON
    final allObligations = {
      1: [
        {
          'title': 'Déclaration TVA Décembre',
          'description': 'Dépôt de la déclaration TVA du mois précédent',
          'deadline': '20 janvier',
          'type': 'tva',
          'priority': 'high',
        },
      ],
      3: [
        {
          'title': 'Liasse Fiscale',
          'description': 'Dépôt des états financiers annuels',
          'deadline': '31 mars',
          'type': 'annual',
          'priority': 'critical',
        },
        {
          'title': 'Déclaration IS',
          'description': 'Déclaration annuelle de l\'impôt sur les sociétés',
          'deadline': '31 mars',
          'type': 'tax',
          'priority': 'critical',
        },
      ],
      6: [
        {
          'title': 'Assemblée Générale',
          'description': 'Approbation des comptes annuels',
          'deadline': '30 juin',
          'type': 'corporate',
          'priority': 'medium',
        },
      ],
    };

    return allObligations[month] ?? [];
  }

  Color _getPriorityColor(String priority, ColorScheme colorScheme) {
    switch (priority) {
      case 'critical':
        return colorScheme.error;
      case 'high':
        return Colors.orange;
      case 'medium':
        return colorScheme.primary;
      default:
        return colorScheme.secondary;
    }
  }

  IconData _getObligationIcon(String type) {
    switch (type) {
      case 'tva':
        return Icons.receipt;
      case 'annual':
        return Icons.description;
      case 'tax':
        return Icons.account_balance;
      case 'corporate':
        return Icons.groups;
      default:
        return Icons.event;
    }
  }

  void _showObligationDetails(Map<String, dynamic> obligation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(obligation['title']),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(obligation['description']),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  'Échéance: ${obligation['deadline']}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Add to calendar or set reminder
            },
            child: const Text('Ajouter un rappel'),
          ),
        ],
      ),
    );
  }
}