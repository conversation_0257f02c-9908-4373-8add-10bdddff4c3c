import 'package:flutter/material.dart';
import '../../../../../widgets/journal_comptable_widget.dart';

class FrancoScreen extends StatelessWidget {
  const FrancoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Franco de Port'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TweenAnimationBuilder(
              tween: Tween<double>(begin: 0, end: 1),
              duration: const Duration(milliseconds: 800),
              builder: (context, double value, child) {
                return Opacity(
                  opacity: value,
                  child: child,
                );
              },
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Définition',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Le franco de port signifie que les frais de transport sont inclus dans le prix de vente. '
                        'Le fournisseur prend en charge les frais de transport.',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            JournalComptableWidget(
              title: 'Exemple de comptabilisation',
              entries: [
                JournalEntry(
                  date: '01/01/2024',
                  lines: [
                    JournalLine(
                      account: '6111',
                      label: 'Achats de marchandises',
                      debit: '100.000,00',
                    ),
                    JournalLine(
                      account: '34551',
                      label: 'État, TVA récupérable',
                      debit: '20.000,00',
                    ),
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      credit: '120.000,00',
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
