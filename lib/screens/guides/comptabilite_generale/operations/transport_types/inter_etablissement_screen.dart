import 'package:flutter/material.dart';
import '../../../../../widgets/journal_comptable_widget.dart';

class InterEtablissementScreen extends StatelessWidget {
  const InterEtablissementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transport Inter-établissements'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TweenAnimationBuilder(
              tween: Tween<double>(begin: 0, end: 1),
              duration: const Duration(milliseconds: 800),
              builder: (context, double value, child) {
                return Opacity(
                  opacity: value,
                  child: child,
                );
              },
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Définition',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Le transport inter-établissements concerne les mouvements de marchandises '
                        'entre différents sites d\'une même entreprise.',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            JournalComptableWidget(
              title: 'Exemple de comptabilisation',
              entries: [
                JournalEntry(
                  date: '01/01/2024',
                  lines: [
                    JournalLine(
                      account: '6124',
                      label: 'Frais de transport',
                      debit: '6.000,00',
                    ),
                    JournalLine(
                      account: '34551',
                      label: 'État, TVA récupérable',
                      debit: '1.200,00',
                    ),
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      credit: '7.200,00',
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
