import 'package:flutter/material.dart';
import '../../../../../widgets/journal_comptable_widget.dart';

class ForfaitaireScreen extends StatelessWidget {
  const ForfaitaireScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transport Forfaitaire'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TweenAnimationBuilder(
              tween: Tween<double>(begin: 0, end: 1),
              duration: const Duration(milliseconds: 800),
              builder: (context, double value, child) {
                return Opacity(
                  opacity: value,
                  child: child,
                );
              },
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Définition',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Le transport forfaitaire correspond à un prix fixe convenu à l\'avance, '
                        'indépendamment de la distance ou du poids transporté.',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            JournalComptableWidget(
              title: 'Exemple de comptabilisation',
              entries: [
                JournalEntry(
                  date: '01/01/2024',
                  lines: [
                    JournalLine(
                      account: '6124',
                      label: 'Frais de transport',
                      debit: '3.000,00',
                    ),
                    JournalLine(
                      account: '34551',
                      label: 'État, TVA récupérable',
                      debit: '600,00',
                    ),
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      credit: '3.600,00',
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
