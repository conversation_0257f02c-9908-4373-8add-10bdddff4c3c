import 'package:flutter/material.dart';
import '../../../../widgets/journal_comptable_widget.dart';
import '../../../../theme/app_theme.dart';

class EffetsCommerceScreen extends StatefulWidget {
  const EffetsCommerceScreen({super.key});

  @override
  State<EffetsCommerceScreen> createState() => _EffetsCommerceScreenState();
}

class _EffetsCommerceScreenState extends State<EffetsCommerceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _tabs = [
    'Introduction',
    'Déroulement',
    'Escompte',
    'Renouvellement',
    'Exercices'
  ];

  // Ajout d'une variable pour gérer l'affichage des solutions
  final Map<String, bool> _showSolutions = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return [
            SliverAppBar(
              title: Text(
                'Effets de Commerce',
                style: textTheme.titleLarge?.copyWith(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
              pinned: true,
              floating: true,
              elevation: 0,
              flexibleSpace: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primaryContainer,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                isScrollable: true,
                labelStyle: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                unselectedLabelStyle: textTheme.titleMedium,
                labelColor: colorScheme.onPrimary,
                unselectedLabelColor: colorScheme.onPrimary.withValues(alpha: 0.7),
                indicatorColor: colorScheme.onPrimary,
                indicatorWeight: 3,
                tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
              ),
            ),
          ];
        },
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                colorScheme.surface,
                colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              ],
            ),
          ),
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildIntroductionSection(context),
              _buildDeroulementSection(context),
              _buildEscompteSection(context),
              _buildRenouvellementSection(context),
              _buildExercicesSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedCard({
    required IconData icon,
    required String title,
    required String content,
    required Color color,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return TweenAnimationBuilder(
      tween: Tween<double>(begin: 0, end: 1),
      duration: const Duration(milliseconds: 500),
      builder: (context, double value, child) {
        return Transform.scale(
          scale: value,
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color.withValues(alpha: 0.1),
                    colorScheme.surface,
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(icon, color: color, size: 28),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          title,
                          style: textTheme.titleMedium?.copyWith(
                            color: color,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    content,
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildExpandableCard({
    required String title,
    required IconData icon,
    required Widget content,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          backgroundColor: colorScheme.surface,
          collapsedBackgroundColor: colorScheme.surface,
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: colorScheme.primary, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(16),
                ),
              ),
              child: content,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlowDiagram({
    required List<String> steps,
    required List<String> descriptions,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      children: List.generate(steps.length * 2 - 1, (index) {
        if (index.isEven) {
          final stepIndex = index ~/ 2;
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Text(
                  steps[stepIndex],
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  descriptions[stepIndex],
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onPrimaryContainer,
                      ),
                ),
              ],
            ),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Icon(
              Icons.arrow_downward,
              color: colorScheme.primary,
            ),
          );
        }
      }),
    );
  }

  Widget _buildProcessCard({
    required String title,
    required List<ProcessStep> steps,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: AppTheme.getCardDecoration(colorScheme),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          ...List.generate(steps.length * 2 - 1, (index) {
            if (index.isEven) {
              final step = steps[index ~/ 2];
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: colorScheme.primary,
                        child: Text(
                          '${(index ~/ 2) + 1}',
                          style: TextStyle(
                            color: colorScheme.onPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(
                        step.title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      subtitle: Text(
                        step.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                      ),
                    ),
                  ),
                  if (step.journalEntry != null) ...[
                    const SizedBox(height: 16),
                    JournalComptableWidget(
                      title: 'Écriture comptable',
                      entries: [step.journalEntry!],
                    ),
                  ],
                ],
              );
            } else {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Icon(
                  Icons.arrow_downward,
                  color: colorScheme.primary,
                ),
              );
            }
          }),
        ],
      ),
    );
  }

  Widget _buildIntroductionSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildAnimatedCard(
            icon: Icons.info_outline,
            title: 'Qu\'est-ce qu\'un effet de commerce ?',
            content:
                'Un effet de commerce est un document qui représente une promesse de paiement. '
                'C\'est un moyen de paiement et de crédit utilisé dans le monde des affaires.',
            color: colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Container(
            decoration: AppTheme.getCardDecoration(colorScheme),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.category, color: colorScheme.primary),
                    const SizedBox(width: 8),
                    Text(
                      'Types d\'effets de commerce',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildTypeCard(
                  context,
                  'La Lettre de Change',
                  'Un ordre de paiement donné par un créancier (tireur) à son débiteur (tiré)',
                  [
                    'Tireur (Créancier) → Émet la lettre',
                    'Tiré (Débiteur) → Accepte et s\'engage à payer',
                    'Bénéficiaire → Reçoit le paiement'
                  ],
                  Icons.description,
                ),
                const SizedBox(height: 16),
                _buildTypeCard(
                  context,
                  'Le Billet à Ordre',
                  'Un engagement direct de paiement pris par le débiteur',
                  [
                    'Souscripteur → S\'engage à payer',
                    'Bénéficiaire → Reçoit le paiement'
                  ],
                  Icons.note,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          _buildAnimatedCard(
            icon: Icons.account_balance,
            title: 'Rôle dans l\'entreprise',
            content: 'Les effets de commerce permettent aux entreprises de :\n'
                '• Gérer leur trésorerie\n'
                '• Obtenir des délais de paiement\n'
                '• Mobiliser leurs créances avant l\'échéance',
            color: colorScheme.secondary,
          ),
        ],
      ),
    );
  }

  Widget _buildTypeCard(
    BuildContext context,
    String title,
    String description,
    List<String> steps,
    IconData icon,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
          ),
          const SizedBox(height: 8),
          ...steps.map((step) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(
                      Icons.arrow_right,
                      color: colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        step,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface,
                            ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildDeroulementSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Création
          _buildProcessCard(
            title: 'Création de l\'effet de commerce',
            steps: [
              ProcessStep(
                title: 'Émission de l\'effet',
                description: 'Le créancier émet l\'effet de commerce',
                journalEntry: JournalEntry(
                  date: '01/01/2024',
                  lines: [
                    JournalLine(
                      account: '3425',
                      label: 'Clients - Effets à recevoir',
                      debit: '10.000,00',
                    ),
                    JournalLine(
                      account: '3421',
                      label: 'Clients',
                      credit: '10.000,00',
                    ),
                  ],
                ),
              ),
              ProcessStep(
                title: 'Acceptation',
                description: 'Le débiteur accepte l\'effet de commerce',
                journalEntry: null,
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Encaissement normal
          _buildProcessCard(
            title: 'Encaissement normal',
            steps: [
              ProcessStep(
                title: 'Remise à l\'encaissement',
                description: 'L\'effet est remis à la banque pour encaissement',
                journalEntry: JournalEntry(
                  date: '15/01/2024',
                  lines: [
                    JournalLine(
                      account: '3427',
                      label: 'Clients - Effets à l\'encaissement',
                      debit: '10.000,00',
                    ),
                    JournalLine(
                      account: '3425',
                      label: 'Clients - Effets à recevoir',
                      credit: '10.000,00',
                    ),
                  ],
                ),
              ),
              ProcessStep(
                title: 'Encaissement à l\'échéance',
                description: 'La banque crédite le compte à l\'échéance',
                journalEntry: JournalEntry(
                  date: '15/02/2024',
                  lines: [
                    JournalLine(
                      account: '5141',
                      label: 'Banques',
                      debit: '10.000,00',
                    ),
                    JournalLine(
                      account: '3427',
                      label: 'Clients - Effets à l\'encaissement',
                      credit: '10.000,00',
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Endossement
          _buildProcessCard(
            title: 'Endossement de l\'effet',
            steps: [
              ProcessStep(
                title: 'Endossement au fournisseur',
                description:
                    'L\'effet est transmis à un fournisseur en règlement',
                journalEntry: JournalEntry(
                  date: '20/01/2024',
                  lines: [
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      debit: '10.000,00',
                    ),
                    JournalLine(
                      account: '3425',
                      label: 'Clients - Effets à recevoir',
                      credit: '10.000,00',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRenouvellementSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildAnimatedCard(
            icon: Icons.update,
            title: 'Le renouvellement d\'effet',
            content:
                'Le renouvellement intervient quand le débiteur ne peut pas payer à l\'échéance. '
                'Il demande un délai supplémentaire moyennant des frais et intérêts.',
            color: colorScheme.primary,
          ),
          const SizedBox(height: 16),
          _buildProcessCard(
            title: 'Processus de renouvellement',
            steps: [
              ProcessStep(
                title: 'Annulation de l\'ancien effet',
                description: 'L\'ancien effet est annulé',
                journalEntry: JournalEntry(
                  date: '01/03/2024',
                  lines: [
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      debit: '10.000,00',
                    ),
                    JournalLine(
                      account: '4415',
                      label: 'Fournisseurs - Effets à payer',
                      credit: '10.000,00',
                    ),
                  ],
                ),
              ),
              ProcessStep(
                title: 'Comptabilisation des frais',
                description:
                    'Enregistrement des intérêts et frais de renouvellement',
                journalEntry: JournalEntry(
                  date: '01/03/2024',
                  lines: [
                    JournalLine(
                      account: '6311',
                      label: 'Intérêts des emprunts',
                      debit: '200,00',
                    ),
                    JournalLine(
                      account: '34552',
                      label: 'État - TVA récupérable',
                      debit: '40,00',
                    ),
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      credit: '240,00',
                    ),
                  ],
                ),
              ),
              ProcessStep(
                title: 'Création du nouvel effet',
                description: 'Un nouvel effet est créé incluant les frais',
                journalEntry: JournalEntry(
                  date: '01/03/2024',
                  lines: [
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      debit: '10.240,00',
                    ),
                    JournalLine(
                      account: '4415',
                      label: 'Fournisseurs - Effets à payer',
                      credit: '10.240,00',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEscompteSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildAnimatedCard(
            icon: Icons.calculate,
            title: 'L\'escompte et l\'agio bancaire',
            content:
                'L\'escompte permet de mobiliser une créance avant son échéance. '
                'L\'agio est le coût total de l\'opération d\'escompte.',
            color: colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Container(
            decoration: AppTheme.getCardDecoration(colorScheme),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.functions, color: colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        'Calcul de l\'agio',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: colorScheme.onPrimaryContainer,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'L\'agio comprend :',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleSmall
                                  ?.copyWith(
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            _buildAgioComponent(
                              context,
                              '• L\'escompte = (Montant × Taux × Jours) / 36000',
                              colorScheme,
                            ),
                            _buildAgioComponent(
                              context,
                              '• Commission bancaire',
                              colorScheme,
                            ),
                            _buildAgioComponent(
                              context,
                              '• TVA sur l\'escompte et la commission',
                              colorScheme,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color:
                              colorScheme.secondaryContainer.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: colorScheme.secondary.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Exemple de calcul :',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleSmall
                                  ?.copyWith(
                                    color: colorScheme.secondary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            const SizedBox(height: 12),
                            _buildCalculStep(
                              context,
                              'Pour un effet de 10.000 DH à 60 jours avec :',
                              colorScheme,
                            ),
                            _buildCalculStep(
                              context,
                              '• Taux d\'escompte : 6%',
                              colorScheme,
                            ),
                            _buildCalculStep(
                              context,
                              '• Commission : 20 DH',
                              colorScheme,
                            ),
                            _buildCalculStep(
                              context,
                              '• TVA : 10%',
                              colorScheme,
                            ),
                            const SizedBox(height: 12),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: colorScheme.surface,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: colorScheme.outline.withValues(alpha: 0.2),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildCalculResult(
                                    context,
                                    'Escompte = (10.000 × 6 × 60) / 36000 = 100 DH',
                                    colorScheme,
                                  ),
                                  _buildCalculResult(
                                    context,
                                    'Commission = 20 DH',
                                    colorScheme,
                                  ),
                                  _buildCalculResult(
                                    context,
                                    'TVA = (100 + 20) × 10% = 12 DH',
                                    colorScheme,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Agio total = 132 DH',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(
                                          color: colorScheme.primary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          _buildProcessCard(
            title: 'Comptabilisation de l\'escompte',
            steps: [
              ProcessStep(
                title: 'Remise à l\'escompte',
                description: 'Enregistrement de l\'opération d\'escompte',
                journalEntry: JournalEntry(
                  date: '01/02/2024',
                  lines: [
                    JournalLine(
                      account: '5141',
                      label: 'Banques',
                      debit: '9.868,00',
                    ),
                    JournalLine(
                      account: '6311',
                      label: 'Intérêts des emprunts',
                      debit: '120,00',
                    ),
                    JournalLine(
                      account: '3425',
                      label: 'Clients - Effets à recevoir',
                      credit: '10.000,00',
                    ),
                    JournalLine(
                      account: '34552',
                      label: 'État - TVA récupérable',
                      debit: '12,00',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAgioComponent(
      BuildContext context, String text, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onPrimaryContainer,
              height: 1.5,
            ),
      ),
    );
  }

  Widget _buildCalculStep(
      BuildContext context, String text, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSecondaryContainer,
              height: 1.5,
            ),
      ),
    );
  }

  Widget _buildCalculResult(
      BuildContext context, String text, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
              height: 1.5,
            ),
      ),
    );
  }

  Widget _buildExercicesSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildAnimatedCard(
            icon: Icons.school,
            title: 'Exercices pratiques',
            content:
                'Une série d\'exercices pour maîtriser les effets de commerce',
            color: colorScheme.primary,
          ),
          const SizedBox(height: 16),
          _buildExerciceComplet(
            context,
            'Exercice 1 : Création et escompte',
            'Le 01/03/2024, l\'entreprise ABC tire une lettre de change de 20.000 DH sur son client XYZ, '
                'échéance 60 jours. Le 15/03, elle remet l\'effet à l\'escompte (taux 7%, commission 30 DH, TVA 10%).',
            'Comptabiliser toutes les écritures.',
            [
              SolutionStep(
                title: '1. Création de la lettre de change (01/03/2024)',
                journalEntry: JournalEntry(
                  date: '01/03/2024',
                  lines: [
                    JournalLine(
                      account: '3425',
                      label: 'Clients - Effets à recevoir',
                      debit: '20.000,00',
                    ),
                    JournalLine(
                      account: '3421',
                      label: 'Clients',
                      credit: '20.000,00',
                    ),
                  ],
                ),
              ),
              SolutionStep(
                title: '2. Calcul de l\'agio',
                explanation: '''
• Nombre de jours : du 15/03 au 30/04 = 46 jours
• Escompte = (20.000 × 7 × 46) / 36000 = 179,67 DH
• Commission = 30 DH
• TVA = (179,67 + 30) × 10% = 20,97 DH
• Total agio = 230,64 DH''',
              ),
              SolutionStep(
                title: '3. Remise à l\'escompte (15/03/2024)',
                journalEntry: JournalEntry(
                  date: '15/03/2024',
                  lines: [
                    JournalLine(
                      account: '5141',
                      label: 'Banques',
                      debit: '19.769,36',
                    ),
                    JournalLine(
                      account: '6311',
                      label: 'Intérêts des emprunts',
                      debit: '209,67',
                    ),
                    JournalLine(
                      account: '34552',
                      label: 'État - TVA récupérable',
                      debit: '20,97',
                    ),
                    JournalLine(
                      account: '3425',
                      label: 'Clients - Effets à recevoir',
                      credit: '20.000,00',
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildExerciceComplet(
            context,
            'Exercice 2 : Renouvellement avec frais',
            'Un effet de 15.000 DH arrive à échéance le 30/04/2024. Le débiteur demande un délai '
                'supplémentaire de 30 jours avec des intérêts de 8% et des frais de 50 DH HT.',
            'Enregistrer les écritures de renouvellement.',
            [
              SolutionStep(
                title: '1. Calcul des intérêts et frais',
                explanation: '''
• Intérêts = (15.000 × 8 × 30) / 36000 = 100 DH
• Frais HT = 50 DH
• TVA (20%) = (100 + 50) × 20% = 30 DH
• Total = 180 DH''',
              ),
              SolutionStep(
                title: '2. Annulation de l\'ancien effet',
                journalEntry: JournalEntry(
                  date: '30/04/2024',
                  lines: [
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      debit: '15.000,00',
                    ),
                    JournalLine(
                      account: '4415',
                      label: 'Fournisseurs - Effets à payer',
                      credit: '15.000,00',
                    ),
                  ],
                ),
              ),
              SolutionStep(
                title: '3. Comptabilisation des frais',
                journalEntry: JournalEntry(
                  date: '30/04/2024',
                  lines: [
                    JournalLine(
                      account: '6311',
                      label: 'Intérêts des emprunts',
                      debit: '150,00',
                    ),
                    JournalLine(
                      account: '34552',
                      label: 'État - TVA récupérable',
                      debit: '30,00',
                    ),
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      credit: '180,00',
                    ),
                  ],
                ),
              ),
              SolutionStep(
                title: '4. Création du nouvel effet',
                journalEntry: JournalEntry(
                  date: '30/04/2024',
                  lines: [
                    JournalLine(
                      account: '4411',
                      label: 'Fournisseurs',
                      debit: '15.180,00',
                    ),
                    JournalLine(
                      account: '4415',
                      label: 'Fournisseurs - Effets à payer',
                      credit: '15.180,00',
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildExerciceComplet(
            context,
            'Exercice 3 : Cas de synthèse',
            '''La société ALPHA réalise les opérations suivantes :
1. 05/01/2024 : Création d'une lettre de change de 30.000 DH sur le client BETA
2. 10/01/2024 : Remise à l'encaissement de la lettre
3. 15/01/2024 : Réception d'une lettre de change de 25.000 DH du fournisseur GAMMA
4. 20/01/2024 : Endossement de la lettre reçue au profit du fournisseur DELTA
5. 25/01/2024 : Remise à l'escompte d'un effet de 18.000 DH (taux 6%, commission 40 DH, TVA 20%)''',
            'Enregistrer toutes les écritures comptables.',
            [
              SolutionStep(
                title: '1. Création LC sur BETA',
                journalEntry: JournalEntry(
                  date: '05/01/2024',
                  lines: [
                    JournalLine(
                      account: '3425',
                      label: 'Clients - Effets à recevoir',
                      debit: '30.000,00',
                    ),
                    JournalLine(
                      account: '3421',
                      label: 'Clients',
                      credit: '30.000,00',
                    ),
                  ],
                ),
              ),
              SolutionStep(
                title: '2. Remise à l\'encaissement',
                journalEntry: JournalEntry(
                  date: '10/01/2024',
                  lines: [
                    JournalLine(
                      account: '3427',
                      label: 'Clients - Effets à l\'encaissement',
                      debit: '30.000,00',
                    ),
                    JournalLine(
                      account: '3425',
                      label: 'Clients - Effets à recevoir',
                      credit: '30.000,00',
                    ),
                  ],
                ),
              ),
              // ... autres étapes de la solution
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExerciceComplet(
    BuildContext context,
    String title,
    String enonce,
    String consigne,
    List<SolutionStep> solutions,
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: AppTheme.getCardDecoration(colorScheme),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.assignment, color: colorScheme.primary),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Énoncé :',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  enonce,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Travail à faire :',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  consigne,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                ),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _showSolutions[title] =
                            !(_showSolutions[title] ?? false);
                      });
                    },
                    icon: Icon(
                      _showSolutions[title] ?? false
                          ? Icons.visibility_off
                          : Icons.visibility,
                      color: colorScheme.onPrimary,
                    ),
                    label: Text(
                      _showSolutions[title] ?? false
                          ? 'Masquer la solution'
                          : 'Afficher la solution',
                      style: TextStyle(color: colorScheme.onPrimary),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
                if (_showSolutions[title] ?? false) ...[
                  const SizedBox(height: 24),
                  Container(
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: solutions.map((step) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              step.title,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleSmall
                                  ?.copyWith(
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            if (step.explanation != null) ...[
                              const SizedBox(height: 8),
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: colorScheme.surface,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: colorScheme.outline.withValues(alpha: 0.2),
                                  ),
                                ),
                                child: Text(
                                  step.explanation!,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: colorScheme.onSurface,
                                        height: 1.5,
                                      ),
                                ),
                              ),
                            ],
                            if (step.journalEntry != null) ...[
                              const SizedBox(height: 12),
                              JournalComptableWidget(
                                title: '',
                                entries: [step.journalEntry!],
                              ),
                            ],
                            const SizedBox(height: 16),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ProcessStep {
  final String title;
  final String description;
  final JournalEntry? journalEntry;

  ProcessStep({
    required this.title,
    required this.description,
    this.journalEntry,
  });
}

class SolutionStep {
  final String title;
  final String? explanation;
  final JournalEntry? journalEntry;

  SolutionStep({
    required this.title,
    this.explanation,
    this.journalEntry,
  });
}
