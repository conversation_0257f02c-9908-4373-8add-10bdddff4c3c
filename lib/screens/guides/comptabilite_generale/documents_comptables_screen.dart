import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../services/theme_service.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/financial_statements/bilan_widget.dart';
import '../../../widgets/financial_statements/cpc_widget.dart';
import '../../../widgets/financial_statements/balance_widget.dart';
import '../../../widgets/financial_statements/grand_livre_widget.dart';
import '../../../widgets/financial_statements/t_account_widget.dart';
import '../../../widgets/financial_statements/document_details_widget.dart';

class DocumentsComptablesScreen extends StatefulWidget {
  const DocumentsComptablesScreen({super.key});

  @override
  State<DocumentsComptablesScreen> createState() =>
      _DocumentsComptablesScreenState();
}

class _DocumentsComptablesScreenState extends State<DocumentsComptablesScreen> {
  Map<String, dynamic>? _data;
  final DateTime _currentDate = DateTime(2024, 12, 31);

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final jsonString = await rootBundle
          .loadString('assets/compta_generale/documents_comptables.json');
      setState(() {
        _data = json.decode(jsonString);
      });
    } catch (e) {
      debugPrint('Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    if (_data == null) {
      return Scaffold(
        backgroundColor: colorScheme.surface,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'Chargement des données...',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Documents Comptables',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: isDark ? 0 : 2,
        surfaceTintColor: Colors.transparent,
      ),
      body: Container(
        decoration: AppTheme.getGradientDecoration(colorScheme),
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildHeader(context),
              _buildContent(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDark
              ? [
                  colorScheme.primary.withValues(alpha: 0.8),
                  colorScheme.primaryContainer.withValues(alpha: 0.6),
                ]
              : [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: isDark ? 0.05 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Documents Essentiels',
            style: textTheme.headlineMedium?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _data!['description'],
            style: textTheme.bodyLarge?.copyWith(
              color: colorScheme.onPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(32),
          topRight: Radius.circular(32),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: isDark ? 0.05 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _data!['documents'].map<Widget>((document) {
          return _buildDocumentCard(context, document);
        }).toList(),
      ),
    );
  }

  Widget _buildDocumentCard(
      BuildContext context, Map<String, dynamic> document) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outlineVariant,
          width: 1,
        ),
      ),
      surfaceTintColor: colorScheme.surfaceTint,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitle(
                    context, document['title'], Icons.description_outlined),
                const SizedBox(height: 16),
                DocumentDetailsWidget(
                  document: DocumentDetails(
                    items: [
                      DocumentDetailsItem(
                        title: '',
                        content: document['description'] ?? '',
                      ),
                      if (document['structure'] != null)
                        ...((document['structure'] as Map<String, dynamic>)
                            .entries
                            .map((entry) => DocumentDetailsItem(
                                  title: entry.key,
                                  content: (entry.value as List).join('\n'),
                                  isStructuredList: entry.key
                                          .toLowerCase()
                                          .contains('colonnes') ||
                                      entry.key
                                          .toLowerCase()
                                          .contains('éléments') ||
                                      entry.key
                                          .toLowerCase()
                                          .contains('elements'),
                                ))),
                      if (document['tips'] != null)
                        ...(document['tips'] as List)
                            .map((tip) => DocumentDetailsItem(
                                  title: '',
                                  content: tip.toString(),
                                )),
                      if (document['erreurs_courantes'] != null)
                        ...(document['erreurs_courantes'] as List)
                            .map((erreur) => DocumentDetailsItem(
                                  title: '',
                                  content: erreur.toString(),
                                )),
                      if (document['methode_elaboration'] != null)
                        ...(document['methode_elaboration'] as List)
                            .map((methode) => DocumentDetailsItem(
                                  title: '',
                                  content: methode.toString(),
                                )),
                      if (document['caractéristiques'] != null)
                        ...(document['caractéristiques'] as List)
                            .map((carac) => DocumentDetailsItem(
                                  title: '',
                                  content: carac.toString(),
                                )),
                    ],
                  ),
                ),
                if (document.containsKey('exemple')) ...[
                  const SizedBox(height: 24),
                  _buildExampleWidget(context, document),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title, IconData icon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color:
            colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.3 : 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 20,
            color: colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExampleWidget(
      BuildContext context, Map<String, dynamic> document) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest
                .withValues(alpha: isDark ? 0.3 : 0.5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.code,
                size: 20,
                color: colorScheme.secondary,
              ),
              const SizedBox(width: 8),
              Text(
                'Exemple',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.secondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: colorScheme.outlineVariant,
            ),
          ),
          child: _buildFinancialWidget(document),
        ),
      ],
    );
  }

  Widget _buildFinancialWidget(Map<String, dynamic> document) {
    final exemple = document['exemple'] as Map<String, dynamic>;

    switch (document['widget']) {
      case 'BilanWidget':
        final actifMap = <String, List<Map<String, dynamic>>>{};
        final passifMap = <String, List<Map<String, dynamic>>>{};

        // Transform actif data structure
        final actifData = exemple['actif'] as Map<String, dynamic>;
        actifData.forEach((key, value) {
          actifMap[key] = (value as List)
              .map((item) => {
                    'rubrique': item['rubrique'] as String,
                    'montant': (item['montant'] as num).toDouble(),
                  })
              .toList();
        });

        // Transform passif data structure
        final passifData = exemple['passif'] as Map<String, dynamic>;
        passifData.forEach((key, value) {
          passifMap[key] = (value as List)
              .map((item) => {
                    'rubrique': item['rubrique'] as String,
                    'montant': (item['montant'] as num).toDouble(),
                  })
              .toList();
        });

        return BilanWidget(
          actif: actifMap,
          passif: passifMap,
          title: 'Bilan au 31/12/2024',
          date: _currentDate,
        );
      case 'CPCWidget':
        return CPCWidget(
          produits: (exemple['produits'] as List)
              .map((item) => {
                    'rubrique': item['rubrique'] as String,
                    'montant': (item['montant'] as num).toDouble(),
                  })
              .toList(),
          charges: (exemple['charges'] as List)
              .map((item) => {
                    'rubrique': item['rubrique'] as String,
                    'montant': (item['montant'] as num).toDouble(),
                  })
              .toList(),
          title: 'CPC Exercice 2024',
          date: _currentDate,
        );
      case 'BalanceWidget':
        return BalanceWidget(
          comptes: (exemple['comptes'] as List)
              .map((compte) => {
                    'numero': compte['numero'] as String,
                    'intitule': compte['intitule'] as String,
                    'mouvements_debit':
                        (compte['mouvements_debit'] as num).toDouble(),
                    'mouvements_credit':
                        (compte['mouvements_credit'] as num).toDouble(),
                    'solde_debit': (compte['solde_debit'] as num).toDouble(),
                    'solde_credit': (compte['solde_credit'] as num).toDouble(),
                  })
              .toList(),
          title: 'Balance au 31/12/2024',
          date: _currentDate,
        );
      case 'GrandLivreWidget':
        final entries = <String, List<GrandLivreEntry>>{};
        final comptes = exemple['comptes'] as Map<String, dynamic>;
        comptes.forEach((key, value) {
          entries[key] = (value as List)
              .map((entry) => GrandLivreEntry(
                    date: entry['date'],
                    pieceNumber: entry['reference'],
                    description: entry['libelle'],
                    debit: (entry['debit'] as num).toDouble(),
                    credit: (entry['credit'] as num).toDouble(),
                  ))
              .toList();
        });
        return GrandLivreWidget(
          entries: entries,
          title: 'Extrait du Grand Livre',
          date: _currentDate,
        );
      case 'TAccountWidget':
        return TAccountWidget(
          accountNumber: exemple['numero'],
          accountName: exemple['intitule'],
          debitEntries: (exemple['debit'] as List)
              .map((entry) => TAccountEntry(
                    date: entry['date'],
                    description: entry['description'],
                    amount: (entry['amount'] as num).toDouble(),
                  ))
              .toList(),
          creditEntries: (exemple['credit'] as List)
              .map((entry) => TAccountEntry(
                    date: entry['date'],
                    description: entry['description'],
                    amount: (entry['amount'] as num).toDouble(),
                  ))
              .toList(),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildStructureSection(
      BuildContext context, Map<String, dynamic> structure) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Structure',
          style: textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.secondary,
          ),
        ),
        const SizedBox(height: 8),
        ...structure.entries.map((entry) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8, top: 8),
                  child: Text(
                    entry.key.toUpperCase(),
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                ),
                ...entry.value.map<Widget>((item) => Padding(
                      padding: const EdgeInsets.only(left: 24, top: 4),
                      child: Row(
                        children: [
                          Icon(Icons.arrow_right,
                              size: 20, color: colorScheme.secondary),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              item,
                              style: textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
              ],
            )),
      ],
    );
  }

  Widget _buildCharacteristicsList(
      BuildContext context, List<dynamic> characteristics) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Caractéristiques',
          style: textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.secondary,
          ),
        ),
        const SizedBox(height: 8),
        ...characteristics.map((item) => Padding(
              padding: const EdgeInsets.only(left: 8, top: 4),
              child: Row(
                children: [
                  Icon(Icons.check_circle_outline,
                      size: 20, color: colorScheme.secondary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }
}
