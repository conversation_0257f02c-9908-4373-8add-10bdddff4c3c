import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../screens/guides/note_graph_screen.dart';

/// Filter options for notes
enum NoteFilterOption {
  all,
  private,
  public,
}

/// Enum for note sorting options
enum NoteSortOption {
  dateCreated,
  dateModified,
  title,
  type,
  reminder,
}

/// Enum for note view modes
enum NoteViewMode {
  list,
  grid,
  compact,
}



/// Comprehensive personal notes management screen with search, filtering, organization, and bulk operations
class PersonalNotesScreen extends ConsumerStatefulWidget {
  const PersonalNotesScreen({super.key});

  @override
  ConsumerState<PersonalNotesScreen> createState() => _PersonalNotesScreenState();
}

class _PersonalNotesScreenState extends ConsumerState<PersonalNotesScreen>
    with TickerProviderStateMixin {
  // Controllers and state
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  // Filter and sort state
  NoteFilterOption _filterOption = NoteFilterOption.all;



  // Statistics and reminders
  bool _showStatistics = false;
  bool _showReminders = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
    _fabAnimationController.forward();

    _searchController.addListener(() {
      setState(() {
        // Search functionality to be implemented
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _tabController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          if (_showReminders) _buildRemindersCard(),
          if (_showStatistics) _buildStatisticsCard(),
          _buildSearchAndFilters(),
          _buildTabBar(),
          Expanded(child: _buildTabBarView()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('Notes Personnelles'),
      actions: [
        IconButton(
          icon: const Icon(Icons.account_tree),
          onPressed: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const NoteGraphScreen()),
          ),
          tooltip: 'Graphique des liaisons',
        ),
        IconButton(
          icon: Icon(_showStatistics ? Icons.analytics : Icons.analytics_outlined),
          onPressed: () => setState(() => _showStatistics = !_showStatistics),
          tooltip: 'Statistiques',
        ),
        PopupMenuButton<String>(
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'export', child: Text('Exporter')),
            const PopupMenuItem(value: 'import', child: Text('Importer')),
            const PopupMenuItem(value: 'settings', child: Text('Paramètres')),
          ],
          onSelected: _handleMenuAction,
        ),
      ],
    );
  }

  Widget _buildRemindersCard() {
    return Container(
      margin: const EdgeInsets.all(8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.notifications_active,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Text('Rappels actifs'),
              const Spacer(),
              TextButton(
                onPressed: () => setState(() => _showReminders = false),
                child: const Text('Masquer'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return Container(
      margin: const EdgeInsets.all(8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  const Text('Statistiques'),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => setState(() => _showStatistics = false),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Add statistics content here
              const Text('Statistiques des notes à implémenter'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Rechercher dans les notes...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                FilterChip(
                  label: const Text('Toutes'),
                  selected: _filterOption == NoteFilterOption.all,
                  onSelected: (_) => setState(() => _filterOption = NoteFilterOption.all),
                ),
                const SizedBox(width: 8),
                FilterChip(
                  label: const Text('Privées'),
                  selected: _filterOption == NoteFilterOption.private,
                  onSelected: (_) => setState(() => _filterOption = NoteFilterOption.private),
                ),
                const SizedBox(width: 8),
                FilterChip(
                  label: const Text('Publiques'),
                  selected: _filterOption == NoteFilterOption.public,
                  onSelected: (_) => setState(() => _filterOption = NoteFilterOption.public),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      tabs: const [
        Tab(icon: Icon(Icons.list), text: 'Toutes'),
        Tab(icon: Icon(Icons.link), text: 'Liaisons'),
        Tab(icon: Icon(Icons.share), text: 'Partagées'),
        Tab(icon: Icon(Icons.comment), text: 'Commentaires'),
        Tab(icon: Icon(Icons.account_tree), text: 'Graphique'),
      ],
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildAllNotesTab(),
        _buildLinksTab(),
        _buildSharedNotesTab(),
        _buildCommentsTab(),
        _buildGraphTab(),
      ],
    );
  }

  Widget _buildAllNotesTab() {
    return const Center(child: Text('Liste de toutes les notes'));
  }

  Widget _buildLinksTab() {
    return const Center(child: Text('Gestion des liaisons'));
  }

  Widget _buildSharedNotesTab() {
    return const Center(child: Text('Notes partagées'));
  }

  Widget _buildCommentsTab() {
    return const Center(child: Text('Commentaires collaboratifs'));
  }

  Widget _buildGraphTab() {
    return const NoteGraphScreen();
  }

  Widget _buildFloatingActionButton() {
    return ScaleTransition(
      scale: _fabAnimation,
      child: FloatingActionButton(
        onPressed: _createNewNote,
        tooltip: 'Nouvelle note',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportNotes();
        break;
      case 'import':
        _importNotes();
        break;
      case 'settings':
        _showSettings();
        break;
    }
  }

  void _createNewNote() {
    // Implementation for creating new note
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Création de note à implémenter')),
    );
  }

  void _exportNotes() {
    // Implementation for exporting notes
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export de notes à implémenter')),
    );
  }

  void _importNotes() {
    // Implementation for importing notes
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import de notes à implémenter')),
    );
  }

  void _showSettings() {
    // Implementation for showing settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Paramètres à implémenter')),
    );
  }
}
