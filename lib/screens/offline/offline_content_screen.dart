import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/offline_cache_service.dart';
import '../../theme/design_tokens.dart';
import '../../theme/semantic_colors.dart';

/// Download status for content items
enum DownloadStatus {
  notDownloaded,
  downloading,
  downloaded,
  paused,
  failed,
  updating,
}

/// Content item model for offline management
class OfflineContentItem {
  final String id;
  final String title;
  final String description;
  final String category;
  final int estimatedSizeBytes;
  final bool isEssential;
  final DateTime? lastUpdated;
  final String? version;
  DownloadStatus status;
  double downloadProgress;
  String? errorMessage;

  OfflineContentItem({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.estimatedSizeBytes,
    this.isEssential = false,
    this.lastUpdated,
    this.version,
    this.status = DownloadStatus.notDownloaded,
    this.downloadProgress = 0.0,
    this.errorMessage,
  });

  OfflineContentItem copyWith({
    DownloadStatus? status,
    double? downloadProgress,
    String? errorMessage,
    DateTime? lastUpdated,
  }) {
    return OfflineContentItem(
      id: id,
      title: title,
      description: description,
      category: category,
      estimatedSizeBytes: estimatedSizeBytes,
      isEssential: isEssential,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      version: version,
      status: status ?? this.status,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  String get formattedSize {
    if (estimatedSizeBytes < 1024) return '$estimatedSizeBytes B';
    if (estimatedSizeBytes < 1024 * 1024) return '${(estimatedSizeBytes / 1024).toStringAsFixed(1)} KB';
    return '${(estimatedSizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  bool get needsUpdate {
    if (lastUpdated == null) return false;
    final daysSinceUpdate = DateTime.now().difference(lastUpdated!).inDays;
    return daysSinceUpdate > 7; // Content older than 7 days needs update
  }
}

/// Download queue item
class DownloadQueueItem {
  final String contentId;
  final int priority;
  final bool wifiOnly;
  final DateTime queuedAt;

  DownloadQueueItem({
    required this.contentId,
    required this.priority,
    this.wifiOnly = false,
    required this.queuedAt,
  });
}

/// Screen for managing offline content with comprehensive download and cache management
class OfflineContentScreen extends StatefulWidget {
  const OfflineContentScreen({super.key});

  @override
  State<OfflineContentScreen> createState() => _OfflineContentScreenState();
}

class _OfflineContentScreenState extends State<OfflineContentScreen>
    with TickerProviderStateMixin {
  final OfflineCacheService _cacheService = OfflineCacheService();
  late TabController _tabController;
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  // State variables
  bool _isOnline = true;
  bool _isLoading = true;
  bool _wifiOnlyDownloads = true;
  bool _autoDownloadEnabled = false;
  String _searchQuery = '';
  CacheStatistics? _cacheStats;
  List<OfflineContentItem> _availableContent = [];
  List<OfflineContentItem> _downloadedContent = [];
  List<DownloadQueueItem> _downloadQueue = [];
  Map<String, Timer> _downloadTimers = {};

  // Controllers
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeScreen();
    _setupConnectivityListener();
    _loadPreferences();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    _connectivitySubscription.cancel();
    _downloadTimers.values.forEach((timer) => timer.cancel());
    super.dispose();
  }

  Future<void> _initializeScreen() async {
    setState(() => _isLoading = true);

    try {
      // Check connectivity
      _isOnline = await _cacheService.isOnline();

      // Load cache statistics
      _cacheStats = await _cacheService.getCacheStatistics();

      // Initialize content lists
      await _loadAvailableContent();
      await _loadDownloadedContent();
      await _loadDownloadQueue();

      // Start periodic updates
      _startPeriodicUpdates();
    } catch (e) {
      _showErrorSnackBar('Failed to initialize offline content: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _setupConnectivityListener() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (ConnectivityResult result) {
        final wasOnline = _isOnline;
        _isOnline = result != ConnectivityResult.none;

        if (_isOnline && !wasOnline) {
          _showInfoSnackBar('Back online - resuming downloads');
          _resumeDownloads();
        } else if (!_isOnline && wasOnline) {
          _showInfoSnackBar('Offline mode - downloads paused');
          _pauseDownloads();
        }

        setState(() {});
      },
    );
  }

  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _wifiOnlyDownloads = prefs.getBool('wifi_only_downloads') ?? true;
      _autoDownloadEnabled = prefs.getBool('auto_download_enabled') ?? false;
    });
  }

  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('wifi_only_downloads', _wifiOnlyDownloads);
    await prefs.setBool('auto_download_enabled', _autoDownloadEnabled);
  }

  Future<void> _loadAvailableContent() async {
    // Define available content items
    _availableContent = [
      OfflineContentItem(
        id: 'ir',
        title: 'Impôt sur le Revenu (IR)',
        description: 'Guide complet sur l\'impôt sur le revenu au Maroc',
        category: 'Fiscalité',
        estimatedSizeBytes: 2 * 1024 * 1024, // 2MB
        isEssential: true,
        version: '1.2',
      ),
      OfflineContentItem(
        id: 'is',
        title: 'Impôt sur les Sociétés (IS)',
        description: 'Tout sur l\'impôt sur les sociétés',
        category: 'Fiscalité',
        estimatedSizeBytes: 3 * 1024 * 1024, // 3MB
        isEssential: true,
        version: '1.1',
      ),
      OfflineContentItem(
        id: 'comptabilite_generale',
        title: 'Comptabilité Générale',
        description: 'Principes fondamentaux de la comptabilité',
        category: 'Comptabilité',
        estimatedSizeBytes: 4 * 1024 * 1024, // 4MB
        isEssential: true,
        version: '2.0',
      ),
      OfflineContentItem(
        id: 'droits_enregistrement',
        title: 'Droits d\'Enregistrement',
        description: 'Guide des droits d\'enregistrement',
        category: 'Fiscalité',
        estimatedSizeBytes: (1.5 * 1024 * 1024).round(), // 1.5MB
        version: '1.0',
      ),
      OfflineContentItem(
        id: 'quiz_data',
        title: 'Données Quiz',
        description: 'Questions et réponses pour les quiz',
        category: 'Quiz',
        estimatedSizeBytes: 500 * 1024, // 500KB
        isEssential: true,
        version: '1.3',
      ),
      OfflineContentItem(
        id: 'tax_calculator_data',
        title: 'Calculateur d\'Impôts',
        description: 'Données pour le calculateur d\'impôts',
        category: 'Outils',
        estimatedSizeBytes: 200 * 1024, // 200KB
        isEssential: true,
        version: '1.1',
      ),
      OfflineContentItem(
        id: 'common_forms',
        title: 'Formulaires Communs',
        description: 'Formulaires fiscaux et comptables',
        category: 'Formulaires',
        estimatedSizeBytes: 800 * 1024, // 800KB
        version: '1.0',
      ),
    ];

    // Update status based on cache
    for (int i = 0; i < _availableContent.length; i++) {
      final item = _availableContent[i];
      final cacheKey = _getCacheKey(item.id);
      
      if (_cacheService.isCacheValid(cacheKey)) {
        _availableContent[i] = item.copyWith(
          status: DownloadStatus.downloaded,
          downloadProgress: 1.0,
        );
      }
    }
  }

  Future<void> _loadDownloadedContent() async {
    _downloadedContent = _availableContent
        .where((item) => item.status == DownloadStatus.downloaded)
        .toList();
  }

  Future<void> _loadDownloadQueue() async {
    // Load download queue from preferences or state management
    // For now, initialize empty
    _downloadQueue = [];
  }

  void _startPeriodicUpdates() {
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (mounted) {
        final stats = await _cacheService.getCacheStatistics();
        setState(() => _cacheStats = stats);
      }
    });
  }

  String _getCacheKey(String contentId) {
    switch (contentId) {
      case 'quiz_data':
      case 'tax_calculator_data':
      case 'common_forms':
        return contentId;
      default:
        return 'guide_$contentId';
    }
  }

  List<OfflineContentItem> get _filteredAvailableContent {
    if (_searchQuery.isEmpty) return _availableContent;
    
    return _availableContent.where((item) {
      return item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             item.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             item.category.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  List<OfflineContentItem> get _filteredDownloadedContent {
    if (_searchQuery.isEmpty) return _downloadedContent;
    
    return _downloadedContent.where((item) {
      return item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             item.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             item.category.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  Future<void> _downloadContent(OfflineContentItem item) async {
    if (!_isOnline) {
      _showErrorSnackBar('Connexion internet requise pour télécharger');
      return;
    }

    if (_wifiOnlyDownloads) {
      final connectivity = await Connectivity().checkConnectivity();
      if (connectivity != ConnectivityResult.wifi) {
        _showErrorSnackBar('Téléchargement Wi-Fi uniquement activé');
        return;
      }
    }

    setState(() {
      final index = _availableContent.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _availableContent[index] = item.copyWith(
          status: DownloadStatus.downloading,
          downloadProgress: 0.0,
        );
      }
    });

    try {
      // Simulate download progress
      _simulateDownload(item);

      // Perform actual download
      bool success = false;
      switch (item.id) {
        case 'quiz_data':
          success = await _cacheService.cacheQuizData();
          break;
        case 'tax_calculator_data':
        case 'common_forms':
          // These would be handled by preloadEssentialContent
          await _cacheService.preloadEssentialContent();
          success = true;
          break;
        default:
          success = await _cacheService.cacheGuideContent(item.id);
          break;
      }

      if (success) {
        setState(() {
          final index = _availableContent.indexWhere((i) => i.id == item.id);
          if (index != -1) {
            _availableContent[index] = item.copyWith(
              status: DownloadStatus.downloaded,
              downloadProgress: 1.0,
              lastUpdated: DateTime.now(),
            );
          }
        });
        
        await _loadDownloadedContent();
        _showSuccessSnackBar('${item.title} téléchargé avec succès');
      } else {
        throw Exception('Échec du téléchargement');
      }
    } catch (e) {
      setState(() {
        final index = _availableContent.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          _availableContent[index] = item.copyWith(
            status: DownloadStatus.failed,
            errorMessage: e.toString(),
          );
        }
      });
      _showErrorSnackBar('Échec du téléchargement: $e');
    }
  }

  void _simulateDownload(OfflineContentItem item) {
    const duration = Duration(milliseconds: 100);
    _downloadTimers[item.id] = Timer.periodic(duration, (timer) {
      setState(() {
        final index = _availableContent.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          final currentItem = _availableContent[index];
          if (currentItem.status == DownloadStatus.downloading) {
            final newProgress = (currentItem.downloadProgress + 0.05).clamp(0.0, 0.95);
            _availableContent[index] = currentItem.copyWith(downloadProgress: newProgress);
            
            if (newProgress >= 0.95) {
              timer.cancel();
              _downloadTimers.remove(item.id);
            }
          } else {
            timer.cancel();
            _downloadTimers.remove(item.id);
          }
        }
      });
    });
  }

  Future<void> _pauseDownload(OfflineContentItem item) async {
    _downloadTimers[item.id]?.cancel();
    _downloadTimers.remove(item.id);
    
    setState(() {
      final index = _availableContent.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _availableContent[index] = item.copyWith(status: DownloadStatus.paused);
      }
    });
  }

  Future<void> _resumeDownload(OfflineContentItem item) async {
    if (!_isOnline) {
      _showErrorSnackBar('Connexion internet requise');
      return;
    }

    setState(() {
      final index = _availableContent.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _availableContent[index] = item.copyWith(status: DownloadStatus.downloading);
      }
    });

    _simulateDownload(item);
  }

  Future<void> _deleteContent(OfflineContentItem item) async {
    try {
      final cacheKey = _getCacheKey(item.id);
      // Note: OfflineCacheService doesn't have a public delete method
      // This would need to be added to the service
      
      setState(() {
        final index = _availableContent.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          _availableContent[index] = item.copyWith(
            status: DownloadStatus.notDownloaded,
            downloadProgress: 0.0,
            lastUpdated: null,
          );
        }
        _downloadedContent.removeWhere((i) => i.id == item.id);
      });

      _showSuccessSnackBar('${item.title} supprimé');
    } catch (e) {
      _showErrorSnackBar('Erreur lors de la suppression: $e');
    }
  }

  Future<void> _downloadAllEssential() async {
    if (!_isOnline) {
      _showErrorSnackBar('Connexion internet requise');
      return;
    }

    final essentialItems = _availableContent
        .where((item) => item.isEssential && item.status != DownloadStatus.downloaded)
        .toList();

    for (final item in essentialItems) {
      await _downloadContent(item);
      // Add small delay between downloads
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  void _pauseDownloads() {
    for (final item in _availableContent) {
      if (item.status == DownloadStatus.downloading) {
        _pauseDownload(item);
      }
    }
  }

  void _resumeDownloads() {
    for (final item in _availableContent) {
      if (item.status == DownloadStatus.paused) {
        _resumeDownload(item);
      }
    }
  }

  Future<void> _optimizeCache() async {
    try {
      await _cacheService.optimizeCache();
      _cacheStats = await _cacheService.getCacheStatistics();
      setState(() {});
      _showSuccessSnackBar('Cache optimisé avec succès');
    } catch (e) {
      _showErrorSnackBar('Erreur lors de l\'optimisation: $e');
    }
  }

  Future<void> _clearExpiredCache() async {
    try {
      final removedCount = await _cacheService.clearExpiredCache();
      _cacheStats = await _cacheService.getCacheStatistics();
      setState(() {});
      _showSuccessSnackBar('$removedCount éléments expirés supprimés');
    } catch (e) {
      _showErrorSnackBar('Erreur lors du nettoyage: $e');
    }
  }

  Future<void> _clearAllCache() async {
    final confirmed = await _showConfirmDialog(
      'Vider le cache',
      'Êtes-vous sûr de vouloir supprimer tout le contenu hors ligne ?',
    );

    if (confirmed) {
      try {
        await _cacheService.clearCache();
        await _initializeScreen();
        _showSuccessSnackBar('Cache vidé avec succès');
      } catch (e) {
        _showErrorSnackBar('Erreur lors du vidage: $e');
      }
    }
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirmer'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: context.semanticColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: context.semanticColors.errorVariant,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: context.semanticColors.info,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Contenu Hors Ligne'),
        actions: [
          // Online/Offline indicator
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _isOnline ? Icons.wifi : Icons.wifi_off,
                  color: _isOnline ? context.semanticColors.success : context.semanticColors.errorVariant,
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  _isOnline ? 'En ligne' : 'Hors ligne',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _isOnline ? context.semanticColors.success : context.semanticColors.errorVariant,
                  ),
                ),
              ],
            ),
          ),
          // Settings menu
          PopupMenuButton<String>(
            onSelected: (value) async {
              switch (value) {
                case 'download_essential':
                  await _downloadAllEssential();
                  break;
                case 'optimize_cache':
                  await _optimizeCache();
                  break;
                case 'clear_expired':
                  await _clearExpiredCache();
                  break;
                case 'clear_all':
                  await _clearAllCache();
                  break;
                case 'settings':
                  _showSettingsDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'download_essential',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Télécharger l\'essentiel'),
                  dense: true,
                ),
              ),
              const PopupMenuItem(
                value: 'optimize_cache',
                child: ListTile(
                  leading: Icon(Icons.tune),
                  title: Text('Optimiser le cache'),
                  dense: true,
                ),
              ),
              const PopupMenuItem(
                value: 'clear_expired',
                child: ListTile(
                  leading: Icon(Icons.cleaning_services),
                  title: Text('Nettoyer expiré'),
                  dense: true,
                ),
              ),
              const PopupMenuItem(
                value: 'clear_all',
                child: ListTile(
                  leading: Icon(Icons.delete_sweep),
                  title: Text('Vider le cache'),
                  dense: true,
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Paramètres'),
                  dense: true,
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Disponible', icon: Icon(Icons.cloud_download)),
            Tab(text: 'Téléchargé', icon: Icon(Icons.download_done)),
            Tab(text: 'Statistiques', icon: Icon(Icons.analytics)),
            Tab(text: 'File d\'attente', icon: Icon(Icons.queue)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Search bar
                Padding(
                  padding: const EdgeInsets.all(DesignTokens.spacing16),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Rechercher du contenu...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                setState(() => _searchQuery = '');
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(DesignTokens.borderRadius8),
                      ),
                    ),
                    onChanged: (value) => setState(() => _searchQuery = value),
                  ),
                ),
                // Tab content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAvailableContentTab(),
                      _buildDownloadedContentTab(),
                      _buildStatisticsTab(),
                      _buildQueueTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildAvailableContentTab() {
    final filteredContent = _filteredAvailableContent;
    
    if (filteredContent.isEmpty) {
      return const Center(
        child: Text('Aucun contenu disponible'),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(DesignTokens.spacing16),
      itemCount: filteredContent.length,
      itemBuilder: (context, index) {
        final item = filteredContent[index];
        return _buildContentCard(item, isAvailable: true);
      },
    );
  }

  Widget _buildDownloadedContentTab() {
    final filteredContent = _filteredDownloadedContent;
    
    if (filteredContent.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.download_done, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('Aucun contenu téléchargé'),
            SizedBox(height: 8),
            Text(
              'Téléchargez du contenu depuis l\'onglet "Disponible"',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(DesignTokens.spacing16),
      itemCount: filteredContent.length,
      itemBuilder: (context, index) {
        final item = filteredContent[index];
        return _buildContentCard(item, isAvailable: false);
      },
    );
  }

  Widget _buildStatisticsTab() {
    if (_cacheStats == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final stats = _cacheStats!;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignTokens.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cache overview
          Card(
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacing16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Aperçu du Cache',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: DesignTokens.spacing16),
                  _buildStatRow('Taille totale', stats.formattedSize),
                  _buildStatRow('Nombre d\'éléments', '${stats.totalEntries}'),
                  _buildStatRow('Éléments expirés', '${stats.expiredEntries}'),
                  _buildStatRow('Éléments compressés', '${stats.compressedEntries}'),
                  _buildStatRow('Taux de réussite', '${(stats.hitRate * 100).toStringAsFixed(1)}%'),
                  _buildStatRow('Ratio de compression', '${(stats.compressionRatio * 100).toStringAsFixed(1)}%'),
                ],
              ),
            ),
          ),
          const SizedBox(height: DesignTokens.spacing16),
          
          // Content types
          Card(
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacing16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Types de Contenu',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: DesignTokens.spacing16),
                  ...stats.contentTypeCounts.entries.map(
                    (entry) => _buildStatRow(entry.key, '${entry.value}'),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: DesignTokens.spacing16),
          
          // Cache health
          Card(
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacing16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Santé du Cache',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: DesignTokens.spacing16),
                  _buildHealthIndicator('Espace utilisé', stats.totalSizeBytes, 500 * 1024 * 1024),
                  const SizedBox(height: DesignTokens.spacing8),
                  _buildHealthIndicator('Éléments expirés', stats.expiredEntries, stats.totalEntries),
                  const SizedBox(height: DesignTokens.spacing16),
                  Text(
                    'Dernière optimisation: ${_formatDate(stats.lastOptimization)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQueueTab() {
    if (_downloadQueue.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.queue, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('File d\'attente vide'),
            SizedBox(height: 8),
            Text(
              'Les téléchargements en attente apparaîtront ici',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(DesignTokens.spacing16),
      itemCount: _downloadQueue.length,
      itemBuilder: (context, index) {
        final queueItem = _downloadQueue[index];
        final contentItem = _availableContent.firstWhere(
          (item) => item.id == queueItem.contentId,
        );
        
        return Card(
          child: ListTile(
            leading: CircleAvatar(
              child: Text('${queueItem.priority}'),
            ),
            title: Text(contentItem.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(contentItem.description),
                const SizedBox(height: 4),
                Row(
                  children: [
                    if (queueItem.wifiOnly)
                      const Icon(Icons.wifi, size: 16),
                    Text(
                      'Ajouté: ${_formatDate(queueItem.queuedAt)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ],
            ),
            trailing: IconButton(
              icon: const Icon(Icons.remove_circle_outline),
              onPressed: () {
                setState(() {
                  _downloadQueue.removeAt(index);
                });
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildContentCard(OfflineContentItem item, {required bool isAvailable}) {
    return Card(
      margin: const EdgeInsets.only(bottom: DesignTokens.spacing12),
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              item.title,
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                          if (item.isEssential)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: context.semanticColors.warning.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Essentiel',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: context.semanticColors.warning,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        item.description,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                _buildStatusIcon(item),
              ],
            ),
            const SizedBox(height: DesignTokens.spacing12),
            
            // Details
            Row(
              children: [
                _buildDetailChip(item.category, Icons.category),
                const SizedBox(width: 8),
                _buildDetailChip(item.formattedSize, Icons.storage),
                if (item.version != null) ...[
                  const SizedBox(width: 8),
                  _buildDetailChip('v${item.version}', Icons.info_outline),
                ],
              ],
            ),
            
            // Progress bar for downloading items
            if (item.status == DownloadStatus.downloading || item.status == DownloadStatus.paused)
              Column(
                children: [
                  const SizedBox(height: DesignTokens.spacing12),
                  LinearProgressIndicator(
                    value: item.downloadProgress,
                    backgroundColor: Colors.grey[300],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${(item.downloadProgress * 100).toStringAsFixed(0)}%',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        item.status == DownloadStatus.downloading ? 'Téléchargement...' : 'En pause',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            
            // Last updated info
            if (item.lastUpdated != null)
              Column(
                children: [
                  const SizedBox(height: DesignTokens.spacing8),
                  Row(
                    children: [
                      Icon(
                        item.needsUpdate ? Icons.update : Icons.check_circle,
                        size: 16,
                        color: item.needsUpdate ? context.semanticColors.warning : context.semanticColors.success,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Mis à jour: ${_formatDate(item.lastUpdated!)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      if (item.needsUpdate) ...[
                        const SizedBox(width: 8),
                        Text(
                          'Mise à jour disponible',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: context.semanticColors.warning,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            
            // Error message
            if (item.errorMessage != null)
              Column(
                children: [
                  const SizedBox(height: DesignTokens.spacing8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: context.semanticColors.errorVariant.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error, size: 16, color: context.semanticColors.errorVariant),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            item.errorMessage!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: context.semanticColors.errorVariant,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            
            const SizedBox(height: DesignTokens.spacing12),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: _buildActionButtons(item, isAvailable),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon(OfflineContentItem item) {
    switch (item.status) {
      case DownloadStatus.downloaded:
        return Icon(Icons.check_circle, color: context.semanticColors.success);
      case DownloadStatus.downloading:
        return const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case DownloadStatus.paused:
        return Icon(Icons.pause_circle, color: context.semanticColors.warning);
      case DownloadStatus.failed:
        return Icon(Icons.error, color: context.semanticColors.errorVariant);
      case DownloadStatus.updating:
        return Icon(Icons.update, color: context.semanticColors.info);
      default:
        return Icon(Icons.cloud_download, color: Colors.grey[600]);
    }
  }

  Widget _buildDetailChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildActionButtons(OfflineContentItem item, bool isAvailable) {
    final buttons = <Widget>[];

    switch (item.status) {
      case DownloadStatus.notDownloaded:
        buttons.add(
          ElevatedButton.icon(
            onPressed: () => _downloadContent(item),
            icon: const Icon(Icons.download),
            label: const Text('Télécharger'),
          ),
        );
        break;
        
      case DownloadStatus.downloading:
        buttons.addAll([
          TextButton.icon(
            onPressed: () => _pauseDownload(item),
            icon: const Icon(Icons.pause),
            label: const Text('Pause'),
          ),
        ]);
        break;
        
      case DownloadStatus.paused:
        buttons.addAll([
          TextButton.icon(
            onPressed: () => _resumeDownload(item),
            icon: const Icon(Icons.play_arrow),
            label: const Text('Reprendre'),
          ),
          const SizedBox(width: 8),
          TextButton.icon(
            onPressed: () => _deleteContent(item),
            icon: const Icon(Icons.cancel),
            label: const Text('Annuler'),
          ),
        ]);
        break;
        
      case DownloadStatus.downloaded:
        if (item.needsUpdate) {
          buttons.add(
            ElevatedButton.icon(
              onPressed: () => _downloadContent(item),
              icon: const Icon(Icons.update),
              label: const Text('Mettre à jour'),
            ),
          );
          buttons.add(const SizedBox(width: 8));
        }
        buttons.add(
          TextButton.icon(
            onPressed: () => _deleteContent(item),
            icon: const Icon(Icons.delete),
            label: const Text('Supprimer'),
          ),
        );
        break;
        
      case DownloadStatus.failed:
        buttons.addAll([
          ElevatedButton.icon(
            onPressed: () => _downloadContent(item),
            icon: const Icon(Icons.refresh),
            label: const Text('Réessayer'),
          ),
          const SizedBox(width: 8),
          TextButton.icon(
            onPressed: () => _deleteContent(item),
            icon: const Icon(Icons.cancel),
            label: const Text('Annuler'),
          ),
        ]);
        break;
        
      case DownloadStatus.updating:
        buttons.add(
          const CircularProgressIndicator(),
        );
        break;
    }

    return buttons;
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthIndicator(String label, int current, int max) {
    final percentage = max > 0 ? current / max : 0.0;
    Color color = context.semanticColors.success;

    if (percentage > 0.8) {
      color = context.semanticColors.errorVariant;
    } else if (percentage > 0.6) {
      color = context.semanticColors.warning;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label),
            Text('${(percentage * 100).toStringAsFixed(1)}%'),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: percentage.clamp(0.0, 1.0),
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Paramètres de Téléchargement'),
        content: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SwitchListTile(
                title: const Text('Wi-Fi uniquement'),
                subtitle: const Text('Télécharger seulement en Wi-Fi'),
                value: _wifiOnlyDownloads,
                onChanged: (value) {
                  setDialogState(() => _wifiOnlyDownloads = value);
                },
              ),
              SwitchListTile(
                title: const Text('Téléchargement automatique'),
                subtitle: const Text('Télécharger automatiquement les mises à jour'),
                value: _autoDownloadEnabled,
                onChanged: (value) {
                  setDialogState(() => _autoDownloadEnabled = value);
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              _savePreferences();
              Navigator.of(context).pop();
            },
            child: const Text('Sauvegarder'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return 'Il y a ${difference.inDays} jour${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'Il y a ${difference.inHours} heure${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'Il y a ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'À l\'instant';
    }
  }
}