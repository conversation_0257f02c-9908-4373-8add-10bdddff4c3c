import 'package:flutter/material.dart';
import 'guides/fiscalite/is/sections/calculator_section.dart';
import 'guides/fiscalite/ir/sections/calculator_section.dart' as ir;
import 'guides/fiscalite/tva/sections/calculateur_section.dart';
import 'guides/fiscalite/tva/widgets/ras_tva_checker_widget.dart';
import 'guides/comptabilite_approfondie/regles_evaluation/sections/calculateur_section.dart' as amort;
// Calculator screens
import 'calculators/financial_ratios_screen.dart';
import 'calculators/enhanced_depreciation_screen.dart';
import 'calculators/tax_optimization_screen.dart';
import 'calculators/calculation_history_screen.dart';

class ToolsScreen extends StatelessWidget {
  const ToolsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Outils de calcul',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        elevation: 0,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isNarrow = constraints.maxWidth < 600;
          final crossAxisCount = isNarrow ? 1 : (constraints.maxWidth < 900 ? 2 : 3);
          final childAspectRatio = isNarrow ? 2.2 : 1.4;

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Basic Calculators Section
                  Text(
                    'Calculateurs de Base',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: crossAxisCount,
                    childAspectRatio: childAspectRatio,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    children: [
              _buildToolCard(
                context: context,
                title: 'Calculateur IS',
                subtitle: 'Calcul de l\'impôt sur les sociétés',
                icon: Icons.business,
                color: colorScheme.primary,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Calculateur IS',
                  calculator: const IsCalculatorSection(),
                ),
              ),
              _buildToolCard(
                context: context,
                title: 'Calculateur IR',
                subtitle: 'Calcul de l\'impôt sur le revenu',
                icon: Icons.person,
                color: colorScheme.secondary,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Calculateur IR',
                  calculator: const ir.CalculatorSection(),
                ),
              ),
              _buildToolCard(
                context: context,
                title: 'Calculateur TVA',
                subtitle: 'Calcul de la taxe sur la valeur ajoutée',
                icon: Icons.calculate,
                color: colorScheme.tertiary,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Calculateur TVA',
                  calculator: const CalculateurSection(),
                ),
              ),
              _buildToolCard(
                context: context,
                title: 'Calculateur Amortissement',
                subtitle: 'Calcul des amortissements linéaires et dégressifs',
                icon: Icons.trending_down,
                color: colorScheme.error,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Calculateur Amortissement',
                  calculator: const amort.CalculateurSection(),
                ),
              ),
              _buildToolCard(
                context: context,
                title: 'Vérificateur RAS TVA',
                subtitle: 'Vérification d\'assujettissement à la RAS TVA',
                icon: Icons.verified,
                color: colorScheme.primaryContainer,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Vérificateur RAS TVA',
                  calculator: const RASTVACheckerWidget(),
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Advanced Calculators Section
          Text(
            'Calculateurs Avancés',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Outils spécialisés avec analyses approfondies et recommandations',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspectRatio,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            children: [
              _buildAdvancedToolCard(
                context: context,
                title: 'Ratios Financiers',
                subtitle: 'Analyse complète de la santé financière',
                icon: Icons.analytics,
                color: Colors.blue,
                isNew: true,
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const FinancialRatiosScreen(),
                  ),
                ),
              ),
              _buildAdvancedToolCard(
                context: context,
                title: 'Amortissement Avancé',
                subtitle: 'Comparaison de méthodes et optimisation fiscale',
                icon: Icons.trending_down,
                color: Colors.orange,
                isNew: true,
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EnhancedDepreciationScreen(),
                  ),
                ),
              ),
              _buildAdvancedToolCard(
                context: context,
                title: 'Optimisation Fiscale',
                subtitle: 'Assistant intelligent pour stratégies fiscales',
                icon: Icons.lightbulb,
                color: Colors.purple,
                isWizard: true,
                isNew: true,
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const TaxOptimizationScreen(),
                  ),
                ),
              ),
              _buildAdvancedToolCard(
                context: context,
                title: 'Historique des Calculs',
                subtitle: 'Gestion et analyse de vos calculs précédents',
                icon: Icons.history,
                color: Colors.green,
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const CalculationHistoryScreen(),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
        },
      ),
    );
  }

  void _showCalculator({
    required BuildContext context,
    required String title,
    required Widget calculator,
  }) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text(title),
          ),
          body: calculator,
        ),
      ),
    );
  }

  Widget _buildToolCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 6,
      shadowColor: color.withValues(alpha: 0.4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 36,
                  color: color,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                textAlign: TextAlign.center,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                subtitle,
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedToolCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isNew = false,
    bool isWizard = false,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 8,
      shadowColor: color.withValues(alpha: 0.5),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.15),
                color.withValues(alpha: 0.08),
              ],
            ),
          ),
          child: Stack(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: color.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      icon,
                      size: 32,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Flexible(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Flexible(
                    child: Text(
                      subtitle,
                      textAlign: TextAlign.center,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              // Badges
              Positioned(
                top: 0,
                right: 0,
                child: Column(
                  children: [
                    if (isNew)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'NOUVEAU',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    if (isWizard)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.purple,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.auto_awesome, size: 12, color: Colors.white),
                            const SizedBox(width: 4),
                            Text(
                              'ASSISTANT',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
