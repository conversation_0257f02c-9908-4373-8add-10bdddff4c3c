import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart'; // Import for groupBy
import 'package:provider/provider.dart';

import '../../providers/exam/exam_data_provider.dart';
import '../../models/exam/exam.dart';
import '../../services/theme_service.dart';
import 'exam_screen.dart'; // Screen to navigate to

class ExamListScreen extends ConsumerWidget {
  const ExamListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final examListAsync = ref.watch(examListProvider);
    final theme = Theme.of(context); // Get theme for styling
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Examens Disponibles',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: isDark ? 0 : 1,
      ),
      backgroundColor: colorScheme.surface,
      body: examListAsync.when(
        data: (exams) {
          if (exams.isEmpty) {
            return Center(
              child: Text(
                'Aucun examen disponible pour le moment.',
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                ),
              )
            );
          }

          // Group exams by mainCategory
          final groupedExams = groupBy(exams, (Exam exam) => exam.mainCategory);

          // Get sorted list of categories
          final categories = groupedExams.keys.toList()..sort();

          return ListView.builder(
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final categoryExams = groupedExams[category]!;

              // Sort exams within the category (e.g., by year, then session)
              categoryExams.sort((a, b) {
                int yearCompare = (b.year ?? 0).compareTo(a.year ?? 0); // Descending year
                if (yearCompare != 0) return yearCompare;
                // Add session sorting if needed (e.g., Ordinaire before Rattrapage)
                return (a.session ?? '').compareTo(b.session ?? '');
              });

              return Card( // Wrap ExpansionTile in a Card for better visual separation
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                elevation: isDark ? 0 : 1,
                surfaceTintColor: colorScheme.surfaceTint,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: isDark 
                    ? BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)) 
                    : BorderSide.none,
                ),
                child: ExpansionTile(
                  title: Text(
                    category,
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  collapsedIconColor: colorScheme.primary,
                  iconColor: colorScheme.primary,
                  initiallyExpanded: true, // Keep categories expanded by default initially
                  childrenPadding: const EdgeInsets.only(bottom: 8.0), // Padding for list items
                  children: categoryExams.map((exam) {
                    // Build subtitle string with available info
                    List<String> subtitleParts = [];
                    if (exam.university != null) subtitleParts.add(exam.university!);
                    if (exam.faculty != null && exam.faculty != 'Global') subtitleParts.add(exam.faculty!);
                    if (exam.year != null) subtitleParts.add(exam.year.toString());
                    if (exam.session != null) subtitleParts.add(exam.session!);
                    String subtitle = subtitleParts.join(' - ');

                    return ListTile(
                       title: Text(
                         exam.title, 
                         style: textTheme.titleMedium?.copyWith(
                           color: colorScheme.onSurface,
                         ),
                       ),
                       subtitle: Text(
                         subtitle, 
                         style: textTheme.bodySmall?.copyWith(
                           color: colorScheme.onSurfaceVariant,
                         ),
                       ),
                       leading: Icon(
                         Icons.assignment_outlined,
                         color: colorScheme.primary,
                       ),
                       trailing: Icon(
                         Icons.arrow_forward_ios, 
                         size: 16,
                         color: colorScheme.onSurfaceVariant,
                       ),
                       onTap: () {
                         Navigator.push(
                           context,
                           MaterialPageRoute(
                             builder: (context) => ExamScreen(examId: exam.id),
                           ),
                         );
                       },
                    );
                  }).toList(),
                ),
              );
            },
          );
        },
        loading: () => Center(
          child: CircularProgressIndicator(
            color: colorScheme.primary,
          ),
        ),
        error: (error, stackTrace) => Center(
          child: Text(
            'Erreur lors du chargement des examens: $error',
            style: textTheme.bodyLarge?.copyWith(
              color: colorScheme.error,
            ),
          ),
        ),
      ),
    );
  }
}
