import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart'; // For rendering statement
import 'package:markdown/markdown.dart' as md;
import 'package:provider/provider.dart' as provider_pkg; // Renamed to avoid conflicts

import '../../providers/exam/exam_data_provider.dart';
import '../../providers/exam/exam_state_provider.dart';
import '../../services/theme_service.dart';
import '../../services/orientation_service.dart';
import '../../widgets/exam_layout_widgets.dart';
import 'exam_question_view.dart';
import 'exam_result_screen.dart';

class ExamScreen extends ConsumerStatefulWidget {
  final String examId;
  const ExamScreen({required this.examId, super.key});

  @override
  ConsumerState<ExamScreen> createState() => _ExamScreenState();
}

class _ExamScreenState extends ConsumerState<ExamScreen> {
  late ScrollController _verticalScrollController;
  late ScrollController _horizontalScrollController;
  Timer? _autoSaveTimer;
  bool _showSplitPane = false;
  bool _showQuestionOverview = false;
  bool _showKeyboardHelp = false;
  final Set<int> _flaggedQuestions = <int>{};

  // Remove BETA flag for modern UI
  final bool isPreviewVersion = false;

  @override
  void initState() {
    super.initState();
    _verticalScrollController = ScrollController();
    _horizontalScrollController = ScrollController();
    _startAutoSaveTimer();

    // Force landscape orientation for exam on mobile devices
    OrientationService().enableExamMode();

    // Use postFrameCallback to load exam data after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExamData();
      _setupKeyboardShortcuts();
    });
  }

  void _startAutoSaveTimer() {
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _autoSave();
      } else {
        timer.cancel();
      }
    });
  }

  void _autoSave() {
    // Auto-save exam progress - only if widget is still mounted
    if (!mounted) return;

    try {
      final examState = ref.read(examStateProvider);
      if (examState != null && !examState.isSubmitted) {
        // Save progress logic here
        debugPrint('Auto-saving exam progress...');
      }
    } catch (e) {
      debugPrint('Error during auto-save: $e');
    }
  }

  void _setupKeyboardShortcuts() {
    // Focus node for keyboard shortcuts will be handled in the build method
  }



  Future<void> _loadExamData() async {
    final examData = await ref.read(examByIdProvider(widget.examId).future);
    if (examData != null) {
      // Load the exam into the state notifier
      await ref.read(examStateProvider.notifier).loadExam(examData);
    } else {
      // Handle error: Exam not found
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Erreur: Examen non trouvé.')),
        );
        Navigator.pop(context); // Go back if exam not found
      }
    }
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _verticalScrollController.dispose(); // Dispose controllers
    _horizontalScrollController.dispose();

    // Restore original orientation when leaving exam
    OrientationService().disableExamMode();

    // Removed the Future.microtask call that was causing the StateError.
    // Progress is saved on answer/navigation, so explicit pause here is not strictly needed and caused issues.
    super.dispose();
  }

  void _handleKeyPress(KeyEvent event) {
    if (event is KeyDownEvent) {
      final examState = ref.read(examStateProvider);
      if (examState == null) return;

      switch (event.logicalKey) {
        case LogicalKeyboardKey.keyN:
          if (examState.currentQuestionIndex < examState.exam.questions.length - 1) {
            ref.read(examStateProvider.notifier).nextQuestion();
          }
          break;
        case LogicalKeyboardKey.keyP:
          if (examState.currentQuestionIndex > 0) {
            ref.read(examStateProvider.notifier).previousQuestion();
          }
          break;
        case LogicalKeyboardKey.digit1:
          ref.read(examStateProvider.notifier).selectAnswer(0);
          break;
        case LogicalKeyboardKey.digit2:
          ref.read(examStateProvider.notifier).selectAnswer(1);
          break;
        case LogicalKeyboardKey.digit3:
          ref.read(examStateProvider.notifier).selectAnswer(2);
          break;
        case LogicalKeyboardKey.digit4:
          ref.read(examStateProvider.notifier).selectAnswer(3);
          break;
        case LogicalKeyboardKey.keyF:
          _toggleQuestionFlag(examState.currentQuestionIndex);
          break;
        case LogicalKeyboardKey.keyS:
          setState(() => _showSplitPane = !_showSplitPane);
          break;
        case LogicalKeyboardKey.escape:
          setState(() {
            _showSplitPane = false;
            _showQuestionOverview = false;
            _showKeyboardHelp = false;
          });
          break;
      }
    }
  }

  void _toggleQuestionFlag(int questionIndex) {
    setState(() {
      if (_flaggedQuestions.contains(questionIndex)) {
        _flaggedQuestions.remove(questionIndex);
      } else {
        _flaggedQuestions.add(questionIndex);
      }
    });
    HapticFeedback.selectionClick();
  }


  @override
  Widget build(BuildContext context) {
    final examSessionState = ref.watch(examStateProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = provider_pkg.Provider.of<ThemeService>(context, listen: false).isDarkMode;

    // Show loading indicator until the exam state is initialized
    if (examSessionState == null) {
      // _loadExamData is called in initState, so we just wait here
      return Scaffold(
        appBar: AppBar(
          title: Text(
            'Chargement de l\'examen...',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
          backgroundColor: colorScheme.surface,
          elevation: isDark ? 0 : 1,
        ),
        backgroundColor: colorScheme.surface,
        body: Center(
          child: CircularProgressIndicator(
            color: colorScheme.primary,
          ),
        ),
      );
    }

    // If exam is submitted, show results screen
    if (examSessionState.isSubmitted) {
      // Navigate to results screen or display results directly
       WidgetsBinding.instance.addPostFrameCallback((_) {
         if (mounted) {
            // Restore orientation before navigating to results
            OrientationService().disableExamMode();

            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => ExamResultScreen(
                  // Pass the entire attempt object
                  attempt: examSessionState.attempt!,
                ),
              ),
            );
         }
       });
       // Show loading while navigating
       return Scaffold(
         appBar: AppBar(
           backgroundColor: colorScheme.surface,
           elevation: isDark ? 0 : 1,
         ), 
         backgroundColor: colorScheme.surface,
         body: Center(
           child: CircularProgressIndicator(
             color: colorScheme.primary,
           ),
         ),
       );
    }

    // Exam loaded and not submitted, show modern exam interface
    return PopScope(
       canPop: false,
       onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldPop = await _showExitConfirmation() ?? false;
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: KeyboardListener(
        focusNode: FocusNode(),
        autofocus: true,
        onKeyEvent: _handleKeyPress,
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              examSessionState.exam.title,
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: colorScheme.surface,
            elevation: isDark ? 0 : 1,
            actions: [
              // Split pane toggle
              IconButton(
                icon: Icon(
                  _showSplitPane ? Icons.view_sidebar : Icons.description_outlined,
                  color: colorScheme.primary,
                ),
                tooltip: _showSplitPane ? "Masquer l'énoncé" : "Voir l'énoncé",
                onPressed: () => setState(() => _showSplitPane = !_showSplitPane),
              ),
              // Question overview toggle
              IconButton(
                icon: Icon(
                  Icons.grid_view,
                  color: _showQuestionOverview ? colorScheme.primary : colorScheme.onSurfaceVariant,
                ),
                tooltip: "Vue d'ensemble des questions",
                onPressed: () => setState(() => _showQuestionOverview = !_showQuestionOverview),
              ),
              // Keyboard shortcuts help
              IconButton(
                icon: Icon(
                  Icons.keyboard,
                  color: _showKeyboardHelp ? colorScheme.primary : colorScheme.onSurfaceVariant,
                ),
                tooltip: "Raccourcis clavier",
                onPressed: () => setState(() => _showKeyboardHelp = !_showKeyboardHelp),
              ),
            ],
          ),
          backgroundColor: colorScheme.surface,
          body: Column(
            children: [
              // Breadcrumb and progress
              ExamBreadcrumb(
                examTitle: examSessionState.exam.title,
                currentQuestion: examSessionState.currentQuestionIndex,
                totalQuestions: examSessionState.exam.questions.length,
              ),
              Divider(height: 1, color: colorScheme.outlineVariant),
              
              // Main content area
              Expanded(
                child: _buildMainContent(examSessionState),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent(dynamic examSessionState) {
    return Row(
      children: [
        // Left sidebar with additional panels
        if (_showQuestionOverview || _showKeyboardHelp)
          Container(
            width: 280,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).colorScheme.outlineVariant,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                if (_showQuestionOverview) ...[
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          QuestionOverviewMiniMap(
                            totalQuestions: examSessionState.exam.questions.length,
                            currentQuestion: examSessionState.currentQuestionIndex,
                            answeredQuestions: _getAnsweredQuestions(examSessionState),
                            flaggedQuestions: _flaggedQuestions,
                            onQuestionTap: (index) {
                              ref.read(examStateProvider.notifier).goToQuestion(index);
                            },
                          ),
                          const SizedBox(height: 16),
                          ExamProgressIndicator(
                            timeElapsed: examSessionState.timeElapsed ?? Duration.zero,
                            timeLimit: null, // Add if available
                            currentQuestion: examSessionState.currentQuestionIndex,
                            totalQuestions: examSessionState.exam.questions.length,
                            answeredQuestions: _getAnsweredQuestions(examSessionState).length,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
                if (_showKeyboardHelp) ...[
                  const Divider(),
                  const Padding(
                    padding: EdgeInsets.all(16),
                    child: KeyboardShortcutsHelp(),
                  ),
                ],
              ],
            ),
          ),
        
        // Main exam content
        Expanded(
          child: _showSplitPane
              ? SplitPaneLayout(
                  leftPane: _buildStatementPane(examSessionState.exam.statementContent),
                  rightPane: const ExamQuestionView(),
                  initialSplitRatio: 0.4,
                )
              : const ExamQuestionView(),
        ),
      ],
    );
  }

  Widget _buildStatementPane(String statementContent) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Énoncé de l'examen",
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: MarkdownBody(
                data: statementContent,
                selectable: true,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Set<int> _getAnsweredQuestions(dynamic examSessionState) {
    final answered = <int>{};
    for (int i = 0; i < examSessionState.exam.questions.length; i++) {
      if (examSessionState.attempt?.responses.containsKey(i) == true) {
        answered.add(i);
      }
    }
    return answered;
  }

  Future<bool?> _showExitConfirmation() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quitter l\'examen'),
        content: const Text('Êtes-vous sûr de vouloir quitter cet examen ? Votre progression sera sauvegardée.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Quitter'),
          ),
        ],
      ),
    );
  }


}


// Custom MarkdownTableBuilder to handle table width and potentially other customizations
class MarkdownTableBuilder extends MarkdownElementBuilder {
  final double tableWidth;

  MarkdownTableBuilder({required this.tableWidth});

  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    if (element.tag != 'table') return null;

    final rows = <TableRow>[];

    // Process thead, tbody, and direct tr elements
    for (final child in element.children!) {
      if (child is md.Element && child.tag == 'thead') {
        _processTableSection(child, rows, preferredStyle, isHeader: true);
      } else if (child is md.Element && child.tag == 'tbody') {
        _processTableSection(child, rows, preferredStyle, isHeader: false);
      } else if (child is md.Element && child.tag == 'tr') {
         _processTableRow(child, rows, preferredStyle, isHeader: false); // Assume direct tr is body
      }
    }

    // Use a container to potentially constrain width if needed,
    // but rely on SingleChildScrollView for scrolling
    return Container(
       margin: const EdgeInsets.symmetric(vertical: 12.0),
       child: Table(
          border: TableBorder.all(
            // Use a default color instead of trying to access context from attributes
            color: Colors.grey.withValues(alpha: 0.3),
          ),
          defaultColumnWidth: const IntrinsicColumnWidth(), // Let columns size themselves initially
          children: rows,
        ),
    );
  }

  void _processTableSection(md.Element section, List<TableRow> rows, TextStyle? baseStyle, {required bool isHeader}) {
    for (final child in section.children!) {
      if (child is md.Element && child.tag == 'tr') {
        _processTableRow(child, rows, baseStyle, isHeader: isHeader);
      }
    }
  }

 void _processTableRow(md.Element row, List<TableRow> rows, TextStyle? baseStyle, {required bool isHeader}) {
    final cells = <Widget>[];
    final context = row.attributes['context'] as BuildContext?;
    final colorScheme = context != null ? Theme.of(context).colorScheme : null;
    
    for (final cell in row.children!) {
      if (cell is md.Element && (cell.tag == 'td' || cell.tag == 'th')) {
        final cellText = _getCellContent(cell);
        cells.add(
          Padding( // Use Padding instead of Container for simplicity
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
            child: Text(
              cellText,
              style: isHeader
                  ? baseStyle?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme?.primary,
                    )
                  : baseStyle?.copyWith(
                      color: colorScheme?.onSurface,
                    ),
              textAlign: isHeader ? TextAlign.center : TextAlign.left, // Center header text
            ),
          ),
        );
      }
    }
    if (cells.isNotEmpty) {
       // Apply decoration to the row itself if needed (e.g., background for header)
       BoxDecoration? rowDecoration = isHeader && colorScheme != null
           ? BoxDecoration(
               color: colorScheme.primaryContainer.withValues(alpha: 0.3),
             )
           : null;
      rows.add(TableRow(children: cells, decoration: rowDecoration));
    }
  }


  String _getCellContent(md.Element cell) {
    final buffer = StringBuffer();
    _extractTextFromNode(cell, buffer);
    return buffer.toString().trim();
  }

  void _extractTextFromNode(md.Node node, StringBuffer buffer) {
    if (node is md.Text) {
      buffer.write(node.text);
    } else if (node is md.Element) {
      for (final child in node.children!) {
        _extractTextFromNode(child, buffer);
        if (child is md.Element && _isBlockElement(child.tag)) {
           // Add newline for block elements for better separation if needed
           // buffer.write('\n');
        }
      }
    }
  }

  bool _isBlockElement(String tag) {
    // More comprehensive list might be needed depending on markdown source
    return ['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'blockquote', 'pre'].contains(tag);
  }


}
