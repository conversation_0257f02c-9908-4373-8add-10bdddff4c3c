import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart' as provider_pkg; // Add prefix
import '../../providers/exam/exam_state_provider.dart';
import '../../services/theme_service.dart';
import '../../providers/user_progress_provider.dart';
import '../../models/hive/user_profile.dart';
import '../../models/exam/exam.dart'; // Import Exam
import '../../providers/exam/exam_data_provider.dart'; // Import Exam provider
import '../../models/exam/exam_attempt.dart';
import 'exam_screen.dart';


class ExamResultScreen extends ConsumerWidget {
  // Replace individual fields with the ExamAttempt object
  final ExamAttempt attempt;

  const ExamResultScreen({
    required this.attempt,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Extract data from the attempt object
    final double percentage = attempt.percentage; // Use the getter from ExamAttempt
    final bool passed = percentage >= 50; // Example passing threshold
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    // Use the prefixed provider_pkg.Provider here
    final isDark = provider_pkg.Provider.of<ThemeService>(context).isDarkMode; 
    
    // Try to update user profile with exam results
    final userProgressService = ref.watch(userProgressServiceProvider);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final userProgress = userProgressService.getCurrentUserProgress();
        if (userProgress != null) {
          // Update last exam data using attempt data
          final UserProfile profile = userProgress.userProfile;
          profile.lastExamScore = attempt.score;
          profile.lastExamTotal = attempt.totalQuestions;
          profile.lastExamId = attempt.examId;
          profile.lastExamDate = attempt.timestamp; // Use attempt timestamp
          
          // Update best score if this is better
          if (profile.bestExamPercentage == null || percentage > profile.bestExamPercentage!) {
            profile.bestExamPercentage = percentage;
          }
          
          // Use the correct method from the service
          userProgressService.updateUserProfile(profile);
        }
      } catch (e) {
        debugPrint('Error updating user profile with exam results: $e');
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Résultats de l\'examen',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: isDark ? 0 : 1,
        automaticallyImplyLeading: false, // Prevent back button to exam screen
      ),
      backgroundColor: colorScheme.surface,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Lottie animation based on pass/fail
              SizedBox(
                height: 150,
                child: Lottie.asset(
                  passed ? 'assets/lottie/success.json' : 'assets/lottie/fail.json',
                  repeat: false,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                passed ? 'Félicitations !' : 'Essayez encore !',
                style: textTheme.headlineMedium?.copyWith(
                  // Use appropriate colors based on theme and pass/fail status
                  color: passed 
                      ? colorScheme.primary
                      : colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Card(
                elevation: isDark ? 0 : 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: isDark
                      ? BorderSide(color: colorScheme.outline.withValues(alpha: 0.2))
                      : BorderSide.none,
                ),
                color: colorScheme.surface,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Votre score',
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${attempt.score} / ${attempt.totalQuestions}', // Use attempt data
                        style: textTheme.headlineLarge?.copyWith(
                          color: colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: passed
                              ? colorScheme.primaryContainer
                              : colorScheme.errorContainer,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          '${percentage.toStringAsFixed(1)}%',
                          style: textTheme.titleMedium?.copyWith(
                            color: passed
                                ? colorScheme.onPrimaryContainer
                                : colorScheme.onErrorContainer,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 32),
              // Future feature: Review answers button
              // ElevatedButton.icon(
              //   onPressed: () {
              //     // Navigate to a review screen showing answers
              //   },
              //   icon: const Icon(Icons.rate_review_outlined),
              //   label: const Text('Revoir les réponses'),
              //   style: ElevatedButton.styleFrom(
              //     backgroundColor: colorScheme.secondaryContainer,
              //     foregroundColor: colorScheme.onSecondaryContainer,
              //   ),
              // ),
              // const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Navigate back to the home screen
                        Navigator.popUntil(context, ModalRoute.withName('/home'));
                      },
                      icon: const Icon(Icons.home_outlined),
                      label: const Text('Accueil'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Add the "Review Answers" button
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Implement navigation or expansion for review
                  _showReviewDialog(context, ref, attempt);
                },
                icon: const Icon(Icons.rate_review_outlined),
                label: const Text('Revoir les réponses'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.secondaryContainer,
                  foregroundColor: colorScheme.onSecondaryContainer,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                   shape: RoundedRectangleBorder(
                     borderRadius: BorderRadius.circular(10),
                   ),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        // Reset the exam state for this exam before navigating
                        ref.read(examStateProvider.notifier).reset();
                        // Navigate back to ExamScreen, replacing the result screen
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ExamScreen(examId: attempt.examId), // Use attempt.examId
                          ),
                        );
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('Réessayer l\'examen'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: colorScheme.primary,
                        side: BorderSide(color: colorScheme.primary),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Function to show the review dialog
  void _showReviewDialog(BuildContext context, WidgetRef ref, ExamAttempt attempt) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    // Use the prefixed provider_pkg.Provider here
    final isDark = provider_pkg.Provider.of<ThemeService>(context, listen: false).isDarkMode; 

    showDialog(
      context: context,
      // Use a Consumer builder to access ref inside the dialog
      builder: (BuildContext dialogContext) => Consumer(
        builder: (context, ref, child) {
          // Fetch the exam data asynchronously
          final examAsyncValue = ref.watch(examByIdProvider(attempt.examId));

          return Dialog(
            insetPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
            backgroundColor: colorScheme.surface,
            surfaceTintColor: colorScheme.surfaceTint,
            elevation: isDark ? 4 : 8,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
              side: isDark
                  ? BorderSide(color: colorScheme.outline.withValues(alpha: 0.2))
                  : BorderSide.none,
            ),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              height: MediaQuery.of(context).size.height * 0.8,
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Revue de l'examen",
                        style: textTheme.titleLarge?.copyWith(
                          color: colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.close,
                          color: colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                  Divider(color: colorScheme.outlineVariant),
                  Expanded(
                    child: examAsyncValue.when(
                      data: (exam) {
                        if (exam == null) {
                          return const Center(child: Text('Examen non trouvé.'));
                        }
                        return _buildReviewList(context, theme, exam, attempt);
                      },
                      loading: () => Center(
                        child: CircularProgressIndicator(color: colorScheme.primary),
                      ),
                      error: (error, stack) => Center(
                        child: Text('Erreur de chargement: $error'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Helper widget to build the list of questions for review
  Widget _buildReviewList(BuildContext context, ThemeData theme, Exam exam, ExamAttempt attempt) {
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return ListView.separated(
      itemCount: exam.questions.length,
      separatorBuilder: (context, index) => Divider(height: 24, thickness: 1, color: colorScheme.outlineVariant.withValues(alpha: 0.5)),
      itemBuilder: (context, index) {
        final question = exam.questions[index];
        final userAnswerIndex = attempt.userAnswers[index];
        final isCorrect = userAnswerIndex == question.correctAnswerIndex;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Question Text
              Text(
                'Question ${question.questionNumber}: ${question.questionText}',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 12),
              // Options
              ...List.generate(question.options.length, (optionIndex) {
                final isUserAnswer = optionIndex == userAnswerIndex;
                final isCorrectAnswer = optionIndex == question.correctAnswerIndex;
                IconData? leadingIcon;
                Color? tileColor;
                Color? textColor = colorScheme.onSurfaceVariant; // Default text color

                if (isUserAnswer && isCorrectAnswer) {
                  leadingIcon = Icons.check_circle;
                  tileColor = Colors.green.withValues(alpha: 0.1);
                  textColor = colorScheme.onSurface; // Make correct answer text stand out
                } else if (isUserAnswer && !isCorrectAnswer) {
                  leadingIcon = Icons.cancel;
                  tileColor = Colors.red.withValues(alpha: 0.1);
                   textColor = colorScheme.onSurface; // Make incorrect answer text stand out
                } else if (!isUserAnswer && isCorrectAnswer) {
                  leadingIcon = Icons.check_circle_outline; // Indicate correct if not chosen
                   textColor = Colors.green; // Highlight correct answer text
                }

                return Container(
                   margin: const EdgeInsets.only(bottom: 6),
                   decoration: BoxDecoration(
                     color: tileColor,
                     borderRadius: BorderRadius.circular(8),
                     border: Border.all(
                       color: isUserAnswer || isCorrectAnswer 
                         ? (isCorrect ? Colors.green.withValues(alpha: 0.5) : Colors.red.withValues(alpha: 0.5)) 
                         : Colors.transparent,
                       width: 1.5
                     )
                   ),
                   child: ListTile(
                    dense: true,
                    leading: leadingIcon != null 
                      ? Icon(
                          leadingIcon, 
                          color: isCorrect ? Colors.green : (isUserAnswer ? Colors.red : Colors.grey)
                        ) 
                      : const SizedBox(width: 24), // Placeholder for alignment
                    title: Text(
                      question.options[optionIndex],
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: isUserAnswer || isCorrectAnswer ? FontWeight.bold : FontWeight.normal,
                        color: textColor,
                      ),
                    ),
                  ),
                );
              }),
              // Explanation (if available)
              if (question.explanation != null && question.explanation!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.secondaryContainer.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: colorScheme.outline.withValues(alpha: 0.1))
                  ),
                  child: Column(
                     crossAxisAlignment: CrossAxisAlignment.start,
                     children: [
                       Text(
                         'Explication:',
                         style: textTheme.labelMedium?.copyWith(
                           fontWeight: FontWeight.bold,
                           color: colorScheme.onSecondaryContainer,
                         ),
                       ),
                       const SizedBox(height: 4),
                       Text(
                         question.explanation!,
                         style: textTheme.bodySmall?.copyWith(
                           color: colorScheme.onSecondaryContainer.withValues(alpha: 0.9),
                         ),
                       ),
                     ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
