import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:provider/provider.dart' as provider; // Renamed import
import '../../providers/exam/exam_state_provider.dart';
import '../../services/theme_service.dart';
import '../../theme/exam_visibility_theme_extension.dart'; // Direct import

class ExamQuestionView extends ConsumerWidget {
  const ExamQuestionView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final examState = ref.watch(examStateProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    // Use provider_pkg for Provider, but ThemeService is directly imported
    final isDark = provider.Provider.of<ThemeService>(context, listen: false).isDarkMode;
    // Use enhanced visibility settings for better readability on Android devices
    final examVisibility = ExamVisibilityThemeExtension.enhanced();

    if (examState == null) {
      // Should ideally not happen if ExamScreen handles loading
      return Center(
        child: Text(
          "Chargement de l'examen...",
          style: textTheme.bodyLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
      );
    }

    final question = examState.currentQuestion;
    final selectedAnswerIndex = examState.userAnswers[examState.currentQuestionIndex];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Question Number and Progress - More compact
          Container(
            padding: const EdgeInsets.symmetric(vertical: 6),
            child: Text(
              'Question ${examState.currentQuestionIndex + 1} / ${examState.totalQuestions}',
              style: textTheme.titleSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: (examState.currentQuestionIndex + 1) / examState.totalQuestions,
            minHeight: 6,
            borderRadius: BorderRadius.circular(3),
            backgroundColor: colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.4 : 0.6),
            valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
          ),
          const SizedBox(height: 12),

          // Question Text - Smaller and more compact
          Text(
            question.questionText,
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w500,
              fontSize: 18,
              height: 1.3,
            ),
          ),
          const SizedBox(height: 16),

          // Answer Options - More compact for mobile
          Expanded(
            child: ListView.builder(
              itemCount: question.options.length,
              itemBuilder: (context, index) {
                final isSelected = selectedAnswerIndex == index;

                return Card(
                  elevation: isSelected ? (isDark ? 2 : 4) : (isDark ? 1 : 2),
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  color: examVisibility.getAnswerBackgroundColor(colorScheme, isSelected, isDark, index),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                    side: BorderSide(
                      color: examVisibility.getBorderColor(colorScheme, isSelected, isDark),
                      width: examVisibility.getBorderWidth(isSelected),
                    ),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    title: Text(
                      question.options[index],
                      style: textTheme.bodyLarge?.copyWith(
                        color: examVisibility.getAnswerTextColor(colorScheme, isSelected, isDark, index),
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                        fontSize: examVisibility.getEnhancedFontSize(textTheme.bodyLarge?.fontSize ?? 16.0),
                        shadows: examVisibility.getTextShadows(colorScheme, isDark),
                        height: 1.3,
                      ),
                    ),
                    leading: CircleAvatar(
                      radius: 16,
                      backgroundColor: isSelected
                        ? colorScheme.primary
                        : colorScheme.secondaryContainer,
                      foregroundColor: isSelected
                        ? colorScheme.onPrimary
                        : colorScheme.onSecondaryContainer,
                      child: Text(
                        String.fromCharCode(65 + index), // A, B, C...
                        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                      ),
                    ),
                    onTap: () {
                      ref.read(examStateProvider.notifier).selectAnswer(index);
                    },
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 8),

          // Navigation Buttons - Compact for mobile
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: examState.isFirstQuestion
                        ? null
                        : () => ref.read(examStateProvider.notifier).previousQuestion(),
                    icon: const Icon(Icons.arrow_back, size: 18),
                    label: const Text('Précédent', style: TextStyle(fontSize: 13)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.surfaceContainerHighest,
                      foregroundColor: colorScheme.onSurfaceVariant,
                      disabledBackgroundColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      disabledForegroundColor: colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                      elevation: isDark ? 0 : 1,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      minimumSize: const Size(0, 36),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: examState.isLastQuestion
                      ? ElevatedButton.icon(
                          onPressed: () {
                            // Show confirmation dialog before submitting
                            _showSubmitConfirmationDialog(context, ref);
                          },
                          icon: const Icon(Icons.check_circle_outline, size: 18),
                          label: const Text('Soumettre', style: TextStyle(fontSize: 13)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorScheme.primary,
                            foregroundColor: colorScheme.onPrimary,
                            elevation: isDark ? 0 : 1,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            minimumSize: const Size(0, 36),
                          ),
                        )
                      : ElevatedButton.icon(
                          onPressed: () => ref.read(examStateProvider.notifier).nextQuestion(),
                          icon: const Icon(Icons.arrow_forward, size: 18),
                          label: const Text('Suivant', style: TextStyle(fontSize: 13)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorScheme.primary,
                            foregroundColor: colorScheme.onPrimary,
                            elevation: isDark ? 0 : 1,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            minimumSize: const Size(0, 36),
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSubmitConfirmationDialog(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    // Use listen: false to prevent provider errors outside of build
    final isDark = provider.Provider.of<ThemeService>(context, listen: false).isDarkMode;
    
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: colorScheme.surface,
          surfaceTintColor: colorScheme.surfaceTint,
          elevation: isDark ? 0 : 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isDark 
              ? BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)) 
              : BorderSide.none,
          ),
          title: Text(
            'Confirmer la soumission',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir soumettre vos réponses ?',
            style: textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'Annuler',
                style: TextStyle(color: colorScheme.primary),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            FilledButton(
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.primary,
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                ref.read(examStateProvider.notifier).submitExam(); // Submit the exam
              },
              child: Text(
                'Soumettre',
                style: TextStyle(color: colorScheme.onPrimary),
              ),
            ),
          ],
        );
      },
    );
  }


}
