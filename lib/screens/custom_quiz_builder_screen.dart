// Custom Quiz Builder Screen
// Uses CustomQuizProvider and CustomQuizBuilderWidget
// .windsurfrules: add comments during changes

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/custom_quiz_provider.dart';
import '../widgets/quiz/custom_quiz_builder_widget.dart';

class CustomQuizBuilderScreen extends ConsumerWidget {
  const CustomQuizBuilderScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Access the custom quiz provider
    // Get the current quiz config from provider
    final quizState = ref.watch(customQuizProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Créer un Quiz Personnalisé'),
      ),
      body: CustomQuizBuilderWidget(
        initialConfig: quizState.config,
        onConfigChanged: (config) {
          ref.read(customQuizProvider.notifier).updateConfig(config);
        },
      ),
    );
  }
}
