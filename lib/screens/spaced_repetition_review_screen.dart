// Spaced Repetition Review Screen
// Uses SpacedRepetitionProvider and SpacedRepetitionReviewWidget
// .windsurfrules: add comments during changes

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/adaptive_learning/spaced_repetition_review_widget.dart';

class SpacedRepetitionReviewScreen extends ConsumerWidget {
  final dynamic spacedRepetitionService;

  const SpacedRepetitionReviewScreen({
    super.key,
    required this.spacedRepetitionService,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the injected spacedRepetitionService
    return Scaffold(
      appBar: AppBar(
        title: const Text('Révisions Programmées'),
      ),
      body: SpacedRepetitionReviewWidget(
        service: spacedRepetitionService,
      ),
    );
  }
}
