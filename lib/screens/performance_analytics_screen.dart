// Performance Analytics Screen
// Uses PerformanceAnalyticsProvider and PerformanceAnalyticsDashboard
// .windsurfrules: add comments during changes

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/performance_analytics_provider.dart';
import '../widgets/analytics/performance_analytics_dashboard.dart';

class PerformanceAnalyticsScreen extends ConsumerWidget {
  // Inject adaptive learning and related services for analytics
  final dynamic adaptiveLearningService;
  final dynamic userProgressService;
  final dynamic spacedRepetitionService;
  // Enhancement: Add performance analytics service
  final dynamic performanceAnalyticsService;

  const PerformanceAnalyticsScreen({
    super.key,
    required this.adaptiveLearningService,
    required this.userProgressService,
    required this.spacedRepetitionService,
    required this.performanceAnalyticsService, // Enhancement
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Access the performance analytics provider
    final analyticsState = ref.watch(performanceAnalyticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Analyse de Performance'),
      ),
      body: PerformanceAnalyticsDashboard(
        adaptiveLearningService: adaptiveLearningService,
        userProgressService: userProgressService,
        spacedRepetitionService: spacedRepetitionService,
        performanceAnalyticsService: performanceAnalyticsService, // Enhancement: pass analytics service
        categoryName: 'Comptabilité', // Default category, can be made dynamic
      ),
    );
  }
}
