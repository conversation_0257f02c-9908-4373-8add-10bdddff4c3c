import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'package:provider/provider.dart';
import '../services/theme_service.dart';

enum ViewType { list, tree }

class PlanComptableScreen extends StatefulWidget {
  const PlanComptableScreen({super.key});

  @override
  _PlanComptableScreenState createState() => _PlanComptableScreenState();
}

class _PlanComptableScreenState extends State<PlanComptableScreen>
    with SingleTickerProviderStateMixin {
  Map<String, dynamic> _planComptableData = {};
  Map<String, dynamic> _filteredData = {};
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  String _errorMessage = '';
  String _selectedClass = 'Toutes';
  late AnimationController _animationController;
  late Animation<double> _animation;
  final ScrollController _scrollController = ScrollController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  ViewType _currentView = ViewType.list;
  final Map<String, bool> _expandedNodes = {};

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _loadPlanComptableData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadPlanComptableData() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/plan_comptable.json');
      final Map<String, dynamic> data = json.decode(jsonString);

      if (mounted) {
        setState(() {
          _planComptableData = data;
          _filteredData = data;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Erreur de chargement des données';
        });
      }
      print('Error loading Plan Comptable data: $e');
    }
  }

  List<String> get _availableClasses {
    final classes = _planComptableData.values
        .map((account) => account['classe']?.toString() ?? '')
        .where(
            (classe) => classe.isNotEmpty && classe != 'Financement Permanent')
        .toSet()
        .toList();
    classes.sort();
    return ['Toutes', ...classes];
  }

  void _filterData(String query) {
    setState(() {
      _filteredData = Map.fromEntries(_planComptableData.entries.where((entry) {
        final matchesQuery =
            entry.key.toLowerCase().contains(query.toLowerCase()) ||
                entry.value['intitule']
                    .toString()
                    .toLowerCase()
                    .contains(query.toLowerCase()) ||
                (entry.value['classe'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(query.toLowerCase());

        final matchesClass = _selectedClass == 'Toutes' ||
            entry.value['classe']?.toString() == _selectedClass;

        return matchesQuery && matchesClass;
      }));
    });
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Copié dans le presse-papiers'),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Theme.of(context).colorScheme.secondary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(8),
        action: SnackBarAction(
          label: 'OK',
          textColor: Theme.of(context).colorScheme.onSecondary,
          onPressed: () {},
        ),
      ),
    );
  }

  void _showClassFilterBottomSheet() {
    final theme = Theme.of(context);
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filtrer par classe',
                  style: theme.textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const Divider(),
            Expanded(
              child: ListView.builder(
                itemCount: _availableClasses.length,
                itemBuilder: (context, index) {
                  final classe = _availableClasses[index];
                  return ListTile(
                    leading: Radio<String>(
                      value: classe,
                      groupValue: _selectedClass,
                      onChanged: (value) {
                        setState(() {
                          _selectedClass = value!;
                          _filterData(_searchController.text);
                        });
                        Navigator.pop(context);
                      },
                    ),
                    title: Text(classe),
                    onTap: () {
                      setState(() {
                        _selectedClass = classe;
                        _filterData(_searchController.text);
                      });
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    final theme = Theme.of(context);
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              decoration: InputDecoration(
                labelText: 'Rechercher un compte',
                labelStyle: TextStyle(color: theme.colorScheme.primary),
                hintText: 'Numéro, intitulé ou classe...',
                hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                prefixIcon:
                    Icon(Icons.search, color: theme.colorScheme.primary),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon:
                            Icon(Icons.clear, color: theme.colorScheme.primary),
                        onPressed: () {
                          _searchController.clear();
                          _filterData('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: theme.colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
                fillColor: theme.colorScheme.surface,
                filled: true,
              ),
              onChanged: _filterData,
            ),
          ),
          InkWell(
            onTap: _showClassFilterBottomSheet,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                children: [
                  Icon(Icons.filter_list,
                      size: 20, color: theme.colorScheme.primary),
                  const SizedBox(width: 4),
                  Flexible(
                    child: Text(
                      'Classe: $_selectedClass',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.primary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(Icons.arrow_drop_down,
                      size: 20, color: theme.colorScheme.primary),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getIndentationForAccount(String accountNumber) {
    return (accountNumber.length - 1) * 16.0;
  }

  Widget _buildAccountTile(String accountNumber, Map<String, dynamic> account) {
    final theme = Theme.of(context);
    final Color accountColor = _getAccountColor(accountNumber);
    final indentation = _getIndentationForAccount(accountNumber);

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1, 0),
        end: Offset.zero,
      ).animate(_animation),
      child: Card(
        margin: EdgeInsets.fromLTRB(8 + indentation, 2, 8, 2),
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Theme(
          data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
          child: ExpansionTile(
            leading: CircleAvatar(
              backgroundColor: accountColor.withValues(alpha: 0.2),
              radius: 14,
              child: Text(
                accountNumber.substring(0, 1),
                style: TextStyle(
                  color: accountColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            title: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        accountNumber,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        account['intitule'],
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.copy, size: 16, color: accountColor),
                  onPressed: () => _copyToClipboard(accountNumber),
                  tooltip: 'Copier le numéro',
                ),
              ],
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (account['classe'] != null)
                      _buildDetailRow(
                        'Classe',
                        account['classe'].toString(),
                        Icons.category,
                        accountColor,
                      ),
                    if (account['rubrique'] != null)
                      _buildDetailRow(
                        'Rubrique',
                        account['rubrique'].toString(),
                        Icons.bookmark,
                        accountColor,
                      ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton.icon(
                        icon: Icon(Icons.copy, size: 16, color: accountColor),
                        label: Text(
                          'Copier tout',
                          style: TextStyle(color: accountColor, fontSize: 14),
                        ),
                        onPressed: () => _copyToClipboard(
                          '$accountNumber - ${account['intitule']}\n'
                          'Classe: ${account['classe']}\n'
                          'Rubrique: ${account['rubrique']}',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getAccountColor(String accountNumber) {
    final themeService = Provider.of<ThemeService>(context);
    final firstDigit = int.tryParse(accountNumber.substring(0, 1)) ?? 0;

    // Définir des palettes de couleurs spécifiques pour chaque thème
    final Map<ThemeType, List<Color>> themeColors = {
      ThemeType.light: [
        Colors.blue,
        Colors.green,
        Colors.orange,
        Colors.purple,
        Colors.red,
        Colors.teal,
        Colors.indigo,
        Colors.pink,
        Colors.amber,
        Colors.cyan,
      ],
      ThemeType.dark: [
        Colors.blue.shade300,
        Colors.green.shade300,
        Colors.orange.shade300,
        Colors.purple.shade300,
        Colors.red.shade300,
        Colors.teal.shade300,
        Colors.indigo.shade300,
        Colors.pink.shade300,
        Colors.amber.shade300,
        Colors.cyan.shade300,
      ],
      ThemeType.sepia: [
        const Color(0xFF8B4513),
        const Color(0xFF8B7355),
        const Color(0xFFCD853F),
        const Color(0xFFD2691E),
        const Color(0xFFA0522D),
        const Color(0xFF6B4423),
        const Color(0xFF8B7765),
        const Color(0xFFBC8F8F),
        const Color(0xFFDEB887),
        const Color(0xFFD2B48C),
      ],
      ThemeType.tokyoNight: [
        const Color(0xFF7AA2F7),
        const Color(0xFF9ECE6A),
        const Color(0xFFFF9E64),
        const Color(0xFFBB9AF7),
        const Color(0xFFF7768E),
        const Color(0xFF7DCFFF),
        const Color(0xFFE0AF68),
        const Color(0xFF2AC3DE),
        const Color(0xFF73DACA),
        const Color(0xFFB4F9F8),
      ],
      ThemeType.solarizedDark: [
        const Color(0xFF268BD2),
        const Color(0xFF859900),
        const Color(0xFFCB4B16),
        const Color(0xFF6C71C4),
        const Color(0xFFDC322F),
        const Color(0xFF2AA198),
        const Color(0xFFB58900),
        const Color(0xFFD33682),
        const Color(0xFF839496),
        const Color(0xFF93A1A1),
      ],
      ThemeType.monokaiDimmed: [
        const Color(0xFFAE81FF),
        const Color(0xFFA6E22E),
        const Color(0xFFE6DB74),
        const Color(0xFFFD971F),
        const Color(0xFFF92672),
        const Color(0xFF66D9EF),
        const Color(0xFFFFE792),
        const Color(0xFFFF669D),
        const Color(0xFFCFCFC2),
        const Color(0xFF75715E),
      ],
    };

    final colors =
        themeColors[themeService.currentTheme] ?? themeColors[ThemeType.light]!;
    return colors[firstDigit % colors.length];
  }

  Widget _buildDetailRow(
      String label, String value, IconData icon, Color color) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewToggle() {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: SegmentedButton<ViewType>(
        segments: const [
          ButtonSegment<ViewType>(
            value: ViewType.list,
            icon: Icon(Icons.view_list),
            label: Text('Liste'),
          ),
          ButtonSegment<ViewType>(
            value: ViewType.tree,
            icon: Icon(Icons.account_tree),
            label: Text('Arbre'),
          ),
        ],
        selected: {_currentView},
        onSelectionChanged: (Set<ViewType> newSelection) {
          setState(() {
            _currentView = newSelection.first;
          });
        },
        style: SegmentedButton.styleFrom(
          visualDensity: VisualDensity.compact,
          backgroundColor: theme.colorScheme.surface,
          selectedBackgroundColor: theme.colorScheme.primaryContainer,
        ),
      ),
    );
  }

  Widget _buildTreeView() {
    if (_filteredData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun résultat trouvé',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    // Organize data into a tree structure
    Map<String, List<MapEntry<String, dynamic>>> accountTree = {};

    // First, group by class
    for (var entry in _filteredData.entries) {
      final classe = entry.value['classe']?.toString() ?? 'Autres';
      accountTree.putIfAbsent(classe, () => []);
      accountTree[classe]!.add(entry);
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 80),
      itemCount: accountTree.length,
      itemBuilder: (context, index) {
        final classe = accountTree.keys.elementAt(index);
        final accounts = accountTree[classe]!;

        return _buildClassSection(classe, accounts);
      },
    );
  }

  Widget _buildClassSection(
      String classe, List<MapEntry<String, dynamic>> accounts) {
    final theme = Theme.of(context);
    final isExpanded = _expandedNodes[classe] ?? false;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Column(
        children: [
          ListTile(
            title: Text(
              classe,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            trailing: RotatedBox(
              quarterTurns: isExpanded ? 1 : 0,
              child: Icon(
                Icons.chevron_right,
                color: theme.colorScheme.primary,
              ),
            ),
            onTap: () {
              setState(() {
                _expandedNodes[classe] = !isExpanded;
              });
            },
          ),
          if (isExpanded)
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: accounts.length,
              itemBuilder: (context, index) {
                final account = accounts[index];
                return _buildTreeAccountTile(account.key, account.value);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildTreeAccountTile(
      String accountNumber, Map<String, dynamic> account) {
    final theme = Theme.of(context);
    final Color accountColor = _getAccountColor(accountNumber);
    final indentation = _getIndentationForAccount(accountNumber);
    final hasChildren = _filteredData.keys
        .any((k) => k.startsWith(accountNumber) && k != accountNumber);
    final isExpanded = _expandedNodes[accountNumber] ?? false;

    return Column(
      children: [
        InkWell(
          onTap: hasChildren
              ? () {
                  setState(() {
                    _expandedNodes[accountNumber] = !isExpanded;
                  });
                }
              : null,
          child: Padding(
            padding: EdgeInsets.only(left: indentation),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: accountColor.withValues(alpha: 0.2),
                radius: 14,
                child: Text(
                  accountNumber.substring(0, 1),
                  style: TextStyle(
                    color: accountColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              title: Text(
                '$accountNumber - ${account['intitule']}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.copy, size: 16, color: accountColor),
                    onPressed: () => _copyToClipboard(accountNumber),
                    tooltip: 'Copier le numéro',
                  ),
                  if (hasChildren)
                    RotatedBox(
                      quarterTurns: isExpanded ? 1 : 0,
                      child: Icon(
                        Icons.chevron_right,
                        color: theme.colorScheme.onSurfaceVariant,
                        size: 20,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
        if (isExpanded && hasChildren)
          ...(_filteredData.entries
              .where((e) =>
                  e.key.startsWith(accountNumber) && e.key != accountNumber)
              .map((e) => _buildTreeAccountTile(e.key, e.value))
              .toList()),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: Hero(
          tag: 'Plan Comptable',
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              'Plan Comptable Marocain',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline, color: theme.colorScheme.onSurface),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  surfaceTintColor: theme.colorScheme.surfaceTint,
                  title: Text(
                    'À propos',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  content: Text(
                    'Plan Comptable Marocain normalisé selon les normes comptables marocaines.\n\n'
                    'Ce plan comptable est basé sur les normes comptables marocaines en vigueur '
                    'et est régulièrement mis à jour pour refléter les dernières modifications '
                    'réglementaires.',
                    style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        'Fermer',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: theme.colorScheme.primary),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: theme.colorScheme.primary,
              ),
            )
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        size: 48,
                        color: theme.colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.error,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.refresh_rounded),
                        label: const Text('Réessayer'),
                        onPressed: _loadPlanComptableData,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                        ),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    _buildSearchBar(),
                    const SizedBox(height: 8),
                    _buildViewToggle(),
                    const Divider(height: 1, thickness: 1),
                    const SizedBox(height: 8),
                    Expanded(
                      child: _currentView == ViewType.list
                          ? RefreshIndicator(
                              onRefresh: _loadPlanComptableData,
                              color: theme.colorScheme.primary,
                              child: ListView.builder(
                                controller: _scrollController,
                                physics: const AlwaysScrollableScrollPhysics(),
                                padding: const EdgeInsets.only(bottom: 80),
                                itemCount: _filteredData.length,
                                itemBuilder: (context, index) {
                                  final accountNumber =
                                      _filteredData.keys.elementAt(index);
                                  final account = _filteredData[accountNumber];
                                  return _buildAccountTile(
                                    accountNumber,
                                    account,
                                  );
                                },
                              ),
                            )
                          : _buildTreeView(),
                    ),
                  ],
                ),
      floatingActionButton: _filteredData.isNotEmpty
          ? FloatingActionButton.extended(
              onPressed: () {
                _scrollController.animateTo(
                  0,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
              },
              icon: const Icon(Icons.arrow_upward),
              label: const Text('Haut'),
              backgroundColor: theme.colorScheme.primaryContainer,
              foregroundColor: theme.colorScheme.onPrimaryContainer,
            )
          : null,
    );
  }
}
