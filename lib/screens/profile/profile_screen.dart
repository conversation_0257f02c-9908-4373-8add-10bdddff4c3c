import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:provider/provider.dart' as provider_pkg; // Renamed import

import '../../models/hive/user_profile.dart';
import '../../models/hive/quiz_attempt.dart';
import '../../models/hive/active_quiz_state.dart';
import '../../models/exam/exam_attempt.dart';
import '../../providers/user_progress_provider.dart';
import '../../providers/quiz_data_provider.dart';
import '../../providers/exam/exam_data_provider.dart'; // Cleaned import
import '../../controllers/quiz_controller.dart';
import '../../screens/quiz/quiz_question_screen.dart';
import '../../models/quiz_model.dart';
import '../../services/theme_service.dart'; // Import for theme service
import '../../services/accessibility_service.dart'; // Accessibility service
// import '../offline/offline_cache_service.dart'; // For offline sync status - service not implemented yet

// Provider to watch the QuizAttempt box for changes
final quizAttemptsStreamProvider = StreamProvider.autoDispose<BoxEvent>((ref) {
  final box = Hive.box<QuizAttempt>('quizAttemptsBox');
  return box.watch();
});

// Provider to watch the active quiz box for changes
final activeQuizStreamProvider = StreamProvider.autoDispose<BoxEvent>((ref) {
  final box = Hive.box<ActiveQuizState>('activeQuizBox');
  return box.watch();
});

// Provider to watch the ExamAttempt box for changes (New)
final examAttemptsStreamProvider = StreamProvider.autoDispose<BoxEvent>((ref) {
  final box = Hive.box<ExamAttempt>('examAttemptsBox');
  return box.watch();
});

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  // This is a placeholder function and will not work as is.
  QuizLevel? findQuizLevel(WidgetRef ref, String categoryName, String levelName) {
    // Ideally, load quiz data from a service/provider here
    // For now, return null to indicate it's not implemented
    print("Warning: findQuizLevel is not implemented. Cannot resume quiz from profile.");
    return null;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final accessibility = AccessibilityService.of(context);
    final userProgressService = ref.watch(userProgressServiceProvider);
    final userProfile = userProgressService.getCurrentUserProgress()?.userProfile;

    // Watch streams to trigger rebuilds, but fetch data directly inside build
    ref.watch(quizAttemptsStreamProvider);
    ref.watch(activeQuizStreamProvider);
    ref.watch(examAttemptsStreamProvider); // Watch exam attempts box

    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    // Get current data directly from boxes
    final List<QuizAttempt> quizAttempts = Hive.box<QuizAttempt>('quizAttemptsBox')
        .values
        .toList()
        .reversed
        .toList();
    final List<ActiveQuizState> activeQuizzes =
        Hive.box<ActiveQuizState>('activeQuizBox').values.toList();
    final List<ExamAttempt> examAttempts =
        Hive.box<ExamAttempt>('examAttemptsBox').values.toList().reversed.toList();

    // Visual flag for preview/test status
    final bool isPreviewVersion = true;

    if (userProfile == null) {
      return Scaffold(
        appBar: AppBar(
          title: Semantics(
            header: true,
            child: Text(
              'Profil Utilisateur',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          backgroundColor: colorScheme.surface,
          elevation: isDark ? 0 : 1,
        ),
        backgroundColor: colorScheme.surface,
        body: Center(
          child: CircularProgressIndicator(
            color: colorScheme.primary,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Semantics(
          header: true,
          child: Text(
            'Profil Utilisateur',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: isDark ? 0 : 1,
        actions: [
          if (isPreviewVersion)
            Chip(
              label: Text(
                'PREVIEW',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
              backgroundColor: Colors.purple,
              padding: EdgeInsets.zero,
              labelPadding: EdgeInsets.symmetric(horizontal: 8),
            ),
          SizedBox(width: 8),
        ],
      ),
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildProfileSection(
                  context, ref, userProfile, textTheme, colorScheme, isDark),
              const SizedBox(height: 24),
              Divider(
                height: 48,
                thickness: 1,
                color: colorScheme.outlineVariant.withValues(alpha: 0.5),
              ),
              _buildAccessibilitySettingsSection(context, ref),
              Divider(
                height: 48,
                thickness: 1,
                color: colorScheme.outlineVariant.withValues(alpha: 0.5),
              ),
              _buildActiveQuizzesSection(
                  context, ref, activeQuizzes, textTheme, colorScheme, isDark),
              Divider(
                height: 48,
                thickness: 1,
                color: colorScheme.outlineVariant.withValues(alpha: 0.5),
              ),
              _buildHistorySection(
                  context, ref, quizAttempts, textTheme, colorScheme, isDark),
              Divider(
                height: 48,
                thickness: 1,
                color: colorScheme.outlineVariant.withValues(alpha: 0.5),
              ),
              _buildExamHistorySection(
                  context, ref, examAttempts, textTheme, colorScheme, isDark),
            ],
          ),
          if (isPreviewVersion)
            Positioned(
              top: 0,
              right: 0,
              child: Banner(
                message: 'TEST',
                color: Colors.purple,
                location: BannerLocation.topEnd,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, WidgetRef ref,
      UserProfile profile, TextTheme textTheme, ColorScheme colorScheme, bool isDark) {
    // final offlineStatus =
    //     provider_pkg.Provider.of<OfflineCacheService>(context, listen: false)
    //         .isCacheValid('user_profile');
    final offlineStatus = true; // Placeholder until OfflineCacheService is implemented
    return Semantics(
      container: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: isDark ? 0 : 1,
            color: colorScheme.surfaceContainerLow,
            surfaceTintColor: colorScheme.surfaceTint,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: isDark
                  ? BorderSide(
                      color: colorScheme.outline.withValues(alpha: 0.2))
                  : BorderSide.none,
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: colorScheme.primaryContainer,
                        child: Text(
                          profile.username.isNotEmpty
                              ? profile.username[0].toUpperCase()
                              : 'U',
                          style: textTheme.headlineMedium
                              ?.copyWith(color: colorScheme.onPrimaryContainer),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Semantics(
                          label:
                              'Nom d\'utilisateur: ${profile.username}',
                          child: Text(
                            profile.username,
                            style: textTheme.titleLarge?.copyWith(
                              color: colorScheme.onSurface,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      focusableIconButton(
                        context: context,
                        tooltip: 'Modifier le nom d\'utilisateur',
                        icon: Icons.edit,
                        onPressed: () => _showEditUsernameDialog(
                            context,
                            ref,
                            profile.username,
                            colorScheme,
                            textTheme,
                            isDark),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Semantics(
                    label: offlineStatus
                        ? 'Statut synchronisation: À jour'
                        : 'Statut synchronisation: Hors ligne',
                    child: Row(
                      children: [
                        Icon(
                          Icons.sync,
                          size: 16,
                          color: offlineStatus
                              ? colorScheme.primary
                              : colorScheme.error,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          offlineStatus ? 'À jour' : 'Hors ligne',
                          style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (profile.hasExamData)
            _buildLastExamScoreDisplay(
                context, profile, textTheme, colorScheme, isDark),
        ],
      ),
    );
  }

  Widget _buildAccessibilitySettingsSection(
      BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return provider_pkg.Consumer<AccessibilityService>(
      builder: (context, accessibility, _) {
        return Semantics(
          container: true,
          explicitChildNodes: true,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Semantics(
                header: true,
                child: Text(
                  'Accessibilité',
                  style: textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Font size slider
              Semantics(
                label: 'Taille de la police',
                value:
                    '${(accessibility?.fontScale ?? 1.0 * 100).round()}%',
                increasedValue: 'Augmenter la taille',
                decreasedValue: 'Diminuer la taille',
                child: Slider(
                  value: accessibility?.fontScale ?? 1.0,
                  min: 0.8,
                  max: 2.0,
                  divisions: 12,
                  label:
                      '${((accessibility?.fontScale ?? 1.0) * 100).round()}%',
                  onChanged: (value) {
                    accessibility?.updateFontScale(value);
                  },
                ),
              ),
              // High contrast toggle
              SwitchListTile(
                title: Semantics(
                  label: 'Mode contraste élevé',
                  child: Text('Contraste élevé'),
                ),
                value: accessibility?.isHighContrastEnabled ?? false,
                onChanged: (value) {
                  accessibility?.toggleHighContrast();
                },
                activeColor: colorScheme.primary,
                contentPadding: EdgeInsets.zero,
              ),
              // Screen reader toggle
              SwitchListTile(
                title: Semantics(
                  label: 'Lecteur d’écran',
                  child: Text('Lecteur d’écran'),
                ),
                value: accessibility?.isScreenReaderEnabled ?? false,
                onChanged: (value) {
                  accessibility?.enableScreenReader(value);
                },
                activeColor: colorScheme.primary,
                contentPadding: EdgeInsets.zero,
              ),
              // Keyboard navigation toggle
              SwitchListTile(
                title: Semantics(
                  label: 'Navigation clavier',
                  child: Text('Navigation clavier'),
                ),
                value: accessibility?.isKeyboardNavigationEnabled ?? false,
                onChanged: (value) {
                  accessibility?.updateKeyboardNavigation(value);
                },
                activeColor: colorScheme.primary,
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLastExamScoreDisplay(
      BuildContext context,
      UserProfile profile,
      TextTheme textTheme,
      ColorScheme colorScheme,
      bool isDark) {
    final percentage = profile.lastExamPercentage;
    final scoreColor = _getExamScoreColor(percentage, colorScheme);
    final isPassing = percentage >= 50;

    return Semantics(
      container: true,
      child: Card(
        margin: const EdgeInsets.only(top: 16),
        elevation: isDark ? 0 : 2,
        surfaceTintColor: colorScheme.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: scoreColor.withValues(alpha: isDark ? 0.4 : 0.6),
            width: isDark ? 1 : 2,
          ),
        ),
        color: colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Semantics(
                header: true,
                child: Row(
                  children: [
                    Icon(Icons.assignment_outlined, color: scoreColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Dernier Examen',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ),
                    if (profile.lastExamDate != null)
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          DateFormat('dd/MM/yyyy')
                              .format(profile.lastExamDate!),
                          style: textTheme.bodySmall
                              ?.copyWith(color: colorScheme.onSurfaceVariant),
                        ),
                      ),
                  ],
                ),
              ),
              Divider(
                height: 24,
                color: colorScheme.outlineVariant.withValues(alpha: 0.5),
              ),
              Row(
                children: [
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        CircularProgressIndicator(
                          value: percentage / 100,
                          strokeWidth: 8,
                          backgroundColor: colorScheme.surfaceContainerHighest
                              .withValues(alpha: isDark ? 0.3 : 0.5),
                          valueColor:
                              AlwaysStoppedAnimation<Color>(scoreColor),
                        ),
                        Text(
                          '${percentage.toInt()}%',
                          style: textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: scoreColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Score: ${profile.lastExamScore}/${profile.lastExamTotal}',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: scoreColor.withValues(
                                alpha: isDark ? 0.1 : 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isPassing
                                    ? Icons.emoji_events_outlined
                                    : Icons.refresh_outlined,
                                size: 16,
                                color: scoreColor,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                isPassing ? 'Réussi' : 'À revoir',
                                style: textTheme.bodyMedium?.copyWith(
                                  color: scoreColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (profile.bestExamPercentage != null &&
                            profile.bestExamPercentage! > percentage)
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              'Meilleur résultat: ${profile.bestExamPercentage!.toInt()}%',
                              style: textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest
                      .withValues(alpha: isDark ? 0.2 : 0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color: colorScheme.outline.withValues(alpha: isDark ? 0.1 : 0.2)),
                ),
                child: Row(
                  children: [
                    Icon(
                      isPassing
                          ? Icons.tips_and_updates_outlined
                          : Icons.lightbulb_outline,
                      color: isPassing ? Colors.amber : colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isPassing
                            ? 'Excellent! Continuez votre progression.'
                            : 'Continuez à pratiquer pour améliorer vos résultats.',
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActiveQuizzesSection(BuildContext context, WidgetRef ref,
      List<ActiveQuizState> activeQuizzes, TextTheme textTheme, ColorScheme colorScheme, bool isDark) {
    return Semantics(
      container: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Semantics(
            header: true,
            child: Text(
              'Quiz en Cours',
              style: textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (activeQuizzes.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 24.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.pause_circle_outline,
                      size: 48,
                      color:
                          colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Aucun quiz en cours.',
                      style: textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: activeQuizzes.length,
              itemBuilder: (context, index) {
                final activeQuiz = activeQuizzes[index];
                final formattedDate = DateFormat('dd/MM/yyyy HH:mm')
                    .format(activeQuiz.lastSavedTimestamp);

                return FocusableActionDetector(
                  child: Card(
                    elevation: isDark ? 0 : 1,
                    margin: const EdgeInsets.only(bottom: 12),
                    color: colorScheme.secondaryContainer
                        .withValues(alpha: isDark ? 0.2 : 0.3),
                    surfaceTintColor: colorScheme.secondaryContainer,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: isDark
                          ? BorderSide(
                              color: colorScheme.outline.withValues(alpha: 0.2))
                          : BorderSide.none,
                    ),
                    child: ListTile(
                      leading: Icon(
                        Icons.pause_circle_filled_outlined,
                        color: colorScheme.onSecondaryContainer
                            .withValues(alpha: 0.8),
                      ),
                      title: Text(
                        '${activeQuiz.categoryName} - ${activeQuiz.levelName}',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      subtitle: Text(
                        'Question ${activeQuiz.currentQuestionIndex + 1} - Sauvegardé: $formattedDate',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      trailing: Icon(
                        Icons.play_arrow_rounded,
                        color: colorScheme.onSecondaryContainer
                            .withValues(alpha: 0.8),
                      ),
                      onTap: () {
                        final quizDataService =
                            ref.read(quizDataServiceProvider);
                        QuizLevel? originalLevel = quizDataService
                            .findQuizLevel(activeQuiz.categoryName,
                                activeQuiz.levelName);
                        Color categoryColor = quizDataService
                                .findCategoryColor(activeQuiz.categoryName) ??
                            Colors.blue;

                        if (originalLevel != null && context.mounted) {
                          ref.read(quizControllerProvider((originalLevel,
                              activeQuiz.categoryName, activeQuiz)));
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => QuizQuestionScreen(
                                level: originalLevel,
                                categoryName: activeQuiz.categoryName,
                                categoryColor: categoryColor,
                              ),
                            ),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text(
                                    'Erreur: Données du quiz introuvables pour reprendre.',
                                    style:
                                        TextStyle(color: colorScheme.onError),
                                  ),
                                  backgroundColor: colorScheme.error));
                        }
                      },
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildHistorySection(BuildContext context, WidgetRef ref,
      List<QuizAttempt> attempts, TextTheme textTheme, ColorScheme colorScheme, bool isDark) {
    return Semantics(
      container: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Semantics(
            header: true,
            child: Text(
              'Historique des Quiz',
              style: textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (attempts.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 32.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.history_toggle_off_outlined,
                      size: 48,
                      color:
                          colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Aucun quiz terminé pour le moment.',
                      style: textTheme.bodyLarge?.copyWith(
                        color:
                            colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: attempts.length,
              itemBuilder: (context, index) {
                final attempt = attempts[index];
                final percentage =
                    (attempt.score / attempt.totalQuestions * 100).round();
                final formattedDate =
                    DateFormat('dd/MM/yyyy HH:mm').format(attempt.timestamp);

                return FocusableActionDetector(
                  child: Card(
                    elevation: isDark ? 0 : 1,
                    margin: const EdgeInsets.only(bottom: 12),
                    color: colorScheme.surfaceContainer,
                    surfaceTintColor: colorScheme.surfaceTint,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: isDark
                          ? BorderSide(
                              color:
                                  colorScheme.outline.withValues(alpha: 0.2))
                          : BorderSide.none,
                    ),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor:
                            _getScoreColor(percentage, colorScheme)
                                .withValues(alpha: isDark ? 0.1 : 0.1),
                        child: Icon(
                          ref
                                  .read(quizDataServiceProvider)
                                  .findCategoryIcon(attempt.categoryName) ??
                              Icons.question_mark,
                          color:
                              _getScoreColor(percentage, colorScheme),
                          size: 20,
                        ),
                      ),
                      title: Text(
                        '${attempt.categoryName} - ${attempt.levelName}',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      subtitle: Text(
                        'Score: ${attempt.score}/${attempt.totalQuestions} (${attempt.earnedPoints} pts) - $formattedDate',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      trailing: Icon(
                        Icons.check_circle_outline,
                        color:
                            colorScheme.outline.withValues(alpha: 0.6),
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildExamHistorySection(BuildContext context, WidgetRef ref,
      List<ExamAttempt> attempts, TextTheme textTheme, ColorScheme colorScheme, bool isDark) {
    final allExamsAsync = ref.watch(examListProvider);
    return Semantics(
      container: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Semantics(
            header: true,
            child: Text(
              'Historique des Examens',
              style: textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (attempts.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 32.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.assignment_turned_in_outlined,
                      size: 48,
                      color: colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Aucun examen terminé pour le moment.',
                      style: textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            allExamsAsync.when(
              data: (allExams) {
                final examTitleMap = {for (var exam in allExams) exam.id: exam.title};
                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: attempts.length,
                  itemBuilder: (context, index) {
                    final attempt = attempts[index];
                    final percentage = attempt.percentage;
                    final formattedDate =
                        DateFormat('dd/MM/yyyy HH:mm').format(attempt.timestamp);
                    final examTitle =
                        examTitleMap[attempt.examId] ?? 'Examen Inconnu';
                    final scoreColor =
                        _getExamScoreColor(percentage, colorScheme);
                    return FocusableActionDetector(
                      child: Card(
                        elevation: isDark ? 0 : 1,
                        margin: const EdgeInsets.only(bottom: 12),
                        color: colorScheme.surfaceContainer,
                        surfaceTintColor: colorScheme.surfaceTint,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: isDark
                              ? BorderSide(color: colorScheme.outline.withValues(alpha: 0.2))
                              : BorderSide.none,
                        ),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor:
                                scoreColor.withValues(alpha: isDark ? 0.1 : 0.1),
                            child: Icon(
                              Icons.assignment_outlined,
                              color: scoreColor,
                              size: 20,
                            ),
                          ),
                          title: Text(
                            examTitle,
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          subtitle: Text(
                            'Score: ${attempt.score}/${attempt.totalQuestions} (${percentage.toStringAsFixed(1)}%) - $formattedDate',
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                          trailing: Icon(
                            percentage >= 50 ? Icons.check_circle : Icons.cancel,
                            color: scoreColor.withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.primary)),
              error: (error, stack) =>
                  Center(child: Text('Erreur chargement examens: $error')),
            ),
        ],
      ),
    );
  }

  Color _getScoreColor(int percentage, ColorScheme colorScheme) {
    if (percentage >= 90) return Colors.green.shade700;
    if (percentage >= 70) return Colors.blue.shade700;
    if (percentage >= 50) return Colors.orange.shade700;
    return colorScheme.error;
  }

  void _showEditUsernameDialog(BuildContext context, WidgetRef ref,
      String currentUsername, ColorScheme colorScheme, TextTheme textTheme, bool isDark) {
    final controller = TextEditingController(text: currentUsername);
    final formKey = GlobalKey<FormState>();
    final accessibility = AccessibilityService.of(context);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Semantics(
            header: true,
            child: Text(
              'Modifier le nom d\'utilisateur',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          backgroundColor: colorScheme.surface,
          surfaceTintColor: colorScheme.surfaceTint,
          elevation: isDark ? 0 : 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isDark
                ? BorderSide(
                    color: colorScheme.outline.withValues(alpha: 0.2))
                : BorderSide.none,
          ),
          content: Form(
            key: formKey,
            child: Semantics(
              label: accessibility?.generateFormFieldLabel(
                  'Nouveau nom', true, null, 'Entrez votre nom'),
              child: TextFormField(
                controller: controller,
                autofocus: true,
                style: TextStyle(color: colorScheme.onSurface),
                decoration: InputDecoration(
                  hintText: 'Nouveau nom',
                  hintStyle: TextStyle(
                      color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: colorScheme.outline),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: colorScheme.primary, width: 2),
                  ),
                  filled: isDark,
                  fillColor: isDark
                      ? colorScheme.surfaceContainerHighest.withValues(alpha: 0.3)
                      : null,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    accessibility?.announceError('Le nom ne peut pas être vide.');
                    return 'Le nom ne peut pas être vide.';
                  }
                  return null;
                },
              ),
            ),
          ),
          actions: [
            focusableTextButton(
              context: context,
              label: 'Annuler',
              onPressed: () => Navigator.pop(context),
              color: colorScheme.primary,
            ),
            focusableElevatedButton(
              context: context,
              label: 'Enregistrer',
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
              elevation: isDark ? 0 : 2,
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final newUsername = controller.text.trim();
                  final userProgressService =
                      ref.read(userProgressServiceProvider);
                  final currentProgress =
                      userProgressService.getCurrentUserProgress();
                  if (currentProgress != null) {
                    final updatedProfile =
                        UserProfile(username: newUsername);
                    await userProgressService
                        .updateUserProfile(updatedProfile);
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text(
                        'Nom d\'utilisateur mis à jour!',
                        style:
                            TextStyle(color: colorScheme.onPrimaryContainer),
                      ),
                      backgroundColor: colorScheme.primaryContainer,
                    ));
                    accessibility?.announceSuccess(
                        'Nom d\'utilisateur mis à jour');
                  } else {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text(
                        'Erreur: Impossible de trouver le profil.',
                        style: TextStyle(color: colorScheme.onError),
                      ),
                      backgroundColor: colorScheme.error,
                    ));
                    accessibility?.announceError(
                        'Impossible de trouver le profil');
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  Widget focusableIconButton({
    required BuildContext context,
    required String tooltip,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    final focusNode = FocusNode();
    return Focus(
      focusNode: focusNode,
      onKeyEvent: (node, event) {
        if (event.logicalKey == LogicalKeyboardKey.enter ||
            event.logicalKey == LogicalKeyboardKey.space) {
          onPressed();
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: IconButton(
        icon: Icon(icon, color: Theme.of(context).colorScheme.primary),
        tooltip: tooltip,
        onPressed: onPressed,
      ),
    );
  }

  Widget focusableTextButton({
    required BuildContext context,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    final focusNode = FocusNode();
    return Focus(
      focusNode: focusNode,
      onKeyEvent: (node, event) {
        if (event.logicalKey == LogicalKeyboardKey.enter ||
            event.logicalKey == LogicalKeyboardKey.space) {
          onPressed();
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: TextButton(
        onPressed: onPressed,
        child: Text(label, style: TextStyle(color: color)),
      ),
    );
  }

  Widget focusableElevatedButton({
    required BuildContext context,
    required String label,
    required Color backgroundColor,
    required Color foregroundColor,
    required double elevation,
    required VoidCallback onPressed,
  }) {
    final focusNode = FocusNode();
    return Focus(
      focusNode: focusNode,
      onKeyEvent: (node, event) {
        if (event.logicalKey == LogicalKeyboardKey.enter ||
            event.logicalKey == LogicalKeyboardKey.space) {
          onPressed();
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          elevation: elevation,
        ),
        child: Text(label),
      ),
    );
  }

  /// Get color for exam score based on percentage
  Color _getExamScoreColor(double percentage, ColorScheme colorScheme) {
    if (percentage >= 80) {
      return Colors.green;
    } else if (percentage >= 60) {
      return Colors.orange;
    } else {
      return colorScheme.error;
    }
  }
}