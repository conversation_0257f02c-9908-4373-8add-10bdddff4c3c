import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/calculators/calculation_history_widget.dart';
import '../../providers/calculation_history_provider.dart';
import '../../theme/design_tokens.dart';
import '../../models/calculators/calculation_history_item.dart';

class CalculationHistoryScreen extends ConsumerStatefulWidget {
  const CalculationHistoryScreen({super.key});

  @override
  ConsumerState<CalculationHistoryScreen> createState() => _CalculationHistoryScreenState();
}

class _CalculationHistoryScreenState extends ConsumerState<CalculationHistoryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final historyActions = ref.read(historyActionsProvider);
    final selectedItems = ref.watch(selectedHistoryItemsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Historique des Calculs'),
        backgroundColor: DesignTokens.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(
              icon: Icon(Icons.history),
              text: 'Tous',
            ),
            Tab(
              icon: Icon(Icons.star),
              text: 'Favoris',
            ),
            Tab(
              icon: Icon(Icons.analytics),
              text: 'Statistiques',
            ),
          ],
        ),
        actions: [
          if (selectedItems.isNotEmpty)
            IconButton(
              icon: Badge(
                label: Text(selectedItems.length.toString()),
                child: const Icon(Icons.checklist),
              ),
              onPressed: _showBulkActionsDialog,
            ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_all',
                child: ListTile(
                  leading: Icon(Icons.file_download),
                  title: Text('Exporter tout'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'cleanup',
                child: ListTile(
                  leading: Icon(Icons.cleaning_services),
                  title: Text('Nettoyer'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'backup',
                child: ListTile(
                  leading: Icon(Icons.backup),
                  title: Text('Sauvegarde'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'restore',
                child: ListTile(
                  leading: Icon(Icons.restore),
                  title: Text('Restaurer'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllHistoryTab(),
          _buildFavoritesTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  Widget _buildAllHistoryTab() {
    return const CalculationHistoryWidget();
  }

  Widget _buildFavoritesTab() {
    final favoritesAsync = ref.watch(favoriteCalculationsProvider);

    return favoritesAsync.when(
      data: (favorites) {
        if (favorites.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.star_border,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'Aucun calcul favori',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Marquez vos calculs importants\ncomme favoris pour les retrouver ici',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: favorites.length,
          itemBuilder: (context, index) {
            final item = favorites[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.amber.withOpacity(0.1),
                  child: const Icon(Icons.star, color: Colors.amber),
                ),
                title: Text(item.displayName),
                subtitle: Text(_formatDate(item.createdAt)),
                trailing: IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: () => _showItemActions(item),
                ),
                onTap: () => _openCalculation(item),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Erreur: $error'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsTab() {
    final statisticsAsync = ref.watch(historyStatisticsProvider);
    final typeCountsAsync = ref.watch(calculatorTypeCountsProvider);
    final mostUsedAsync = ref.watch(mostUsedCalculatorsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Statistiques d\'utilisation',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: DesignTokens.colorPrimary,
            ),
          ),
          const SizedBox(height: 16),

          // General statistics
          statisticsAsync.when(
            data: (stats) => _buildStatisticsCards(stats),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Text('Erreur: $error'),
          ),

          const SizedBox(height: 24),

          // Calculator usage breakdown
          Text(
            'Répartition par calculateur',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          typeCountsAsync.when(
            data: (counts) => _buildCalculatorBreakdown(counts),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Text('Erreur: $error'),
          ),

          const SizedBox(height: 24),

          // Most used calculators
          Text(
            'Calculateurs les plus utilisés',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          mostUsedAsync.when(
            data: (mostUsed) => _buildMostUsedList(mostUsed),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Text('Erreur: $error'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards(Map<String, dynamic> stats) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          'Total Calculs',
          stats['totalCalculations'].toString(),
          Icons.calculate,
          Colors.blue,
        ),
        _buildStatCard(
          'Favoris',
          stats['favoriteCount'].toString(),
          Icons.star,
          Colors.amber,
        ),
        _buildStatCard(
          'Ce mois',
          stats['last30Days'].toString(),
          Icons.calendar_month,
          Colors.green,
        ),
        _buildStatCard(
          'Moyenne/semaine',
          stats['averagePerWeek'],
          Icons.trending_up,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatorBreakdown(Map<dynamic, int> counts) {
    final total = counts.values.fold(0, (sum, count) => sum + count);
    
    return Column(
      children: counts.entries.map((entry) {
        final type = entry.key;
        final count = entry.value;
        final percentage = total > 0 ? (count / total * 100) : 0;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        type.toString(),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: percentage / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          DesignTokens.colorPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      count.toString(),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${percentage.toStringAsFixed(1)}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMostUsedList(List<MapEntry<dynamic, int>> mostUsed) {
    return Column(
      children: mostUsed.take(5).map((entry) {
        final type = entry.key;
        final count = entry.value;
        
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: DesignTokens.colorPrimary.withOpacity(0.1),
            child: Text(
              (mostUsed.indexOf(entry) + 1).toString(),
              style: TextStyle(
                color: DesignTokens.colorPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          title: Text(type.toString()),
          trailing: Text(
            '$count calculs',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        );
      }).toList(),
    );
  }

  void _showBulkActionsDialog() {
    final bulkActions = ref.read(historyBulkActionsProvider);
    
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.star),
            title: const Text('Ajouter aux favoris'),
            onTap: () {
              Navigator.pop(context);
              bulkActions.toggleFavoriteSelected(true);
            },
          ),
          ListTile(
            leading: const Icon(Icons.star_border),
            title: const Text('Retirer des favoris'),
            onTap: () {
              Navigator.pop(context);
              bulkActions.toggleFavoriteSelected(false);
            },
          ),
          ListTile(
            leading: const Icon(Icons.file_download),
            title: const Text('Exporter'),
            onTap: () {
              Navigator.pop(context);
              _showExportDialog();
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Supprimer', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              _confirmBulkDelete();
            },
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    final bulkActions = ref.read(historyBulkActionsProvider);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Format d\'export'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Excel'),
              onTap: () {
                Navigator.pop(context);
                bulkActions.exportSelected(ExportFormat.excel);
              },
            ),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('PDF'),
              onTap: () {
                Navigator.pop(context);
                bulkActions.exportSelected(ExportFormat.pdf);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _confirmBulkDelete() {
    final selectedItems = ref.read(selectedHistoryItemsProvider);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text('Voulez-vous vraiment supprimer ${selectedItems.length} élément(s) ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(historyBulkActionsProvider).deleteSelected();
            },
            child: const Text('Supprimer', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    final historyActions = ref.read(historyActionsProvider);
    
    switch (action) {
      case 'export_all':
        _exportAllHistory();
        break;
      case 'cleanup':
        _showCleanupDialog();
        break;
      case 'backup':
        _createBackup();
        break;
      case 'restore':
        _restoreBackup();
        break;
    }
  }

  void _exportAllHistory() {
    // TODO: Implement export all functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export en cours...')),
    );
  }

  void _showCleanupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nettoyer l\'historique'),
        content: const Text('Supprimer les calculs de plus d\'un an (sauf favoris) ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(historyActionsProvider).cleanupOldEntries();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Nettoyage effectué')),
              );
            },
            child: const Text('Nettoyer'),
          ),
        ],
      ),
    );
  }

  void _createBackup() async {
    try {
      final backup = await ref.read(historyActionsProvider).exportBackup();
      // TODO: Save backup to file
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Sauvegarde créée'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _restoreBackup() {
    // TODO: Implement restore functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fonctionnalité à venir')),
    );
  }

  void _showItemActions(dynamic item) {
    // TODO: Implement item actions
  }

  void _openCalculation(dynamic item) {
    // TODO: Implement calculation opening
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
