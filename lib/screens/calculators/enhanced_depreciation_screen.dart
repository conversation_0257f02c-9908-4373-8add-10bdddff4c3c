import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/calculators/enhanced_depreciation_calculator.dart';
import '../../providers/calculator_providers.dart';
import '../../models/calculators/calculation_history_item.dart';
import '../../theme/design_tokens.dart';
import '../../theme/responsive_breakpoints.dart';

class EnhancedDepreciationScreen extends ConsumerStatefulWidget {
  const EnhancedDepreciationScreen({super.key});

  @override
  ConsumerState<EnhancedDepreciationScreen> createState() => _EnhancedDepreciationScreenState();
}

class _EnhancedDepreciationScreenState extends ConsumerState<EnhancedDepreciationScreen> {
  bool _showHelp = false;

  @override
  void initState() {
    super.initState();
    // Set active calculator
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(activeCalculatorProvider.notifier).state = CalculatorType.depreciation;
    });
  }

  @override
  Widget build(BuildContext context) {
    final calculatorState = ref.watch(calculatorStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Amortissement Avancé'),
        backgroundColor: DesignTokens.colorPrimary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_showHelp ? Icons.help : Icons.help_outline),
            onPressed: () {
              setState(() {
                _showHelp = !_showHelp;
              });
            },
          ),
          if (calculatorState.canExport)
            PopupMenuButton<String>(
              icon: const Icon(Icons.file_download),
              onSelected: _handleExport,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'pdf',
                  child: ListTile(
                    leading: Icon(Icons.picture_as_pdf),
                    title: Text('Exporter en PDF'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'excel',
                  child: ListTile(
                    leading: Icon(Icons.table_chart),
                    title: Text('Exporter en Excel'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'presets',
                child: ListTile(
                  leading: Icon(Icons.bookmark),
                  title: Text('Modèles d\'actifs'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: ListTile(
                  leading: Icon(Icons.clear),
                  title: Text('Effacer'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'history',
                child: ListTile(
                  leading: Icon(Icons.history),
                  title: Text('Historique'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('À propos'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          if (_showHelp) _buildHelpSection(),
          Expanded(
            child: const EnhancedDepreciationCalculator(),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpSection() {
    final isMobile = context.isMobile;

    if (isMobile) {
      return ExpansionTile(
        leading: Icon(Icons.lightbulb, color: Colors.orange[700]),
        title: Text(
          'Guide des méthodes d\'amortissement',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.orange[700],
            fontWeight: FontWeight.bold,
          ),
        ),
        children: [
          Container(
            margin: context.responsivePadding(
              mobile: const EdgeInsets.symmetric(horizontal: 12),
              tablet: const EdgeInsets.all(16),
              desktop: const EdgeInsets.all(20),
            ),
            padding: context.responsivePadding(
              mobile: const EdgeInsets.all(12),
              tablet: const EdgeInsets.all(16),
              desktop: const EdgeInsets.all(20),
            ),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: _buildHelpContent(),
          ),
        ],
      );
    }

    return Container(
      margin: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      padding: context.responsivePadding(
        mobile: const EdgeInsets.all(12),
        tablet: const EdgeInsets.all(16),
        desktop: const EdgeInsets.all(20),
      ),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.orange[700]),
              const SizedBox(width: 8),
              Text(
                'Guide des méthodes d\'amortissement',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildHelpContent(),
        ],
      ),
    );
  }

  Widget _buildHelpContent() {
    return Column(
      children: [
        _buildMethodHelp(
          'Linéaire',
          'Amortissement constant chaque année. Simple et conforme.',
          Icons.trending_flat,
        ),
        _buildMethodHelp(
          'Dégressive',
          'Amortissement plus important les premières années. Optimise la trésorerie.',
          Icons.trending_down,
        ),
        _buildMethodHelp(
          'Somme des chiffres',
          'Amortissement dégressif basé sur la somme des années.',
          Icons.functions,
        ),
        _buildMethodHelp(
          'Unités de production',
          'Amortissement basé sur l\'utilisation réelle de l\'actif.',
          Icons.precision_manufacturing,
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.tips_and_updates, color: Colors.orange[700], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Utilisez le mode comparaison pour voir toutes les méthodes et choisir la plus avantageuse.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMethodHelp(String method, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.orange[700], size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  method,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.orange[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleExport(String format) async {
    try {
      final exportActions = ref.read(calculatorExportProvider);
      await exportActions.exportDepreciation(format: format);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export $format réussi'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'export: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'presets':
        _showPresetsDialog();
        break;
      case 'clear':
        _showClearConfirmation();
        break;
      case 'history':
        _navigateToHistory();
        break;
      case 'about':
        _showAboutDialog();
        break;
    }
  }

  void _showPresetsDialog() {
    final presets = ref.read(calculatorPresetsProvider)[CalculatorType.depreciation] ?? [];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Modèles d\'Actifs'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: presets.length,
            itemBuilder: (context, index) {
              final preset = presets[index];
              return ListTile(
                title: Text(preset.name),
                subtitle: Text(preset.description),
                onTap: () {
                  Navigator.pop(context);
                  _applyPreset(preset);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _applyPreset(CalculatorPreset preset) {
    // TODO: Apply preset to calculator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Modèle "${preset.name}" appliqué')),
    );
  }

  void _showClearConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Effacer les données'),
        content: const Text('Voulez-vous vraiment effacer toutes les données saisies ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(calculatorActionsProvider).clearDepreciationResults();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Données effacées')),
              );
            },
            child: const Text('Effacer', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _navigateToHistory() {
    Navigator.pushNamed(context, '/calculation-history');
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Calculateur d\'Amortissement Avancé'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Ce calculateur propose quatre méthodes d\'amortissement conformes à la réglementation marocaine :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('• Linéaire : Amortissement constant sur la durée de vie'),
              Text('• Dégressive : Amortissement accéléré les premières années'),
              Text('• Somme des chiffres : Méthode dégressive alternative'),
              Text('• Unités de production : Basé sur l\'utilisation réelle'),
              SizedBox(height: 16),
              Text(
                'Fonctionnalités avancées :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Comparaison automatique des méthodes'),
              Text('• Conseils d\'optimisation fiscale'),
              Text('• Modèles d\'actifs prédéfinis'),
              Text('• Convention de mi-année'),
              Text('• Export PDF et Excel'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
