import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/calculators/tax_optimization_wizard.dart';
import '../../providers/calculator_providers.dart';
import '../../models/calculators/calculation_history_item.dart';
import '../../theme/design_tokens.dart';

class TaxOptimizationScreen extends ConsumerStatefulWidget {
  const TaxOptimizationScreen({super.key});

  @override
  ConsumerState<TaxOptimizationScreen> createState() => _TaxOptimizationScreenState();
}

class _TaxOptimizationScreenState extends ConsumerState<TaxOptimizationScreen> {
  bool _showHelp = false;

  @override
  void initState() {
    super.initState();
    // Set active calculator
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(activeCalculatorProvider.notifier).state = CalculatorType.taxOptimization;
    });
  }

  @override
  Widget build(BuildContext context) {
    final calculatorState = ref.watch(calculatorStateProvider);
    final currentStep = ref.watch(taxOptimizationCurrentStepProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Optimisation Fiscale'),
        backgroundColor: DesignTokens.colorPrimary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_showHelp ? Icons.help : Icons.help_outline),
            onPressed: () {
              setState(() {
                _showHelp = !_showHelp;
              });
            },
          ),
          if (calculatorState.canExport)
            PopupMenuButton<String>(
              icon: const Icon(Icons.file_download),
              onSelected: _handleExport,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'pdf',
                  child: ListTile(
                    leading: Icon(Icons.picture_as_pdf),
                    title: Text('Rapport PDF'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'excel',
                  child: ListTile(
                    leading: Icon(Icons.table_chart),
                    title: Text('Données Excel'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'save_draft',
                child: ListTile(
                  leading: Icon(Icons.save),
                  title: Text('Sauvegarder brouillon'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'load_draft',
                child: ListTile(
                  leading: Icon(Icons.folder_open),
                  title: Text('Charger brouillon'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: ListTile(
                  leading: Icon(Icons.clear),
                  title: Text('Recommencer'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'history',
                child: ListTile(
                  leading: Icon(Icons.history),
                  title: Text('Historique'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('À propos'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          if (_showHelp) _buildHelpSection(),
          Expanded(
            child: const TaxOptimizationWizard(),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.purple[700]),
              const SizedBox(width: 8),
              Text(
                'Assistant d\'optimisation fiscale',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.purple[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildWizardStep(
            1,
            'Profil Entreprise',
            'Informations générales sur votre société',
            Icons.business,
          ),
          _buildWizardStep(
            2,
            'Situation Fiscale',
            'Analyse de votre situation actuelle',
            Icons.assessment,
          ),
          _buildWizardStep(
            3,
            'Objectifs',
            'Définition de vos priorités d\'optimisation',
            Icons.flag,
          ),
          _buildWizardStep(
            4,
            'Contraintes',
            'Budget et tolérance au risque',
            Icons.tune,
          ),
          _buildWizardStep(
            5,
            'Recommandations',
            'Stratégies personnalisées et plan d\'action',
            Icons.recommend,
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.security, color: Colors.purple[700], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Toutes les recommandations respectent la réglementation fiscale marocaine en vigueur.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.purple[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWizardStep(int stepNumber, String title, String description, IconData icon) {
    final currentStep = ref.watch(taxOptimizationCurrentStepProvider);
    final isActive = stepNumber == currentStep + 1;
    final isCompleted = stepNumber < currentStep + 1;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isCompleted
                  ? Colors.green[600]
                  : isActive
                      ? Colors.purple[600]
                      : Colors.grey[300],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: isCompleted
                  ? const Icon(Icons.check, color: Colors.white, size: 16)
                  : isActive
                      ? Icon(icon, color: Colors.white, size: 16)
                      : Text(
                          stepNumber.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isActive ? Colors.purple[700] : Colors.grey[700],
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isActive ? Colors.purple[600] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleExport(String format) async {
    try {
      final exportActions = ref.read(calculatorExportProvider);
      await exportActions.exportTaxOptimization(format: format);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export $format réussi'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'export: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'save_draft':
        _saveDraft();
        break;
      case 'load_draft':
        _loadDraft();
        break;
      case 'clear':
        _showClearConfirmation();
        break;
      case 'history':
        _navigateToHistory();
        break;
      case 'about':
        _showAboutDialog();
        break;
    }
  }

  void _saveDraft() {
    // TODO: Implement draft saving
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Brouillon sauvegardé'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _loadDraft() {
    // TODO: Implement draft loading
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Charger un brouillon'),
        content: const Text('Aucun brouillon disponible.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showClearConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Recommencer l\'analyse'),
        content: const Text('Voulez-vous vraiment effacer toutes les données et recommencer ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(calculatorActionsProvider).clearTaxOptimizationResults();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Analyse réinitialisée')),
              );
            },
            child: const Text('Recommencer', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _navigateToHistory() {
    Navigator.pushNamed(context, '/calculation-history');
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Assistant d\'Optimisation Fiscale'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Cet assistant analyse votre situation fiscale et propose des stratégies d\'optimisation personnalisées.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text(
                'Stratégies d\'optimisation :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Optimisation des amortissements'),
              Text('• Constitution de provisions'),
              Text('• Planification des investissements'),
              Text('• Optimisation du timing des charges'),
              Text('• Changement de régime fiscal'),
              Text('• Avantages fiscaux sectoriels'),
              SizedBox(height: 16),
              Text(
                'L\'analyse prend en compte :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Votre profil d\'entreprise'),
              Text('• Votre situation fiscale actuelle'),
              Text('• Vos objectifs d\'optimisation'),
              Text('• Vos contraintes budgétaires'),
              Text('• Votre tolérance au risque'),
              SizedBox(height: 16),
              Text(
                'Toutes les recommandations sont conformes à la législation marocaine et incluent une évaluation des risques.',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
