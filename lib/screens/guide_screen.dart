import 'package:flutter/material.dart';
import 'guides/comptabilite_approfondie/comptabilite_approfondie_screen.dart';
import 'guides/comptabilite_approfondie/contrats_terme/contrats_terme_screen.dart';
import 'guides/comptabilite_approfondie/consolidation/consolidation_screen.dart';
import 'guides/comptabilite_approfondie/immobilisations/immobilisations_screen.dart';
import 'guides/comptabilite_generale/comptabilite_generale_screen.dart';
import 'guides/comptabilite_generale/introduction_screen.dart';
import 'guides/comptabilite_generale/documents_comptables_screen.dart';
import 'guides/comptabilite_generale/plan_comptable_screen.dart';
import 'guides/comptabilite_generale/operations_courantes_screen.dart';
import 'guides/fiscalite/fiscalite_screen.dart';
import 'guides/fiscalite/tva/tva_screen.dart';
import 'guides/fiscalite/is/is_screen.dart';
import 'guides/fiscalite/ir/ir_screen.dart';
import 'guides/fiscalite/droits_enregistrement/droits_enregistrement_screen.dart';
import 'guides/comptabilite_approfondie/regles_evaluation/regles_evaluation_screen.dart';
import 'guides/fiscalite/agriculture/agriculture_screen.dart';
import 'guides/comptabilite_societes/comptabilite_societes_screen.dart';
import 'guides/comptabilite_societes/constitution/constitution_screen.dart';
import 'guides/comptabilite_societes/modification_capital/modification_capital_screen.dart';
import 'guides/comptabilite_societes/affectation_resultats/affectation_resultats_screen.dart';
import 'guides/comptabilite_societes/dissolution/dissolution_screen.dart';
import 'guides/comptabilite_analytique/comptabilite_analytique_screen.dart';
import 'guides/comptabilite_analytique/methodes_calcul/methodes_calcul_screen.dart';
import 'guides/comptabilite_analytique/centres_analyse/centres_analyse_screen.dart';
import 'guides/comptabilite_analytique/couts_complets/couts_complets_screen.dart';
import 'guides/comptabilite_analytique/seuil_rentabilite/seuil_rentabilite_screen.dart';
import 'guides/comptabilite_analytique/abc_method/abc_method_screen.dart';
import 'guides/comptabilite_analytique/couts_standards/couts_standards_screen.dart';
import 'guides/comptabilite_generale/gestion_stocks_screen.dart';

class GuideScreen extends StatefulWidget {
  const GuideScreen({super.key});

  @override
  State<GuideScreen> createState() => _GuideScreenState();
}

class _GuideScreenState extends State<GuideScreen> {
  final List<Map<String, dynamic>> _guideItems = [
    {
      'number': '1',
      'title': 'Comptabilité Générale',
      'description': 'Principes fondamentaux et opérations',
      'icon': Icons.menu_book,
      'color': Colors.blue,
      'screen': const ComptabiliteGeneraleScreen(),
      'subsections': [
        {
          'title': 'Introduction',
          'description': 'Principes et concepts de base',
          'screen': const IntroductionScreen(),
        },
        {
          'title': 'Documents Comptables',
          'description': 'Bilan, CPC, Balance et Journal',
          'screen': const DocumentsComptablesScreen(),
        },
        {
          'title': 'Plan Comptable',
          'description': 'Structure et organisation des comptes',
          'screen': const PlanComptableScreen(),
        },
        {
          'title': 'Opérations Courantes',
          'description': 'Factures, réductions, TVA et plus',
          'screen': const OperationsCourantesScreen(),
        },
        {
          'title': 'Gestion des Stocks',
          'description': 'Méthodes d\'évaluation et systèmes d\'inventaire',
          'screen': const GestionStocksScreen(),
        },
      ],
    },
    {
      'number': '2',
      'title': 'Comptabilité des Sociétés',
      'description': 'Constitution, modifications et opérations spéciales',
      'icon': Icons.business,
      'color': Colors.indigo,
      'screen': const ComptabiliteSocietesScreen(),
      'subsections': [
        {
          'title': 'Constitution',
          'description': 'Création et formalités de constitution',
          'screen': const ConstitutionScreen(),
        },
        {
          'title': 'Modification du Capital',
          'description': 'Augmentation et réduction du capital',
          'screen': const ModificationCapitalScreen(),
        },
        {
          'title': 'Affectation des Résultats',
          'description': 'Distribution des bénéfices et réserves',
          'screen': const AffectationResultatsScreen(),
        },
        {
          'title': 'Dissolution',
          'description': 'Procédures de dissolution et liquidation',
          'screen': const DissolutionScreen(),
        },
      ],
    },
    {
      'number': '3',
      'title': 'Comptabilité Analytique',
      'description': 'Analyse des coûts et rentabilité',
      'icon': Icons.analytics,
      'color': Colors.teal,
      'screen': const ComptabiliteAnalytiqueScreen(),
      'subsections': [
        {
          'title': 'Méthodes de Calcul des Coûts',
          'description': 'Introduction aux différentes approches de calcul',
          'screen': const MethodesCalculScreen(),
        },
        {
          'title': 'Centres d\'Analyse',
          'description': 'Répartition et imputation des charges',
          'screen': const CentresAnalyseScreen(),
        },
        {
          'title': 'Coûts Complets & Sections Homogènes',
          'description': 'Méthode des coûts complets et sections homogènes',
          'screen': const CoutsCompletsScreen(),
        },
        {
          'title': 'Méthode ABC',
          'description': 'Activity-Based Costing et gestion par activités',
          'screen': const ABCMethodScreen(),
        },
        {
          'title': 'Coûts Standards & Analyse des Écarts',
          'description': 'Coûts préétablis et analyse des performances',
          'screen': const CoutsStandardsScreen(),
        },
        {
          'title': 'Seuil de Rentabilité',
          'description': 'Point mort, analyse CVP et prise de décision',
          'screen': const SeuilRentabiliteScreen(),
        },
      ],
    },
    {
      'number': '4',
      'title': 'Techniques Comptables Approfondies',
      'description': 'Complexité & Difficulté Comptable',
      'icon': Icons.account_balance,
      'color': Colors.brown,
      'screen': const ComptabiliteApprofondieScreen(),
      'subsections': [
        {
          'title': 'Évaluation des Immobilisations',
          'description': 'Corporelles et incorporelles',
          'screen': const ImmobilisationsScreen(),
        },
        {
          'title': 'Règles d\'évaluation',
          'description': 'Amortissements et provisions',
          'screen': const ReglesEvaluationScreen(),
        },
        {
          'title': 'Contrats à terme',
          'description': 'Méthodes d\'achèvement et avancement',
          'screen': const ContratsTermeScreen(),
        },
        {
          'title': 'Techniques de Consolidation',
          'description': 'Méthodes et périmètre de consolidation',
          'screen': const ConsolidationScreen(),
        },
      ],
    },
    {
      'number': '5',
      'title': 'Guide de la Fiscalité',
      'description': 'Loi de Finances 2025',
      'icon': Icons.account_balance_wallet,
      'color': Colors.green,
      'screen': const FiscaliteScreen(),
      'subsections': [
        {
          'title': 'TVA',
          'description': 'Taxe sur la Valeur Ajoutée',
          'screen': const TvaScreen(),
        },
        {
          'title': 'IS',
          'description': 'Impôt sur les Sociétés',
          'screen': const IsScreen(),
        },
        {
          'title': 'IR',
          'description': 'Impôt sur le Revenu',
          'screen': const IRScreen(),
        },
        {
          'title': 'Droits d\'Enregistrement',
          'description': 'Taux et nouveautés 2025',
          'screen': const DroitsEnregistrementScreen(),
        },
      ],
    },
    {
      'number': '6',
      'title': 'Comptabilité Agricole',
      'description': 'Fiscalité et comptabilité du secteur agricole',
      'icon': Icons.agriculture,
      'color': Colors.green.shade700,
      'screen': const AgricultureScreen(),
      'subsections': [
        {
          'title': 'TVA Agricole',
          'description': 'Régimes et exonérations spécifiques',
          'screen': const AgricultureScreen(),
        },
        {
          'title': 'IS Agricole',
          'description': 'Coopératives et entreprises agricoles',
          'screen': const AgricultureScreen(),
        },
        {
          'title': 'IR Agricole',
          'description': 'Revenus et profits agricoles',
          'screen': const AgricultureScreen(),
        },
        {
          'title': 'Comptabilité Spécialisée',
          'description': 'Actifs biologiques et cycles de production',
          'screen': const AgricultureScreen(),
        },
      ],
    },
  ];
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Guide Comptable et Fiscale',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isDesktop = constraints.maxWidth > 900;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context),
                const SizedBox(height: 24),
                Wrap(
                  spacing: 16,
                  runSpacing: 16,
                  children: _guideItems
                      .map((item) => _buildGuideCard(context, item, isDesktop))
                      .toList(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary,
            colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Guide Comptable et Fiscale Marocain',
            style: textTheme.headlineMedium?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Votre référence pour la comptabilité et la fiscalité marocaine',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGuideCard(
      BuildContext context, Map<String, dynamic> item, bool isDesktop) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: isDesktop ? 400 : double.infinity,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => item['screen'],
              ),
            ),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        item['icon'] as IconData,
                        color: colorScheme.primary,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['title'],
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          item['description'],
                          style: textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (item['subsections'] != null) ...[
            Divider(color: colorScheme.outlineVariant),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sections détaillées',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...(item['subsections'] as List).map(
                    (subsection) => ListTile(
                      title: Text(
                        subsection['title'],
                        style: textTheme.titleSmall?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                      subtitle: Text(
                        subsection['description'],
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: colorScheme.primary,
                      ),
                      contentPadding: EdgeInsets.zero,
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => subsection['screen'],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
