import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
import '../../models/quiz_model.dart';
import '../../models/hive/active_quiz_state.dart'; 
import '../../providers/user_progress_provider.dart'; 
import '../../controllers/quiz_controller.dart'; 
import '../../providers/quiz_data_provider.dart'; // Import QuizDataProvider
import 'quiz_question_screen.dart';

class QuizScreen extends ConsumerStatefulWidget { 
  const QuizScreen({super.key});

  @override
  ConsumerState<QuizScreen> createState() => _QuizScreenState(); // Changed to ConsumerState
}

class _QuizScreenState extends ConsumerState<QuizScreen> with SingleTickerProviderStateMixin { 
  late PageController _pageController;
  // Removed: QuizData? _quizData;
  // Removed: bool _isLoading = true; // Loading state handled by provider potentially
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 0.9);
    // Removed: _loadQuizData(); // Data loaded by service in main
  }

  // Removed: _loadQuizData() method

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // --- Helper method to handle level tap ---
  Future<void> _handleLevelTap(BuildContext context, WidgetRef ref, QuizCategory category, QuizLevel level) async {
    // Note: 'ref' is available because _QuizScreenState extends ConsumerState
    final userProgressService = ref.read(userProgressServiceProvider); 
    final activeState = userProgressService.getActiveQuizState(category.name, level.level);

    bool startNewQuiz = true; // Default action
    ActiveQuizState? stateToPass; // State to potentially pass to the controller

    if (activeState != null) {
      // Found saved progress, ask the user
      final resume = await showDialog<bool>(
        context: context,
        barrierDismissible: false, // User must choose an option
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            title: const Text('Quiz en cours'),
            content: Text('Vous avez un quiz "${category.name} - ${level.level}" non terminé. Voulez-vous reprendre?'),
            actions: <Widget>[
              TextButton(
                child: const Text('Recommencer'),
                onPressed: () {
                  Navigator.of(dialogContext).pop(false); // Don't resume
                },
              ),
              ElevatedButton(
                child: const Text('Reprendre'),
                onPressed: () {
                  Navigator.of(dialogContext).pop(true); // Resume
                },
              ),
            ],
          );
        },
      );

      if (resume == true) {
        startNewQuiz = false;
        stateToPass = activeState; // Will resume with this state
      } else {
        // User chose to start new, delete the old state
        await userProgressService.deleteActiveQuizState(category.name, level.level);
        stateToPass = null; // Ensure we start fresh
      }
    }

    // Navigate to QuizQuestionScreen
    if (mounted) { // Check if the widget is still in the tree
      // Ensure the provider is ready with the correct state (either null or the activeState)
      // We read the provider here to potentially initialize it with the state if resuming
      ref.read(quizControllerProvider((level, category.name, stateToPass))); 

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => QuizQuestionScreen(
            level: level,
            categoryName: category.name,
            categoryColor: category.getColor(),
            // QuizQuestionScreen doesn't need the state directly, the provider handles it
          ),
        ),
      );
    }
  }
  // --- End Helper Method ---

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final quizData = ref.watch(quizDataProvider); // Get data from provider

    // Handle loading state if service hasn't finished (though it should have in main)
    if (quizData == null) {
       return Scaffold(
         body: Center(
           child: Column(
             mainAxisAlignment: MainAxisAlignment.center,
             children: [
               const CircularProgressIndicator(),
               const SizedBox(height: 16),
               Text(
                 'Chargement des données du quiz...', // Updated text
                 style: textTheme.titleMedium,
               ),
             ],
           ),
         ),
       );
    }

    if (quizData.categories.isEmpty) { // Check directly on quizData
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
          'Aucune catégorie de quiz disponible',
          style: textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              // Removed retry button as data loading is now centralized
              // ElevatedButton(
              //   onPressed: _loadQuizData, 
              //   child: const Text('Réessayer'),
              // ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            quizData.title, // Use quizData from provider
                            style: textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            quizData.description, // Use quizData from provider
                            style: textTheme.bodyLarge?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'v${quizData.version}', // Use quizData from provider
                            style: textTheme.labelLarge?.copyWith(
                              color: colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Mis à jour: ${quizData.lastUpdated}', // Use quizData from provider
                            style: textTheme.labelSmall?.copyWith(
                              color: colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 50,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: quizData.categories.length, // Use quizData from provider
                  itemBuilder: (context, index) {
                    final category = quizData.categories[index]; // Use quizData from provider
                    final isSelected = index == _currentPage;
                    
                    return GestureDetector(
                      onTap: () {
                        _pageController.animateToPage(
                          index,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: isSelected 
                              ? category.getColor().withValues(alpha: 0.2)
                              : colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: isSelected 
                                ? category.getColor()
                                : Colors.transparent,
                            width: 2,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              category.getIconData(),
                              color: isSelected 
                                  ? category.getColor()
                                  : colorScheme.onSurfaceVariant,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              category.name,
                              style: textTheme.titleSmall?.copyWith(
                                color: isSelected 
                                    ? category.getColor()
                                    : colorScheme.onSurfaceVariant,
                                fontWeight: isSelected 
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: quizData.categories.length, // Use quizData from provider
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  itemBuilder: (context, index) {
                    final category = quizData.categories[index]; // Use quizData from provider
                    // Pass the callback when creating QuizCategoryCard
                    return QuizCategoryCard(
                      category: category,
                      color: category.getColor(),
                      onLevelTap: _handleLevelTap, 
                    );
                  },
              ),
            ),
          ],
          ),
        ),
      ),
    );
  }
}

// Changed to ConsumerWidget and added onLevelTap parameter
class QuizCategoryCard extends ConsumerWidget { 
  final QuizCategory category;
  final Color color;
  final Function(BuildContext context, WidgetRef ref, QuizCategory category, QuizLevel level) onLevelTap; 

  const QuizCategoryCard({
    super.key,
    required this.category,
    required this.color,
    required this.onLevelTap, // Require the callback
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) { // Added WidgetRef ref
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

        return Card(
      margin: const EdgeInsets.fromLTRB(8, 0, 8, 24),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  color.withValues(alpha: 0.8),
                  color.withValues(alpha: 0.6),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    category.getIconData(),
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.name,
                        style: textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              offset: const Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                      if (category.description != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          category.description!,
                          style: textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                            shadows: [
                              Shadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                offset: const Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: category.levels.length,
              itemBuilder: (context, index) {
                final level = category.levels[index];
                final totalQuestions = level.questions.length;
                final totalPoints = level.questions.fold<int>(
                  0, 
                  (sum, question) => sum + level.pointsPerQuestion * question.difficulty,
                );
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  // Use InkWell directly, ref is available in build method
                  child: InkWell( 
                    onTap: () => onLevelTap(context, ref, category, level), // Use the passed callback
                    borderRadius: BorderRadius.circular(16),
                    child: Ink(
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: color.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: color.withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Center(
                                child: Icon(
                                  level.getIconData(),
                                  color: color,
                                  size: 28,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    level.level,
                                    style: textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Wrap(
                                    spacing: 8,
                                    runSpacing: 8,
                                    children: [
                                      _buildInfoChip(
                                        context,
                                        Icons.quiz,
                                        '$totalQuestions questions',
                                        colorScheme,
                                      ),
                                      _buildInfoChip(
                                        context,
                                        Icons.timer,
                                        '${level.timePerQuestion}s',
                                        colorScheme,
                                      ),
                                      _buildInfoChip(
                                        context,
                                        Icons.star,
                                        '$totalPoints pts',
                                        colorScheme,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: color,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ).animate()
                  .fadeIn(delay: Duration(milliseconds: 100 * index))
                  .slideX(begin: 0.2, end: 0, delay: Duration(milliseconds: 100 * index));
              },
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildInfoChip(BuildContext context, IconData icon, String label, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
