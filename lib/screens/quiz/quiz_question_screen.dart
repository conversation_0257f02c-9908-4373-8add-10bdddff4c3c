import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/quiz_model.dart';
import '../../models/hive/quiz_attempt.dart' as hive_models; 
import '../../models/hive/active_quiz_state.dart'; // Import active state model
import '../../controllers/quiz_controller.dart';
import '../../providers/user_progress_provider.dart'; 
import '../../widgets/quiz/quiz_option_button.dart';
import '../../widgets/quiz/quiz_progress_timer.dart';
import '../../services/audio_service.dart';
// Import service

class QuizQuestionScreen extends ConsumerStatefulWidget {
  final QuizLevel level;
  final String categoryName;
  final Color categoryColor;

  const QuizQuestionScreen({
    super.key,
    required this.level,
    required this.categoryName,
    required this.categoryColor,
  });

  @override
  ConsumerState<QuizQuestionScreen> createState() => _QuizQuestionScreenState();
}

class _QuizQuestionScreenState extends ConsumerState<QuizQuestionScreen> {
  final AudioService _audioService = AudioService();
  final ConfettiController _confettiController = ConfettiController(duration: const Duration(seconds: 1));
  final PageController _pageController = PageController();
  bool _showResults = false;

  @override
  void initState() {
    super.initState();
    _initializeAudio();
  }

  Future<void> _initializeAudio() async {
    await _audioService.initialize();
  }

  Future<void> _playSound(bool isCorrect) async {
    final String soundPath = isCorrect ? 'assets/sounds/correct.mp3' : 'assets/sounds/incorrect.mp3';
    await _audioService.playSound(soundPath);
    if (isCorrect) {
      _confettiController.play();
    }
  }

  // Helper method to save current progress
  Future<void> _saveCurrentProgress() async {
    // Avoid saving if results are already shown or quiz is completed
    if (_showResults || ref.read(quizControllerProvider((widget.level, widget.categoryName, null))).isCompleted) {
      return;
    }
    
    final quizState = ref.read(quizControllerProvider((widget.level, widget.categoryName, null)));
    final userProgressService = ref.read(userProgressServiceProvider);

    final activeState = ActiveQuizState(
      categoryName: widget.categoryName,
      levelName: widget.level.level,
      currentQuestionIndex: quizState.currentQuestionIndex,
      userAnswers: List.from(quizState.userAnswers), // Save a copy
      score: quizState.score,
      earnedPoints: quizState.earnedPoints,
      timeSpentPerQuestion: List.from(quizState.timeSpentPerQuestion), // Save a copy
      streakCount: quizState.streakCount,
      lastSavedTimestamp: DateTime.now(),
    );

    try {
      await userProgressService.saveActiveQuizState(activeState);
      print("Quiz progress saved.");
    } catch (e) {
      print("Error saving quiz progress: $e");
    }
  }


  @override
  void dispose() {
    // Save progress when the screen is disposed (e.g., back button)
    // Use unawaited if you don't need to wait for it here
    unawaited(_saveCurrentProgress()); 

    _audioService.dispose();
    _confettiController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Pass null for the initial state when watching/reading the provider here
    final quizState = ref.watch(quizControllerProvider((widget.level, widget.categoryName, null)));
    final quizController = ref.read(quizControllerProvider((widget.level, widget.categoryName, null)).notifier);
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    if (_showResults) {
      return _buildResultsScreen(quizState, colorScheme, textTheme);
    }

    final currentQuestion = widget.level.questions[quizState.currentQuestionIndex];
    final questionNumber = quizState.currentQuestionIndex + 1;
    final totalQuestions = widget.level.questions.length;
    final progress = questionNumber / totalQuestions;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  'Score: ${quizState.score}',
                  style: textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  widget.categoryColor,
                  widget.categoryColor.withValues(alpha: 0.7),
                  colorScheme.surface,
                ],
                stops: const [0.0, 0.4, 1.0],
              ),
            ),
          ),
          
          // Main content
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Progress bar
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 8, 24, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Question $questionNumber/$totalQuestions',
                            style: textTheme.titleMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          QuizProgressTimer(
                            progress: quizState.progressValue,
                            timeRemaining: quizState.timeRemaining,
                            color: widget.categoryColor,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: LinearProgressIndicator(
                          value: progress,
                          backgroundColor: Colors.white.withValues(alpha: 0.3),
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          minHeight: 6,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Question card
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Question
                        Card(
                          elevation: 4,
                          color: Colors.white,
                          margin: const EdgeInsets.only(bottom: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: widget.categoryColor.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Icon(
                                        Icons.quiz,
                                        color: widget.categoryColor,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        'Difficulté: ${_getDifficultyText(currentQuestion.difficulty)}',
                                        style: textTheme.titleSmall?.copyWith(
                                          color: Colors.black54,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: widget.categoryColor.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '${widget.level.pointsPerQuestion * currentQuestion.difficulty} pts',
                                        style: textTheme.labelMedium?.copyWith(
                                          color: widget.categoryColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  currentQuestion.question,
                                  style: textTheme.headlineSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                    height: 1.3,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Options
                        ...List.generate(
                          currentQuestion.options.length,
                          (index) => Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: QuizOptionButton(
                              question: currentQuestion,
                              index: index,
                              // Check if we have an answer for the current question and if it matches this index
                              isSelected: quizState.currentQuestionIndex < quizState.userAnswers.length && 
                                        quizState.userAnswers[quizState.currentQuestionIndex] == index,
                              showResult: quizState.showExplanation,
                              color: widget.categoryColor,
                              onPressed: () {
                                if (!quizState.showExplanation) {
                                  quizController.handleAnswer(index);
                                  _playSound(index == currentQuestion.correct);
                                  HapticFeedback.mediumImpact();
                                }
                              },
                            ),
                          ),
                        ),
                        
                        // Explanation
                        if (quizState.showExplanation) ...[
                          const SizedBox(height: 20),
                          Card(
                            elevation: 4,
                            color: colorScheme.primaryContainer,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.lightbulb,
                                        color: colorScheme.onPrimaryContainer,
                                        size: 24,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Explication',
                                        style: textTheme.titleMedium?.copyWith(
                                          color: colorScheme.onPrimaryContainer,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    currentQuestion.explanation,
                                    style: textTheme.bodyLarge?.copyWith(
                                      color: colorScheme.onPrimaryContainer,
                                    ),
                                  ),
                                  if (currentQuestion.reference != null) ...[
                                    const SizedBox(height: 12),
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: colorScheme.onPrimaryContainer.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: colorScheme.onPrimaryContainer.withValues(alpha: 0.2),
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.info_outline,
                                            color: colorScheme.onPrimaryContainer.withValues(alpha: 0.7),
                                            size: 16,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'Référence: ${currentQuestion.reference}',
                                              style: textTheme.bodySmall?.copyWith(
                                                color: colorScheme.onPrimaryContainer.withValues(alpha: 0.7),
                                                fontStyle: FontStyle.italic,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // Next button
                          ElevatedButton(
                            onPressed: () async { 
                              if (quizState.currentQuestionIndex < widget.level.questions.length - 1) {
                                // Save progress before moving to next question
                                await _saveCurrentProgress(); 
                                quizController.nextQuestion();
                              } else {
                                // --- Quiz Completed ---
                                final userProgressService = ref.read(userProgressServiceProvider);
                                
                                // 1. Delete Active State
                                try {
                                   await userProgressService.deleteActiveQuizState(widget.categoryName, widget.level.level);
                                   print("Active quiz state deleted on completion.");
                                } catch (e) {
                                   print("Error deleting active quiz state: $e");
                                }

                                // 2. Save Final Attempt History
                                final attempt = hive_models.QuizAttempt(
                                  categoryName: widget.categoryName,
                                  levelName: widget.level.level,
                                  score: quizState.score, // Correct answers count
                                  totalQuestions: widget.level.questions.length,
                                  earnedPoints: quizState.earnedPoints,
                                  totalPoints: quizState.totalPoints,
                                  timestamp: DateTime.now(),
                                );
                                try {
                                  await userProgressService.addQuizAttempt(attempt);
                                  print("Quiz attempt successfully saved!"); // Debug
                                } catch (e) {
                                   print("Error saving quiz attempt: $e"); // Debug
                                }
                                // --- End Save ---

                                setState(() {
                                  _showResults = true;
                                });
                                _confettiController.play();
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: widget.categoryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                              elevation: 4,
                            ),
                            child: Text(
                              quizState.currentQuestionIndex < widget.level.questions.length - 1
                                  ? 'Question Suivante'
                                  : 'Voir les Résultats',
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Confetti effect
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: pi / 2,
              maxBlastForce: 5,
              minBlastForce: 2,
              emissionFrequency: 0.05,
              numberOfParticles: 50,
              gravity: 0.1,
              colors: [
                widget.categoryColor,
                Colors.white,
                colorScheme.primary,
                colorScheme.secondary,
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildResultsScreen(QuizState quizState, ColorScheme colorScheme, TextTheme textTheme) {
    final percentage = (quizState.score / widget.level.questions.length) * 100;
    final isPerfect = percentage == 100;
    final isGood = percentage >= 70 && percentage < 100;
    final isAverage = percentage >= 50 && percentage < 70;
    
    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              widget.categoryColor.withValues(alpha: 0.8),
              colorScheme.surface,
            ],
            stops: const [0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Confetti animation
                      Align(
                        alignment: Alignment.topCenter,
                        child: ConfettiWidget(
                          confettiController: _confettiController,
                          blastDirection: pi / 2,
                          maxBlastForce: 5,
                          minBlastForce: 2,
                          emissionFrequency: 0.05,
                          numberOfParticles: 50,
                          gravity: 0.1,
                          colors: [
                            widget.categoryColor,
                            Colors.white,
                            colorScheme.primary,
                            colorScheme.secondary,
                          ],
                        ),
                      ),
                      
                      // Title
                      Text(
                        'Quiz Terminé!',
                        style: textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              offset: const Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Animation based on score
                      Container(
                        height: 150,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: colorScheme.surface,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Center(
                          child: _buildResultAnimation(isPerfect, isGood, isAverage),
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Score card
                      Card(
                        elevation: 8,
                        color: colorScheme.surface,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: [
                                    _buildStatColumn(
                                      context,
                                      'Score',
                                      '${quizState.score}/${widget.level.questions.length}',
                                      Icons.check_circle,
                                      widget.categoryColor,
                                    ),
                                    const SizedBox(width: 16),
                                    _buildStatColumn(
                                      context,
                                      'Pourcentage',
                                      '${percentage.round()}%',
                                      Icons.percent,
                                      widget.categoryColor,
                                    ),
                                    const SizedBox(width: 16),
                                    _buildStatColumn(
                                      context,
                                      'Points',
                                      '${quizState.earnedPoints}/${quizState.totalPoints}',
                                      Icons.star,
                                      widget.categoryColor,
                                    ),
                                  ],
                                ),
                              ),
                              
                              const SizedBox(height: 24),
                              
                              Text(
                                _getScoreMessage(percentage),
                                style: textTheme.titleMedium?.copyWith(
                                  color: colorScheme.onSurface,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Performance details
                      Card(
                        elevation: 4,
                        color: colorScheme.surface,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Détails de Performance',
                                style: textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(height: 16),
                              
                              // Average time per question
                              if (quizState.timeSpentPerQuestion.isNotEmpty) ...[
                                _buildPerformanceRow(
                                  context,
                                  'Temps moyen par question',
                                  '${(quizState.timeSpentPerQuestion.reduce((a, b) => a + b) / quizState.timeSpentPerQuestion.length).round()} sec',
                                  Icons.timer,
                                  colorScheme,
                                ),
                                const SizedBox(height: 12),
                              ],
                              
                              // Longest streak
                              _buildPerformanceRow(
                                context,
                                'Plus longue série correcte',
                                '${quizState.streakCount} questions',
                                Icons.bolt,
                                colorScheme,
                              ),
                              
                              const SizedBox(height: 12),
                              
                              // Points earned
                              _buildPerformanceRow(
                                context,
                                'Points gagnés',
                                '${quizState.earnedPoints} pts',
                                Icons.stars,
                                colorScheme,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Bottom buttons
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: widget.categoryColor),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Text(
                          'Retour au Menu',
                          style: textTheme.labelLarge?.copyWith(
                            color: widget.categoryColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          // Invalidate the provider, passing null for initial state again
                          ref.invalidate(quizControllerProvider((widget.level, widget.categoryName, null)));
                          
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (context) => QuizQuestionScreen(
                                level: widget.level,
                                categoryName: widget.categoryName,
                                categoryColor: widget.categoryColor,
                              ),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: widget.categoryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Text(
                          'Réessayer',
                          style: textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildStatColumn(BuildContext context, String label, String value, IconData icon, Color color) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
        ),
        Text(
          label,
          style: textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
  
  Widget _buildPerformanceRow(BuildContext context, String label, String value, IconData icon, ColorScheme colorScheme) {
    final textTheme = Theme.of(context).textTheme;
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: colorScheme.onPrimaryContainer,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: textTheme.labelSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
  
  String _getDifficultyText(int difficulty) {
    switch (difficulty) {
      case 1:
        return 'Facile';
      case 2:
        return 'Moyen';
      case 3:
        return 'Difficile';
      case 4:
        return 'Expert';
      default:
        return 'Moyen';
    }
  }

  String _getScoreMessage(double percentage) {
    if (percentage >= 90) return 'Excellent! Vous maîtrisez le sujet!';
    if (percentage >= 70) return 'Très bien! Continuez comme ça!';
    if (percentage >= 50) return 'Pas mal! Avec un peu plus de pratique, vous y arriverez!';
    return 'Continuez à pratiquer, vous vous améliorerez!';
  }

  Widget _buildResultAnimation(bool isPerfect, bool isGood, bool isAverage) {
    // Use smaller icon sizes and add a container with constraints to prevent overflow
    return Container(
      constraints: const BoxConstraints(maxWidth: 100, maxHeight: 100),
      child: FittedBox(
        fit: BoxFit.contain,
        child: isPerfect
            ? Icon(Icons.emoji_events, size: 80, color: Colors.amber)
            : isGood
                ? Icon(Icons.thumb_up, size: 80, color: Colors.blue)
                : isAverage
                    ? Icon(Icons.star_half, size: 80, color: Colors.orange)
                    : Icon(Icons.refresh, size: 80, color: Colors.purple),
      ),
    );
  }
}
