import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Enum defining all available keyboard shortcuts in the app
enum AppShortcut {
  // Navigation shortcuts
  home,
  guides,
  quiz,
  tools,
  planComptable,
  references,
  settings,
  profile,
  
  // Action shortcuts
  help,
  closeBack,
  activate,
  search,
  refresh,
  
  // Accessibility shortcuts
  fontIncrease,
  fontDecrease,
  fontReset,
  highContrastToggle,
  screenReaderToggle,
  
  // Navigation flow
  tabForward,
  tabBackward,
  arrowUp,
  arrowDown,
  arrowLeft,
  arrowRight,
  
  // App-specific shortcuts
  newQuiz,
  saveProgress,
  exportData,
  importData,
  toggleTheme,
}

/// Platform-specific modifier keys
class PlatformModifiers {
  static bool get isMacOS => !kIsWeb && Platform.isMacOS;
  
  static LogicalKeyboardKey get primaryModifier => 
      isMacOS ? LogicalKeyboardKey.metaLeft : LogicalKeyboardKey.controlLeft;
  
  static LogicalKeyboardKey get secondaryModifier => 
      LogicalKeyboardKey.shiftLeft;
  
  static LogicalKeyboardKey get altModifier => 
      LogicalKeyboardKey.altLeft;
}

/// Keyboard shortcut definition with platform-specific variations
class ShortcutDefinition {
  final AppShortcut shortcut;
  final String description;
  final String category;
  final SingleActivator activator;
  final SingleActivator? macActivator;
  final bool isAccessibilityFeature;
  final bool isContextSensitive;
  final List<String> contexts;

  const ShortcutDefinition({
    required this.shortcut,
    required this.description,
    required this.category,
    required this.activator,
    this.macActivator,
    this.isAccessibilityFeature = false,
    this.isContextSensitive = false,
    this.contexts = const [],
  });

  /// Get the appropriate activator for the current platform
  SingleActivator get platformActivator {
    if (PlatformModifiers.isMacOS && macActivator != null) {
      return macActivator!;
    }
    return activator;
  }

  /// Get a human-readable string representation of the shortcut
  String get displayString {
    final activator = platformActivator;
    final modifiers = <String>[];
    
    if (activator.control) {
      modifiers.add(PlatformModifiers.isMacOS ? 'Cmd' : 'Ctrl');
    }
    if (activator.shift) {
      modifiers.add('Shift');
    }
    if (activator.alt) {
      modifiers.add('Alt');
    }
    if (activator.meta && !PlatformModifiers.isMacOS) {
      modifiers.add('Meta');
    }
    
    final keyLabel = _getKeyLabel(activator.trigger);
    modifiers.add(keyLabel);
    
    return modifiers.join(' + ');
  }

  String _getKeyLabel(LogicalKeyboardKey key) {
    if (key == LogicalKeyboardKey.escape) return 'Esc';
    if (key == LogicalKeyboardKey.enter) return 'Enter';
    if (key == LogicalKeyboardKey.space) return 'Space';
    if (key == LogicalKeyboardKey.tab) return 'Tab';
    if (key == LogicalKeyboardKey.arrowUp) return '↑';
    if (key == LogicalKeyboardKey.arrowDown) return '↓';
    if (key == LogicalKeyboardKey.arrowLeft) return '←';
    if (key == LogicalKeyboardKey.arrowRight) return '→';
    if (key == LogicalKeyboardKey.f1) return 'F1';
    if (key == LogicalKeyboardKey.equal) return '+';
    if (key == LogicalKeyboardKey.minus) return '-';
    
    return key.keyLabel.toUpperCase();
  }
}

/// Centralized keyboard shortcuts registry and management system
class KeyboardShortcutsService extends ChangeNotifier {
  static final KeyboardShortcutsService _instance = KeyboardShortcutsService._internal();
  factory KeyboardShortcutsService() => _instance;
  KeyboardShortcutsService._internal();

  final Map<AppShortcut, ShortcutDefinition> _shortcuts = {};
  final Map<AppShortcut, VoidCallback> _callbacks = {};
  final Map<String, List<AppShortcut>> _contextShortcuts = {};
  final Set<AppShortcut> _disabledShortcuts = {};
  final Map<AppShortcut, ShortcutDefinition> _customShortcuts = {};
  
  bool _shortcutsEnabled = true;
  String _currentContext = 'global';
  bool _showHelpOverlay = false;

  /// Initialize the keyboard shortcuts system with default shortcuts
  void initialize() {
    _registerDefaultShortcuts();
    _buildContextMappings();
  }

  /// Register all default keyboard shortcuts
  void _registerDefaultShortcuts() {
    final shortcuts = [
      // Navigation shortcuts
      ShortcutDefinition(
        shortcut: AppShortcut.home,
        description: 'Aller à l\'accueil',
        category: 'Navigation',
        activator: SingleActivator(
          LogicalKeyboardKey.keyH,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.keyH,
          meta: true,
        ),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.guides,
        description: 'Ouvrir les guides',
        category: 'Navigation',
        activator: SingleActivator(
          LogicalKeyboardKey.keyG,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.keyG,
          meta: true,
        ),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.quiz,
        description: 'Aller aux quiz',
        category: 'Navigation',
        activator: SingleActivator(
          LogicalKeyboardKey.keyQ,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.keyQ,
          meta: true,
        ),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.tools,
        description: 'Ouvrir les outils',
        category: 'Navigation',
        activator: SingleActivator(
          LogicalKeyboardKey.keyT,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.keyT,
          meta: true,
        ),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.settings,
        description: 'Ouvrir les paramètres',
        category: 'Navigation',
        activator: const SingleActivator(
          LogicalKeyboardKey.comma,
          control: true,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.comma,
          meta: true,
        ),
      ),
      
      // Action shortcuts
      ShortcutDefinition(
        shortcut: AppShortcut.help,
        description: 'Afficher l\'aide',
        category: 'Actions',
        activator: const SingleActivator(LogicalKeyboardKey.f1),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.closeBack,
        description: 'Fermer/Retour',
        category: 'Actions',
        activator: const SingleActivator(LogicalKeyboardKey.escape),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.activate,
        description: 'Activer l\'élément sélectionné',
        category: 'Actions',
        activator: const SingleActivator(LogicalKeyboardKey.enter),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.search,
        description: 'Rechercher',
        category: 'Actions',
        activator: SingleActivator(
          LogicalKeyboardKey.keyF,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.keyF,
          meta: true,
        ),
      ),
      
      // Accessibility shortcuts
      ShortcutDefinition(
        shortcut: AppShortcut.fontIncrease,
        description: 'Augmenter la taille de police',
        category: 'Accessibilité',
        activator: SingleActivator(
          LogicalKeyboardKey.equal,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.equal,
          meta: true,
        ),
        isAccessibilityFeature: true,
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.fontDecrease,
        description: 'Diminuer la taille de police',
        category: 'Accessibilité',
        activator: SingleActivator(
          LogicalKeyboardKey.minus,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.minus,
          meta: true,
        ),
        isAccessibilityFeature: true,
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.fontReset,
        description: 'Réinitialiser la taille de police',
        category: 'Accessibilité',
        activator: SingleActivator(
          LogicalKeyboardKey.digit0,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.digit0,
          meta: true,
        ),
        isAccessibilityFeature: true,
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.highContrastToggle,
        description: 'Basculer le mode contraste élevé',
        category: 'Accessibilité',
        activator: SingleActivator(
          LogicalKeyboardKey.keyC,
          control: !PlatformModifiers.isMacOS,
          shift: true,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.keyC,
          meta: true,
          shift: true,
        ),
        isAccessibilityFeature: true,
      ),
      
      // Navigation flow shortcuts
      ShortcutDefinition(
        shortcut: AppShortcut.tabForward,
        description: 'Navigation suivante',
        category: 'Navigation',
        activator: const SingleActivator(LogicalKeyboardKey.tab),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.tabBackward,
        description: 'Navigation précédente',
        category: 'Navigation',
        activator: const SingleActivator(
          LogicalKeyboardKey.tab,
          shift: true,
        ),
      ),
      
      // Arrow key navigation
      ShortcutDefinition(
        shortcut: AppShortcut.arrowUp,
        description: 'Naviguer vers le haut',
        category: 'Navigation',
        activator: const SingleActivator(LogicalKeyboardKey.arrowUp),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.arrowDown,
        description: 'Naviguer vers le bas',
        category: 'Navigation',
        activator: const SingleActivator(LogicalKeyboardKey.arrowDown),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.arrowLeft,
        description: 'Naviguer vers la gauche',
        category: 'Navigation',
        activator: const SingleActivator(LogicalKeyboardKey.arrowLeft),
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.arrowRight,
        description: 'Naviguer vers la droite',
        category: 'Navigation',
        activator: const SingleActivator(LogicalKeyboardKey.arrowRight),
      ),
      
      // App-specific shortcuts
      ShortcutDefinition(
        shortcut: AppShortcut.newQuiz,
        description: 'Nouveau quiz',
        category: 'Quiz',
        activator: SingleActivator(
          LogicalKeyboardKey.keyN,
          control: !PlatformModifiers.isMacOS,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.keyN,
          meta: true,
        ),
        isContextSensitive: true,
        contexts: ['quiz', 'home'],
      ),
      
      ShortcutDefinition(
        shortcut: AppShortcut.toggleTheme,
        description: 'Changer de thème',
        category: 'Interface',
        activator: SingleActivator(
          LogicalKeyboardKey.keyT,
          control: !PlatformModifiers.isMacOS,
          shift: true,
          meta: PlatformModifiers.isMacOS,
        ),
        macActivator: const SingleActivator(
          LogicalKeyboardKey.keyT,
          meta: true,
          shift: true,
        ),
      ),
    ];

    for (final shortcut in shortcuts) {
      _shortcuts[shortcut.shortcut] = shortcut;
    }
  }

  /// Build context-specific shortcut mappings
  void _buildContextMappings() {
    _contextShortcuts.clear();
    
    for (final entry in _shortcuts.entries) {
      final shortcut = entry.value;
      
      // Add to global context
      _contextShortcuts.putIfAbsent('global', () => []).add(entry.key);
      
      // Add to specific contexts if defined
      for (final context in shortcut.contexts) {
        _contextShortcuts.putIfAbsent(context, () => []).add(entry.key);
      }
    }
  }

  /// Register a callback for a specific shortcut
  void registerCallback(AppShortcut shortcut, VoidCallback callback) {
    _callbacks[shortcut] = callback;
  }

  /// Unregister a callback for a specific shortcut
  void unregisterCallback(AppShortcut shortcut) {
    _callbacks.remove(shortcut);
  }

  /// Execute a shortcut if it has a registered callback
  bool executeShortcut(AppShortcut shortcut) {
    if (!_shortcutsEnabled || _disabledShortcuts.contains(shortcut)) {
      return false;
    }

    final callback = _callbacks[shortcut];
    if (callback != null) {
      callback();
      return true;
    }
    return false;
  }

  /// Get all shortcuts for the current context
  List<ShortcutDefinition> getContextShortcuts([String? context]) {
    final contextKey = context ?? _currentContext;
    final shortcutKeys = _contextShortcuts[contextKey] ?? [];
    
    return shortcutKeys
        .map((key) => _shortcuts[key])
        .where((shortcut) => shortcut != null)
        .cast<ShortcutDefinition>()
        .toList();
  }

  /// Get all accessibility-related shortcuts
  List<ShortcutDefinition> getAccessibilityShortcuts() {
    return _shortcuts.values
        .where((shortcut) => shortcut.isAccessibilityFeature)
        .toList();
  }

  /// Get shortcuts grouped by category
  Map<String, List<ShortcutDefinition>> getShortcutsByCategory() {
    final grouped = <String, List<ShortcutDefinition>>{};
    
    for (final shortcut in _shortcuts.values) {
      grouped.putIfAbsent(shortcut.category, () => []).add(shortcut);
    }
    
    return grouped;
  }

  /// Set the current context for context-sensitive shortcuts
  void setContext(String context) {
    if (_currentContext != context) {
      _currentContext = context;
      notifyListeners();
    }
  }

  /// Enable or disable all shortcuts
  void setShortcutsEnabled(bool enabled) {
    if (_shortcutsEnabled != enabled) {
      _shortcutsEnabled = enabled;
      notifyListeners();
    }
  }

  /// Enable or disable a specific shortcut
  void setShortcutEnabled(AppShortcut shortcut, bool enabled) {
    if (enabled) {
      _disabledShortcuts.remove(shortcut);
    } else {
      _disabledShortcuts.add(shortcut);
    }
    notifyListeners();
  }

  /// Check if a shortcut is enabled
  bool isShortcutEnabled(AppShortcut shortcut) {
    return _shortcutsEnabled && !_disabledShortcuts.contains(shortcut);
  }

  /// Customize a shortcut with a new key combination
  void customizeShortcut(AppShortcut shortcut, SingleActivator newActivator) {
    final originalShortcut = _shortcuts[shortcut];
    if (originalShortcut != null) {
      _customShortcuts[shortcut] = ShortcutDefinition(
        shortcut: originalShortcut.shortcut,
        description: originalShortcut.description,
        category: originalShortcut.category,
        activator: newActivator,
        isAccessibilityFeature: originalShortcut.isAccessibilityFeature,
        isContextSensitive: originalShortcut.isContextSensitive,
        contexts: originalShortcut.contexts,
      );
      notifyListeners();
    }
  }

  /// Reset a shortcut to its default key combination
  void resetShortcut(AppShortcut shortcut) {
    _customShortcuts.remove(shortcut);
    notifyListeners();
  }

  /// Reset all shortcuts to defaults
  void resetAllShortcuts() {
    _customShortcuts.clear();
    notifyListeners();
  }

  /// Get the effective shortcut definition (custom or default)
  ShortcutDefinition? getShortcutDefinition(AppShortcut shortcut) {
    return _customShortcuts[shortcut] ?? _shortcuts[shortcut];
  }

  /// Check for shortcut conflicts
  List<AppShortcut> getConflictingShortcuts(SingleActivator activator) {
    final conflicts = <AppShortcut>[];
    
    for (final entry in _shortcuts.entries) {
      final definition = _customShortcuts[entry.key] ?? entry.value;
      if (definition.platformActivator == activator) {
        conflicts.add(entry.key);
      }
    }
    
    return conflicts;
  }

  /// Show or hide the help overlay
  void toggleHelpOverlay() {
    _showHelpOverlay = !_showHelpOverlay;
    notifyListeners();
  }

  /// Get help overlay visibility state
  bool get isHelpOverlayVisible => _showHelpOverlay;

  /// Hide the help overlay
  void hideHelpOverlay() {
    if (_showHelpOverlay) {
      _showHelpOverlay = false;
      notifyListeners();
    }
  }

  /// Get current context
  String get currentContext => _currentContext;

  /// Get shortcuts enabled state
  bool get shortcutsEnabled => _shortcutsEnabled;

  /// Generate a map of shortcuts for use with CallbackShortcuts widget
  Map<ShortcutActivator, VoidCallback> getCallbackShortcuts([String? context]) {
    final contextShortcuts = getContextShortcuts(context);
    final shortcuts = <ShortcutActivator, VoidCallback>{};
    
    for (final shortcut in contextShortcuts) {
      final callback = _callbacks[shortcut.shortcut];
      if (callback != null && isShortcutEnabled(shortcut.shortcut)) {
        shortcuts[shortcut.platformActivator] = callback;
      }
    }
    
    return shortcuts;
  }

  /// Get a human-readable description of a shortcut
  String getShortcutDescription(AppShortcut shortcut) {
    final definition = getShortcutDefinition(shortcut);
    return definition?.description ?? '';
  }

  /// Get the display string for a shortcut
  String getShortcutDisplayString(AppShortcut shortcut) {
    final definition = getShortcutDefinition(shortcut);
    return definition?.displayString ?? '';
  }

  /// Dispose of the service
  @override
  void dispose() {
    _callbacks.clear();
    _contextShortcuts.clear();
    _disabledShortcuts.clear();
    _customShortcuts.clear();
    super.dispose();
  }
}

/// Widget that provides keyboard shortcuts functionality to its child
class KeyboardShortcutsProvider extends StatefulWidget {
  final Widget child;
  final String? context;
  final Map<AppShortcut, VoidCallback>? additionalCallbacks;

  const KeyboardShortcutsProvider({
    super.key,
    required this.child,
    this.context,
    this.additionalCallbacks,
  });

  @override
  State<KeyboardShortcutsProvider> createState() => _KeyboardShortcutsProviderState();
}

class _KeyboardShortcutsProviderState extends State<KeyboardShortcutsProvider> {
  final _shortcutsService = KeyboardShortcutsService();

  @override
  void initState() {
    super.initState();
    
    // Set context if provided
    if (widget.context != null) {
      _shortcutsService.setContext(widget.context!);
    }
    
    // Register additional callbacks
    if (widget.additionalCallbacks != null) {
      for (final entry in widget.additionalCallbacks!.entries) {
        _shortcutsService.registerCallback(entry.key, entry.value);
      }
    }
  }

  @override
  void didUpdateWidget(KeyboardShortcutsProvider oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update context if changed
    if (widget.context != oldWidget.context && widget.context != null) {
      _shortcutsService.setContext(widget.context!);
    }
    
    // Update callbacks if changed
    if (widget.additionalCallbacks != oldWidget.additionalCallbacks) {
      // Unregister old callbacks
      if (oldWidget.additionalCallbacks != null) {
        for (final shortcut in oldWidget.additionalCallbacks!.keys) {
          _shortcutsService.unregisterCallback(shortcut);
        }
      }
      
      // Register new callbacks
      if (widget.additionalCallbacks != null) {
        for (final entry in widget.additionalCallbacks!.entries) {
          _shortcutsService.registerCallback(entry.key, entry.value);
        }
      }
    }
  }

  @override
  void dispose() {
    // Unregister callbacks
    if (widget.additionalCallbacks != null) {
      for (final shortcut in widget.additionalCallbacks!.keys) {
        _shortcutsService.unregisterCallback(shortcut);
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CallbackShortcuts(
      bindings: _shortcutsService.getCallbackShortcuts(widget.context),
      child: Focus(
        autofocus: true,
        child: widget.child,
      ),
    );
  }
}

/// Widget that displays a help overlay with available shortcuts
class ShortcutsHelpOverlay extends StatelessWidget {
  final VoidCallback? onClose;

  const ShortcutsHelpOverlay({
    super.key,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final shortcutsService = KeyboardShortcutsService();
    final shortcutsByCategory = shortcutsService.getShortcutsByCategory();

    return Material(
      color: Colors.black54,
      child: Center(
        child: Container(
          constraints: const BoxConstraints(
            maxWidth: 800,
            maxHeight: 600,
          ),
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.keyboard,
                      color: theme.colorScheme.onPrimaryContainer,
                      size: 28,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Raccourcis Clavier',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          color: theme.colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: onClose,
                      icon: Icon(
                        Icons.close,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      for (final entry in shortcutsByCategory.entries) ...[
                        _buildCategorySection(context, entry.key, entry.value),
                        const SizedBox(height: 24),
                      ],
                    ],
                  ),
                ),
              ),
              
              // Footer
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant,
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(16),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Appuyez sur F1 pour afficher cette aide',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategorySection(
    BuildContext context,
    String category,
    List<ShortcutDefinition> shortcuts,
  ) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          category,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 12),
        ...shortcuts.map((shortcut) => _buildShortcutItem(context, shortcut)),
      ],
    );
  }

  Widget _buildShortcutItem(BuildContext context, ShortcutDefinition shortcut) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              shortcut.description,
              style: theme.textTheme.bodyMedium,
            ),
          ),
          const SizedBox(width: 16),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.3),
              ),
            ),
            child: Text(
              shortcut.displayString,
              style: theme.textTheme.bodySmall?.copyWith(
                fontFamily: 'monospace',
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
}