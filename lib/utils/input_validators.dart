import 'package:flutter/services.dart';

/// Comprehensive input validation utilities for tax calculators
class InputValidators {
  // Common error messages
  static const String requiredFieldError = 'Ce champ est obligatoire';
  static const String invalidNumberError = 'Veuillez saisir un nombre valide';
  static const String negativeNumberError = 'La valeur ne peut pas être négative';
  static const String invalidPercentageError = 'Le pourcentage doit être entre 0 et 100';
  static const String invalidTaxRateError = 'Le taux d\'impôt doit être entre 0 et 50';
  static const String maxValueExceededError = 'La valeur dépasse le maximum autorisé';
  static const String minValueError = 'La valeur est inférieure au minimum requis';
  static const String invalidQuantityError = 'La quantité doit être supérieure à 0';
  static const String invalidDescriptionError = 'La description doit contenir au moins 2 caractères';
  static const String descriptionTooLongError = 'La description ne peut pas dépasser 100 caractères';
  
  // IS Calculator specific error messages
  static const String invalidAccountingResultError = 'Le résultat comptable doit être entre -100M et +100M DH';
  static const String invalidReintegrationError = 'Le montant de réintégration doit être entre 0 et 50M DH';
  static const String invalidDeductionError = 'Le montant de déduction doit être entre 0 et 50M DH';
  static const String invalidBusinessRevenueError = 'Le chiffre d\'affaires doit être valide selon le secteur d\'activité';
  static const String accountingResultTooHighError = 'Le résultat comptable dépasse la limite maximale (100M DH)';
  static const String accountingResultTooLowError = 'Le résultat comptable est inférieur à la limite minimale (-100M DH)';
  static const String reintegrationTooHighError = 'Le montant de réintégration dépasse la limite (50M DH)';
  static const String deductionTooHighError = 'Le montant de déduction dépasse la limite (50M DH)';

  /// Validate monetary amount (salary, revenue, etc.)
  static String? validateMonetaryAmount(String? value, {
    bool required = true,
    double? minValue,
    double? maxValue,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0) {
      return customErrorMessage ?? negativeNumberError;
    }

    if (minValue != null && parsedValue < minValue) {
      return customErrorMessage ?? '$minValueError (minimum: ${minValue.toStringAsFixed(2)})';
    }

    if (maxValue != null && parsedValue > maxValue) {
      return customErrorMessage ?? '$maxValueExceededError (maximum: ${maxValue.toStringAsFixed(2)})';
    }

    return null;
  }

  /// Validate percentage values (0-100)
  static String? validatePercentage(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0 || parsedValue > 100) {
      return customErrorMessage ?? invalidPercentageError;
    }

    return null;
  }

  /// Validate tax rate (0-50)
  static String? validateTaxRate(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0 || parsedValue > 50) {
      return customErrorMessage ?? invalidTaxRateError;
    }

    return null;
  }

  /// Validate integer values (number of children, years, etc.)
  static String? validateInteger(String? value, {
    bool required = true,
    int? minValue,
    int? maxValue,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final parsedValue = int.tryParse(value.trim());

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0) {
      return customErrorMessage ?? negativeNumberError;
    }

    if (minValue != null && parsedValue < minValue) {
      return customErrorMessage ?? '$minValueError (minimum: $minValue)';
    }

    if (maxValue != null && parsedValue > maxValue) {
      return customErrorMessage ?? '$maxValueExceededError (maximum: $maxValue)';
    }

    return null;
  }

  /// Validate salary amount with specific constraints
  static String? validateSalary(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    return validateMonetaryAmount(
      value,
      required: required,
      minValue: 0,
      maxValue: 1000000, // 1M MAD maximum
      customErrorMessage: customErrorMessage ?? 'Veuillez saisir un salaire valide (0 - 1,000,000 DH)',
    );
  }

  /// Validate revenue amount
  static String? validateRevenue(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    return validateMonetaryAmount(
      value,
      required: required,
      minValue: 0,
      maxValue: *********, // 100M MAD maximum
      customErrorMessage: customErrorMessage ?? 'Veuillez saisir un chiffre d\'affaires valide',
    );
  }

  /// Validate number of dependents
  static String? validateDependents(String? value, {
    bool required = false,
    String? customErrorMessage,
  }) {
    return validateInteger(
      value,
      required: required,
      minValue: 0,
      maxValue: 10,
      customErrorMessage: customErrorMessage ?? 'Le nombre de personnes à charge doit être entre 0 et 10',
    );
  }

  /// Validate years of service/seniority
  static String? validateYearsOfService(String? value, {
    bool required = false,
    String? customErrorMessage,
  }) {
    return validateInteger(
      value,
      required: required,
      minValue: 0,
      maxValue: 50,
      customErrorMessage: customErrorMessage ?? 'L\'ancienneté doit être entre 0 et 50 ans',
    );
  }

  /// Validate quantity for TVA line items
  static String? validateQuantity(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue <= 0) {
      return customErrorMessage ?? invalidQuantityError;
    }

    if (parsedValue < 0.001) {
      return customErrorMessage ?? '$minValueError (minimum: 0.001)';
    }

    if (parsedValue > 999999) {
      return customErrorMessage ?? '$maxValueExceededError (maximum: 999,999)';
    }

    return null;
  }

  /// Validate unit price for TVA line items (allows zero)
  static String? validateUnitPrice(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0) {
      return customErrorMessage ?? negativeNumberError;
    }

    if (parsedValue > 1000000) {
      return customErrorMessage ?? '$maxValueExceededError (maximum: 1,000,000 DH)';
    }

    return null;
  }

  /// Validate description for TVA line items
  static String? validateDescription(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length < 2) {
      return customErrorMessage ?? invalidDescriptionError;
    }

    if (trimmedValue.length > 100) {
      return customErrorMessage ?? descriptionTooLongError;
    }

    return null;
  }

  /// Validate accounting result (allows negative values for losses)
  static String? validateAccountingResult(String? value, {
    bool required = true,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,-]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < -*********) { // -100M MAD
      return customErrorMessage ?? accountingResultTooLowError;
    }

    if (parsedValue > *********) { // +100M MAD
      return customErrorMessage ?? accountingResultTooHighError;
    }

    return null;
  }

  /// Validate reintegration amounts (0 to 50M MAD)
  static String? validateReintegrationAmount(String? value, {
    bool required = false,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0) {
      return customErrorMessage ?? negativeNumberError;
    }

    if (parsedValue > ********) { // 50M MAD
      return customErrorMessage ?? reintegrationTooHighError;
    }

    return null;
  }

  /// Validate deduction amounts (0 to 50M MAD)
  static String? validateDeductionAmount(String? value, {
    bool required = false,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0) {
      return customErrorMessage ?? negativeNumberError;
    }

    if (parsedValue > ********) { // 50M MAD
      return customErrorMessage ?? deductionTooHighError;
    }

    return null;
  }

  /// Enhanced revenue validation with sector-specific constraints
  static String? validateBusinessRevenue(String? value, {
    bool required = true,
    double? sectorMaxLimit,
    String? customErrorMessage,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? (customErrorMessage ?? requiredFieldError) : null;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final parsedValue = double.tryParse(cleanValue);

    if (parsedValue == null) {
      return customErrorMessage ?? invalidNumberError;
    }

    if (parsedValue < 0) {
      return customErrorMessage ?? negativeNumberError;
    }

    // Apply sector-specific limit if provided, otherwise use general business limit
    final maxLimit = sectorMaxLimit ?? ********0; // 500M MAD default
    if (parsedValue > maxLimit) {
      return customErrorMessage ?? 'Le chiffre d\'affaires dépasse la limite du secteur (${(maxLimit / 1000000).toStringAsFixed(0)}M DH)';
    }

    return null;
  }

  /// Clean and parse monetary input
  static double parseMonetaryInput(String input) {
    final cleanValue = input.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Clean and parse percentage input
  static double parsePercentageInput(String input) {
    final cleanValue = input.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Clean and parse integer input
  static int parseIntegerInput(String input) {
    return int.tryParse(input.trim()) ?? 0;
  }

  /// Clean and parse quantity input
  static double parseQuantityInput(String input) {
    final cleanValue = input.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Parse accounting result input (allows negative values)
  static double parseAccountingInput(String input) {
    final cleanValue = input.replaceAll(RegExp(r'[^\d.,-]'), '').replaceAll(',', '.');
    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Parse reintegration amounts with business rules validation
  static double parseReintegrationInput(String input) {
    final cleanValue = input.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
    final value = double.tryParse(cleanValue) ?? 0.0;
    // Ensure non-negative and within business limits
    return value < 0 ? 0.0 : (value > ******** ? ******** : value);
  }

  /// Format large business amounts with appropriate separators
  static String formatBusinessAmount(String input) {
    final value = parseAccountingInput(input);
    if (value == 0) return '';
    
    final isNegative = value < 0;
    final absoluteValue = value.abs();
    
    // Format with thousand separators
    final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    String formatted = absoluteValue.toStringAsFixed(2).replaceAllMapped(formatter, (Match m) => '${m[1]} ');
    
    return isNegative ? '-$formatted' : formatted;
  }

  /// Format input for display (add thousand separators)
  static String formatMonetaryInput(String input) {
    final value = parseMonetaryInput(input);
    if (value == 0) return '';
    
    final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    return value.toStringAsFixed(2).replaceAllMapped(formatter, (Match m) => '${m[1]} ');
  }

  /// Input formatter for monetary fields
  static List<TextInputFormatter> getMonetaryInputFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.,\s]')),
      TextInputFormatter.withFunction((oldValue, newValue) {
        // Remove any non-numeric characters except comma and dot
        String newText = newValue.text.replaceAll(RegExp(r'[^\d.,]'), '');
        
        // Replace comma with dot for decimal separator
        newText = newText.replaceAll(',', '.');
        
        // Ensure only one decimal point
        final parts = newText.split('.');
        if (parts.length > 2) {
          newText = '${parts[0]}.${parts.sublist(1).join('')}';
        }
        
        // Limit decimal places to 2
        if (parts.length == 2 && parts[1].length > 2) {
          newText = '${parts[0]}.${parts[1].substring(0, 2)}';
        }
        
        return TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }),
    ];
  }

  /// Input formatter for percentage fields
  static List<TextInputFormatter> getPercentageInputFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
      TextInputFormatter.withFunction((oldValue, newValue) {
        String newText = newValue.text.replaceAll(',', '.');
        final value = double.tryParse(newText);
        
        if (value != null && value > 100) {
          return oldValue;
        }
        
        return newValue;
      }),
    ];
  }

  /// Input formatter for integer fields
  static List<TextInputFormatter> getIntegerInputFormatters({int? maxValue}) {
    return [
      FilteringTextInputFormatter.digitsOnly,
      if (maxValue != null)
        TextInputFormatter.withFunction((oldValue, newValue) {
          final value = int.tryParse(newValue.text);
          if (value != null && value > maxValue) {
            return oldValue;
          }
          return newValue;
        }),
    ];
  }

  /// Input formatter for quantity fields (up to 3 decimal places)
  static List<TextInputFormatter> getQuantityInputFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
      TextInputFormatter.withFunction((oldValue, newValue) {
        // Remove any non-numeric characters except comma and dot
        String newText = newValue.text.replaceAll(RegExp(r'[^\d.,]'), '');
        
        // Replace comma with dot for decimal separator
        newText = newText.replaceAll(',', '.');
        
        // Ensure only one decimal point
        final parts = newText.split('.');
        if (parts.length > 2) {
          newText = '${parts[0]}.${parts.sublist(1).join('')}';
        }
        
        // Limit decimal places to 3
        if (parts.length == 2 && parts[1].length > 3) {
          newText = '${parts[0]}.${parts[1].substring(0, 3)}';
        }
        
        // Check maximum value constraint
        final value = double.tryParse(newText);
        if (value != null && value > 999999) {
          return oldValue;
        }
        
        return TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }),
    ];
  }

  /// Input formatter for accounting result fields (allows negative values)
  static List<TextInputFormatter> getAccountingInputFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.,-]')),
      TextInputFormatter.withFunction((oldValue, newValue) {
        String newText = newValue.text;
        
        // Handle negative sign - only allow at the beginning
        final hasNegative = newText.startsWith('-');
        if (hasNegative) {
          newText = newText.substring(1);
        }
        
        // Remove any non-numeric characters except comma and dot
        newText = newText.replaceAll(RegExp(r'[^\d.,]'), '');
        
        // Replace comma with dot for decimal separator
        newText = newText.replaceAll(',', '.');
        
        // Ensure only one decimal point
        final parts = newText.split('.');
        if (parts.length > 2) {
          newText = '${parts[0]}.${parts.sublist(1).join('')}';
        }
        
        // Limit decimal places to 2
        if (parts.length == 2 && parts[1].length > 2) {
          newText = '${parts[0]}.${parts[1].substring(0, 2)}';
        }
        
        // Add back negative sign if needed
        if (hasNegative && newText.isNotEmpty) {
          newText = '-$newText';
        }
        
        // Check value constraints (-100M to +100M)
        final value = double.tryParse(newText);
        if (value != null && (value < -********* || value > *********)) {
          return oldValue;
        }
        
        return TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }),
    ];
  }

  /// Input formatter for business amount fields (large amounts, no negative)
  static List<TextInputFormatter> getBusinessAmountFormatters({double? maxValue}) {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
      TextInputFormatter.withFunction((oldValue, newValue) {
        // Remove any non-numeric characters except comma and dot
        String newText = newValue.text.replaceAll(RegExp(r'[^\d.,]'), '');
        
        // Replace comma with dot for decimal separator
        newText = newText.replaceAll(',', '.');
        
        // Ensure only one decimal point
        final parts = newText.split('.');
        if (parts.length > 2) {
          newText = '${parts[0]}.${parts.sublist(1).join('')}';
        }
        
        // Limit decimal places to 2
        if (parts.length == 2 && parts[1].length > 2) {
          newText = '${parts[0]}.${parts[1].substring(0, 2)}';
        }
        
        // Check maximum value constraint (default 50M for reintegrations/deductions)
        final constraintValue = maxValue ?? ********;
        final value = double.tryParse(newText);
        if (value != null && value > constraintValue) {
          return oldValue;
        }
        
        return TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }),
    ];
  }
}
