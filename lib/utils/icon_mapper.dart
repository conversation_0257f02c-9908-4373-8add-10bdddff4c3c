import 'package:flutter/material.dart';

class IconMapper {
  static const Map<String, IconData> _iconMap = {
    // Deductions Icons
    'social_distance': Icons.social_distance,
    'family_restroom': Icons.family_restroom,
    'work': Icons.work_outline,
    'list_alt': Icons.list_alt,

    // Revenus Icons
    'business': Icons.business,
    'home': Icons.home_outlined,
    'account_balance': Icons.account_balance_outlined,

    // Bareme Icons
    'calendar_month': Icons.calendar_month,
    'date_range': Icons.date_range,
    'calculate': Icons.calculate_outlined,
    'percent': Icons.percent,
    'table_chart': Icons.table_chart_outlined,

    // New icons for CNSS, Presentation, and Exercices
    'health_and_safety': Icons.health_and_safety_outlined,
    'local_hospital': Icons.local_hospital_outlined,
    'document_scanner': Icons.document_scanner_outlined,
    'book': Icons.book_outlined,
    'school': Icons.school_outlined,
    'assignment': Icons.assignment_outlined,
    'description': Icons.description_outlined,
    'info': Icons.info_outline,
    'rule': Icons.rule_outlined,
    'edit_document': Icons.edit_document,

    // Fallback
    'default': Icons.category_outlined,
  };

  static IconData getIcon(String iconName) {
    return _iconMap[iconName] ?? _iconMap['default']!;
  }

  static Color getIconColor(BuildContext context, {Color? customColor}) {
    final colorScheme = Theme.of(context).colorScheme;
    return customColor ?? colorScheme.primary;
  }

  static double getIconSize({double? size}) {
    return size ?? 24.0;
  }
}
