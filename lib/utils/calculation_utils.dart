import 'dart:math' as math;

/// Utility class for standardized calculation precision and rounding
class CalculationUtils {
  // Standard precision for monetary calculations (2 decimal places)
  static const int monetaryPrecision = 2;
  
  // Standard precision for percentage calculations (4 decimal places)
  static const int percentagePrecision = 4;
  
  // Standard precision for tax rate calculations (2 decimal places)
  static const int taxRatePrecision = 2;

  /// Round a monetary amount to standard precision (2 decimal places)
  static double roundMonetary(double value) {
    return _roundToPrecision(value, monetaryPrecision);
  }

  /// Round a percentage to standard precision (4 decimal places)
  static double roundPercentage(double value) {
    return _roundToPrecision(value, percentagePrecision);
  }

  /// Round a tax rate to standard precision (2 decimal places)
  static double roundTaxRate(double value) {
    return _roundToPrecision(value, taxRatePrecision);
  }

  /// Round to specified number of decimal places
  static double _roundToPrecision(double value, int precision) {
    final factor = math.pow(10, precision);
    return (value * factor).round() / factor;
  }

  /// Format monetary amount for display
  static String formatMonetary(double value, {String currency = 'DH'}) {
    final rounded = roundMonetary(value);
    return '${rounded.toStringAsFixed(monetaryPrecision)} $currency';
  }

  /// Format percentage for display
  static String formatPercentage(double value, {bool includeSymbol = true}) {
    final rounded = roundPercentage(value);
    final formatted = rounded.toStringAsFixed(percentagePrecision);
    return includeSymbol ? '$formatted%' : formatted;
  }

  /// Calculate percentage of a value with proper rounding
  static double calculatePercentage(double value, double percentage) {
    final result = value * (percentage / 100);
    return roundMonetary(result);
  }

  /// Calculate tax amount with proper rounding
  static double calculateTax(double taxableAmount, double taxRate) {
    final result = taxableAmount * (taxRate / 100);
    return roundMonetary(result);
  }

  /// Calculate net amount after deducting tax
  static double calculateNetAmount(double grossAmount, double taxAmount) {
    final result = grossAmount - taxAmount;
    return roundMonetary(result);
  }

  /// Calculate HT amount from TTC amount and VAT rate
  static double calculateHTFromTTC(double ttcAmount, double vatRate) {
    final result = ttcAmount / (1 + (vatRate / 100));
    return roundMonetary(result);
  }

  /// Calculate TTC amount from HT amount and VAT rate
  static double calculateTTCFromHT(double htAmount, double vatRate) {
    final result = htAmount * (1 + (vatRate / 100));
    return roundMonetary(result);
  }

  /// Calculate VAT amount from HT amount and VAT rate
  static double calculateVATFromHT(double htAmount, double vatRate) {
    final result = htAmount * (vatRate / 100);
    return roundMonetary(result);
  }

  /// Calculate VAT amount from TTC amount and VAT rate
  static double calculateVATFromTTC(double ttcAmount, double vatRate) {
    final htAmount = calculateHTFromTTC(ttcAmount, vatRate);
    return roundMonetary(ttcAmount - htAmount);
  }

  /// Validate that a monetary amount is positive
  static bool isValidMonetaryAmount(double amount) {
    return amount >= 0 && amount.isFinite;
  }

  /// Validate that a percentage is within valid range (0-100)
  static bool isValidPercentage(double percentage) {
    return percentage >= 0 && percentage <= 100 && percentage.isFinite;
  }

  /// Validate that a tax rate is within reasonable range (0-50)
  static bool isValidTaxRate(double taxRate) {
    return taxRate >= 0 && taxRate <= 50 && taxRate.isFinite;
  }

  /// Clamp a value to ensure it's not negative (useful for tax calculations)
  static double ensureNonNegative(double value) {
    return math.max(0.0, value);
  }

  /// Calculate progressive tax using brackets
  static double calculateProgressiveTax(
    double taxableIncome,
    List<Map<String, dynamic>> brackets,
  ) {
    double totalTax = 0.0;
    double remainingIncome = taxableIncome;

    for (final bracket in brackets) {
      if (remainingIncome <= 0) break;

      final min = (bracket['min'] as num).toDouble();
      final max = bracket['max'] != null ? (bracket['max'] as num).toDouble() : null;
      final rate = (bracket['rate'] as num).toDouble();

      double taxableAmount;
      if (max == null) {
        // Last bracket - tax all remaining income
        taxableAmount = remainingIncome;
      } else {
        // Calculate taxable amount for this bracket
        final bracketSize = max - min;
        taxableAmount = math.min(remainingIncome, bracketSize);
      }

      if (taxableAmount > 0) {
        final bracketTax = calculateTax(taxableAmount, rate);
        totalTax += bracketTax;
        remainingIncome -= taxableAmount;
      }
    }

    return roundMonetary(totalTax);
  }

  /// Calculate social security contributions with ceiling
  static double calculateSocialContribution(
    double salary,
    double rate,
    double? ceiling,
  ) {
    final baseAmount = ceiling != null ? math.min(salary, ceiling) : salary;
    return calculatePercentage(baseAmount, rate);
  }

  /// Calculate professional expenses with rate and ceiling
  static double calculateProfessionalExpenses(
    double baseAmount,
    double rate,
    double ceiling,
  ) {
    final calculated = calculatePercentage(baseAmount, rate);
    return math.min(calculated, ceiling);
  }

  // TVA Line Item and Invoice Calculation Methods

  /// Calculate totals for a single line item
  /// Returns a record with (totalHT, totalTVA, totalTTC)
  static ({double totalHT, double totalTVA, double totalTTC}) calculateLineItemTotals(
    double quantity,
    double unitPrice,
    double vatRate,
  ) {
    final totalHT = roundMonetary(quantity * unitPrice);
    final totalTVA = calculateVATFromHT(totalHT, vatRate);
    final totalTTC = roundMonetary(totalHT + totalTVA);
    
    return (
      totalHT: totalHT,
      totalTVA: totalTVA,
      totalTTC: totalTTC,
    );
  }

  /// Calculate comprehensive invoice totals from a list of line items
  /// Returns a record with totals and VAT breakdown
  static ({
    double totalHT,
    double totalTVA,
    double totalTTC,
    Map<double, double> vatBreakdown,
    int exemptItemsCount
  }) calculateInvoiceTotals(List<dynamic> items) {
    double totalHT = 0.0;
    double totalTVA = 0.0;
    Map<double, double> vatBreakdown = {};
    int exemptItemsCount = 0;

    for (final item in items) {
      // Access item properties dynamically since TvaLineItem is defined elsewhere
      final itemTotalHT = (item as dynamic).totalHT as double;
      final itemTotalTVA = (item as dynamic).totalTVA as double;
      final vatRate = (item as dynamic).vatRate as double;
      final isExempt = (item as dynamic).isExempt as bool;

      totalHT += itemTotalHT;
      totalTVA += itemTotalTVA;

      // Build VAT breakdown by rate
      if (vatBreakdown.containsKey(vatRate)) {
        vatBreakdown[vatRate] = vatBreakdown[vatRate]! + itemTotalTVA;
      } else {
        vatBreakdown[vatRate] = itemTotalTVA;
      }

      if (isExempt) {
        exemptItemsCount++;
      }
    }

    // Round final totals
    totalHT = roundMonetary(totalHT);
    totalTVA = roundMonetary(totalTVA);
    final totalTTC = roundMonetary(totalHT + totalTVA);

    // Round VAT breakdown values
    vatBreakdown = vatBreakdown.map(
      (rate, amount) => MapEntry(rate, roundMonetary(amount)),
    );

    return (
      totalHT: totalHT,
      totalTVA: totalTVA,
      totalTTC: totalTTC,
      vatBreakdown: vatBreakdown,
      exemptItemsCount: exemptItemsCount,
    );
  }

  /// Validate that a quantity is positive and finite
  static bool isValidQuantity(double quantity) {
    return quantity > 0 && quantity.isFinite;
  }

  /// Validate that a unit price is non-negative and finite
  /// Allows zero unit price for free items
  static bool isValidUnitPrice(double unitPrice) {
    return unitPrice >= 0 && unitPrice.isFinite;
  }
}
