import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'excel_export_utils.dart';

class PdfExportUtils {
  static const PdfColor primaryColor = PdfColor.fromInt(0xFF1976D2);
  static const PdfColor secondaryColor = PdfColor.fromInt(0xFF424242);
  static const PdfColor accentColor = PdfColor.fromInt(0xFF4CAF50);

  static Future<void> exportCalculationToPdf(
    List<TableData> tables,
    String fileName, {
    String? title,
    String? subtitle,
    Map<String, String>? metadata,
  }) async {
    final pdf = pw.Document();
    
    // Add pages
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildHeader(title ?? fileName),
        footer: (context) => _buildFooter(context),
        build: (context) => [
          if (subtitle != null) ...[
            pw.Text(
              subtitle,
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: secondaryColor,
              ),
            ),
            pw.SizedBox(height: 20),
          ],
          if (metadata != null) ...[
            _buildMetadataSection(metadata),
            pw.SizedBox(height: 20),
          ],
          ...tables.map((table) => _buildTableSection(table)),
        ],
      ),
    );

    await _savePdfFile(pdf, fileName);
  }

  static Future<void> exportHistoryToPdf(
    List<TableData> tables,
    String fileName, {
    Map<String, dynamic>? statistics,
  }) async {
    final pdf = pw.Document();
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildHeader('Historique des Calculs'),
        footer: (context) => _buildFooter(context),
        build: (context) => [
          pw.Text(
            'Rapport d\'Historique des Calculs',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: primaryColor,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'Généré le ${_formatDate(DateTime.now())}',
            style: const pw.TextStyle(
              fontSize: 12,
              color: secondaryColor,
            ),
          ),
          pw.SizedBox(height: 20),
          if (statistics != null) ...[
            _buildStatisticsSection(statistics),
            pw.SizedBox(height: 20),
          ],
          ...tables.map((table) => _buildTableSection(table)),
        ],
      ),
    );

    await _savePdfFile(pdf, fileName);
  }

  static Future<void> exportComparisonToPdf(
    List<TableData> tables,
    String fileName, {
    String? comparisonTitle,
    List<String>? conclusions,
  }) async {
    final pdf = pw.Document();
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildHeader(comparisonTitle ?? 'Comparaison'),
        footer: (context) => _buildFooter(context),
        build: (context) => [
          pw.Text(
            comparisonTitle ?? 'Rapport de Comparaison',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: primaryColor,
            ),
          ),
          pw.SizedBox(height: 20),
          ...tables.map((table) => _buildTableSection(table)),
          if (conclusions != null && conclusions.isNotEmpty) ...[
            pw.SizedBox(height: 20),
            _buildConclusionsSection(conclusions),
          ],
        ],
      ),
    );

    await _savePdfFile(pdf, fileName);
  }

  static pw.Widget _buildHeader(String title) {
    return pw.Container(
      alignment: pw.Alignment.centerLeft,
      margin: const pw.EdgeInsets.only(bottom: 20),
      padding: const pw.EdgeInsets.only(bottom: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: primaryColor,
            width: 2,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              color: primaryColor,
            ),
          ),
          pw.Text(
            'Comptabilité Marocaine',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: secondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 20),
      padding: const pw.EdgeInsets.only(top: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(
            color: secondaryColor,
            width: 1,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'Généré le ${_formatDate(DateTime.now())}',
            style: const pw.TextStyle(
              fontSize: 10,
              color: secondaryColor,
            ),
          ),
          pw.Text(
            'Page ${context.pageNumber} / ${context.pagesCount}',
            style: const pw.TextStyle(
              fontSize: 10,
              color: secondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildMetadataSection(Map<String, String> metadata) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromInt(0xFFF5F5F5),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Informations du Calcul',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: primaryColor,
            ),
          ),
          pw.SizedBox(height: 10),
          ...metadata.entries.map(
            (entry) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 4),
              child: pw.Row(
                children: [
                  pw.SizedBox(
                    width: 120,
                    child: pw.Text(
                      '${entry.key}:',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ),
                  pw.Expanded(
                    child: pw.Text(
                      entry.value,
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildStatisticsSection(Map<String, dynamic> statistics) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromInt(0xFFE8F5E8),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Statistiques',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: accentColor,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Wrap(
            spacing: 20,
            runSpacing: 8,
            children: statistics.entries.map(
              (entry) => pw.Container(
                padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: pw.BoxDecoration(
                  color: PdfColors.white,
                  borderRadius: pw.BorderRadius.circular(4),
                ),
                child: pw.Text(
                  '${entry.key}: ${entry.value}',
                  style: const pw.TextStyle(fontSize: 11),
                ),
              ),
            ).toList(),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildTableSection(TableData table) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          table.title,
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor,
          ),
        ),
        pw.SizedBox(height: 10),
        _buildTable(table),
        pw.SizedBox(height: 20),
      ],
    );
  }

  static pw.Widget _buildTable(TableData table) {
    return pw.Table(
      border: pw.TableBorder.all(
        color: PdfColor.fromInt(0xFFE0E0E0),
        width: 1,
      ),
      columnWidths: _calculateColumnWidths(table.headers.length),
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(
            color: primaryColor,
          ),
          children: table.headers.map(
            (header) => pw.Container(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                header,
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ).toList(),
        ),
        // Data rows
        ...table.rows.asMap().entries.map(
          (entry) => pw.TableRow(
            decoration: pw.BoxDecoration(
              color: entry.key % 2 == 0 
                  ? PdfColors.white 
                  : PdfColor.fromInt(0xFFF9F9F9),
            ),
            children: entry.value.map(
              (cell) => pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  cell.toString(),
                  style: const pw.TextStyle(fontSize: 11),
                  textAlign: _isNumeric(cell.toString()) 
                      ? pw.TextAlign.right 
                      : pw.TextAlign.left,
                ),
              ),
            ).toList(),
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildConclusionsSection(List<String> conclusions) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromInt(0xFFFFF3E0),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Conclusions et Recommandations',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromInt(0xFFFF9800),
            ),
          ),
          pw.SizedBox(height: 10),
          ...conclusions.map(
            (conclusion) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 6),
              child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    '• ',
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColor.fromInt(0xFFFF9800),
                    ),
                  ),
                  pw.Expanded(
                    child: pw.Text(
                      conclusion,
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Map<int, pw.TableColumnWidth> _calculateColumnWidths(int columnCount) {
    final widths = <int, pw.TableColumnWidth>{};
    final flexValue = 1.0 / columnCount;
    
    for (int i = 0; i < columnCount; i++) {
      widths[i] = pw.FlexColumnWidth(flexValue);
    }
    
    return widths;
  }

  static bool _isNumeric(String str) {
    return double.tryParse(str.replaceAll(',', '').replaceAll(' ', '')) != null;
  }

  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  static Future<void> _savePdfFile(pw.Document pdf, String fileName) async {
    try {
      final output = await getApplicationDocumentsDirectory();
      final file = File('${output.path}/$fileName.pdf');
      
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);
      
      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Rapport PDF généré par Comptabilité Marocaine',
      );
    } catch (e) {
      throw Exception('Erreur lors de la génération du PDF: $e');
    }
  }

  static Future<Uint8List> generatePdfBytes(
    List<TableData> tables,
    String title, {
    String? subtitle,
    Map<String, String>? metadata,
  }) async {
    final pdf = pw.Document();
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildHeader(title),
        footer: (context) => _buildFooter(context),
        build: (context) => [
          if (subtitle != null) ...[
            pw.Text(
              subtitle,
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: secondaryColor,
              ),
            ),
            pw.SizedBox(height: 20),
          ],
          if (metadata != null) ...[
            _buildMetadataSection(metadata),
            pw.SizedBox(height: 20),
          ],
          ...tables.map((table) => _buildTableSection(table)),
        ],
      ),
    );

    return await pdf.save();
  }

  static Future<void> printPdf(Uint8List pdfBytes) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfBytes,
    );
  }
}
