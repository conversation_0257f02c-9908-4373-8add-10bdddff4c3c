
/// Base class for fusion-related journal entries
abstract class FusionEntry {
  final String date;
  final double capital;
  final double reserves;
  final Map<String, double> assets;
  final Map<String, double> liabilities;

  const FusionEntry({
    required this.date,
    required this.capital,
    required this.reserves,
    required this.assets,
    required this.liabilities,
  });

  /// Calculate net worth of the company
  double calculateNetWorth() {
    final totalAssets = assets.values.fold(0.0, (sum, value) => sum + value);
    final totalLiabilities =
        liabilities.values.fold(0.0, (sum, value) => sum + value);
    return totalAssets - totalLiabilities + reserves;
  }

  /// Calculate share value
  double calculateShareValue(int numberOfShares) {
    return calculateNetWorth() / numberOfShares;
  }

  /// Validate entry data
  bool validate() {
    return capital > 0 && assets.isNotEmpty;
  }
}

/// Class for handling fusion by creation entries
class FusionCreationEntry extends FusionEntry {
  final List<FusionEntry> contributingCompanies;
  final double newCapital;

  const FusionCreationEntry({
    required super.date,
    required super.capital,
    required super.reserves,
    required super.assets,
    required super.liabilities,
    required this.contributingCompanies,
    required this.newCapital,
  });

  /// Calculate share distribution
  Map<String, int> calculateShareDistribution(int totalNewShares) {
    final Map<String, int> distribution = {};
    final totalContribution = contributingCompanies.fold(
        0.0, (sum, company) => sum + company.calculateNetWorth());

    for (var company in contributingCompanies) {
      final share =
          (company.calculateNetWorth() / totalContribution * totalNewShares)
              .round();
      distribution[company.toString()] = share;
    }

    return distribution;
  }

  @override
  bool validate() {
    return super.validate() &&
        contributingCompanies.isNotEmpty &&
        newCapital > 0;
  }
}

/// Class for handling fusion by absorption entries
class FusionAbsorptionEntry extends FusionEntry {
  final FusionEntry absorbedCompany;
  final double exchangeRatio;
  final int newShares;

  const FusionAbsorptionEntry({
    required super.date,
    required super.capital,
    required super.reserves,
    required super.assets,
    required super.liabilities,
    required this.absorbedCompany,
    required this.exchangeRatio,
    required this.newShares,
  });

  /// Calculate merger premium
  double calculateMergerPremium(double nominalValue) {
    final contributionValue = absorbedCompany.calculateNetWorth();
    final capitalIncrease = newShares * nominalValue;
    return contributionValue - capitalIncrease;
  }

  @override
  bool validate() {
    return super.validate() && exchangeRatio > 0 && newShares > 0;
  }
}

/// Class for handling fusion with shareholdings
class FusionDependentEntry extends FusionAbsorptionEntry {
  final double shareholdingPercentage;
  final double shareholdingValue;

  const FusionDependentEntry({
    required super.date,
    required super.capital,
    required super.reserves,
    required super.assets,
    required super.liabilities,
    required super.absorbedCompany,
    required super.exchangeRatio,
    required super.newShares,
    required this.shareholdingPercentage,
    required this.shareholdingValue,
  });

  /// Calculate merger gain or loss
  double calculateMergerResult() {
    final actualValue =
        absorbedCompany.calculateNetWorth() * shareholdingPercentage;
    return actualValue - shareholdingValue;
  }

  @override
  bool validate() {
    return super.validate() &&
        shareholdingPercentage > 0 &&
        shareholdingPercentage <= 1;
  }
}

/// Factory for creating fusion entries
class FusionEntryFactory {
  static FusionCreationEntry createFusionCreation({
    required String date,
    required double capital,
    required double reserves,
    required Map<String, double> assets,
    required Map<String, double> liabilities,
    required List<FusionEntry> contributingCompanies,
    required double newCapital,
  }) {
    return FusionCreationEntry(
      date: date,
      capital: capital,
      reserves: reserves,
      assets: assets,
      liabilities: liabilities,
      contributingCompanies: contributingCompanies,
      newCapital: newCapital,
    );
  }

  static FusionAbsorptionEntry createFusionAbsorption({
    required String date,
    required double capital,
    required double reserves,
    required Map<String, double> assets,
    required Map<String, double> liabilities,
    required FusionEntry absorbedCompany,
    required double exchangeRatio,
    required int newShares,
  }) {
    return FusionAbsorptionEntry(
      date: date,
      capital: capital,
      reserves: reserves,
      assets: assets,
      liabilities: liabilities,
      absorbedCompany: absorbedCompany,
      exchangeRatio: exchangeRatio,
      newShares: newShares,
    );
  }

  static FusionDependentEntry createFusionDependent({
    required String date,
    required double capital,
    required double reserves,
    required Map<String, double> assets,
    required Map<String, double> liabilities,
    required FusionEntry absorbedCompany,
    required double exchangeRatio,
    required int newShares,
    required double shareholdingPercentage,
    required double shareholdingValue,
  }) {
    return FusionDependentEntry(
      date: date,
      capital: capital,
      reserves: reserves,
      assets: assets,
      liabilities: liabilities,
      absorbedCompany: absorbedCompany,
      exchangeRatio: exchangeRatio,
      newShares: newShares,
      shareholdingPercentage: shareholdingPercentage,
      shareholdingValue: shareholdingValue,
    );
  }
}
