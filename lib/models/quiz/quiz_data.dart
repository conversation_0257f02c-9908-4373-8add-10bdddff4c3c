class QuizData {
  final String title;
  final String description;
  final List<Quiz> quizzes;

  QuizData({
    required this.title,
    required this.description,
    required this.quizzes,
  });

  factory QuizData.fromJson(Map<String, dynamic> json) {
    return QuizData(
      title: json['title'] as String,
      description: json['description'] as String,
      quizzes: (json['quizzes'] as List<dynamic>)
          .map((e) => Quiz.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class Quiz {
  final String titre;
  final String description;
  final List<Question> questions;
  final String? difficulte;
  final String categorie;

  Quiz({
    required this.titre,
    required this.description,
    required this.questions,
    this.difficulte,
    required this.categorie,
  });

  factory Quiz.fromJson(Map<String, dynamic> json) {
    return Quiz(
      titre: json['titre'] as String,
      description: json['description'] as String,
      questions: (json['questions'] as List<dynamic>)
          .map((e) => Question.fromJson(e as Map<String, dynamic>))
          .toList(),
      difficulte: json['difficulte'] as String?,
      categorie: json['categorie'] as String,
    );
  }
}

class Question {
  final String question;
  final List<String> options;
  final int correctAnswer;
  final String? explication;

  Question({
    required this.question,
    required this.options,
    required this.correctAnswer,
    this.explication,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      question: json['question'] as String,
      options: (json['options'] as List<dynamic>).map((e) => e as String).toList(),
      correctAnswer: json['correctAnswer'] as int,
      explication: json['explication'] as String?,
    );
  }
}
