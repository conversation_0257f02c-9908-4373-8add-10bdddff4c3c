import 'package:flutter/foundation.dart';
import '../quiz_model.dart';

@immutable
class CustomQuizConfig {
  final List<String> selectedTopics;
  final List<String> selectedCategories;
  final int minDifficulty; // 1-5 scale
  final int maxDifficulty; // 1-5 scale
  final int questionCount;
  final int timePerQuestion; // seconds
  final bool includeSpacedRepetition;
  final bool adaptiveDifficulty;
  final bool focusOnWeakAreas;
  final String customTitle;

  const CustomQuizConfig({
    this.selectedTopics = const [],
    this.selectedCategories = const [],
    this.minDifficulty = 1,
    this.maxDifficulty = 5,
    this.questionCount = 10,
    this.timePerQuestion = 30,
    this.includeSpacedRepetition = false,
    this.adaptiveDifficulty = true,
    this.focusOnWeakAreas = false,
    this.customTitle = 'Quiz personnalisé',
  });

  // Validation method to ensure configuration is valid
  bool isValid() {
    // Must have at least one topic or category selected
    if (selectedTopics.isEmpty && selectedCategories.isEmpty) {
      return false;
    }

    // Difficulty range must be valid
    if (minDifficulty < 1 || maxDifficulty > 5 || minDifficulty > maxDifficulty) {
      return false;
    }

    // Question count must be reasonable
    if (questionCount < 1 || questionCount > 100) {
      return false;
    }

    // Time per question must be reasonable
    if (timePerQuestion < 10 || timePerQuestion > 300) {
      return false;
    }

    return true;
  }

  // Get estimated quiz duration in minutes
  int getEstimatedDuration() {
    return ((questionCount * timePerQuestion) / 60).ceil();
  }

  // Get difficulty range as a readable string
  String getDifficultyRangeText() {
    if (minDifficulty == maxDifficulty) {
      return 'Niveau $minDifficulty';
    }
    return 'Niveaux $minDifficulty-$maxDifficulty';
  }

  // Get question pool description
  String getQuestionPoolDescription() {
    List<String> descriptions = [];

    if (selectedCategories.isNotEmpty) {
      descriptions.add('${selectedCategories.length} catégorie(s)');
    }

    if (selectedTopics.isNotEmpty) {
      descriptions.add('${selectedTopics.length} sujet(s)');
    }

    if (includeSpacedRepetition) {
      descriptions.add('Révisions programmées');
    }

    if (focusOnWeakAreas) {
      descriptions.add('Focus sur les faiblesses');
    }

    return descriptions.join(', ');
  }

  // Check if configuration matches a question
  bool matchesQuestion(QuizQuestion question) {
    // Check topic filter
    if (selectedTopics.isNotEmpty && !selectedTopics.contains(question.topic)) {
      return false;
    }

    // Check difficulty range
    if (question.difficulty < minDifficulty || question.difficulty > maxDifficulty) {
      return false;
    }

    return true;
  }

  // Factory constructor for focused review configuration
  factory CustomQuizConfig.focusedReview({
    required List<String> topics,
    int questionCount = 15,
    bool includeSpacedRepetition = true,
  }) {
    return CustomQuizConfig(
      selectedTopics: topics,
      questionCount: questionCount,
      includeSpacedRepetition: includeSpacedRepetition,
      adaptiveDifficulty: false,
      focusOnWeakAreas: false,
      customTitle: 'Révision ciblée',
      timePerQuestion: 45,
    );
  }

  // Factory constructor for weakness training configuration
  factory CustomQuizConfig.weaknessTraining({
    required List<String> categories,
    int questionCount = 20,
  }) {
    return CustomQuizConfig(
      selectedCategories: categories,
      questionCount: questionCount,
      focusOnWeakAreas: true,
      adaptiveDifficulty: true,
      includeSpacedRepetition: true,
      customTitle: 'Entraînement des faiblesses',
      minDifficulty: 1,
      maxDifficulty: 3, // Start with easier questions
      timePerQuestion: 60,
    );
  }

  // Factory constructor for comprehensive test configuration
  factory CustomQuizConfig.comprehensiveTest({
    required List<String> categories,
    int questionCount = 50,
  }) {
    return CustomQuizConfig(
      selectedCategories: categories,
      questionCount: questionCount,
      adaptiveDifficulty: false,
      includeSpacedRepetition: false,
      focusOnWeakAreas: false,
      customTitle: 'Test complet',
      minDifficulty: 1,
      maxDifficulty: 5,
      timePerQuestion: 90,
    );
  }

  // Factory constructor for quick review configuration
  factory CustomQuizConfig.quickReview({
    List<String> topics = const [],
    int questionCount = 5,
  }) {
    return CustomQuizConfig(
      selectedTopics: topics,
      questionCount: questionCount,
      adaptiveDifficulty: false,
      includeSpacedRepetition: false,
      focusOnWeakAreas: false,
      customTitle: 'Révision rapide',
      timePerQuestion: 20,
      minDifficulty: 1,
      maxDifficulty: 3,
    );
  }

  // Create copy with updated values
  CustomQuizConfig copyWith({
    List<String>? selectedTopics,
    List<String>? selectedCategories,
    int? minDifficulty,
    int? maxDifficulty,
    int? questionCount,
    int? timePerQuestion,
    bool? includeSpacedRepetition,
    bool? adaptiveDifficulty,
    bool? focusOnWeakAreas,
    String? customTitle,
  }) {
    return CustomQuizConfig(
      selectedTopics: selectedTopics ?? this.selectedTopics,
      selectedCategories: selectedCategories ?? this.selectedCategories,
      minDifficulty: minDifficulty ?? this.minDifficulty,
      maxDifficulty: maxDifficulty ?? this.maxDifficulty,
      questionCount: questionCount ?? this.questionCount,
      timePerQuestion: timePerQuestion ?? this.timePerQuestion,
      includeSpacedRepetition: includeSpacedRepetition ?? this.includeSpacedRepetition,
      adaptiveDifficulty: adaptiveDifficulty ?? this.adaptiveDifficulty,
      focusOnWeakAreas: focusOnWeakAreas ?? this.focusOnWeakAreas,
      customTitle: customTitle ?? this.customTitle,
    );
  }

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'selectedTopics': selectedTopics,
      'selectedCategories': selectedCategories,
      'minDifficulty': minDifficulty,
      'maxDifficulty': maxDifficulty,
      'questionCount': questionCount,
      'timePerQuestion': timePerQuestion,
      'includeSpacedRepetition': includeSpacedRepetition,
      'adaptiveDifficulty': adaptiveDifficulty,
      'focusOnWeakAreas': focusOnWeakAreas,
      'customTitle': customTitle,
    };
  }

  // Create from JSON
  factory CustomQuizConfig.fromJson(Map<String, dynamic> json) {
    return CustomQuizConfig(
      selectedTopics: List<String>.from(json['selectedTopics'] ?? []),
      selectedCategories: List<String>.from(json['selectedCategories'] ?? []),
      minDifficulty: json['minDifficulty'] as int? ?? 1,
      maxDifficulty: json['maxDifficulty'] as int? ?? 5,
      questionCount: json['questionCount'] as int? ?? 10,
      timePerQuestion: json['timePerQuestion'] as int? ?? 30,
      includeSpacedRepetition: json['includeSpacedRepetition'] as bool? ?? false,
      adaptiveDifficulty: json['adaptiveDifficulty'] as bool? ?? true,
      focusOnWeakAreas: json['focusOnWeakAreas'] as bool? ?? false,
      customTitle: json['customTitle'] as String? ?? 'Quiz personnalisé',
    );
  }

  // Get configuration summary for display
  String getSummary() {
    List<String> summary = [];
    
    summary.add('$questionCount questions');
    summary.add(getDifficultyRangeText());
    
    if (selectedTopics.isNotEmpty) {
      summary.add('${selectedTopics.length} sujet(s)');
    }
    
    if (selectedCategories.isNotEmpty) {
      summary.add('${selectedCategories.length} catégorie(s)');
    }
    
    if (adaptiveDifficulty) {
      summary.add('Difficulté adaptive');
    }
    
    if (includeSpacedRepetition) {
      summary.add('Révisions');
    }
    
    if (focusOnWeakAreas) {
      summary.add('Focus faiblesses');
    }
    
    return summary.join(' • ');
  }

  // Get validation errors
  List<String> getValidationErrors() {
    List<String> errors = [];

    if (selectedTopics.isEmpty && selectedCategories.isEmpty) {
      errors.add('Sélectionnez au moins un sujet ou une catégorie');
    }

    if (minDifficulty < 1 || maxDifficulty > 5) {
      errors.add('La difficulté doit être entre 1 et 5');
    }

    if (minDifficulty > maxDifficulty) {
      errors.add('La difficulté minimale ne peut pas être supérieure à la maximale');
    }

    if (questionCount < 1) {
      errors.add('Le nombre de questions doit être au moins 1');
    }

    if (questionCount > 100) {
      errors.add('Le nombre de questions ne peut pas dépasser 100');
    }

    if (timePerQuestion < 10) {
      errors.add('Le temps par question doit être au moins 10 secondes');
    }

    if (timePerQuestion > 300) {
      errors.add('Le temps par question ne peut pas dépasser 5 minutes');
    }

    return errors;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is CustomQuizConfig &&
        listEquals(other.selectedTopics, selectedTopics) &&
        listEquals(other.selectedCategories, selectedCategories) &&
        other.minDifficulty == minDifficulty &&
        other.maxDifficulty == maxDifficulty &&
        other.questionCount == questionCount &&
        other.timePerQuestion == timePerQuestion &&
        other.includeSpacedRepetition == includeSpacedRepetition &&
        other.adaptiveDifficulty == adaptiveDifficulty &&
        other.focusOnWeakAreas == focusOnWeakAreas &&
        other.customTitle == customTitle;
  }

  @override
  int get hashCode {
    return Object.hash(
      Object.hashAll(selectedTopics),
      Object.hashAll(selectedCategories),
      minDifficulty,
      maxDifficulty,
      questionCount,
      timePerQuestion,
      includeSpacedRepetition,
      adaptiveDifficulty,
      focusOnWeakAreas,
      customTitle,
    );
  }

  @override
  String toString() {
    return 'CustomQuizConfig(${getSummary()})';
  }
}
