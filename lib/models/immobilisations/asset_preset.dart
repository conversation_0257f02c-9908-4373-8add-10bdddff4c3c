import 'package:flutter/material.dart';

/// Comprehensive model class for asset presets used in amortization calculations
/// 
/// This class represents predefined asset configurations with validation rules
/// and helper methods for the Moroccan accounting amortization calculator.
class AssetPreset {
  /// Unique identifier for the asset preset
  final String id;
  
  /// Display name of the asset
  final String name;
  
  /// Category grouping (e.g., "Buildings", "Equipment", "Vehicles")
  final String category;
  
  /// Default amortization rate as a percentage (e.g., 10.0 for 10%)
  final double standardRate;
  
  /// Default duration in years
  final int defaultDuration;
  
  /// Default amortization mode ("linear" or "degressive")
  final String defaultMode;
  
  /// Default residual value as a percentage of initial value
  final double residualValuePercent;
  
  /// Description of the asset type
  final String description;
  
  /// Material icon name for UI display
  final String iconName;
  
  /// List of specific examples of this asset type
  final List<String> examples;
  
  /// Minimum allowed duration in years
  final int minDuration;
  
  /// Maximum allowed duration in years
  final int maxDuration;
  
  /// List of permitted amortization modes
  final List<String> allowedModes;
  
  /// Regulatory compliance information for Moroccan tax law
  final String complianceNote;

  const AssetPreset({
    required this.id,
    required this.name,
    required this.category,
    required this.standardRate,
    required this.defaultDuration,
    required this.defaultMode,
    required this.residualValuePercent,
    required this.description,
    required this.iconName,
    required this.examples,
    required this.minDuration,
    required this.maxDuration,
    required this.allowedModes,
    required this.complianceNote,
  });

  /// Creates an AssetPreset instance from a JSON map
  factory AssetPreset.fromJson(Map<String, dynamic> json) {
    return AssetPreset(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      standardRate: (json['standardRate'] as num).toDouble(),
      defaultDuration: json['defaultDuration'] as int,
      defaultMode: json['defaultMode'] as String,
      residualValuePercent: (json['residualValuePercent'] as num).toDouble(),
      description: json['description'] as String,
      iconName: json['iconName'] as String,
      examples: List<String>.from(json['examples'] as List),
      minDuration: json['minDuration'] as int,
      maxDuration: json['maxDuration'] as int,
      allowedModes: List<String>.from(json['allowedModes'] as List),
      complianceNote: json['complianceNote'] as String,
    );
  }

  /// Converts the AssetPreset instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'standardRate': standardRate,
      'defaultDuration': defaultDuration,
      'defaultMode': defaultMode,
      'residualValuePercent': residualValuePercent,
      'description': description,
      'iconName': iconName,
      'examples': examples,
      'minDuration': minDuration,
      'maxDuration': maxDuration,
      'allowedModes': allowedModes,
      'complianceNote': complianceNote,
    };
  }

  /// Validates if the given duration is within allowed range
  bool isValidDuration(int duration) {
    return duration >= minDuration && duration <= maxDuration;
  }

  /// Validates if the given amortization mode is allowed for this asset
  bool isModeAllowed(String mode) {
    return allowedModes.contains(mode);
  }

  /// Creates a copy of this AssetPreset with updated values
  AssetPreset copyWith({
    String? id,
    String? name,
    String? category,
    double? standardRate,
    int? defaultDuration,
    String? defaultMode,
    double? residualValuePercent,
    String? description,
    String? iconName,
    List<String>? examples,
    int? minDuration,
    int? maxDuration,
    List<String>? allowedModes,
    String? complianceNote,
  }) {
    return AssetPreset(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      standardRate: standardRate ?? this.standardRate,
      defaultDuration: defaultDuration ?? this.defaultDuration,
      defaultMode: defaultMode ?? this.defaultMode,
      residualValuePercent: residualValuePercent ?? this.residualValuePercent,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      examples: examples ?? this.examples,
      minDuration: minDuration ?? this.minDuration,
      maxDuration: maxDuration ?? this.maxDuration,
      allowedModes: allowedModes ?? this.allowedModes,
      complianceNote: complianceNote ?? this.complianceNote,
    );
  }

  /// Converts icon name to IconData using a predefined mapping
  /// 
  /// Returns a default icon if the specified icon name is not found
  IconData get icon {
    // Common Material icons mapping for asset types
    const iconMap = <String, IconData>{
      'business': Icons.business,
      'home': Icons.home,
      'apartment': Icons.apartment,
      'factory': Icons.factory,
      'construction': Icons.construction,
      'precision_manufacturing': Icons.precision_manufacturing,
      'handyman': Icons.handyman,
      'build': Icons.build,
      'directions_car': Icons.directions_car,
      'local_shipping': Icons.local_shipping,
      'two_wheeler': Icons.two_wheeler,
      'agriculture': Icons.agriculture,
      'computer': Icons.computer,
      'phone_android': Icons.phone_android,
      'laptop': Icons.laptop,
      'desktop_windows': Icons.desktop_windows,
      'print': Icons.print,
      'router': Icons.router,
      'chair': Icons.chair,
      'table_restaurant': Icons.table_restaurant,
      'bed': Icons.bed,
      'kitchen': Icons.kitchen,
      'air_conditioner': Icons.air,
      'lightbulb': Icons.lightbulb,
      'security': Icons.security,
      'camera_alt': Icons.camera_alt,
      'medical_services': Icons.medical_services,
      'science': Icons.science,
      'library_books': Icons.library_books,
      'menu_book': Icons.menu_book,
      'sports': Icons.sports,
      'fitness_center': Icons.fitness_center,
      'restaurant': Icons.restaurant,
      'local_cafe': Icons.local_cafe,
      'store': Icons.store,
      'shopping_cart': Icons.shopping_cart,
      'inventory': Icons.inventory,
      'warehouse': Icons.warehouse,
      'solar_power': Icons.solar_power,
      'water_drop': Icons.water_drop,
      'electrical_services': Icons.electrical_services,
      'plumbing': Icons.plumbing,
      'hvac': Icons.hvac,
      'elevator': Icons.elevator,
      'stairs': Icons.stairs,
      'door_front': Icons.door_sliding,
      'window': Icons.window,
      'roofing': Icons.roofing,
      'foundation': Icons.foundation,
      'landscape': Icons.landscape,
      'park': Icons.park,
      'pool': Icons.pool,
      'garage': Icons.garage,
      'storage': Icons.storage,
      'archive': Icons.archive,
      'folder': Icons.folder,
      'description': Icons.description,
      'account_balance': Icons.account_balance,
      'gavel': Icons.gavel,
      'balance': Icons.balance,
      'trending_up': Icons.trending_up,
      'analytics': Icons.analytics,
      'assessment': Icons.assessment,
      'pie_chart': Icons.pie_chart,
      'bar_chart': Icons.bar_chart,
      'show_chart': Icons.show_chart,
      'timeline': Icons.timeline,
      'schedule': Icons.schedule,
      'event': Icons.event,
      'today': Icons.today,
      'date_range': Icons.date_range,
      'access_time': Icons.access_time,
      'timer': Icons.timer,
      'hourglass_empty': Icons.hourglass_empty,
      'update': Icons.update,
      'refresh': Icons.refresh,
      'sync': Icons.sync,
      'backup': Icons.backup,
      'cloud': Icons.cloud,
      'wifi': Icons.wifi,
      'signal_cellular_4_bar': Icons.signal_cellular_4_bar,
      'bluetooth': Icons.bluetooth,
      'usb': Icons.usb,
      'cable': Icons.cable,
      'power': Icons.power,
      'battery_full': Icons.battery_full,
      'flash_on': Icons.flash_on,
      'wb_sunny': Icons.wb_sunny,
      'wb_cloudy': Icons.wb_cloudy,
      'ac_unit': Icons.ac_unit,
      'thermostat': Icons.thermostat,
      'device_thermostat': Icons.device_thermostat,
      'opacity': Icons.opacity,
      'invert_colors': Icons.invert_colors,
      'palette': Icons.palette,
      'brush': Icons.brush,
      'format_paint': Icons.format_paint,
      'color_lens': Icons.color_lens,
      'image': Icons.image,
      'photo': Icons.photo,
      'camera': Icons.camera,
      'videocam': Icons.videocam,
      'mic': Icons.mic,
      'headset': Icons.headset,
      'speaker': Icons.speaker,
      'volume_up': Icons.volume_up,
      'radio': Icons.radio,
      'tv': Icons.tv,
      'monitor': Icons.monitor,
      'smartphone': Icons.smartphone,
      'tablet': Icons.tablet,
      'watch': Icons.watch,
      'keyboard': Icons.keyboard,
      'mouse': Icons.mouse,
      'gamepad': Icons.gamepad,
      'sports_esports': Icons.sports_esports,
      'casino': Icons.casino,
      'toys': Icons.toys,
      'child_friendly': Icons.child_friendly,
      'school': Icons.school,
      'class_': Icons.class_,
      'auto_stories': Icons.auto_stories,
      'quiz': Icons.quiz,
      'psychology': Icons.psychology,
      'biotech': Icons.biotech,
      'eco': Icons.eco,
      'recycling': Icons.recycling,
      'energy_savings_leaf': Icons.energy_savings_leaf,
      'bolt': Icons.bolt,
      'flash_auto': Icons.flash_auto,
      'offline_bolt': Icons.offline_bolt,
      'power_settings_new': Icons.power_settings_new,
      'settings_power': Icons.settings_power,
      'battery_charging_full': Icons.battery_charging_full,
      'electric_car': Icons.electric_car,
      'electric_bike': Icons.electric_bike,
      'electric_scooter': Icons.electric_scooter,
      'train': Icons.train,
      'subway': Icons.subway,
      'tram': Icons.tram,
      'bus_alert': Icons.bus_alert,
      'airport_shuttle': Icons.airport_shuttle,
      'flight': Icons.flight,
      'sailing': Icons.sailing,
      'directions_boat': Icons.directions_boat,
      'anchor': Icons.anchor,
      'waves': Icons.waves,
      'beach_access': Icons.beach_access,
      'umbrella': Icons.beach_access,
      'outdoor_grill': Icons.outdoor_grill,
      'fireplace': Icons.fireplace,
      'local_fire_department': Icons.local_fire_department,
      'whatshot': Icons.whatshot,
      'local_gas_station': Icons.local_gas_station,
      'propane_tank': Icons.propane_tank,
      'oil_barrel': Icons.oil_barrel,
      'water': Icons.water,
      'waves_outlined': Icons.waves,
      'pool_outlined': Icons.pool,
      'hot_tub': Icons.hot_tub,
      'spa': Icons.spa,
      'fitness': Icons.fitness_center,
      'self_improvement': Icons.self_improvement,
      'psychology_alt': Icons.psychology_alt,
      'favorite': Icons.favorite,
      'health_and_safety': Icons.health_and_safety,
      'healing': Icons.healing,
      'medication': Icons.medication,
      'vaccines': Icons.vaccines,
      'coronavirus': Icons.coronavirus,
      'masks': Icons.masks,
      'sanitizer': Icons.sanitizer,
      'clean_hands': Icons.clean_hands,
      'soap': Icons.soap,
      'shower': Icons.shower,
      'bathtub': Icons.bathtub,
      'bathroom': Icons.bathroom,
      'wc': Icons.wc,
      'accessible': Icons.accessible,
      'accessibility': Icons.accessibility,
      'elderly': Icons.elderly,
      'pregnant_woman': Icons.pregnant_woman,
      'child_care': Icons.child_care,
      'baby_changing_station': Icons.baby_changing_station,
      'stroller': Icons.stroller,
      'family_restroom': Icons.family_restroom,
      'escalator_warning': Icons.escalator_warning,
      'elevator_outlined': Icons.elevator,
      'stairs_outlined': Icons.stairs,
      'ramp_left': Icons.ramp_left,
      'ramp_right': Icons.ramp_right,
      'accessible_forward': Icons.accessible_forward,
      'wheelchair_pickup': Icons.wheelchair_pickup,
      'hearing': Icons.hearing,
      'hearing_disabled': Icons.hearing_disabled,
      'visibility': Icons.visibility,
      'visibility_off': Icons.visibility_off,
      'record_voice_over': Icons.record_voice_over,
      'voice_over_off': Icons.voice_over_off,
      'sign_language': Icons.sign_language,
      'translate': Icons.translate,
      'g_translate': Icons.g_translate,
      'language': Icons.language,
      'public': Icons.public,
      'travel_explore': Icons.travel_explore,
      'explore': Icons.explore,
      'map': Icons.map,
      'place': Icons.place,
      'location_on': Icons.location_on,
      'my_location': Icons.my_location,
      'gps_fixed': Icons.gps_fixed,
      'gps_not_fixed': Icons.gps_not_fixed,
      'gps_off': Icons.gps_off,
      'navigation': Icons.navigation,
      'near_me': Icons.near_me,
      'compass_calibration': Icons.compass_calibration,
      'explore_off': Icons.explore_off,
      'wrong_location': Icons.wrong_location,
      'not_listed_location': Icons.not_listed_location,
      'location_searching': Icons.location_searching,
      'location_disabled': Icons.location_disabled,
      'add_location': Icons.add_location,
      'edit_location': Icons.edit_location,
      'delete_location': Icons.delete,
      'pin_drop': Icons.pin_drop,
      'room': Icons.room,
      'meeting_room': Icons.meeting_room,
      'sensor_door': Icons.sensor_door,
      'door_sliding': Icons.door_sliding,
      'door_back': Icons.door_sliding,
      'sensor_window': Icons.sensor_window,
      'blinds': Icons.blinds,
      'curtains': Icons.curtains,
      'vertical_blinds': Icons.blinds,
      'horizontal_rule': Icons.horizontal_rule,
      'vertical_align_top': Icons.vertical_align_top,
      'vertical_align_bottom': Icons.vertical_align_bottom,
      'vertical_align_center': Icons.vertical_align_center,
      'horizontal_distribute': Icons.horizontal_distribute,
      'vertical_distribute': Icons.vertical_distribute,
      'format_align_left': Icons.format_align_left,
      'format_align_center': Icons.format_align_center,
      'format_align_right': Icons.format_align_right,
      'format_align_justify': Icons.format_align_justify,
      'text_fields': Icons.text_fields,
      'title': Icons.title,
      'subject': Icons.subject,
      'short_text': Icons.short_text,
      'notes': Icons.notes,
      'sticky_note_2': Icons.sticky_note_2,
      'post_add': Icons.post_add,
      'dynamic_form': Icons.dynamic_form,
      'ballot': Icons.ballot,
      'how_to_vote': Icons.how_to_vote,
      'poll': Icons.poll,
      'equalizer': Icons.equalizer,
      'graphic_eq': Icons.graphic_eq,
      'multiline_chart': Icons.multiline_chart,
      'candlestick_chart': Icons.candlestick_chart,
      'waterfall_chart': Icons.waterfall_chart,
      'stacked_bar_chart': Icons.stacked_bar_chart,
      'stacked_line_chart': Icons.stacked_line_chart,
      'area_chart': Icons.area_chart,
      'scatter_plot': Icons.scatter_plot,
      'bubble_chart': Icons.bubble_chart,
      'donut_large': Icons.donut_large,
      'donut_small': Icons.donut_small,
      'pie_chart_outline': Icons.pie_chart_outline,
      'insert_chart': Icons.insert_chart,
      'insert_chart_outlined': Icons.insert_chart_outlined,
      'query_stats': Icons.query_stats,
      'monitoring': Icons.monitor,
      'speed': Icons.speed,
      'dashboard': Icons.dashboard,
      'dashboard_customize': Icons.dashboard_customize,
      'widgets': Icons.widgets,
      'extension': Icons.extension,
      'apps': Icons.apps,
      'grid_view': Icons.grid_view,
      'view_module': Icons.view_module,
      'view_quilt': Icons.view_quilt,
      'view_stream': Icons.view_stream,
      'view_list': Icons.view_list,
      'view_agenda': Icons.view_agenda,
      'view_day': Icons.view_day,
      'view_week': Icons.view_week,
      'view_array': Icons.view_array,
      'view_column': Icons.view_column,
      'view_sidebar': Icons.view_sidebar,
      'view_headline': Icons.view_headline,
      'view_carousel': Icons.view_carousel,
      'view_comfy': Icons.view_comfy,
      'view_compact': Icons.view_compact,
      'density_medium': Icons.density_medium,
      'density_small': Icons.density_small,
      'density_large': Icons.density_large,
      'tune': Icons.tune,
      'filter_list': Icons.filter_list,
      'filter_alt': Icons.filter_alt,
      'sort': Icons.sort,
      'sort_by_alpha': Icons.sort_by_alpha,
      'search': Icons.search,
      'search_off': Icons.search_off,
      'find_in_page': Icons.find_in_page,
      'find_replace': Icons.find_replace,
      'zoom_in': Icons.zoom_in,
      'zoom_out': Icons.zoom_out,
      'zoom_out_map': Icons.zoom_out_map,
      'center_focus_strong': Icons.center_focus_strong,
      'center_focus_weak': Icons.center_focus_weak,
      'crop': Icons.crop,
      'crop_free': Icons.crop_free,
      'aspect_ratio': Icons.aspect_ratio,
      'straighten': Icons.straighten,
      'rotate_left': Icons.rotate_left,
      'rotate_right': Icons.rotate_right,
      'rotate_90_degrees_ccw': Icons.rotate_90_degrees_ccw,
      'flip': Icons.flip,
      'flip_camera_android': Icons.flip_camera_android,
      'flip_camera_ios': Icons.flip_camera_ios,
      'switch_camera': Icons.switch_camera,
      'camera_front': Icons.camera_front,
      'camera_rear': Icons.camera_rear,
      'camera_enhance': Icons.camera_enhance,
      'photo_camera': Icons.photo_camera,
      'photo_camera_back': Icons.photo_camera_back,
      'photo_camera_front': Icons.photo_camera_front,
      'photo_library': Icons.photo_library,
      'photo_album': Icons.photo_album,
      'photo_size_select_actual': Icons.photo_size_select_actual,
      'photo_size_select_large': Icons.photo_size_select_large,
      'photo_size_select_small': Icons.photo_size_select_small,
      'collections': Icons.collections,
      'collections_bookmark': Icons.collections_bookmark,
      'bookmark': Icons.bookmark,
      'bookmark_border': Icons.bookmark_border,
      'bookmarks': Icons.bookmarks,
      'book': Icons.book,
      'book_online': Icons.book_online,
      'chrome_reader_mode': Icons.chrome_reader_mode,
      'import_contacts': Icons.import_contacts,
      'contact_page': Icons.contact_page,
      'contact_phone': Icons.contact_phone,
      'contact_mail': Icons.contact_mail,
      'contacts': Icons.contacts,
      'recent_actors': Icons.recent_actors,
      'people': Icons.people,
      'people_alt': Icons.people_alt,
      'people_outline': Icons.people_outline,
      'person': Icons.person,
      'person_add': Icons.person_add,
      'person_remove': Icons.person_remove,
      'person_outline': Icons.person_outline,
      'account_circle': Icons.account_circle,
      'account_box': Icons.account_box,
      'badge': Icons.badge,
      'card_membership': Icons.card_membership,
      'credit_card': Icons.credit_card,
      'payment': Icons.payment,
      'account_balance_wallet': Icons.account_balance_wallet,
      'monetization_on': Icons.monetization_on,
      'money': Icons.money,
      'currency_exchange': Icons.currency_exchange,
      'euro': Icons.euro,
      'attach_money': Icons.attach_money,
      'dollar_sign': Icons.attach_money,
      'savings': Icons.savings,
      'piggy_bank': Icons.savings,
      'request_quote': Icons.request_quote,
      'receipt': Icons.receipt,
      'receipt_long': Icons.receipt_long,
      'point_of_sale': Icons.point_of_sale,
      'shopping_bag': Icons.shopping_bag,
      'shopping_basket': Icons.shopping_basket,
      'add_shopping_cart': Icons.add_shopping_cart,
      'remove_shopping_cart': Icons.remove_shopping_cart,
      'production_quantity_limits': Icons.production_quantity_limits,
      'inventory_2': Icons.inventory_2,
      'category': Icons.category,
      'label': Icons.label,
      'label_important': Icons.label_important,
      'local_offer': Icons.local_offer,
      'sell': Icons.sell,
      'new_releases': Icons.new_releases,
      'fiber_new': Icons.fiber_new,
      'update_disabled': Icons.update_disabled,
      'system_update': Icons.system_update,
      'system_update_alt': Icons.system_update_alt,
      'get_app': Icons.get_app,
      'file_download': Icons.file_download,
      'file_upload': Icons.file_upload,
      'cloud_download': Icons.cloud_download,
      'cloud_upload': Icons.cloud_upload,
      'cloud_done': Icons.cloud_done,
      'cloud_off': Icons.cloud_off,
      'cloud_queue': Icons.cloud_queue,
      'cloud_sync': Icons.cloud_sync,
      'folder_open': Icons.folder_open,
      'folder_shared': Icons.folder_shared,
      'folder_special': Icons.folder_special,
      'create_new_folder': Icons.create_new_folder,
      'snippet_folder': Icons.snippet_folder,
      'rule_folder': Icons.rule_folder,
      'source': Icons.source,
      'code': Icons.code,
      'html': Icons.html,
      'css': Icons.css,
      'javascript': Icons.javascript,
      'data_object': Icons.data_object,
      'data_array': Icons.data_array,
      'dataset': Icons.dataset,
      'table_view': Icons.table_view,
      'table_chart': Icons.table_chart,
      'table_rows': Icons.table_rows,
      'view_compact_alt': Icons.view_compact_alt,
      'splitscreen': Icons.splitscreen,
      'picture_in_picture': Icons.picture_in_picture,
      'picture_in_picture_alt': Icons.picture_in_picture_alt,
      'fullscreen': Icons.fullscreen,
      'fullscreen_exit': Icons.fullscreen_exit,
      'fit_screen': Icons.fit_screen,
      'open_in_full': Icons.open_in_full,
      'close_fullscreen': Icons.close_fullscreen,
      'aspect_ratio_outlined': Icons.aspect_ratio,
      'crop_landscape': Icons.crop_landscape,
      'crop_portrait': Icons.crop_portrait,
      'crop_square': Icons.crop_square,
      'crop_din': Icons.crop_din,
      'crop_16_9': Icons.crop_16_9,
      'crop_3_2': Icons.crop_3_2,
      'crop_5_4': Icons.crop_5_4,
      'crop_7_5': Icons.crop_7_5,
      'crop_original': Icons.crop_original,
      'straighten_outlined': Icons.straighten,
      'transform': Icons.transform,
      'auto_fix_high': Icons.auto_fix_high,
      'auto_fix_normal': Icons.auto_fix_normal,
      'auto_fix_off': Icons.auto_fix_off,
      'auto_awesome': Icons.auto_awesome,
      'auto_awesome_mosaic': Icons.auto_awesome_mosaic,
      'auto_awesome_motion': Icons.auto_awesome_motion,
      'motion_photos_on': Icons.motion_photos_on,
      'motion_photos_off': Icons.motion_photos_off,
      'motion_photos_auto': Icons.motion_photos_auto,
      'motion_photos_pause': Icons.motion_photos_pause,
      'slideshow': Icons.slideshow,
      'movie': Icons.movie,
      'movie_creation': Icons.movie_creation,
      'movie_filter': Icons.movie_filter,
      'video_collection': Icons.video_collection,
      'video_library': Icons.video_library,
      'video_settings': Icons.video_settings,
      'videocam_off': Icons.videocam_off,
      'video_call': Icons.video_call,
      'video_chat': Icons.video_chat,
      'duo': Icons.duo,
      'call': Icons.call,
      'call_end': Icons.call_end,
      'call_made': Icons.call_made,
      'call_received': Icons.call_received,
      'call_split': Icons.call_split,
      'call_merge': Icons.call_merge,
      'phone': Icons.phone,
      'phone_in_talk': Icons.phone_in_talk,
      'phone_callback': Icons.phone_callback,
      'phone_forwarded': Icons.phone_forwarded,
      'phone_missed': Icons.phone_missed,
      'phone_paused': Icons.phone_paused,
      'phonelink': Icons.phonelink,
      'phonelink_erase': Icons.phonelink_erase,
      'phonelink_lock': Icons.phonelink_lock,
      'phonelink_off': Icons.phonelink_off,
      'phonelink_ring': Icons.phonelink_ring,
      'phonelink_setup': Icons.phonelink_setup,
      'portable_wifi_off': Icons.portable_wifi_off,
      'ring_volume': Icons.ring_volume,
      'volume_down': Icons.volume_down,
      'volume_mute': Icons.volume_mute,
      'volume_off': Icons.volume_off,
      'hearing_impaired': Icons.hearing,
      'closed_caption': Icons.closed_caption,
      'closed_caption_disabled': Icons.closed_caption_disabled,
      'closed_caption_off': Icons.closed_caption_off,
      'subtitles': Icons.subtitles,
      'subtitles_off': Icons.subtitles_off,
      'surround_sound': Icons.surround_sound,
      'graphic_eq_outlined': Icons.graphic_eq,
      'equalizer_outlined': Icons.equalizer,
      'high_quality': Icons.high_quality,
      'hd': Icons.hd,
      'sd': Icons.sd,
      '4k': Icons.four_k,
      '4k_plus': Icons.four_k_plus,
      'fiber_dvr': Icons.fiber_dvr,
      'fiber_manual_record': Icons.fiber_manual_record,
      'fiber_pin': Icons.fiber_pin,
      'fiber_smart_record': Icons.fiber_smart_record,
      'forward_10': Icons.forward_10,
      'forward_30': Icons.forward_30,
      'forward_5': Icons.forward_5,
      'replay_10': Icons.replay_10,
      'replay_30': Icons.replay_30,
      'replay_5': Icons.replay_5,
      'replay': Icons.replay,
      'repeat': Icons.repeat,
      'repeat_on': Icons.repeat_on,
      'repeat_one': Icons.repeat_one,
      'repeat_one_on': Icons.repeat_one_on,
      'shuffle': Icons.shuffle,
      'shuffle_on': Icons.shuffle_on,
      'skip_next': Icons.skip_next,
      'skip_previous': Icons.skip_previous,
      'fast_forward': Icons.fast_forward,
      'fast_rewind': Icons.fast_rewind,
      'play_arrow': Icons.play_arrow,
      'play_circle': Icons.play_circle,
      'play_circle_filled': Icons.play_circle_filled,
      'play_circle_outline': Icons.play_circle_outline,
      'pause': Icons.pause,
      'pause_circle': Icons.pause_circle,
      'pause_circle_filled': Icons.pause_circle_filled,
      'pause_circle_outline': Icons.pause_circle_outline,
      'stop': Icons.stop,
      'stop_circle': Icons.stop_circle,
      'stop_circle_outlined': Icons.stop_circle_outlined,
      'not_started': Icons.not_started,
      'slow_motion_video': Icons.slow_motion_video,
      'speed_outlined': Icons.speed,
      'playlist_add': Icons.playlist_add,
      'playlist_add_check': Icons.playlist_add_check,
      'playlist_play': Icons.playlist_play,
      'playlist_remove': Icons.playlist_remove,
      'queue': Icons.queue,
      'queue_music': Icons.queue_music,
      'queue_play_next': Icons.queue_play_next,
      'add_to_queue': Icons.add_to_queue,
      'remove_from_queue': Icons.remove_from_queue,
      'library_add': Icons.library_add,
      'library_add_check': Icons.library_add_check,
      'library_music': Icons.library_music,
      'music_note': Icons.music_note,
      'music_off': Icons.music_off,
      'music_video': Icons.music_video,
      'album': Icons.album,
      'artist': Icons.person,
      'audiotrack': Icons.audiotrack,
      'headset_mic': Icons.headset_mic,
      'headset_off': Icons.headset_off,
      'headphones': Icons.headphones,
      'speaker_group': Icons.speaker_group,
      'speaker_phone': Icons.speaker_phone,
      'portable_wifi': Icons.wifi,
      'wifi_off': Icons.wifi_off,
      'wifi_lock': Icons.wifi_lock,
      'wifi_protected_setup': Icons.wifi_protected_setup,
      'wifi_tethering': Icons.wifi_tethering,
      'wifi_tethering_error': Icons.wifi_tethering_error,
      'wifi_tethering_off': Icons.wifi_tethering_off,
      'network_wifi': Icons.network_wifi,
      'network_wifi_1_bar': Icons.network_wifi_1_bar,
      'network_wifi_2_bar': Icons.network_wifi_2_bar,
      'network_wifi_3_bar': Icons.network_wifi_3_bar,
      'signal_wifi_0_bar': Icons.signal_wifi_0_bar,
      'signal_wifi_4_bar_lock': Icons.signal_wifi_4_bar_lock,
      'signal_wifi_bad': Icons.signal_wifi_bad,
      'signal_wifi_connected_no_internet_4': Icons.signal_wifi_connected_no_internet_4,
      'signal_wifi_off': Icons.signal_wifi_off,
      'signal_wifi_statusbar_4_bar': Icons.signal_wifi_statusbar_4_bar,
      'signal_wifi_statusbar_connected_no_internet_4': Icons.signal_wifi_statusbar_connected_no_internet_4,
      'signal_wifi_statusbar_null': Icons.signal_wifi_statusbar_null,
      'signal_cellular_0_bar': Icons.signal_cellular_0_bar,
      'signal_cellular_1_bar': Icons.signal_cellular_4_bar,
      'signal_cellular_2_bar': Icons.signal_cellular_4_bar,
      'signal_cellular_3_bar': Icons.signal_cellular_4_bar,
      'signal_cellular_alt': Icons.signal_cellular_alt,
      'signal_cellular_connected_no_internet_0_bar': Icons.signal_cellular_connected_no_internet_0_bar,
      'signal_cellular_connected_no_internet_4_bar': Icons.signal_cellular_connected_no_internet_4_bar,
      'signal_cellular_no_sim': Icons.signal_cellular_no_sim,
      'signal_cellular_nodata': Icons.signal_cellular_nodata,
      'signal_cellular_null': Icons.signal_cellular_null,
      'signal_cellular_off': Icons.signal_cellular_off,
      'network_cell': Icons.network_cell,
      'network_check': Icons.network_check,
      'network_locked': Icons.network_locked,
      'perm_scan_wifi': Icons.perm_scan_wifi,
      'rss_feed': Icons.rss_feed,
      'cast': Icons.cast,
      'cast_connected': Icons.cast_connected,
      'cast_for_education': Icons.cast_for_education,
      'screen_share': Icons.screen_share,
      'stop_screen_share': Icons.stop_screen_share,
      'present_to_all': Icons.present_to_all,
      'airplay': Icons.airplay,
      'devices': Icons.devices,
      'devices_other': Icons.devices_other,
      'device_hub': Icons.device_hub,
      'device_unknown': Icons.device_unknown,
      'developer_board': Icons.developer_board,
      'developer_board_off': Icons.developer_board_off,
      'memory': Icons.memory,
      'storage_device': Icons.storage,
      'sd_storage': Icons.sd_storage,
      'usb_off': Icons.usb_off,
      'bluetooth_audio': Icons.bluetooth_audio,
      'bluetooth_connected': Icons.bluetooth_connected,
      'bluetooth_disabled': Icons.bluetooth_disabled,
      'bluetooth_drive': Icons.bluetooth_drive,
      'bluetooth_searching': Icons.bluetooth_searching,
      'nfc': Icons.nfc,
      'tap_and_play': Icons.tap_and_play,
      'contactless': Icons.contactless,
      'qr_code': Icons.qr_code,
      'qr_code_2': Icons.qr_code_2,
      'qr_code_scanner': Icons.qr_code_scanner,
      'scanner': Icons.scanner,
      'document_scanner': Icons.document_scanner,
      'barcode_reader': Icons.barcode_reader,
      'confirmation_number': Icons.confirmation_number,
      'local_activity': Icons.local_activity,
      'event_available': Icons.event_available,
      'event_busy': Icons.event_busy,
      'event_note': Icons.event_note,
      'event_seat': Icons.event_seat,
      'folder_delete': Icons.delete,
      'create': Icons.create,
      'edit': Icons.edit,
      'edit_off': Icons.edit_off,
      'edit_note': Icons.edit_note,
      'edit_attributes': Icons.edit_attributes,
      'edit_location_alt': Icons.edit_location_alt,
      'save': Icons.save,
      'save_alt': Icons.save_alt,
      'save_as': Icons.save_as,
      'undo': Icons.undo,
      'redo': Icons.redo,
      'content_copy': Icons.content_copy,
      'content_cut': Icons.content_cut,
      'content_paste': Icons.content_paste,
      'content_paste_go': Icons.content_paste_go,
      'content_paste_off': Icons.content_paste_off,
      'content_paste_search': Icons.content_paste_search,
      'select_all': Icons.select_all,
      'deselect': Icons.deselect,
      'clear': Icons.clear,
      'clear_all': Icons.clear_all,
      'delete_forever': Icons.delete_forever,
      'delete_outline': Icons.delete_outline,
      'delete_sweep': Icons.delete_sweep,
      'restore': Icons.restore,
      'restore_from_trash': Icons.restore_from_trash,
      'restore_page': Icons.restore_page,
      'unarchive': Icons.unarchive,
      'inbox': Icons.inbox,
      'outbox': Icons.outbox,
      'drafts': Icons.drafts,
      'send': Icons.send,
      'send_and_archive': Icons.send_and_archive,
      'forward': Icons.forward,
      'reply': Icons.reply,
      'reply_all': Icons.reply_all,
      'mark_as_unread': Icons.mark_as_unread,
      'mark_chat_read': Icons.mark_chat_read,
      'mark_chat_unread': Icons.mark_chat_unread,
      'mark_email_read': Icons.mark_email_read,
      'mark_email_unread': Icons.mark_email_unread,
      'markunread': Icons.markunread,
      'markunread_mailbox': Icons.markunread_mailbox,
      'email': Icons.email,
      'alternate_email': Icons.alternate_email,
      'mail': Icons.mail,
      'mail_lock': Icons.mail_lock,
      'mail_outline': Icons.mail_outline,
      'unsubscribe': Icons.unsubscribe,
      'subscriptions': Icons.subscriptions,
      'rss_feed_outlined': Icons.rss_feed,
      'announcement': Icons.announcement,
      'campaign': Icons.campaign,
      'notifications': Icons.notifications,
      'notifications_active': Icons.notifications_active,
      'notifications_none': Icons.notifications_none,
      'notifications_off': Icons.notifications_off,
      'notifications_on': Icons.notifications_on,
      'notifications_paused': Icons.notifications_paused,
      'notification_add': Icons.notification_add,
      'notification_important': Icons.notification_important,
      'circle_notifications': Icons.circle_notifications,
      'do_not_disturb': Icons.do_not_disturb,
      'do_not_disturb_alt': Icons.do_not_disturb_alt,
      'do_not_disturb_off': Icons.do_not_disturb_off,
      'do_not_disturb_on': Icons.do_not_disturb_on,
      'priority_high': Icons.priority_high,
      'low_priority': Icons.low_priority,
      'flag': Icons.flag,
      'flag_circle': Icons.flag_circle,
      'outlined_flag': Icons.outlined_flag,
      'report': Icons.report,
      'report_gmailerrorred': Icons.report_gmailerrorred,
      'report_off': Icons.report_off,
      'report_problem': Icons.report_problem,
      'warning': Icons.warning,
      'warning_amber': Icons.warning_amber,
      'error': Icons.error,
      'error_outline': Icons.error_outline,
      'info': Icons.info,
      'info_outline': Icons.info_outline,
      'help': Icons.help,
      'help_center': Icons.help_center,
      'help_outline': Icons.help_outline,
      'live_help': Icons.live_help,
      'contact_support': Icons.contact_support,
      'support': Icons.support,
      'support_agent': Icons.support_agent,
      'feedback': Icons.feedback,
      'bug_report': Icons.bug_report,
      'rate_review': Icons.rate_review,
      'thumb_up': Icons.thumb_up,
      'thumb_down': Icons.thumb_down,
      'thumb_up_off_alt': Icons.thumb_up_off_alt,
      'thumb_down_off_alt': Icons.thumb_down_off_alt,
      'thumbs_up_down': Icons.thumbs_up_down,
      'sentiment_very_satisfied': Icons.sentiment_very_satisfied,
      'sentiment_satisfied': Icons.sentiment_satisfied,
      'sentiment_neutral': Icons.sentiment_neutral,
      'sentiment_dissatisfied': Icons.sentiment_dissatisfied,
      'sentiment_very_dissatisfied': Icons.sentiment_very_dissatisfied,
      'mood': Icons.mood,
      'mood_bad': Icons.mood_bad,
      'sick': Icons.sick,
      'coronavirus_outlined': Icons.coronavirus,
      'masks_outlined': Icons.masks,
      'elderly_outlined': Icons.elderly,
      'groups': Icons.groups,
      'group': Icons.group,
      'group_add': Icons.group_add,
      'group_off': Icons.group_off,
      'group_remove': Icons.group_remove,
      'group_work': Icons.group_work,
      'supervisor_account': Icons.supervisor_account,
      'admin_panel_settings': Icons.admin_panel_settings,
      'manage_accounts': Icons.manage_accounts,
      'switch_account': Icons.switch_account,
      'logout': Icons.logout,
      'login': Icons.login,
      'lock': Icons.lock,
      'lock_open': Icons.lock_open,
      'lock_outline': Icons.lock_outline,
      'lock_clock': Icons.lock_clock,
      'lock_person': Icons.lock_person,
      'lock_reset': Icons.lock_reset,
      'no_encryption': Icons.no_encryption,
      'enhanced_encryption': Icons.enhanced_encryption,
      'vpn_key': Icons.vpn_key,
      'vpn_key_off': Icons.vpn_key_off,
      'vpn_lock': Icons.vpn_lock,
      'key': Icons.key,
      'key_off': Icons.key_off,
      'password': Icons.password,
      'pin': Icons.pin,
      'fingerprint': Icons.fingerprint,
      'face': Icons.face,
      'face_retouching_natural': Icons.face_retouching_natural,
      'face_retouching_off': Icons.face_retouching_off,
      'verified': Icons.verified,
      'verified_user': Icons.verified_user,
      'shield': Icons.shield,
      'privacy_tip': Icons.privacy_tip,
      'policy': Icons.policy,
      'gpp_good': Icons.gpp_good,
      'gpp_bad': Icons.gpp_bad,
      'gpp_maybe': Icons.gpp_maybe,
      'https': Icons.https,
      'http': Icons.http,
      'public_off': Icons.public_off,
      'language_outlined': Icons.language,
      'translate_outlined': Icons.translate,
      'closed_caption_outlined': Icons.closed_caption,
      'subtitles_outlined': Icons.subtitles,
      'record_voice_over_outlined': Icons.record_voice_over,
      'hearing_outlined': Icons.hearing,
      'visibility_outlined': Icons.visibility,
      'accessible_outlined': Icons.accessible,
      'wheelchair_pickup_outlined': Icons.wheelchair_pickup,
      'elderly_woman': Icons.elderly,
      'pregnant_woman_outlined': Icons.pregnant_woman,
      'child_care_outlined': Icons.child_care,
      'baby_changing_station_outlined': Icons.baby_changing_station,
      'family_restroom_outlined': Icons.family_restroom,
      'wc_outlined': Icons.wc,
      'bathroom_outlined': Icons.bathroom,
      'shower_outlined': Icons.shower,
      'bathtub_outlined': Icons.bathtub,
      'hot_tub_outlined': Icons.hot_tub,
      'pool_outlined_alt': Icons.pool,
      'spa_outlined': Icons.spa,
      'fitness_center_outlined': Icons.fitness_center,
      'self_improvement_outlined': Icons.self_improvement,
      'sports_outlined': Icons.sports,
      'sports_basketball': Icons.sports_basketball,
      'sports_baseball': Icons.sports_baseball,
      'sports_cricket': Icons.sports_cricket,
      'sports_football': Icons.sports_football,
      'sports_golf': Icons.sports_golf,
      'sports_handball': Icons.sports_handball,
      'sports_hockey': Icons.sports_hockey,
      'sports_kabaddi': Icons.sports_kabaddi,
      'sports_mma': Icons.sports_mma,
      'sports_motorsports': Icons.sports_motorsports,
      'sports_rugby': Icons.sports_rugby,
      'sports_soccer': Icons.sports_soccer,
      'sports_tennis': Icons.sports_tennis,
      'sports_volleyball': Icons.sports_volleyball,
      'pool_table': Icons.pool,
      'casino_outlined': Icons.casino,
      'toys_outlined': Icons.toys,
      'games': Icons.games,
      'videogame_asset': Icons.videogame_asset,
      'videogame_asset_off': Icons.videogame_asset_off,
      'sports_esports_outlined': Icons.sports_esports,
      'extension_outlined': Icons.extension,
      'puzzle': Icons.extension,
      'smart_toy': Icons.smart_toy,
      'precision_manufacturing_outlined': Icons.precision_manufacturing,
      'engineering': Icons.engineering,
      'construction_outlined': Icons.construction,
      'handyman_outlined': Icons.handyman,
      'build_outlined': Icons.build,
      'build_circle': Icons.build_circle,
      'home_repair_service': Icons.home_repair_service,
      'plumbing_outlined': Icons.plumbing,
      'electrical_services_outlined': Icons.electrical_services,
      'hvac_outlined': Icons.hvac,
      'carpenter': Icons.carpenter,
      'architecture': Icons.architecture,
      'design_services': Icons.design_services,
      'draw': Icons.draw,
      'gesture': Icons.gesture,
      'touch_app': Icons.touch_app,
      'pan_tool': Icons.pan_tool,
      'back_hand': Icons.back_hand,
      'front_hand': Icons.front_hand,
      'waving_hand': Icons.waving_hand,
      'handshake': Icons.handshake,
      'volunteer_activism': Icons.volunteer_activism,
      'favorite_outlined': Icons.favorite_border,
      'favorite_border': Icons.favorite_border,
      'heart_broken': Icons.heart_broken,
      'healing_outlined': Icons.healing,
      'health_and_safety_outlined': Icons.health_and_safety,
      'medical_services_outlined': Icons.medical_services,
      'medication_outlined': Icons.medication,
      'vaccines_outlined': Icons.vaccines,
      'biotech_outlined': Icons.biotech,
      'science_outlined': Icons.science,
      'psychology_outlined': Icons.psychology,
      'school_outlined': Icons.school,
      'class_outlined': Icons.class_,
      'auto_stories_outlined': Icons.auto_stories,
      'menu_book_outlined': Icons.menu_book,
      'library_books_outlined': Icons.library_books,
      'quiz_outlined': Icons.quiz,
      'assignment': Icons.assignment,
      'assignment_ind': Icons.assignment_ind,
      'assignment_late': Icons.assignment_late,
      'assignment_return': Icons.assignment_return,
      'assignment_returned': Icons.assignment_returned,
      'assignment_turned_in': Icons.assignment_turned_in,
      'task': Icons.task,
      'task_alt': Icons.task_alt,
      'checklist': Icons.checklist,
      'checklist_rtl': Icons.checklist_rtl,
      'check': Icons.check,
      'check_circle': Icons.check_circle,
      'check_circle_outline': Icons.check_circle_outline,
      'check_box': Icons.check_box,
      'check_box_outline_blank': Icons.check_box_outline_blank,
      'indeterminate_check_box': Icons.indeterminate_check_box,
      'radio_button_checked': Icons.radio_button_checked,
      'radio_button_unchecked': Icons.radio_button_unchecked,
      'toggle_on': Icons.toggle_on,
      'toggle_off': Icons.toggle_off,
      'star': Icons.star,
      'star_border': Icons.star_border,
      'star_half': Icons.star_half,
      'star_outline': Icons.star_outline,
      'star_purple500': Icons.star_purple500,
      'star_rate': Icons.star_rate,
      'grade': Icons.grade,
      'workspace_premium': Icons.workspace_premium,
      'military_tech': Icons.military_tech,
      'emoji_events': Icons.emoji_events,
      'celebration': Icons.celebration,
      'party_mode': Icons.party_mode,
      'cake': Icons.cake,
      'card_giftcard': Icons.card_giftcard,
      'redeem': Icons.redeem,
      'loyalty': Icons.loyalty,
      'local_mall': Icons.local_mall,
      'local_grocery_store': Icons.local_grocery_store,
      'local_convenience_store': Icons.local_convenience_store,
      'local_pharmacy': Icons.local_pharmacy,
      'local_hospital': Icons.local_hospital,
      'local_hotel': Icons.local_hotel,
      'local_laundry_service': Icons.local_laundry_service,
      'local_library': Icons.local_library,
      'local_movies': Icons.local_movies,
      'local_parking': Icons.local_parking,
      'local_police': Icons.local_police,
      'local_post_office': Icons.local_post_office,
      'local_printshop': Icons.local_printshop,
      'local_restaurant': Icons.local_restaurant,
      'local_see': Icons.local_see,
      'local_shipping_outlined': Icons.local_shipping,
      'local_taxi': Icons.local_taxi,
      'ev_station': Icons.ev_station,
      'charging_station': Icons.charging_station,
      'battery_charging_full_outlined': Icons.battery_charging_full,
      'electric_car_outlined': Icons.electric_car,
      'electric_bike_outlined': Icons.electric_bike,
      'electric_scooter_outlined': Icons.electric_scooter,
      'electric_moped': Icons.electric_moped,
      'electric_rickshaw': Icons.electric_rickshaw,
      'solar_power_outlined': Icons.solar_power,
      'wind_power': Icons.wind_power,
      'water_drop_outlined': Icons.water_drop,
      'oil_barrel_outlined': Icons.oil_barrel,
      'propane_tank_outlined': Icons.propane_tank,
      'local_gas_station_outlined': Icons.local_gas_station,
      'local_fire_department_outlined': Icons.local_fire_department,
      'fireplace_outlined': Icons.fireplace,
      'outdoor_grill_outlined': Icons.outdoor_grill,
      'whatshot_outlined': Icons.whatshot,
      'eco_outlined': Icons.eco,
      'recycling_outlined': Icons.recycling,
      'energy_savings_leaf_outlined': Icons.energy_savings_leaf,
      'nature': Icons.nature,
      'nature_people': Icons.nature_people,
      'park_outlined': Icons.park,
      'landscape_outlined': Icons.landscape,
      'forest': Icons.forest,
      'grass': Icons.grass,
      'local_florist': Icons.local_florist,
      'yard': Icons.yard,
      'agriculture_outlined': Icons.agriculture,
      'emoji_nature': Icons.emoji_nature,
      'pets': Icons.pets,
      'cruelty_free': Icons.cruelty_free,
    };

    return iconMap[iconName] ?? Icons.category;
  }

  /// Formats the standard rate for display with percentage symbol
  String get formattedRate {
    return '${standardRate.toStringAsFixed(1)}%';
  }

  /// Formats the duration range for display
  String get durationRange {
    if (minDuration == maxDuration) {
      return '$minDuration ans';
    }
    return '$minDuration-$maxDuration ans';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is AssetPreset &&
        other.id == id &&
        other.name == name &&
        other.category == category &&
        other.standardRate == standardRate &&
        other.defaultDuration == defaultDuration &&
        other.defaultMode == defaultMode &&
        other.residualValuePercent == residualValuePercent &&
        other.description == description &&
        other.iconName == iconName &&
        _listEquals(other.examples, examples) &&
        other.minDuration == minDuration &&
        other.maxDuration == maxDuration &&
        _listEquals(other.allowedModes, allowedModes) &&
        other.complianceNote == complianceNote;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      category,
      standardRate,
      defaultDuration,
      defaultMode,
      residualValuePercent,
      description,
      iconName,
      Object.hashAll(examples),
      minDuration,
      maxDuration,
      Object.hashAll(allowedModes),
      complianceNote,
    );
  }

  @override
  String toString() {
    return 'AssetPreset(id: $id, name: $name, category: $category, '
        'standardRate: $standardRate, defaultDuration: $defaultDuration, '
        'defaultMode: $defaultMode, residualValuePercent: $residualValuePercent, '
        'description: $description, iconName: $iconName, examples: $examples, '
        'minDuration: $minDuration, maxDuration: $maxDuration, '
        'allowedModes: $allowedModes, complianceNote: $complianceNote)';
  }

  /// Helper method to compare lists for equality
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    if (identical(a, b)) return true;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}