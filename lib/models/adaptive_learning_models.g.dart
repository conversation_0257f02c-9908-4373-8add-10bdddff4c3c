// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'adaptive_learning_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserPerformanceAdapter extends TypeAdapter<UserPerformance> {
  @override
  final int typeId = 14;

  @override
  UserPerformance read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserPerformance(
      categoryName: fields[0] as String,
      levelName: fields[1] as String,
      questionsAnswered: fields[2] as int,
      correctAnswers: fields[3] as int,
      averageTimePerQuestion: fields[4] as double,
      weakTopics: (fields[5] as List).cast<String>(),
      strongTopics: (fields[6] as List).cast<String>(),
      lastUpdated: fields[7] as DateTime,
      streakCount: fields[8] as int,
      topicAccuracy: (fields[9] as Map).cast<String, int>(),
    );
  }

  @override
  void write(BinaryWriter writer, UserPerformance obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.categoryName)
      ..writeByte(1)
      ..write(obj.levelName)
      ..writeByte(2)
      ..write(obj.questionsAnswered)
      ..writeByte(3)
      ..write(obj.correctAnswers)
      ..writeByte(4)
      ..write(obj.averageTimePerQuestion)
      ..writeByte(5)
      ..write(obj.weakTopics)
      ..writeByte(6)
      ..write(obj.strongTopics)
      ..writeByte(7)
      ..write(obj.lastUpdated)
      ..writeByte(8)
      ..write(obj.streakCount)
      ..writeByte(9)
      ..write(obj.topicAccuracy);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserPerformanceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LearningPathAdapter extends TypeAdapter<LearningPath> {
  @override
  final int typeId = 15;

  @override
  LearningPath read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LearningPath(
      id: fields[0] as String,
      categoryName: fields[1] as String,
      recommendedTopics: (fields[2] as List).cast<String>(),
      completedTopics: (fields[3] as List).cast<String>(),
      currentDifficulty: fields[4] as String,
      createdAt: fields[5] as DateTime,
      lastAccessed: fields[6] as DateTime,
      targetAccuracy: fields[7] as int,
      isActive: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, LearningPath obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.categoryName)
      ..writeByte(2)
      ..write(obj.recommendedTopics)
      ..writeByte(3)
      ..write(obj.completedTopics)
      ..writeByte(4)
      ..write(obj.currentDifficulty)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.lastAccessed)
      ..writeByte(7)
      ..write(obj.targetAccuracy)
      ..writeByte(8)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LearningPathAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WeaknessAreaAdapter extends TypeAdapter<WeaknessArea> {
  @override
  final int typeId = 16;

  @override
  WeaknessArea read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WeaknessArea(
      topicName: fields[0] as String,
      categoryName: fields[1] as String,
      accuracyRate: fields[2] as double,
      totalAttempts: fields[3] as int,
      commonMistakes: (fields[4] as List).cast<String>(),
      identifiedAt: fields[5] as DateTime,
      needsReview: fields[6] as bool,
      priority: fields[7] as int,
    );
  }

  @override
  void write(BinaryWriter writer, WeaknessArea obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.topicName)
      ..writeByte(1)
      ..write(obj.categoryName)
      ..writeByte(2)
      ..write(obj.accuracyRate)
      ..writeByte(3)
      ..write(obj.totalAttempts)
      ..writeByte(4)
      ..write(obj.commonMistakes)
      ..writeByte(5)
      ..write(obj.identifiedAt)
      ..writeByte(6)
      ..write(obj.needsReview)
      ..writeByte(7)
      ..write(obj.priority);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WeaknessAreaAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AchievementAdapter extends TypeAdapter<Achievement> {
  @override
  final int typeId = 13;

  @override
  Achievement read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Achievement(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String,
      iconName: fields[3] as String,
      categoryName: fields[4] as String,
      unlockedAt: fields[5] as DateTime,
      isUnlocked: fields[6] as bool,
      points: fields[7] as int,
      rarity: fields[8] as String,
      criteria: (fields[9] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, Achievement obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.iconName)
      ..writeByte(4)
      ..write(obj.categoryName)
      ..writeByte(5)
      ..write(obj.unlockedAt)
      ..writeByte(6)
      ..write(obj.isUnlocked)
      ..writeByte(7)
      ..write(obj.points)
      ..writeByte(8)
      ..write(obj.rarity)
      ..writeByte(9)
      ..write(obj.criteria);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AchievementAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class StudySessionAdapter extends TypeAdapter<StudySession> {
  @override
  final int typeId = 14;

  @override
  StudySession read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return StudySession(
      id: fields[0] as String,
      categoryName: fields[1] as String,
      levelName: fields[2] as String,
      startTime: fields[3] as DateTime,
      endTime: fields[4] as DateTime?,
      questionsAttempted: fields[5] as int,
      correctAnswers: fields[6] as int,
      totalTime: fields[7] as Duration,
      topicsStudied: (fields[8] as List).cast<String>(),
      isCompleted: fields[9] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, StudySession obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.categoryName)
      ..writeByte(2)
      ..write(obj.levelName)
      ..writeByte(3)
      ..write(obj.startTime)
      ..writeByte(4)
      ..write(obj.endTime)
      ..writeByte(5)
      ..write(obj.questionsAttempted)
      ..writeByte(6)
      ..write(obj.correctAnswers)
      ..writeByte(7)
      ..write(obj.totalTime)
      ..writeByte(8)
      ..write(obj.topicsStudied)
      ..writeByte(9)
      ..write(obj.isCompleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StudySessionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
