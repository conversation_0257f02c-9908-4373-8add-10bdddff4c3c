import 'package:flutter/foundation.dart';

@immutable
class PerformanceAnalytics {
  final double overallAccuracy;
  final Map<String, double> topicPerformance;
  final Map<int, double> difficultyProgression;
  final List<WeeklyStats> weeklyProgress;
  final StreakData streakData;
  final TimeAnalytics timeAnalytics;
  final List<TopicWeakness> weakestTopics;
  final List<String> improvementAreas;
  final LearningVelocity learningVelocity;
  final DateTime lastUpdated;

  const PerformanceAnalytics({
    this.overallAccuracy = 0.0,
    this.topicPerformance = const {},
    this.difficultyProgression = const {},
    this.weeklyProgress = const [],
    this.streakData = const StreakData(),
    this.timeAnalytics = const TimeAnalytics(),
    this.weakestTopics = const [],
    this.improvementAreas = const [],
    this.learningVelocity = const LearningVelocity(),
    required this.lastUpdated,
  });

  // Calculate accuracy trends over time periods
  List<TrendPoint> calculateTrends(int weeks) {
    List<TrendPoint> trends = [];
    
    final now = DateTime.now();
    for (int i = weeks - 1; i >= 0; i--) {
      final weekStart = now.subtract(Duration(days: i * 7));
      final weekEnd = weekStart.add(Duration(days: 6));
      
      final weekStats = weeklyProgress.where((stats) =>
          stats.weekStart.isAfter(weekStart.subtract(Duration(days: 1))) &&
          stats.weekStart.isBefore(weekEnd.add(Duration(days: 1)))).toList();
      
      if (weekStats.isNotEmpty) {
        final avgAccuracy = weekStats.map((s) => s.accuracy).reduce((a, b) => a + b) / weekStats.length;
        trends.add(TrendPoint(
          date: weekStart,
          value: avgAccuracy,
          label: 'Semaine ${i + 1}',
        ));
      }
    }
    
    return trends;
  }

  // Identify learning patterns based on historical data
  List<LearningPattern> identifyPatterns() {
    List<LearningPattern> patterns = [];

    // Pattern 1: Time of day performance
    if (timeAnalytics.hourlyPerformance.isNotEmpty) {
      final bestHour = timeAnalytics.hourlyPerformance.entries
          .reduce((a, b) => a.value > b.value ? a : b);
      
      patterns.add(LearningPattern(
        type: PatternType.timeOfDay,
        description: 'Meilleure performance à ${bestHour.key}h (${(bestHour.value * 100).toStringAsFixed(1)}%)',
        confidence: 0.8,
        recommendation: 'Planifiez vos sessions d\'étude principales autour de ${bestHour.key}h',
      ));
    }

    // Pattern 2: Difficulty progression
    if (difficultyProgression.length >= 3) {
      final difficulties = difficultyProgression.entries.toList()..sort((a, b) => a.key.compareTo(b.key));
      bool isProgressing = true;
      
      for (int i = 1; i < difficulties.length; i++) {
        if (difficulties[i].value < difficulties[i-1].value) {
          isProgressing = false;
          break;
        }
      }
      
      if (isProgressing) {
        patterns.add(LearningPattern(
          type: PatternType.difficultyProgression,
          description: 'Progression constante dans les niveaux de difficulté',
          confidence: 0.9,
          recommendation: 'Continuez à augmenter progressivement la difficulté',
        ));
      }
    }

    // Pattern 3: Topic mastery
    final masteredTopics = topicPerformance.entries
        .where((entry) => entry.value >= 0.85)
        .length;
    
    if (masteredTopics > 0) {
      patterns.add(LearningPattern(
        type: PatternType.topicMastery,
        description: '$masteredTopics sujet(s) maîtrisé(s) (>85% de réussite)',
        confidence: 0.95,
        recommendation: 'Concentrez-vous sur les sujets non maîtrisés pour un apprentissage équilibré',
      ));
    }

    return patterns;
  }

  // Generate personalized recommendations based on analytics
  List<String> generateRecommendations() {
    List<String> recommendations = [];

    // Recommendation based on overall accuracy
    if (overallAccuracy < 0.6) {
      recommendations.add('Révisez les concepts de base avant de passer aux questions avancées');
    } else if (overallAccuracy > 0.85) {
      recommendations.add('Excellent niveau ! Essayez des questions plus difficiles pour vous challenger');
    }

    // Recommendations based on weak topics
    if (weakestTopics.isNotEmpty) {
      final weakestTopic = weakestTopics.first;
      recommendations.add('Concentrez-vous sur ${weakestTopic.topic} (${(weakestTopic.accuracy * 100).toStringAsFixed(1)}% de réussite)');
    }

    // Recommendation based on learning velocity  
    if (learningVelocity.questionsPerDay < 5) {
      recommendations.add('Augmentez votre rythme d\'apprentissage à au moins 5 questions par jour');
    }

    // Recommendation based on streak data
    if (streakData.currentStreak == 0 && streakData.bestStreak > 0) {
      recommendations.add('Reprenez votre série ! Votre meilleur record était de ${streakData.bestStreak} jours');
    }

    // Recommendation based on time analytics
    if (timeAnalytics.averageTimePerQuestion > 60) {
      recommendations.add('Travaillez sur la vitesse de réponse pour améliorer votre efficacité');
    }

    return recommendations;
  }

  // Get progress summary for dashboard display
  ProgressSummary getProgressSummary() {
    return ProgressSummary(
      totalQuestionsAnswered: weeklyProgress.fold(0, (sum, week) => sum + week.questionsAnswered),
      averageAccuracy: overallAccuracy,
      topicsStudied: topicPerformance.length,
      currentStreak: streakData.currentStreak,
      improvementRate: _calculateImprovementRate(),
      weakAreasCount: weakestTopics.length,
      masteredTopicsCount: topicPerformance.values.where((accuracy) => accuracy >= 0.85).length,
    );
  }

  // Calculate improvement rate based on recent performance
  double _calculateImprovementRate() {
    if (weeklyProgress.length < 2) return 0.0;
    
    final recentWeeks = weeklyProgress.take(4).toList();
    if (recentWeeks.length < 2) return 0.0;
    
    final oldAccuracy = recentWeeks.last.accuracy;
    final newAccuracy = recentWeeks.first.accuracy;
    
    return newAccuracy - oldAccuracy;
  }

  // Create copy with updated values
  PerformanceAnalytics copyWith({
    double? overallAccuracy,
    Map<String, double>? topicPerformance,
    Map<int, double>? difficultyProgression,
    List<WeeklyStats>? weeklyProgress,
    StreakData? streakData,
    TimeAnalytics? timeAnalytics,
    List<TopicWeakness>? weakestTopics,
    List<String>? improvementAreas,
    LearningVelocity? learningVelocity,
    DateTime? lastUpdated,
  }) {
    return PerformanceAnalytics(
      overallAccuracy: overallAccuracy ?? this.overallAccuracy,
      topicPerformance: topicPerformance ?? this.topicPerformance,
      difficultyProgression: difficultyProgression ?? this.difficultyProgression,
      weeklyProgress: weeklyProgress ?? this.weeklyProgress,
      streakData: streakData ?? this.streakData,
      timeAnalytics: timeAnalytics ?? this.timeAnalytics,
      weakestTopics: weakestTopics ?? this.weakestTopics,
      improvementAreas: improvementAreas ?? this.improvementAreas,
      learningVelocity: learningVelocity ?? this.learningVelocity,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

@immutable
class WeeklyStats {
  final DateTime weekStart;
  final int questionsAnswered;
  final double accuracy;
  final int timeSpent; // minutes
  final Map<String, int> topicDistribution;

  const WeeklyStats({
    required this.weekStart,
    this.questionsAnswered = 0,
    this.accuracy = 0.0,
    this.timeSpent = 0,
    this.topicDistribution = const {},
  });

  WeeklyStats copyWith({
    DateTime? weekStart,
    int? questionsAnswered,
    double? accuracy,
    int? timeSpent,
    Map<String, int>? topicDistribution,
  }) {
    return WeeklyStats(
      weekStart: weekStart ?? this.weekStart,
      questionsAnswered: questionsAnswered ?? this.questionsAnswered,
      accuracy: accuracy ?? this.accuracy,
      timeSpent: timeSpent ?? this.timeSpent,
      topicDistribution: topicDistribution ?? this.topicDistribution,
    );
  }
}

@immutable
class StreakData {
  final int currentStreak;
  final int bestStreak;
  final DateTime? lastActivity;
  final List<DateTime> streakDates;

  const StreakData({
    this.currentStreak = 0,
    this.bestStreak = 0,
    this.lastActivity,
    this.streakDates = const [],
  });

  StreakData copyWith({
    int? currentStreak,
    int? bestStreak,
    DateTime? lastActivity,
    List<DateTime>? streakDates,
  }) {
    return StreakData(
      currentStreak: currentStreak ?? this.currentStreak,
      bestStreak: bestStreak ?? this.bestStreak,
      lastActivity: lastActivity ?? this.lastActivity,
      streakDates: streakDates ?? this.streakDates,
    );
  }
}

@immutable
class TimeAnalytics {
  final double averageTimePerQuestion; // seconds
  final Map<int, double> difficultyTimeMap; // difficulty -> average time
  final Map<int, double> hourlyPerformance; // hour -> accuracy
  final int totalTimeSpent; // minutes

  const TimeAnalytics({
    this.averageTimePerQuestion = 0.0,
    this.difficultyTimeMap = const {},
    this.hourlyPerformance = const {},
    this.totalTimeSpent = 0,
  });

  TimeAnalytics copyWith({
    double? averageTimePerQuestion,
    Map<int, double>? difficultyTimeMap,
    Map<int, double>? hourlyPerformance,
    int? totalTimeSpent,
  }) {
    return TimeAnalytics(
      averageTimePerQuestion: averageTimePerQuestion ?? this.averageTimePerQuestion,
      difficultyTimeMap: difficultyTimeMap ?? this.difficultyTimeMap,
      hourlyPerformance: hourlyPerformance ?? this.hourlyPerformance,
      totalTimeSpent: totalTimeSpent ?? this.totalTimeSpent,
    );
  }
}

@immutable
class TopicWeakness {
  final String topic;
  final double accuracy;
  final int attemptCount;
  final List<String> commonMistakes;
  final double improvementTrend; // positive = improving, negative = declining

  const TopicWeakness({
    required this.topic,
    required this.accuracy,
    required this.attemptCount,
    this.commonMistakes = const [],
    this.improvementTrend = 0.0,
  });

  TopicWeakness copyWith({
    String? topic,
    double? accuracy,
    int? attemptCount,
    List<String>? commonMistakes,
    double? improvementTrend,
  }) {
    return TopicWeakness(
      topic: topic ?? this.topic,
      accuracy: accuracy ?? this.accuracy,
      attemptCount: attemptCount ?? this.attemptCount,
      commonMistakes: commonMistakes ?? this.commonMistakes,
      improvementTrend: improvementTrend ?? this.improvementTrend,
    );
  }
}

@immutable
class LearningVelocity {
  final double questionsPerDay;
  final double questionsPerWeek;
  final double accuracyTrend; // change in accuracy over time
  final int activeDays; // days with quiz activity

  const LearningVelocity({
    this.questionsPerDay = 0.0,
    this.questionsPerWeek = 0.0,
    this.accuracyTrend = 0.0,
    this.activeDays = 0,
  });

  LearningVelocity copyWith({
    double? questionsPerDay,
    double? questionsPerWeek,
    double? accuracyTrend,
    int? activeDays,
  }) {
    return LearningVelocity(
      questionsPerDay: questionsPerDay ?? this.questionsPerDay,
      questionsPerWeek: questionsPerWeek ?? this.questionsPerWeek,
      accuracyTrend: accuracyTrend ?? this.accuracyTrend,
      activeDays: activeDays ?? this.activeDays,
    );
  }
}

@immutable
class LearningPattern {
  final PatternType type;
  final String description;
  final double confidence; // 0.0 to 1.0
  final String recommendation;

  const LearningPattern({
    required this.type,
    required this.description,
    required this.confidence,
    required this.recommendation,
  });
}

enum PatternType {
  timeOfDay,
  difficultyProgression,
  topicMastery,
  streakPattern,
  velocityPattern,
}

@immutable
class TrendPoint {
  final DateTime date;
  final double value;
  final String label;

  const TrendPoint({
    required this.date,
    required this.value,
    required this.label,
  });
}

@immutable
class ProgressSummary {
  final int totalQuestionsAnswered;
  final double averageAccuracy;
  final int topicsStudied;
  final int currentStreak;
  final double improvementRate;
  final int weakAreasCount;
  final int masteredTopicsCount;

  const ProgressSummary({
    required this.totalQuestionsAnswered,
    required this.averageAccuracy,
    required this.topicsStudied,
    required this.currentStreak,
    required this.improvementRate,
    required this.weakAreasCount,
    required this.masteredTopicsCount,
  });

  // Get overall progress level (0.0 to 1.0)
  double get progressLevel {
    double score = 0.0;
    
    // Accuracy component (40%)
    score += (averageAccuracy * 0.4);
    
    // Topics studied component (30%)
    score += ((topicsStudied / 10.0).clamp(0.0, 1.0) * 0.3);
    
    // Streak component (20%)
    score += ((currentStreak / 30.0).clamp(0.0, 1.0) * 0.2);
    
    // Mastery component (10%)
    if (topicsStudied > 0) {
      score += ((masteredTopicsCount / topicsStudied) * 0.1);
    }
    
    return score.clamp(0.0, 1.0);
  }

  // Get progress level description
  String get progressLevelDescription {
    final level = progressLevel;
    
    if (level < 0.3) return 'Débutant';
    if (level < 0.6) return 'Intermédiaire';
    if (level < 0.8) return 'Avancé';
    return 'Expert';
  }
}
