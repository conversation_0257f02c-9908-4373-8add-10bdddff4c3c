import 'package:hive/hive.dart';

part 'exam_attempt.g.dart'; // Hive generator will create this file

@HiveType(typeId: 4) // Changed from 3 to 4 to resolve conflict
class ExamAttempt extends HiveObject {
  @HiveField(0)
  final String examId;

  @HiveField(1)
  final DateTime timestamp;

  @HiveField(2)
  final int score;

  @HiveField(3)
  final int totalQuestions;

  @HiveField(4)
  final List<int?> userAnswers; // Store index of user's answer for each question, null if unanswered

  ExamAttempt({
    required this.examId,
    required this.timestamp,
    required this.score,
    required this.totalQuestions,
    required this.userAnswers,
  });

  double get percentage => totalQuestions > 0 ? (score / totalQuestions) * 100 : 0.0;
}
