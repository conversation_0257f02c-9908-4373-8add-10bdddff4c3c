// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'active_exam_state.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ActiveExamStateAdapter extends TypeAdapter<ActiveExamState> {
  @override
  final int typeId = 5;

  @override
  ActiveExamState read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ActiveExamState(
      examId: fields[0] as String,
      currentQuestionIndex: fields[1] as int,
      userAnswers: (fields[2] as Map?)?.cast<int, int>(),
    );
  }

  @override
  void write(BinaryWriter writer, ActiveExamState obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.examId)
      ..writeByte(1)
      ..write(obj.currentQuestionIndex)
      ..writeByte(2)
      ..write(obj.userAnswers);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ActiveExamStateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
