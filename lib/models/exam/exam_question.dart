import 'package:flutter/foundation.dart';

@immutable
class ExamQuestion {
  final int questionNumber;
  final String questionText; // Can be brief if the main question is in the image
  final List<String> options;
  final int correctAnswerIndex;
  final String? explanation; // Optional explanation for the answer

  const ExamQuestion({
    required this.questionNumber,
    required this.questionText,
    required this.options,
    required this.correctAnswerIndex,
    this.explanation,
  });

  // Factory constructor for creating an ExamQuestion from JSON
  factory ExamQuestion.fromJson(Map<String, dynamic> json) {
    return ExamQuestion(
      questionNumber: json['questionNumber'] as int,
      questionText: json['questionText'] as String,
      options: List<String>.from(json['options'] as List),
      correctAnswerIndex: json['correctAnswerIndex'] as int,
      explanation: json['explanation'] as String?,
    );
  }

  // Method to convert an ExamQuestion instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'questionNumber': questionNumber,
      'questionText': questionText,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'explanation': explanation,
    };
  }
}
