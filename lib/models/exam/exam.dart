import 'package:flutter/foundation.dart';
import 'exam_question.dart'; // Import the ExamQuestion model

@immutable
class Exam {
  final String id;
  final String title;
  final String? description;
  // Store statement content as a single string (potentially Markdown)
  final String statementContent;
  final List<ExamQuestion> questions;

  // Categorization fields
  final String mainCategory; // e.g., "Comptabilité Générale", "Fiscalité"
  final String? university;   // e.g., "Université Ibn Zohr"
  final String? faculty;      // e.g., "FSJES Agadir", "Global"
  final int? year;           // e.g., 2018
  final String? session;      // e.g., "Ordinaire", "Rattrapage"

  const Exam({
    required this.id,
    required this.title,
    this.description,
    required this.statementContent,
    required this.questions,
    required this.mainCategory,
    this.university,
    this.faculty,
    this.year,
    this.session,
  });

  // Factory constructor for creating an Exam from JSON
  factory Exam.fromJson(Map<String, dynamic> json) {
    // Parse the list of questions
    var questionsList = json['questions'] as List;
    List<ExamQuestion> parsedQuestions = questionsList
        .map((questionJson) => ExamQuestion.fromJson(questionJson as Map<String, dynamic>))
        .toList();

    return Exam(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      statementContent: json['statementContent'] as String,
      questions: parsedQuestions,
      // Load categorization fields
      mainCategory: json['mainCategory'] as String? ?? 'Inconnu', // Default if missing
      university: json['university'] as String?,
      faculty: json['faculty'] as String?,
      year: json['year'] as int?,
      session: json['session'] as String?,
    );
  }

  // Method to convert an Exam instance to JSON (useful for debugging or potential future use)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'statementContent': statementContent,
      'questions': questions.map((q) => q.toJson()).toList(),
      // Include categorization fields in JSON output
      'mainCategory': mainCategory,
      'university': university,
      'faculty': faculty,
      'year': year,
      'session': session,
    };
  }
}
