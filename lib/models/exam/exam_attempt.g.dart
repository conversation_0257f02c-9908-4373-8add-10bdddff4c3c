// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'exam_attempt.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ExamAttemptAdapter extends TypeAdapter<ExamAttempt> {
  @override
  final int typeId = 4;

  @override
  ExamAttempt read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ExamAttempt(
      examId: fields[0] as String,
      timestamp: fields[1] as DateTime,
      score: fields[2] as int,
      totalQuestions: fields[3] as int,
      userAnswers: (fields[4] as List).cast<int?>(),
    );
  }

  @override
  void write(BinaryWriter writer, ExamAttempt obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.examId)
      ..writeByte(1)
      ..write(obj.timestamp)
      ..writeByte(2)
      ..write(obj.score)
      ..writeByte(3)
      ..write(obj.totalQuestions)
      ..writeByte(4)
      ..write(obj.userAnswers);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExamAttemptAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
