import 'package:hive/hive.dart';

part 'active_exam_state.g.dart'; // Hive generator will create this file

@HiveType(typeId: 5) // Changed from 4 to 5 to resolve conflict
class ActiveExamState extends HiveObject {
  @HiveField(0)
  final String examId;

  @HiveField(1)
  int currentQuestionIndex;

  // Map of question index to selected answer index
  @HiveField(2)
  Map<int, int> userAnswers;

  ActiveExamState({
    required this.examId,
    this.currentQuestionIndex = 0,
    Map<int, int>? userAnswers,
  }) : userAnswers = userAnswers ?? {};
}
