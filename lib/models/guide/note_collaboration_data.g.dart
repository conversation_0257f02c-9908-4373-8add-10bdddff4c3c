// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_collaboration_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NoteCommentAdapter extends TypeAdapter<NoteComment> {
  @override
  final int typeId = 10;

  @override
  NoteComment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NoteComment(
      id: fields[0] as String,
      noteId: fields[1] as String,
      authorId: fields[2] as String,
      authorName: fields[3] as String,
      content: fields[4] as String,
      commentType: fields[5] as CommentType,
      createdAt: fields[6] as DateTime?,
      lastModified: fields[7] as DateTime?,
      parentCommentId: fields[8] as String?,
      lineNumber: fields[9] as int?,
      characterStart: fields[10] as int?,
      characterEnd: fields[11] as int?,
      isResolved: fields[12] as bool,
      mentions: (fields[13] as List).cast<String>(),
      metadata: (fields[14] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, NoteComment obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.noteId)
      ..writeByte(2)
      ..write(obj.authorId)
      ..writeByte(3)
      ..write(obj.authorName)
      ..writeByte(4)
      ..write(obj.content)
      ..writeByte(5)
      ..write(obj.commentType)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.lastModified)
      ..writeByte(8)
      ..write(obj.parentCommentId)
      ..writeByte(9)
      ..write(obj.lineNumber)
      ..writeByte(10)
      ..write(obj.characterStart)
      ..writeByte(11)
      ..write(obj.characterEnd)
      ..writeByte(12)
      ..write(obj.isResolved)
      ..writeByte(13)
      ..write(obj.mentions)
      ..writeByte(14)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteCommentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CollaborationActivityAdapter extends TypeAdapter<CollaborationActivity> {
  @override
  final int typeId = 12;

  @override
  CollaborationActivity read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CollaborationActivity(
      id: fields[0] as String,
      noteId: fields[1] as String,
      userId: fields[2] as String,
      userName: fields[3] as String,
      activityType: fields[4] as ActivityType,
      description: fields[5] as String,
      timestamp: fields[6] as DateTime?,
      metadata: (fields[7] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, CollaborationActivity obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.noteId)
      ..writeByte(2)
      ..write(obj.userId)
      ..writeByte(3)
      ..write(obj.userName)
      ..writeByte(4)
      ..write(obj.activityType)
      ..writeByte(5)
      ..write(obj.description)
      ..writeByte(6)
      ..write(obj.timestamp)
      ..writeByte(7)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CollaborationActivityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EditingSessionAdapter extends TypeAdapter<EditingSession> {
  @override
  final int typeId = 13;

  @override
  EditingSession read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return EditingSession(
      id: fields[0] as String,
      noteId: fields[1] as String,
      userId: fields[2] as String,
      userName: fields[3] as String,
      startTime: fields[4] as DateTime?,
      endTime: fields[5] as DateTime?,
      activeUsers: (fields[6] as List).cast<String>(),
      cursorPositions: (fields[7] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, EditingSession obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.noteId)
      ..writeByte(2)
      ..write(obj.userId)
      ..writeByte(3)
      ..write(obj.userName)
      ..writeByte(4)
      ..write(obj.startTime)
      ..writeByte(5)
      ..write(obj.endTime)
      ..writeByte(6)
      ..write(obj.activeUsers)
      ..writeByte(7)
      ..write(obj.cursorPositions);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EditingSessionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CommentTypeAdapter extends TypeAdapter<CommentType> {
  @override
  final int typeId = 9;

  @override
  CommentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CommentType.general;
      case 1:
        return CommentType.suggestion;
      case 2:
        return CommentType.question;
      case 3:
        return CommentType.approval;
      case 4:
        return CommentType.correction;
      default:
        return CommentType.general;
    }
  }

  @override
  void write(BinaryWriter writer, CommentType obj) {
    switch (obj) {
      case CommentType.general:
        writer.writeByte(0);
        break;
      case CommentType.suggestion:
        writer.writeByte(1);
        break;
      case CommentType.question:
        writer.writeByte(2);
        break;
      case CommentType.approval:
        writer.writeByte(3);
        break;
      case CommentType.correction:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ActivityTypeAdapter extends TypeAdapter<ActivityType> {
  @override
  final int typeId = 11;

  @override
  ActivityType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ActivityType.noteCreated;
      case 1:
        return ActivityType.noteUpdated;
      case 2:
        return ActivityType.noteShared;
      case 3:
        return ActivityType.commentAdded;
      case 4:
        return ActivityType.commentResolved;
      case 5:
        return ActivityType.linkCreated;
      case 6:
        return ActivityType.linkRemoved;
      case 7:
        return ActivityType.userJoined;
      case 8:
        return ActivityType.userLeft;
      default:
        return ActivityType.noteCreated;
    }
  }

  @override
  void write(BinaryWriter writer, ActivityType obj) {
    switch (obj) {
      case ActivityType.noteCreated:
        writer.writeByte(0);
        break;
      case ActivityType.noteUpdated:
        writer.writeByte(1);
        break;
      case ActivityType.noteShared:
        writer.writeByte(2);
        break;
      case ActivityType.commentAdded:
        writer.writeByte(3);
        break;
      case ActivityType.commentResolved:
        writer.writeByte(4);
        break;
      case ActivityType.linkCreated:
        writer.writeByte(5);
        break;
      case ActivityType.linkRemoved:
        writer.writeByte(6);
        break;
      case ActivityType.userJoined:
        writer.writeByte(7);
        break;
      case ActivityType.userLeft:
        writer.writeByte(8);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ActivityTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
