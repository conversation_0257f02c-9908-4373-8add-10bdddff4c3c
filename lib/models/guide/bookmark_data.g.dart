// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bookmark_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BookmarkDataAdapter extends TypeAdapter<BookmarkData> {
  @override
  final int typeId = 3;

  @override
  BookmarkData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BookmarkData(
      id: fields[0] as String,
      guideId: fields[1] as String,
      sectionId: fields[2] as String,
      title: fields[3] as String,
      description: fields[4] as String,
      bookmarkType: fields[5] as BookmarkType,
      createdAt: fields[6] as DateTime?,
      lastAccessed: fields[7] as DateTime?,
      tags: (fields[8] as List).cast<String>(),
      color: fields[9] as Color,
      position: fields[10] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, BookmarkData obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.guideId)
      ..writeByte(2)
      ..write(obj.sectionId)
      ..writeByte(3)
      ..write(obj.title)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.bookmarkType)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.lastAccessed)
      ..writeByte(8)
      ..write(obj.tags)
      ..writeByte(9)
      ..write(obj.color)
      ..writeByte(10)
      ..write(obj.position);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BookmarkDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
