import 'package:hive/hive.dart';

part 'personal_note_data.g.dart';

/// Enumeration for different types of personal notes
@HiveType(typeId: 8)
enum NoteType {
  @HiveField(0)
  text,
  @HiveField(1)
  checklist,
  @HiveField(2)
  reminder,
}

/// Enumeration for note link types
@HiveType(typeId: 9)
enum NoteLinkType {
  @HiveField(0)
  reference,      // Simple reference to another note
  @HiveField(1)
  related,        // Related content
  @HiveField(2)
  followUp,       // Follow-up note
  @HiveField(3)
  prerequisite,   // Prerequisite note
  @HiveField(4)
  child,          // Child note (hierarchical)
  @HiveField(5)
  parent,         // Parent note (hierarchical)
}

/// Data model for note links
@HiveType(typeId: 11)
class NoteLink extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String sourceNoteId;

  @HiveField(2)
  final String targetNoteId;

  @HiveField(3)
  final NoteLinkType linkType;

  @HiveField(4)
  final String? description;

  @HiveField(5)
  final DateTime createdAt;

  NoteLink({
    required this.id,
    required this.sourceNoteId,
    required this.targetNoteId,
    required this.linkType,
    this.description,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Creates a NoteLink from JSON
  factory NoteLink.fromJson(Map<String, dynamic> json) {
    return NoteLink(
      id: json['id'] ?? '',
      sourceNoteId: json['sourceNoteId'] ?? '',
      targetNoteId: json['targetNoteId'] ?? '',
      linkType: _parseLinkType(json['linkType']),
      description: json['description'],
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
    );
  }

  /// Converts NoteLink to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sourceNoteId': sourceNoteId,
      'targetNoteId': targetNoteId,
      'linkType': linkType.name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Gets display label for link type
  String getLinkTypeLabel() {
    switch (linkType) {
      case NoteLinkType.reference:
        return 'Référence';
      case NoteLinkType.related:
        return 'Lié';
      case NoteLinkType.followUp:
        return 'Suivi';
      case NoteLinkType.prerequisite:
        return 'Prérequis';
      case NoteLinkType.child:
        return 'Sous-note';
      case NoteLinkType.parent:
        return 'Note parent';
    }
  }

  /// Parses link type from string
  static NoteLinkType _parseLinkType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'related':
        return NoteLinkType.related;
      case 'followup':
        return NoteLinkType.followUp;
      case 'prerequisite':
        return NoteLinkType.prerequisite;
      case 'child':
        return NoteLinkType.child;
      case 'parent':
        return NoteLinkType.parent;
      default:
        return NoteLinkType.reference;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NoteLink && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Data model for note sharing information
@HiveType(typeId: 12)
class NoteShareInfo extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String noteId;

  @HiveField(2)
  final String shareToken;

  @HiveField(3)
  final bool isPublic;

  @HiveField(4)
  final DateTime? expiresAt;

  @HiveField(5)
  final List<String> allowedUsers;

  @HiveField(6)
  final bool allowComments;

  @HiveField(7)
  final bool allowEditing;

  @HiveField(8)
  final DateTime createdAt;

  @HiveField(9)
  final DateTime lastAccessed;

  NoteShareInfo({
    required this.id,
    required this.noteId,
    required this.shareToken,
    this.isPublic = false,
    this.expiresAt,
    this.allowedUsers = const [],
    this.allowComments = false,
    this.allowEditing = false,
    DateTime? createdAt,
    DateTime? lastAccessed,
  }) : createdAt = createdAt ?? DateTime.now(),
       lastAccessed = lastAccessed ?? DateTime.now();

  /// Creates a NoteShareInfo from JSON
  factory NoteShareInfo.fromJson(Map<String, dynamic> json) {
    return NoteShareInfo(
      id: json['id'] ?? '',
      noteId: json['noteId'] ?? '',
      shareToken: json['shareToken'] ?? '',
      isPublic: json['isPublic'] ?? false,
      expiresAt: json['expiresAt'] != null
          ? DateTime.tryParse(json['expiresAt'])
          : null,
      allowedUsers: List<String>.from(json['allowedUsers'] ?? []),
      allowComments: json['allowComments'] ?? false,
      allowEditing: json['allowEditing'] ?? false,
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      lastAccessed: DateTime.tryParse(json['lastAccessed'] ?? '') ?? DateTime.now(),
    );
  }

  /// Converts NoteShareInfo to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'noteId': noteId,
      'shareToken': shareToken,
      'isPublic': isPublic,
      'expiresAt': expiresAt?.toIso8601String(),
      'allowedUsers': allowedUsers,
      'allowComments': allowComments,
      'allowEditing': allowEditing,
      'createdAt': createdAt.toIso8601String(),
      'lastAccessed': lastAccessed.toIso8601String(),
    };
  }

  /// Checks if the share is still valid
  bool get isValid {
    if (expiresAt == null) return true;
    return DateTime.now().isBefore(expiresAt!);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NoteShareInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Data model for personal notes with Hive persistence
@HiveType(typeId: 7)
class PersonalNoteData extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String guideId;

  @HiveField(2)
  final String sectionId;

  @HiveField(3)
  String title;

  @HiveField(4)
  String content;

  @HiveField(5)
  final NoteType noteType;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  DateTime lastModified;

  @HiveField(8)
  List<String> tags;

  @HiveField(9)
  final bool isPrivate;

  @HiveField(10)
  final List<String> attachments; // For future file attachments

  @HiveField(11)
  final DateTime? reminderDate;

  @HiveField(12)
  String formattedContent; // For basic markdown support

  @HiveField(13)
  List<String> linkedNoteIds; // IDs of notes this note links to

  @HiveField(14)
  List<String> backlinkedNoteIds; // IDs of notes that link to this note

  @HiveField(15)
  String? shareToken; // Token for sharing this note

  @HiveField(16)
  bool isShared; // Whether this note is shared

  @HiveField(17)
  DateTime? lastSharedAt; // When this note was last shared

  @HiveField(18)
  int viewCount; // Number of times this note has been viewed

  @HiveField(19)
  Map<String, dynamic> metadata; // Additional metadata for extensions

  PersonalNoteData({
    required this.id,
    required this.guideId,
    required this.sectionId,
    required this.title,
    required this.content,
    this.noteType = NoteType.text,
    DateTime? createdAt,
    DateTime? lastModified,
    this.tags = const [],
    this.isPrivate = false,
    this.attachments = const [],
    this.reminderDate,
    String? formattedContent,
    this.linkedNoteIds = const [],
    this.backlinkedNoteIds = const [],
    this.shareToken,
    this.isShared = false,
    this.lastSharedAt,
    this.viewCount = 0,
    this.metadata = const {},
  }) : createdAt = createdAt ?? DateTime.now(),
       lastModified = lastModified ?? DateTime.now(),
       formattedContent = formattedContent ?? content;

  /// Creates a PersonalNoteData from JSON
  factory PersonalNoteData.fromJson(Map<String, dynamic> json) {
    return PersonalNoteData(
      id: json['id'] ?? '',
      guideId: json['guideId'] ?? '',
      sectionId: json['sectionId'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      noteType: _parseNoteType(json['noteType']),
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      lastModified: DateTime.tryParse(json['lastModified'] ?? '') ?? DateTime.now(),
      tags: List<String>.from(json['tags'] ?? []),
      isPrivate: json['isPrivate'] ?? false,
      attachments: List<String>.from(json['attachments'] ?? []),
      reminderDate: json['reminderDate'] != null
          ? DateTime.tryParse(json['reminderDate'])
          : null,
      formattedContent: json['formattedContent'],
      linkedNoteIds: List<String>.from(json['linkedNoteIds'] ?? []),
      backlinkedNoteIds: List<String>.from(json['backlinkedNoteIds'] ?? []),
      shareToken: json['shareToken'],
      isShared: json['isShared'] ?? false,
      lastSharedAt: json['lastSharedAt'] != null
          ? DateTime.tryParse(json['lastSharedAt'])
          : null,
      viewCount: json['viewCount'] ?? 0,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Converts PersonalNoteData to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'guideId': guideId,
      'sectionId': sectionId,
      'title': title,
      'content': content,
      'noteType': noteType.name,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'tags': tags,
      'isPrivate': isPrivate,
      'attachments': attachments,
      'reminderDate': reminderDate?.toIso8601String(),
      'formattedContent': formattedContent,
      'linkedNoteIds': linkedNoteIds,
      'backlinkedNoteIds': backlinkedNoteIds,
      'shareToken': shareToken,
      'isShared': isShared,
      'lastSharedAt': lastSharedAt?.toIso8601String(),
      'viewCount': viewCount,
      'metadata': metadata,
    };
  }

  /// Creates a copy with updated fields
  PersonalNoteData copyWith({
    String? id,
    String? guideId,
    String? sectionId,
    String? title,
    String? content,
    NoteType? noteType,
    DateTime? createdAt,
    DateTime? lastModified,
    List<String>? tags,
    bool? isPrivate,
    List<String>? attachments,
    DateTime? reminderDate,
    String? formattedContent,
    List<String>? linkedNoteIds,
    List<String>? backlinkedNoteIds,
    String? shareToken,
    bool? isShared,
    DateTime? lastSharedAt,
    int? viewCount,
    Map<String, dynamic>? metadata,
  }) {
    return PersonalNoteData(
      id: id ?? this.id,
      guideId: guideId ?? this.guideId,
      sectionId: sectionId ?? this.sectionId,
      title: title ?? this.title,
      content: content ?? this.content,
      noteType: noteType ?? this.noteType,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      tags: tags ?? this.tags,
      isPrivate: isPrivate ?? this.isPrivate,
      attachments: attachments ?? this.attachments,
      reminderDate: reminderDate ?? this.reminderDate,
      formattedContent: formattedContent ?? this.formattedContent,
      linkedNoteIds: linkedNoteIds ?? this.linkedNoteIds,
      backlinkedNoteIds: backlinkedNoteIds ?? this.backlinkedNoteIds,
      shareToken: shareToken ?? this.shareToken,
      isShared: isShared ?? this.isShared,
      lastSharedAt: lastSharedAt ?? this.lastSharedAt,
      viewCount: viewCount ?? this.viewCount,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Updates the note content and last modified time
  void updateContent(String newContent, {String? newTitle}) {
    content = newContent;
    formattedContent = newContent;
    if (newTitle != null) {
      title = newTitle;
    }
    lastModified = DateTime.now();
    save(); // Save to Hive
  }

  /// Adds a tag to the note
  void addTag(String tag) {
    if (!tags.contains(tag)) {
      tags = List<String>.from(tags)..add(tag);
      lastModified = DateTime.now();
      save();
    }
  }

  /// Removes a tag from the note
  void removeTag(String tag) {
    tags = List<String>.from(tags)..remove(tag);
    lastModified = DateTime.now();
    save();
  }

  /// Adds a link to another note
  void addNoteLink(String noteId) {
    if (!linkedNoteIds.contains(noteId)) {
      linkedNoteIds = List<String>.from(linkedNoteIds)..add(noteId);
      lastModified = DateTime.now();
      save();
    }
  }

  /// Removes a link to another note
  void removeNoteLink(String noteId) {
    linkedNoteIds = List<String>.from(linkedNoteIds)..remove(noteId);
    lastModified = DateTime.now();
    save();
  }

  /// Adds a backlink from another note
  void addBacklink(String noteId) {
    if (!backlinkedNoteIds.contains(noteId)) {
      backlinkedNoteIds = List<String>.from(backlinkedNoteIds)..add(noteId);
      lastModified = DateTime.now();
      save();
    }
  }

  /// Removes a backlink from another note
  void removeBacklink(String noteId) {
    backlinkedNoteIds = List<String>.from(backlinkedNoteIds)..remove(noteId);
    lastModified = DateTime.now();
    save();
  }

  /// Increments the view count
  void incrementViewCount() {
    viewCount++;
    save();
  }

  /// Sets sharing information
  void setSharing({
    required String token,
    required bool shared,
    DateTime? sharedAt,
  }) {
    shareToken = token;
    isShared = shared;
    lastSharedAt = sharedAt ?? DateTime.now();
    lastModified = DateTime.now();
    save();
  }

  /// Removes sharing
  void removeSharing() {
    shareToken = null;
    isShared = false;
    lastSharedAt = null;
    lastModified = DateTime.now();
    save();
  }

  /// Updates metadata
  void updateMetadata(String key, dynamic value) {
    metadata = Map<String, dynamic>.from(metadata);
    metadata[key] = value;
    lastModified = DateTime.now();
    save();
  }

  /// Removes metadata
  void removeMetadata(String key) {
    metadata = Map<String, dynamic>.from(metadata);
    metadata.remove(key);
    lastModified = DateTime.now();
    save();
  }

  /// Gets searchable text from the note
  String getSearchableText() {
    final buffer = StringBuffer();
    buffer.write(title.toLowerCase());
    buffer.write(' ');
    buffer.write(content.toLowerCase());
    buffer.write(' ');
    buffer.write(tags.join(' ').toLowerCase());
    return buffer.toString();
  }

  /// Validates note data integrity
  bool isValid() {
    return id.isNotEmpty &&
           guideId.isNotEmpty &&
           sectionId.isNotEmpty &&
           title.isNotEmpty;
  }

  /// Gets display text for note type
  String getNoteTypeLabel() {
    switch (noteType) {
      case NoteType.text:
        return 'Texte';
      case NoteType.checklist:
        return 'Liste de contrôle';
      case NoteType.reminder:
        return 'Rappel';
    }
  }

  /// Gets notes by tag
  static List<PersonalNoteData> getTaggedNotes(
    List<PersonalNoteData> notes, 
    String tag
  ) {
    return notes.where((note) => note.tags.contains(tag)).toList();
  }

  /// Checks if the note has a reminder that's due
  bool hasOverdueReminder() {
    if (reminderDate == null) return false;
    return DateTime.now().isAfter(reminderDate!);
  }

  /// Gets formatted content preview (first 100 characters)
  String getContentPreview() {
    if (content.length <= 100) return content;
    return '${content.substring(0, 97)}...';
  }

  /// Gets all connected note IDs (both links and backlinks)
  List<String> get allConnectedNoteIds {
    final Set<String> connected = {};
    connected.addAll(linkedNoteIds);
    connected.addAll(backlinkedNoteIds);
    return connected.toList();
  }

  /// Checks if this note is linked to another note
  bool isLinkedTo(String noteId) {
    return linkedNoteIds.contains(noteId);
  }

  /// Checks if this note has a backlink from another note
  bool hasBacklinkFrom(String noteId) {
    return backlinkedNoteIds.contains(noteId);
  }

  /// Checks if this note is connected to another note (either direction)
  bool isConnectedTo(String noteId) {
    return isLinkedTo(noteId) || hasBacklinkFrom(noteId);
  }

  /// Gets the total number of connections
  int get connectionCount {
    return linkedNoteIds.length + backlinkedNoteIds.length;
  }

  /// Checks if this note has any connections
  bool get hasConnections {
    return linkedNoteIds.isNotEmpty || backlinkedNoteIds.isNotEmpty;
  }

  /// Gets sharing status summary
  String get sharingStatus {
    if (!isShared) return 'Non partagé';
    if (shareToken == null) return 'Partage configuré';
    return 'Partagé';
  }

  /// Checks if note can be shared (not private)
  bool get canBeShared {
    return !isPrivate;
  }

  /// Parses note type from string
  static NoteType _parseNoteType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'checklist':
        return NoteType.checklist;
      case 'reminder':
        return NoteType.reminder;
      default:
        return NoteType.text;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PersonalNoteData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PersonalNoteData(id: $id, title: $title, guideId: $guideId, sectionId: $sectionId)';
  }
}