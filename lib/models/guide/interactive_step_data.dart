
/// Enumeration for different types of interactive steps
enum StepType {
  explanation,
  input,
  calculation,
  validation,
  selection,
}

/// Enumeration for different types of user interactions
enum InteractionType {
  tap,
  input,
  drag,
  select,
  swipe,
  calculate,
}

/// Data model for interactive walkthrough steps
class InteractiveStepData {
  final String stepId;
  final String title;
  final String description;
  final StepType stepType;
  final String content;
  final InteractionType interactionType;
  final dynamic expectedInput;
  final List<ValidationRule> validationRules;
  final List<String> hints;
  final String? nextStepCondition;
  final Map<String, dynamic> visualElements;
  final bool isRequired;
  final int maxAttempts;

  const InteractiveStepData({
    required this.stepId,
    required this.title,
    required this.description,
    this.stepType = StepType.explanation,
    this.content = '',
    this.interactionType = InteractionType.tap,
    this.expectedInput,
    this.validationRules = const [],
    this.hints = const [],
    this.nextStepCondition,
    this.visualElements = const {},
    this.isRequired = true,
    this.maxAttempts = 3,
  });

  /// Creates an InteractiveStepData from JSON
  factory InteractiveStepData.fromJson(Map<String, dynamic> json) {
    return InteractiveStepData(
      stepId: json['stepId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      stepType: _parseStepType(json['stepType']),
      content: json['content'] ?? '',
      interactionType: _parseInteractionType(json['interactionType']),
      expectedInput: json['expectedInput'],
      validationRules: (json['validationRules'] as List<dynamic>?)
          ?.map((rule) => ValidationRule.fromJson(rule))
          .toList() ?? [],
      hints: List<String>.from(json['hints'] ?? []),
      nextStepCondition: json['nextStepCondition'],
      visualElements: Map<String, dynamic>.from(json['visualElements'] ?? {}),
      isRequired: json['isRequired'] ?? true,
      maxAttempts: json['maxAttempts'] ?? 3,
    );
  }

  /// Converts InteractiveStepData to JSON
  Map<String, dynamic> toJson() {
    return {
      'stepId': stepId,
      'title': title,
      'description': description,
      'stepType': stepType.name,
      'content': content,
      'interactionType': interactionType.name,
      'expectedInput': expectedInput,
      'validationRules': validationRules.map((rule) => rule.toJson()).toList(),
      'hints': hints,
      'nextStepCondition': nextStepCondition,
      'visualElements': visualElements,
      'isRequired': isRequired,
      'maxAttempts': maxAttempts,
    };
  }

  /// Creates a copy with updated fields
  InteractiveStepData copyWith({
    String? stepId,
    String? title,
    String? description,
    StepType? stepType,
    String? content,
    InteractionType? interactionType,
    dynamic expectedInput,
    List<ValidationRule>? validationRules,
    List<String>? hints,
    String? nextStepCondition,
    Map<String, dynamic>? visualElements,
    bool? isRequired,
    int? maxAttempts,
  }) {
    return InteractiveStepData(
      stepId: stepId ?? this.stepId,
      title: title ?? this.title,
      description: description ?? this.description,
      stepType: stepType ?? this.stepType,
      content: content ?? this.content,
      interactionType: interactionType ?? this.interactionType,
      expectedInput: expectedInput ?? this.expectedInput,
      validationRules: validationRules ?? this.validationRules,
      hints: hints ?? this.hints,
      nextStepCondition: nextStepCondition ?? this.nextStepCondition,
      visualElements: visualElements ?? this.visualElements,
      isRequired: isRequired ?? this.isRequired,
      maxAttempts: maxAttempts ?? this.maxAttempts,
    );
  }

  /// Validates user input against expected input and rules
  ValidationResult validateUserInput(dynamic userInput) {
    // Check if input matches expected input
    if (expectedInput != null && userInput != expectedInput) {
      return ValidationResult(
        isValid: false,
        message: 'Réponse incorrecte. Essayez encore.',
        suggestion: hints.isNotEmpty ? hints.first : null,
      );
    }

    // Apply validation rules
    for (final rule in validationRules) {
      final result = rule.validate(userInput);
      if (!result.isValid) {
        return result;
      }
    }

    return ValidationResult(
      isValid: true,
      message: 'Excellent ! Passons à l\'étape suivante.',
    );
  }

  /// Checks if the step can proceed to the next step
  bool canProceedToNext(dynamic userInput, {Map<String, dynamic>? context}) {
    if (!isRequired) return true;
    
    final validationResult = validateUserInput(userInput);
    if (!validationResult.isValid) return false;

    // Check custom next step condition if provided
    if (nextStepCondition != null) {
      return _evaluateCondition(nextStepCondition!, userInput, context);
    }

    return true;
  }

  /// Gets the appropriate hint based on attempt count
  String? getHint(int attemptCount) {
    if (hints.isEmpty) return null;
    
    final hintIndex = (attemptCount - 1).clamp(0, hints.length - 1);
    return hints[hintIndex];
  }

  /// Gets the step type label in French
  String getStepTypeLabel() {
    switch (stepType) {
      case StepType.explanation:
        return 'Explication';
      case StepType.input:
        return 'Saisie';
      case StepType.calculation:
        return 'Calcul';
      case StepType.validation:
        return 'Validation';
      case StepType.selection:
        return 'Sélection';
    }
  }

  /// Gets the interaction type label in French
  String getInteractionTypeLabel() {
    switch (interactionType) {
      case InteractionType.tap:
        return 'Appuyer';
      case InteractionType.input:
        return 'Saisir';
      case InteractionType.drag:
        return 'Glisser';
      case InteractionType.select:
        return 'Sélectionner';
      case InteractionType.swipe:
        return 'Balayer';
      case InteractionType.calculate:
        return 'Calculer';
    }
  }

  /// Evaluates a custom condition for step progression
  bool _evaluateCondition(String condition, dynamic userInput, Map<String, dynamic>? context) {
    // Simple condition evaluation - can be expanded for complex conditions
    // For now, just check basic equality
    return condition == userInput?.toString();
  }

  /// Parses step type from string
  static StepType _parseStepType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'input':
        return StepType.input;
      case 'calculation':
        return StepType.calculation;
      case 'validation':
        return StepType.validation;
      case 'selection':
        return StepType.selection;
      default:
        return StepType.explanation;
    }
  }

  /// Parses interaction type from string
  static InteractionType _parseInteractionType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'input':
        return InteractionType.input;
      case 'drag':
        return InteractionType.drag;
      case 'select':
        return InteractionType.select;
      case 'swipe':
        return InteractionType.swipe;
      case 'calculate':
        return InteractionType.calculate;
      default:
        return InteractionType.tap;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InteractiveStepData && other.stepId == stepId;
  }

  @override
  int get hashCode => stepId.hashCode;

  @override
  String toString() {
    return 'InteractiveStepData(stepId: $stepId, title: $title, type: $stepType)';
  }
}

/// Data model for validation rules
class ValidationRule {
  final String ruleType;
  final dynamic value;
  final String message;
  final String? suggestion;

  const ValidationRule({
    required this.ruleType,
    required this.value,
    required this.message,
    this.suggestion,
  });

  /// Creates a ValidationRule from JSON
  factory ValidationRule.fromJson(Map<String, dynamic> json) {
    return ValidationRule(
      ruleType: json['ruleType'] ?? '',
      value: json['value'],
      message: json['message'] ?? '',
      suggestion: json['suggestion'],
    );
  }

  /// Converts ValidationRule to JSON
  Map<String, dynamic> toJson() {
    return {
      'ruleType': ruleType,
      'value': value,
      'message': message,
      'suggestion': suggestion,
    };
  }

  /// Validates input against this rule
  ValidationResult validate(dynamic input) {
    switch (ruleType.toLowerCase()) {
      case 'required':
        if (input == null || input.toString().trim().isEmpty) {
          return ValidationResult(
            isValid: false,
            message: message,
            suggestion: suggestion,
          );
        }
        break;
      case 'numeric':
        if (double.tryParse(input.toString()) == null) {
          return ValidationResult(
            isValid: false,
            message: message,
            suggestion: suggestion,
          );
        }
        break;
      case 'min_value':
        final numValue = double.tryParse(input.toString());
        if (numValue == null || numValue < value) {
          return ValidationResult(
            isValid: false,
            message: message,
            suggestion: suggestion,
          );
        }
        break;
      case 'max_value':
        final numValue = double.tryParse(input.toString());
        if (numValue == null || numValue > value) {
          return ValidationResult(
            isValid: false,
            message: message,
            suggestion: suggestion,
          );
        }
        break;
      case 'exact_match':
        if (input.toString() != value.toString()) {
          return ValidationResult(
            isValid: false,
            message: message,
            suggestion: suggestion,
          );
        }
        break;
    }

    return ValidationResult(isValid: true, message: 'Valid');
  }
}

/// Data model for validation results
class ValidationResult {
  final bool isValid;
  final String message;
  final String? suggestion;

  const ValidationResult({
    required this.isValid,
    required this.message,
    this.suggestion,
  });
}