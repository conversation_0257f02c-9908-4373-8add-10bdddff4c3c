import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

part 'note_collaboration_data.g.dart';

/// Enumeration for comment types
@HiveType(typeId: 9)
enum CommentType {
  @HiveField(0)
  general,
  @HiveField(1)
  suggestion,
  @HiveField(2)
  question,
  @HiveField(3)
  approval,
  @HiveField(4)
  correction,
}

/// Data model for note comments
@HiveType(typeId: 10)
class NoteComment extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String noteId;

  @HiveField(2)
  final String authorId;

  @HiveField(3)
  final String authorName;

  @HiveField(4)
  String content;

  @HiveField(5)
  final CommentType commentType;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  DateTime lastModified;

  @HiveField(8)
  final String? parentCommentId; // For threaded comments

  @HiveField(9)
  final int? lineNumber; // For line-specific comments

  @HiveField(10)
  final int? characterStart; // For text selection comments

  @HiveField(11)
  final int? characterEnd; // For text selection comments

  @HiveField(12)
  bool isResolved;

  @HiveField(13)
  final List<String> mentions; // User IDs mentioned in the comment

  @HiveField(14)
  final Map<String, dynamic> metadata;

  NoteComment({
    required this.id,
    required this.noteId,
    required this.authorId,
    required this.authorName,
    required this.content,
    this.commentType = CommentType.general,
    DateTime? createdAt,
    DateTime? lastModified,
    this.parentCommentId,
    this.lineNumber,
    this.characterStart,
    this.characterEnd,
    this.isResolved = false,
    this.mentions = const [],
    this.metadata = const {},
  }) : createdAt = createdAt ?? DateTime.now(),
       lastModified = lastModified ?? DateTime.now();

  /// Creates a NoteComment from JSON
  factory NoteComment.fromJson(Map<String, dynamic> json) {
    return NoteComment(
      id: json['id'] ?? '',
      noteId: json['noteId'] ?? '',
      authorId: json['authorId'] ?? '',
      authorName: json['authorName'] ?? '',
      content: json['content'] ?? '',
      commentType: _parseCommentType(json['commentType']),
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      lastModified: DateTime.tryParse(json['lastModified'] ?? '') ?? DateTime.now(),
      parentCommentId: json['parentCommentId'],
      lineNumber: json['lineNumber'],
      characterStart: json['characterStart'],
      characterEnd: json['characterEnd'],
      isResolved: json['isResolved'] ?? false,
      mentions: List<String>.from(json['mentions'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Converts NoteComment to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'noteId': noteId,
      'authorId': authorId,
      'authorName': authorName,
      'content': content,
      'commentType': commentType.name,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'parentCommentId': parentCommentId,
      'lineNumber': lineNumber,
      'characterStart': characterStart,
      'characterEnd': characterEnd,
      'isResolved': isResolved,
      'mentions': mentions,
      'metadata': metadata,
    };
  }

  /// Gets display label for comment type
  String getCommentTypeLabel() {
    switch (commentType) {
      case CommentType.general:
        return 'Commentaire';
      case CommentType.suggestion:
        return 'Suggestion';
      case CommentType.question:
        return 'Question';
      case CommentType.approval:
        return 'Approbation';
      case CommentType.correction:
        return 'Correction';
    }
  }

  /// Gets icon for comment type
  IconData getCommentTypeIcon() {
    switch (commentType) {
      case CommentType.general:
        return Icons.comment;
      case CommentType.suggestion:
        return Icons.lightbulb_outline;
      case CommentType.question:
        return Icons.help_outline;
      case CommentType.approval:
        return Icons.check_circle_outline;
      case CommentType.correction:
        return Icons.edit_outlined;
    }
  }

  /// Gets color for comment type
  Color getCommentTypeColor() {
    switch (commentType) {
      case CommentType.general:
        return Colors.blue;
      case CommentType.suggestion:
        return Colors.orange;
      case CommentType.question:
        return Colors.purple;
      case CommentType.approval:
        return Colors.green;
      case CommentType.correction:
        return Colors.red;
    }
  }

  /// Checks if this is a threaded comment
  bool get isThreaded => parentCommentId != null;

  /// Checks if this is a line-specific comment
  bool get isLineSpecific => lineNumber != null;

  /// Checks if this is a text selection comment
  bool get isTextSelection => characterStart != null && characterEnd != null;

  /// Updates the comment content
  void updateContent(String newContent) {
    content = newContent;
    lastModified = DateTime.now();
    save();
  }

  /// Resolves the comment
  void resolve() {
    isResolved = true;
    lastModified = DateTime.now();
    save();
  }

  /// Unresolves the comment
  void unresolve() {
    isResolved = false;
    lastModified = DateTime.now();
    save();
  }

  /// Parses comment type from string
  static CommentType _parseCommentType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'suggestion':
        return CommentType.suggestion;
      case 'question':
        return CommentType.question;
      case 'approval':
        return CommentType.approval;
      case 'correction':
        return CommentType.correction;
      default:
        return CommentType.general;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NoteComment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Enumeration for collaboration activity types
@HiveType(typeId: 11)
enum ActivityType {
  @HiveField(0)
  noteCreated,
  @HiveField(1)
  noteUpdated,
  @HiveField(2)
  noteShared,
  @HiveField(3)
  commentAdded,
  @HiveField(4)
  commentResolved,
  @HiveField(5)
  linkCreated,
  @HiveField(6)
  linkRemoved,
  @HiveField(7)
  userJoined,
  @HiveField(8)
  userLeft,
}

/// Data model for collaboration activities
@HiveType(typeId: 12)
class CollaborationActivity extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String noteId;

  @HiveField(2)
  final String userId;

  @HiveField(3)
  final String userName;

  @HiveField(4)
  final ActivityType activityType;

  @HiveField(5)
  final String description;

  @HiveField(6)
  final DateTime timestamp;

  @HiveField(7)
  final Map<String, dynamic> metadata;

  CollaborationActivity({
    required this.id,
    required this.noteId,
    required this.userId,
    required this.userName,
    required this.activityType,
    required this.description,
    DateTime? timestamp,
    this.metadata = const {},
  }) : timestamp = timestamp ?? DateTime.now();

  /// Creates a CollaborationActivity from JSON
  factory CollaborationActivity.fromJson(Map<String, dynamic> json) {
    return CollaborationActivity(
      id: json['id'] ?? '',
      noteId: json['noteId'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      activityType: _parseActivityType(json['activityType']),
      description: json['description'] ?? '',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Converts CollaborationActivity to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'noteId': noteId,
      'userId': userId,
      'userName': userName,
      'activityType': activityType.name,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Gets display label for activity type
  String getActivityTypeLabel() {
    switch (activityType) {
      case ActivityType.noteCreated:
        return 'Note créée';
      case ActivityType.noteUpdated:
        return 'Note modifiée';
      case ActivityType.noteShared:
        return 'Note partagée';
      case ActivityType.commentAdded:
        return 'Commentaire ajouté';
      case ActivityType.commentResolved:
        return 'Commentaire résolu';
      case ActivityType.linkCreated:
        return 'Liaison créée';
      case ActivityType.linkRemoved:
        return 'Liaison supprimée';
      case ActivityType.userJoined:
        return 'Utilisateur rejoint';
      case ActivityType.userLeft:
        return 'Utilisateur parti';
    }
  }

  /// Gets icon for activity type
  IconData getActivityTypeIcon() {
    switch (activityType) {
      case ActivityType.noteCreated:
        return Icons.note_add;
      case ActivityType.noteUpdated:
        return Icons.edit;
      case ActivityType.noteShared:
        return Icons.share;
      case ActivityType.commentAdded:
        return Icons.comment;
      case ActivityType.commentResolved:
        return Icons.check_circle;
      case ActivityType.linkCreated:
        return Icons.link;
      case ActivityType.linkRemoved:
        return Icons.link_off;
      case ActivityType.userJoined:
        return Icons.person_add;
      case ActivityType.userLeft:
        return Icons.person_remove;
    }
  }

  /// Parses activity type from string
  static ActivityType _parseActivityType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'noteupdated':
        return ActivityType.noteUpdated;
      case 'noteshared':
        return ActivityType.noteShared;
      case 'commentadded':
        return ActivityType.commentAdded;
      case 'commentresolved':
        return ActivityType.commentResolved;
      case 'linkcreated':
        return ActivityType.linkCreated;
      case 'linkremoved':
        return ActivityType.linkRemoved;
      case 'userjoined':
        return ActivityType.userJoined;
      case 'userleft':
        return ActivityType.userLeft;
      default:
        return ActivityType.noteCreated;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CollaborationActivity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Data model for collaborative editing sessions
@HiveType(typeId: 13)
class EditingSession extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String noteId;

  @HiveField(2)
  final String userId;

  @HiveField(3)
  final String userName;

  @HiveField(4)
  final DateTime startTime;

  @HiveField(5)
  DateTime? endTime;

  @HiveField(6)
  final List<String> activeUsers;

  @HiveField(7)
  final Map<String, dynamic> cursorPositions;

  EditingSession({
    required this.id,
    required this.noteId,
    required this.userId,
    required this.userName,
    DateTime? startTime,
    this.endTime,
    this.activeUsers = const [],
    this.cursorPositions = const {},
  }) : startTime = startTime ?? DateTime.now();

  /// Creates an EditingSession from JSON
  factory EditingSession.fromJson(Map<String, dynamic> json) {
    return EditingSession(
      id: json['id'] ?? '',
      noteId: json['noteId'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      startTime: DateTime.tryParse(json['startTime'] ?? '') ?? DateTime.now(),
      endTime: json['endTime'] != null ? DateTime.tryParse(json['endTime']) : null,
      activeUsers: List<String>.from(json['activeUsers'] ?? []),
      cursorPositions: Map<String, dynamic>.from(json['cursorPositions'] ?? {}),
    );
  }

  /// Converts EditingSession to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'noteId': noteId,
      'userId': userId,
      'userName': userName,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'activeUsers': activeUsers,
      'cursorPositions': cursorPositions,
    };
  }

  /// Checks if the session is active
  bool get isActive => endTime == null;

  /// Gets session duration
  Duration get duration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  /// Ends the editing session
  void endSession() {
    endTime = DateTime.now();
    save();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EditingSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
