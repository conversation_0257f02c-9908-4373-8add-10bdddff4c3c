// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'personal_note_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NoteLinkAdapter extends TypeAdapter<NoteLink> {
  @override
  final int typeId = 11;

  @override
  NoteLink read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NoteLink(
      id: fields[0] as String,
      sourceNoteId: fields[1] as String,
      targetNoteId: fields[2] as String,
      linkType: fields[3] as NoteLinkType,
      description: fields[4] as String?,
      createdAt: fields[5] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, NoteLink obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.sourceNoteId)
      ..writeByte(2)
      ..write(obj.targetNoteId)
      ..writeByte(3)
      ..write(obj.linkType)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteLinkAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NoteShareInfoAdapter extends TypeAdapter<NoteShareInfo> {
  @override
  final int typeId = 12;

  @override
  NoteShareInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NoteShareInfo(
      id: fields[0] as String,
      noteId: fields[1] as String,
      shareToken: fields[2] as String,
      isPublic: fields[3] as bool,
      expiresAt: fields[4] as DateTime?,
      allowedUsers: (fields[5] as List).cast<String>(),
      allowComments: fields[6] as bool,
      allowEditing: fields[7] as bool,
      createdAt: fields[8] as DateTime?,
      lastAccessed: fields[9] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, NoteShareInfo obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.noteId)
      ..writeByte(2)
      ..write(obj.shareToken)
      ..writeByte(3)
      ..write(obj.isPublic)
      ..writeByte(4)
      ..write(obj.expiresAt)
      ..writeByte(5)
      ..write(obj.allowedUsers)
      ..writeByte(6)
      ..write(obj.allowComments)
      ..writeByte(7)
      ..write(obj.allowEditing)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.lastAccessed);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteShareInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PersonalNoteDataAdapter extends TypeAdapter<PersonalNoteData> {
  @override
  final int typeId = 7;

  @override
  PersonalNoteData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PersonalNoteData(
      id: fields[0] as String,
      guideId: fields[1] as String,
      sectionId: fields[2] as String,
      title: fields[3] as String,
      content: fields[4] as String,
      noteType: fields[5] as NoteType,
      createdAt: fields[6] as DateTime?,
      lastModified: fields[7] as DateTime?,
      tags: (fields[8] as List).cast<String>(),
      isPrivate: fields[9] as bool,
      attachments: (fields[10] as List).cast<String>(),
      reminderDate: fields[11] as DateTime?,
      formattedContent: fields[12] as String?,
      linkedNoteIds: (fields[13] as List).cast<String>(),
      backlinkedNoteIds: (fields[14] as List).cast<String>(),
      shareToken: fields[15] as String?,
      isShared: fields[16] as bool,
      lastSharedAt: fields[17] as DateTime?,
      viewCount: fields[18] as int,
      metadata: (fields[19] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, PersonalNoteData obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.guideId)
      ..writeByte(2)
      ..write(obj.sectionId)
      ..writeByte(3)
      ..write(obj.title)
      ..writeByte(4)
      ..write(obj.content)
      ..writeByte(5)
      ..write(obj.noteType)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.lastModified)
      ..writeByte(8)
      ..write(obj.tags)
      ..writeByte(9)
      ..write(obj.isPrivate)
      ..writeByte(10)
      ..write(obj.attachments)
      ..writeByte(11)
      ..write(obj.reminderDate)
      ..writeByte(12)
      ..write(obj.formattedContent)
      ..writeByte(13)
      ..write(obj.linkedNoteIds)
      ..writeByte(14)
      ..write(obj.backlinkedNoteIds)
      ..writeByte(15)
      ..write(obj.shareToken)
      ..writeByte(16)
      ..write(obj.isShared)
      ..writeByte(17)
      ..write(obj.lastSharedAt)
      ..writeByte(18)
      ..write(obj.viewCount)
      ..writeByte(19)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PersonalNoteDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NoteTypeAdapter extends TypeAdapter<NoteType> {
  @override
  final int typeId = 8;

  @override
  NoteType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return NoteType.text;
      case 1:
        return NoteType.checklist;
      case 2:
        return NoteType.reminder;
      default:
        return NoteType.text;
    }
  }

  @override
  void write(BinaryWriter writer, NoteType obj) {
    switch (obj) {
      case NoteType.text:
        writer.writeByte(0);
        break;
      case NoteType.checklist:
        writer.writeByte(1);
        break;
      case NoteType.reminder:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NoteLinkTypeAdapter extends TypeAdapter<NoteLinkType> {
  @override
  final int typeId = 9;

  @override
  NoteLinkType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return NoteLinkType.reference;
      case 1:
        return NoteLinkType.related;
      case 2:
        return NoteLinkType.followUp;
      case 3:
        return NoteLinkType.prerequisite;
      case 4:
        return NoteLinkType.child;
      case 5:
        return NoteLinkType.parent;
      default:
        return NoteLinkType.reference;
    }
  }

  @override
  void write(BinaryWriter writer, NoteLinkType obj) {
    switch (obj) {
      case NoteLinkType.reference:
        writer.writeByte(0);
        break;
      case NoteLinkType.related:
        writer.writeByte(1);
        break;
      case NoteLinkType.followUp:
        writer.writeByte(2);
        break;
      case NoteLinkType.prerequisite:
        writer.writeByte(3);
        break;
      case NoteLinkType.child:
        writer.writeByte(4);
        break;
      case NoteLinkType.parent:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteLinkTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
