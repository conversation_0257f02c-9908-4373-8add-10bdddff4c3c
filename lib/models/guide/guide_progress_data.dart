import 'package:hive/hive.dart';

part 'guide_progress_data.g.dart';

/// Data model for tracking guide progress at the section level
@HiveType(typeId: 10) // Using typeId 10 to avoid conflicts with existing models
class GuideProgressData extends HiveObject {
  @HiveField(0)
  final String guideId;

  @HiveField(1)
  final String sectionId;

  @HiveField(2)
  final bool isCompleted;

  @HiveField(3)
  final DateTime? completionTimestamp;

  @HiveField(4)
  final int timeSpent; // in seconds

  @HiveField(5)
  final DateTime lastVisited;

  @HiveField(6)
  final double scrollProgress; // 0.0 to 1.0

  @HiveField(7)
  final int visitCount;

  GuideProgressData({
    required this.guideId,
    required this.sectionId,
    required this.isCompleted,
    this.completionTimestamp,
    required this.timeSpent,
    required this.lastVisited,
    this.scrollProgress = 0.0,
    this.visitCount = 1,
  });

  /// Creates a new progress entry for a section
  factory GuideProgressData.newSection({
    required String guideId,
    required String sectionId,
  }) {
    return GuideProgressData(
      guideId: guideId,
      sectionId: sectionId,
      isCompleted: false,
      timeSpent: 0,
      lastVisited: DateTime.now(),
      scrollProgress: 0.0,
      visitCount: 1,
    );
  }

  /// Creates a completed progress entry
  factory GuideProgressData.completed({
    required String guideId,
    required String sectionId,
    required int timeSpent,
    double scrollProgress = 1.0,
    int visitCount = 1,
  }) {
    final now = DateTime.now();
    return GuideProgressData(
      guideId: guideId,
      sectionId: sectionId,
      isCompleted: true,
      completionTimestamp: now,
      timeSpent: timeSpent,
      lastVisited: now,
      scrollProgress: scrollProgress,
      visitCount: visitCount,
    );
  }

  /// Creates a copy with updated fields
  GuideProgressData copyWith({
    String? guideId,
    String? sectionId,
    bool? isCompleted,
    DateTime? completionTimestamp,
    int? timeSpent,
    DateTime? lastVisited,
    double? scrollProgress,
    int? visitCount,
  }) {
    return GuideProgressData(
      guideId: guideId ?? this.guideId,
      sectionId: sectionId ?? this.sectionId,
      isCompleted: isCompleted ?? this.isCompleted,
      completionTimestamp: completionTimestamp ?? this.completionTimestamp,
      timeSpent: timeSpent ?? this.timeSpent,
      lastVisited: lastVisited ?? this.lastVisited,
      scrollProgress: scrollProgress ?? this.scrollProgress,
      visitCount: visitCount ?? this.visitCount,
    );
  }

  /// Updates the progress with new visit data
  GuideProgressData updateVisit({
    int? additionalTimeSpent,
    double? newScrollProgress,
    bool? markCompleted,
  }) {
    final now = DateTime.now();
    final updatedTimeSpent = timeSpent + (additionalTimeSpent ?? 0);
    final updatedScrollProgress = newScrollProgress ?? scrollProgress;
    final shouldComplete = markCompleted ?? 
                          (updatedScrollProgress >= 0.9 && !isCompleted);

    return copyWith(
      timeSpent: updatedTimeSpent,
      lastVisited: now,
      scrollProgress: updatedScrollProgress,
      visitCount: visitCount + 1,
      isCompleted: shouldComplete,
      completionTimestamp: shouldComplete ? now : completionTimestamp,
    );
  }

  /// Generates a unique key for this progress entry
  String get progressKey => '${guideId}_$sectionId';

  /// Checks if the section was recently visited (within last 24 hours)
  bool get isRecentlyVisited {
    final now = DateTime.now();
    final difference = now.difference(lastVisited);
    return difference.inHours < 24;
  }

  /// Gets the completion percentage based on scroll progress
  double get completionPercentage {
    if (isCompleted) return 1.0;
    return scrollProgress.clamp(0.0, 1.0);
  }

  /// Gets formatted time spent as a human-readable string
  String get formattedTimeSpent {
    if (timeSpent < 60) {
      return '${timeSpent}s';
    } else if (timeSpent < 3600) {
      final minutes = (timeSpent / 60).floor();
      final seconds = timeSpent % 60;
      return seconds > 0 ? '${minutes}m ${seconds}s' : '${minutes}m';
    } else {
      final hours = (timeSpent / 3600).floor();
      final minutes = ((timeSpent % 3600) / 60).floor();
      return minutes > 0 ? '${hours}h ${minutes}m' : '${hours}h';
    }
  }

  /// Converts to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'guideId': guideId,
      'sectionId': sectionId,
      'isCompleted': isCompleted,
      'completionTimestamp': completionTimestamp?.toIso8601String(),
      'timeSpent': timeSpent,
      'lastVisited': lastVisited.toIso8601String(),
      'scrollProgress': scrollProgress,
      'visitCount': visitCount,
    };
  }

  /// Creates from JSON
  factory GuideProgressData.fromJson(Map<String, dynamic> json) {
    return GuideProgressData(
      guideId: json['guideId'] ?? '',
      sectionId: json['sectionId'] ?? '',
      isCompleted: json['isCompleted'] ?? false,
      completionTimestamp: json['completionTimestamp'] != null
          ? DateTime.parse(json['completionTimestamp'])
          : null,
      timeSpent: json['timeSpent'] ?? 0,
      lastVisited: DateTime.parse(json['lastVisited']),
      scrollProgress: (json['scrollProgress'] ?? 0.0).toDouble(),
      visitCount: json['visitCount'] ?? 1,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GuideProgressData &&
        other.guideId == guideId &&
        other.sectionId == sectionId;
  }

  @override
  int get hashCode => Object.hash(guideId, sectionId);

  @override
  String toString() {
    return 'GuideProgressData(guideId: $guideId, sectionId: $sectionId, '
           'isCompleted: $isCompleted, scrollProgress: $scrollProgress)';
  }
}
