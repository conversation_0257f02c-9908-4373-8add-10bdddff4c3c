// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'guide_progress_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GuideProgressDataAdapter extends TypeAdapter<GuideProgressData> {
  @override
  final int typeId = 10;

  @override
  GuideProgressData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GuideProgressData(
      guideId: fields[0] as String,
      sectionId: fields[1] as String,
      isCompleted: fields[2] as bool,
      completionTimestamp: fields[3] as DateTime?,
      timeSpent: fields[4] as int,
      lastVisited: fields[5] as DateTime,
      scrollProgress: fields[6] as double,
      visitCount: fields[7] as int,
    );
  }

  @override
  void write(BinaryWriter writer, GuideProgressData obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.guideId)
      ..writeByte(1)
      ..write(obj.sectionId)
      ..writeByte(2)
      ..write(obj.isCompleted)
      ..writeByte(3)
      ..write(obj.completionTimestamp)
      ..writeByte(4)
      ..write(obj.timeSpent)
      ..writeByte(5)
      ..write(obj.lastVisited)
      ..writeByte(6)
      ..write(obj.scrollProgress)
      ..writeByte(7)
      ..write(obj.visitCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GuideProgressDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
