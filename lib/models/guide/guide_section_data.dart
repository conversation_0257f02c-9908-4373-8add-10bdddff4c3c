import 'package:flutter/material.dart';

/// Enumeration for different types of guide sections
enum GuideSectionType {
  text,
  list,
  calculator,
  example,
  definition,
  formula,
  table,
  widget,
}

/// Enumeration for difficulty levels
enum DifficultyLevel {
  beginner,
  intermediate,
  advanced,
}

/// Data model for guide subsections
class GuideSubsection {
  final String id;
  final String title;
  final String content;
  final List<String> items;
  final GuideSectionType type;

  const GuideSubsection({
    required this.id,
    required this.title,
    required this.content,
    this.items = const [],
    this.type = GuideSectionType.text,
  });

  factory GuideSubsection.fromJson(Map<String, dynamic> json) {
    return GuideSubsection(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      items: List<String>.from(json['items'] ?? []),
      type: _parseType(json['type']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'items': items,
      'type': type.name,
    };
  }

  static GuideSectionType _parseType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'list':
        return GuideSectionType.list;
      case 'calculator':
        return GuideSectionType.calculator;
      case 'example':
        return GuideSectionType.example;
      case 'definition':
        return GuideSectionType.definition;
      case 'formula':
        return GuideSectionType.formula;
      case 'table':
        return GuideSectionType.table;
      case 'widget':
        return GuideSectionType.widget;
      default:
        return GuideSectionType.text;
    }
  }
}

/// Comprehensive data model for guide sections
class GuideSectionData {
  final String id;
  final String title;
  final String content;
  final List<String> items;
  final List<GuideSubsection> subsections;
  final GuideSectionType type;
  final int estimatedReadTime; // in minutes
  final String searchableContent;
  final Widget? customWidget;
  final Map<String, dynamic>? metadata;
  
  // New fields for enhanced functionality
  final DifficultyLevel difficultyLevel;
  final List<String> prerequisites; // Section IDs that should be completed first
  final int estimatedDifficulty; // 1-5 scale
  final List<String> skillsRequired; // Skills needed for this section

  const GuideSectionData({
    required this.id,
    required this.title,
    required this.content,
    this.items = const [],
    this.subsections = const [],
    this.type = GuideSectionType.text,
    this.estimatedReadTime = 5,
    String? searchableContent,
    this.customWidget,
    this.metadata,
    this.difficultyLevel = DifficultyLevel.beginner,
    this.prerequisites = const [],
    this.estimatedDifficulty = 1,
    this.skillsRequired = const [],
  }) : searchableContent = searchableContent ?? '';

  factory GuideSectionData.fromJson(Map<String, dynamic> json) {
    final subsectionsList = (json['subsections'] as List<dynamic>?)
        ?.map((subsection) => GuideSubsection.fromJson(subsection))
        .toList() ?? [];

    final content = json['content'] ?? '';
    final title = json['title'] ?? '';
    final items = List<String>.from(json['items'] ?? []);
    
    // Build searchable content from all text content
    final searchableContent = _buildSearchableContent(
      title, 
      content, 
      items, 
      subsectionsList,
    );

    return GuideSectionData(
      id: json['id'] ?? '',
      title: title,
      content: content,
      items: items,
      subsections: subsectionsList,
      type: GuideSubsection._parseType(json['type']),
      estimatedReadTime: json['estimatedReadTime'] ?? _calculateReadTime(content, items),
      searchableContent: searchableContent,
      metadata: json['metadata'],
      difficultyLevel: _parseDifficultyLevel(json['difficultyLevel']),
      prerequisites: List<String>.from(json['prerequisites'] ?? []),
      estimatedDifficulty: (json['estimatedDifficulty'] ?? 1).clamp(1, 5),
      skillsRequired: List<String>.from(json['skillsRequired'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'items': items,
      'subsections': subsections.map((s) => s.toJson()).toList(),
      'type': type.name,
      'estimatedReadTime': estimatedReadTime,
      'searchableContent': searchableContent,
      'metadata': metadata,
      'difficultyLevel': difficultyLevel.name,
      'prerequisites': prerequisites,
      'estimatedDifficulty': estimatedDifficulty,
      'skillsRequired': skillsRequired,
    };
  }

  /// Creates a copy with updated fields
  GuideSectionData copyWith({
    String? id,
    String? title,
    String? content,
    List<String>? items,
    List<GuideSubsection>? subsections,
    GuideSectionType? type,
    int? estimatedReadTime,
    String? searchableContent,
    Widget? customWidget,
    Map<String, dynamic>? metadata,
    DifficultyLevel? difficultyLevel,
    List<String>? prerequisites,
    int? estimatedDifficulty,
    List<String>? skillsRequired,
  }) {
    return GuideSectionData(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      items: items ?? this.items,
      subsections: subsections ?? this.subsections,
      type: type ?? this.type,
      estimatedReadTime: estimatedReadTime ?? this.estimatedReadTime,
      searchableContent: searchableContent ?? this.searchableContent,
      customWidget: customWidget ?? this.customWidget,
      metadata: metadata ?? this.metadata,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      prerequisites: prerequisites ?? this.prerequisites,
      estimatedDifficulty: estimatedDifficulty ?? this.estimatedDifficulty,
      skillsRequired: skillsRequired ?? this.skillsRequired,
    );
  }

  /// Builds searchable content from all text elements
  static String _buildSearchableContent(
    String title,
    String content,
    List<String> items,
    List<GuideSubsection> subsections,
  ) {
    final buffer = StringBuffer();
    buffer.write(title);
    buffer.write(' ');
    buffer.write(content);
    
    for (final item in items) {
      buffer.write(' ');
      buffer.write(item);
    }
    
    for (final subsection in subsections) {
      buffer.write(' ');
      buffer.write(subsection.title);
      buffer.write(' ');
      buffer.write(subsection.content);
      for (final item in subsection.items) {
        buffer.write(' ');
        buffer.write(item);
      }
    }
    
    return buffer.toString().toLowerCase();
  }

  /// Calculates estimated read time based on content length
  static int _calculateReadTime(String content, List<String> items) {
    final wordCount = content.split(' ').length + 
                     items.fold(0, (sum, item) => sum + item.split(' ').length);
    // Average reading speed: 200 words per minute
    return (wordCount / 200).ceil().clamp(1, 30);
  }

  /// Parses difficulty level from string
  static DifficultyLevel _parseDifficultyLevel(String? levelString) {
    switch (levelString?.toLowerCase()) {
      case 'intermediate':
      case 'intermédiaire':
        return DifficultyLevel.intermediate;
      case 'advanced':
      case 'avancé':
        return DifficultyLevel.advanced;
      default:
        return DifficultyLevel.beginner;
    }
  }

  /// Checks if prerequisites are met
  bool hasPrerequisitesMet(List<String> completedSections) {
    return prerequisites.every((prerequisite) => 
        completedSections.contains(prerequisite));
  }

  /// Gets color for difficulty level
  Color getDifficultyColor() {
    switch (difficultyLevel) {
      case DifficultyLevel.beginner:
        return Colors.green;
      case DifficultyLevel.intermediate:
        return Colors.orange;
      case DifficultyLevel.advanced:
        return Colors.red;
    }
  }

  /// Gets localized difficulty label
  String getDifficultyLabel() {
    switch (difficultyLevel) {
      case DifficultyLevel.beginner:
        return 'Débutant';
      case DifficultyLevel.intermediate:
        return 'Intermédiaire';
      case DifficultyLevel.advanced:
        return 'Avancé';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GuideSectionData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'GuideSectionData(id: $id, title: $title, type: $type)';
  }
}
