import 'package:flutter/material.dart';

@immutable
class QuizData {
  final String title;
  final String description;
  final String version;
  final String lastUpdated;
  final List<QuizCategory> categories;

  const QuizData({
    required this.title,
    required this.description,
    required this.version,
    required this.lastUpdated,
    required this.categories,
  });

  factory QuizData.fromJson(Map<String, dynamic> json) {
    return QuizData(
      title: json['title'] as String,
      description: json['description'] as String,
      version: json['version'] as String,
      lastUpdated: json['lastUpdated'] as String,
      categories: (json['categories'] as List)
          .map((c) => QuizCategory.fromJson(c))
          .toList(),
    );
  }
}

@immutable
class QuizQuestion {
  final String question;
  final List<String> options;
  final int correct;
  final String explanation;
  final String? reference;
  final int difficulty;
  final String topic; // New field for topic identification
  final List<String> tags; // New field for additional categorization
  final int reviewCount; // New field for tracking review attempts
  final DateTime? lastReviewed; // New field for spaced repetition scheduling

  const QuizQuestion({
    required this.question,
    required this.options,
    required this.correct,
    required this.explanation,
    this.reference,
    this.difficulty = 1,
    required this.topic,
    this.tags = const [],
    this.reviewCount = 0,
    this.lastReviewed,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      question: json['question'] as String,
      options: List<String>.from(json['options']),
      correct: json['correct'] as int,
      explanation: json['explanation'] as String,
      reference: json['reference'] as String?,
      difficulty: json['difficulty'] as int? ?? 1,
      topic: json['topic'] as String? ?? _extractTopicFromReference(json['reference'] as String?),
      tags: json['tags'] != null ? List<String>.from(json['tags']) : const [],
      reviewCount: json['reviewCount'] as int? ?? 0,
      lastReviewed: json['lastReviewed'] != null 
          ? DateTime.parse(json['lastReviewed'] as String)
          : null,
    );
  }

  // Helper method to extract topic from reference field or category context
  static String _extractTopicFromReference(String? reference) {
    if (reference == null) return 'Général';
    
    // Extract topic from reference patterns like "TVA - Exonérations" or "IS - Base imposable"
    if (reference.contains(' - ')) {
      return reference.split(' - ').first.trim();
    }
    
    // Extract topic from reference patterns like "Article 12 TVA"
    if (reference.toLowerCase().contains('tva')) return 'TVA';
    if (reference.toLowerCase().contains('is')) return 'IS';
    if (reference.toLowerCase().contains('ir')) return 'IR';
    if (reference.toLowerCase().contains('cnss')) return 'CNSS';
    if (reference.toLowerCase().contains('droits')) return 'Droits d\'enregistrement';
    
    return 'Général';
  }

  // Helper method to get topic from reference field
  String getTopicFromReference() {
    return _extractTopicFromReference(reference);
  }

  // Helper method to check if question is eligible for review based on spaced repetition
  bool isEligibleForReview() {
    if (lastReviewed == null) return reviewCount > 0; // Has been answered incorrectly before
    
    final daysSinceReview = DateTime.now().difference(lastReviewed!).inDays;
    // Basic spaced repetition intervals: 1, 3, 7, 14, 30 days
    final reviewInterval = _calculateReviewInterval();
    
    return daysSinceReview >= reviewInterval;
  }

  // Calculate review interval based on review count and performance
  int _calculateReviewInterval() {
    switch (reviewCount) {
      case 0:
      case 1:
        return 1; // Review after 1 day
      case 2:
        return 3; // Review after 3 days
      case 3:
        return 7; // Review after 1 week
      case 4:
        return 14; // Review after 2 weeks
      default:
        return 30; // Review after 1 month
    }
  }

  // Create a copy with updated review information
  QuizQuestion copyWithReviewUpdate({
    int? reviewCount,
    DateTime? lastReviewed,
  }) {
    return QuizQuestion(
      question: question,
      options: options,
      correct: correct,
      explanation: explanation,
      reference: reference,
      difficulty: difficulty,
      topic: topic,
      tags: tags,
      reviewCount: reviewCount ?? this.reviewCount,
      lastReviewed: lastReviewed ?? this.lastReviewed,
    );
  }
}

@immutable
class QuizLevel {
  final String level;
  final String? icon;
  final int pointsPerQuestion;
  final int timePerQuestion;
  final List<QuizQuestion> questions;

  const QuizLevel({
    required this.level,
    this.icon,
    this.pointsPerQuestion = 10,
    this.timePerQuestion = 30,
    required this.questions,
  });

  factory QuizLevel.fromJson(Map<String, dynamic> json) {
    return QuizLevel(
      level: json['level'] as String,
      icon: json['icon'] as String?,
      pointsPerQuestion: json['pointsPerQuestion'] as int? ?? 10,
      timePerQuestion: json['timePerQuestion'] as int? ?? 30,
      questions: (json['questions'] as List)
          .map((q) => QuizQuestion.fromJson(q))
          .toList(),
    );
  }
  
  IconData getIconData() {
    switch (icon) {
      case 'school':
        return Icons.school;
      case 'trending_up':
        return Icons.trending_up;
      case 'psychology':
        return Icons.psychology;
      default:
        return Icons.quiz;
    }
  }
}

@immutable
class QuizCategory {
  final String name;
  final String? icon;
  final String? color;
  final String? description;
  final List<QuizLevel> levels;

  const QuizCategory({
    required this.name,
    this.icon,
    this.color,
    this.description,
    required this.levels,
  });

  factory QuizCategory.fromJson(Map<String, dynamic> json) {
    return QuizCategory(
      name: json['name'] as String,
      icon: json['icon'] as String?,
      color: json['color'] as String?,
      description: json['description'] as String?,
      levels: (json['levels'] as List)
          .map((l) => QuizLevel.fromJson(l))
          .toList(),
    );
  }
  
  Color getColor() {
    if (color != null) {
      try {
        return Color(int.parse(color!.substring(1), radix: 16) | 0xFF000000);
      } catch (e) {
        return Colors.blue;
      }
    }
    return Colors.blue;
  }
  
  IconData getIconData() {
    switch (icon) {
      case 'tax':
        return Icons.account_balance;
      case 'receipt_long':
        return Icons.receipt_long;
      case 'business':
        return Icons.business;
      case 'star':
        return Icons.star;
      case 'account_balance':
        return Icons.account_balance;
      default:
        return Icons.quiz;
    }
  }
}
