class TerminationRates {
  final List<IndemnityRate> indemnityRates;
  final PreAvisRates preavis;

  const TerminationRates({
    required this.indemnityRates,
    required this.preavis,
  });

  factory TerminationRates.fromJson(Map<String, dynamic> json) {
    return TerminationRates(
      indemnityRates: (json['indemnity_rates'] as List)
          .map((e) => IndemnityRate.fromJson(e))
          .toList(),
      preavis: PreAvisRates.fromJson(json['preavis']),
    );
  }
}

class IndemnityRate {
  final int? yearsMax;
  final int hoursPerYear;
  final int? maxMonths;

  const IndemnityRate({
    this.yearsMax,
    required this.hoursPerYear,
    this.maxMonths,
  });

  factory IndemnityRate.fromJson(Map<String, dynamic> json) {
    return IndemnityRate(
      yearsMax: json['years_max'],
      hoursPerYear: json['hours_per_year'],
      maxMonths: json['max_months'],
    );
  }
}

class PreAvisRates {
  final List<PreAvisRate> cadre;
  final List<PreAvisRate> salarie;

  const PreAvisRates({
    required this.cadre,
    required this.salarie,
  });

  factory PreAvisRates.fromJson(Map<String, dynamic> json) {
    return PreAvisRates(
      cadre:
          (json['cadre'] as List).map((e) => PreAvisRate.fromJson(e)).toList(),
      salarie: (json['salarie'] as List)
          .map((e) => PreAvisRate.fromJson(e))
          .toList(),
    );
  }
}

class PreAvisRate {
  final int? yearsMax;
  final int months;
  final int days;

  const PreAvisRate({
    this.yearsMax,
    required this.months,
    required this.days,
  });

  factory PreAvisRate.fromJson(Map<String, dynamic> json) {
    return PreAvisRate(
      yearsMax: json['years_max'],
      months: json['months'],
      days: json['days'],
    );
  }
}
