class TaxRates {
  final List<IRBracket> irBrackets;
  final FamilyCharges familyCharges;
  final ProfessionalExpenses professionalExpenses;

  const TaxRates({
    required this.irBrackets,
    required this.familyCharges,
    required this.professionalExpenses,
  });

  factory TaxRates.fromJson(Map<String, dynamic> json) {
    return TaxRates(
      irBrackets: (json['ir_brackets'] as List)
          .map((e) => IRBracket.fromJson(e))
          .toList(),
      familyCharges: FamilyCharges.fromJson(json['family_charges']),
      professionalExpenses:
          ProfessionalExpenses.fromJson(json['professional_expenses']),
    );
  }
}

class IRBracket {
  final double min;
  final num? max;
  final double rate;
  final double deduction;

  const IRBracket({
    required this.min,
    this.max,
    required this.rate,
    required this.deduction,
  });

  factory IRBracket.fromJson(Map<String, dynamic> json) {
    return IRBracket(
      min: json['min'].toDouble(),
      max: json['max']?.toDouble(),
      rate: json['rate'].toDouble(),
      deduction: json['deduction'].toDouble(),
    );
  }
}

class FamilyCharges {
  final double spouse;
  final double child1;
  final double child2;
  final double child3;
  final double child4;
  final double child5;
  final double child6;
  final int maxChildren;

  const FamilyCharges({
    required this.spouse,
    required this.child1,
    required this.child2,
    required this.child3,
    required this.child4,
    required this.child5,
    required this.child6,
    required this.maxChildren,
  });

  factory FamilyCharges.fromJson(Map<String, dynamic> json) {
    return FamilyCharges(
      spouse: json['spouse'].toDouble(),
      child1: json['child_1'].toDouble(),
      child2: json['child_2'].toDouble(),
      child3: json['child_3'].toDouble(),
      child4: json['child_4'].toDouble(),
      child5: json['child_5'].toDouble(),
      child6: json['child_6'].toDouble(),
      maxChildren: json['max_children'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'spouse': spouse,
      'child_1': child1,
      'child_2': child2,
      'child_3': child3,
      'child_4': child4,
      'child_5': child5,
      'child_6': child6,
      'max_children': maxChildren,
    };
  }
}

class ProfessionalExpensesTier {
  final double? maxMonthlySBI;
  final double rate;
  final double max;

  const ProfessionalExpensesTier({
    this.maxMonthlySBI,
    required this.rate,
    required this.max,
  });

  factory ProfessionalExpensesTier.fromJson(Map<String, dynamic> json) {
    return ProfessionalExpensesTier(
      maxMonthlySBI: json['max_monthly_sbi']?.toDouble(),
      rate: json['rate'].toDouble(),
      max: json['max'].toDouble(),
    );
  }
}

class ProfessionalExpenses {
  final List<ProfessionalExpensesTier> tiers;

  const ProfessionalExpenses({
    required this.tiers,
  });

  factory ProfessionalExpenses.fromJson(Map<String, dynamic> json) {
    return ProfessionalExpenses(
      tiers: (json['tiers'] as List)
          .map((e) => ProfessionalExpensesTier.fromJson(e))
          .toList(),
    );
  }

  (double rate, double max) getTierForSalary(double monthlySalary) {
    final tier = tiers.firstWhere(
      (t) => t.maxMonthlySBI == null || monthlySalary <= t.maxMonthlySBI!,
      orElse: () => tiers.last,
    );
    return (tier.rate, tier.max);
  }
}
