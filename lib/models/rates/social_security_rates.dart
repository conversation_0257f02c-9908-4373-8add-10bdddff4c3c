class SocialSecurityRates {
  final ContributionRates cnss;
  final ContributionRates amo;

  const SocialSecurityRates({
    required this.cnss,
    required this.amo,
  });

  factory SocialSecurityRates.fromJson(Map<String, dynamic> json) {
    return SocialSecurityRates(
      cnss: ContributionRates.fromJson(json['cnss']),
      amo: ContributionRates.fromJson(json['amo']),
    );
  }
}

class ContributionRates {
  final ContributionRate employee;
  final ContributionRate employer;

  const ContributionRates({
    required this.employee,
    required this.employer,
  });

  factory ContributionRates.fromJson(Map<String, dynamic> json) {
    return ContributionRates(
      employee: ContributionRate.from<PERSON><PERSON>(json['employee']),
      employer: ContributionRate.from<PERSON><PERSON>(json['employer']),
    );
  }
}

class ContributionRate {
  final double rate;
  final double? ceiling;

  const ContributionRate({
    required this.rate,
    this.ceiling,
  });

  factory ContributionRate.fromJson(Map<String, dynamic> json) {
    return ContributionRate(
      rate: json['rate'].toDouble(),
      ceiling: json['ceiling']?.toDouble(),
    );
  }
}
