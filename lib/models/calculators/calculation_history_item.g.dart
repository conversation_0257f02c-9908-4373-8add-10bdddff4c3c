// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calculation_history_item.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CalculationHistoryItemAdapter
    extends TypeAdapter<CalculationHistoryItem> {
  @override
  final int typeId = 108;

  @override
  CalculationHistoryItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CalculationHistoryItem(
      id: fields[0] as String,
      calculatorType: fields[1] as CalculatorType,
      inputDataJson: fields[2] as String,
      resultDataJson: fields[3] as String,
      createdAt: fields[4] as DateTime,
      name: fields[5] as String?,
      tags: (fields[6] as List).cast<String>(),
      isFavorite: fields[7] as bool,
      metadata: (fields[8] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, CalculationHistoryItem obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.calculatorType)
      ..writeByte(2)
      ..write(obj.inputDataJson)
      ..writeByte(3)
      ..write(obj.resultDataJson)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.name)
      ..writeByte(6)
      ..write(obj.tags)
      ..writeByte(7)
      ..write(obj.isFavorite)
      ..writeByte(8)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CalculationHistoryItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CalculatorTypeAdapter extends TypeAdapter<CalculatorType> {
  @override
  final int typeId = 109;

  @override
  CalculatorType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CalculatorType.salary;
      case 1:
        return CalculatorType.depreciation;
      case 2:
        return CalculatorType.financialRatios;
      case 3:
        return CalculatorType.taxOptimization;
      case 4:
        return CalculatorType.termination;
      case 5:
        return CalculatorType.corporateTax;
      case 6:
        return CalculatorType.tva;
      default:
        return CalculatorType.salary;
    }
  }

  @override
  void write(BinaryWriter writer, CalculatorType obj) {
    switch (obj) {
      case CalculatorType.salary:
        writer.writeByte(0);
        break;
      case CalculatorType.depreciation:
        writer.writeByte(1);
        break;
      case CalculatorType.financialRatios:
        writer.writeByte(2);
        break;
      case CalculatorType.taxOptimization:
        writer.writeByte(3);
        break;
      case CalculatorType.termination:
        writer.writeByte(4);
        break;
      case CalculatorType.corporateTax:
        writer.writeByte(5);
        break;
      case CalculatorType.tva:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CalculatorTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CalculationHistoryItem _$CalculationHistoryItemFromJson(
        Map<String, dynamic> json) =>
    CalculationHistoryItem(
      id: json['id'] as String,
      calculatorType:
          $enumDecode(_$CalculatorTypeEnumMap, json['calculatorType']),
      inputDataJson: json['inputDataJson'] as String,
      resultDataJson: json['resultDataJson'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      name: json['name'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      isFavorite: json['isFavorite'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$CalculationHistoryItemToJson(
        CalculationHistoryItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'calculatorType': _$CalculatorTypeEnumMap[instance.calculatorType]!,
      'inputDataJson': instance.inputDataJson,
      'resultDataJson': instance.resultDataJson,
      'createdAt': instance.createdAt.toIso8601String(),
      'name': instance.name,
      'tags': instance.tags,
      'isFavorite': instance.isFavorite,
      'metadata': instance.metadata,
    };

const _$CalculatorTypeEnumMap = {
  CalculatorType.salary: 'salary',
  CalculatorType.depreciation: 'depreciation',
  CalculatorType.financialRatios: 'financialRatios',
  CalculatorType.taxOptimization: 'taxOptimization',
  CalculatorType.termination: 'termination',
  CalculatorType.corporateTax: 'corporateTax',
  CalculatorType.tva: 'tva',
};
