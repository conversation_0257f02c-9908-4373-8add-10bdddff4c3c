import 'package:json_annotation/json_annotation.dart';
import '../immobilisations/amortization_row.dart';

part 'enhanced_depreciation_data.g.dart';

@JsonSerializable()
class EnhancedDepreciationInput {
  final String assetName;
  final double assetCost;
  final double residualValue;
  final int usefulLifeYears;
  final DepreciationMethod method;
  final DateTime acquisitionDate;
  final double? degressiveCoefficient;
  final double? totalUnits;
  final List<double>? annualUnitsProduced;
  final bool midYearConvention;

  const EnhancedDepreciationInput({
    required this.assetName,
    required this.assetCost,
    required this.residualValue,
    required this.usefulLifeYears,
    required this.method,
    required this.acquisitionDate,
    this.degressiveCoefficient,
    this.totalUnits,
    this.annualUnitsProduced,
    this.midYearConvention = false,
  });

  factory EnhancedDepreciationInput.fromJson(Map<String, dynamic> json) =>
      _$EnhancedDepreciationInputFromJson(json);

  Map<String, dynamic> toJson() => _$EnhancedDepreciationInputToJson(this);

  EnhancedDepreciationInput copyWith({
    String? assetName,
    double? assetCost,
    double? residualValue,
    int? usefulLifeYears,
    DepreciationMethod? method,
    DateTime? acquisitionDate,
    double? degressiveCoefficient,
    double? totalUnits,
    List<double>? annualUnitsProduced,
    bool? midYearConvention,
  }) {
    return EnhancedDepreciationInput(
      assetName: assetName ?? this.assetName,
      assetCost: assetCost ?? this.assetCost,
      residualValue: residualValue ?? this.residualValue,
      usefulLifeYears: usefulLifeYears ?? this.usefulLifeYears,
      method: method ?? this.method,
      acquisitionDate: acquisitionDate ?? this.acquisitionDate,
      degressiveCoefficient: degressiveCoefficient ?? this.degressiveCoefficient,
      totalUnits: totalUnits ?? this.totalUnits,
      annualUnitsProduced: annualUnitsProduced ?? this.annualUnitsProduced,
      midYearConvention: midYearConvention ?? this.midYearConvention,
    );
  }

  double get depreciableAmount => assetCost - residualValue;

  bool get isValid {
    if (assetCost <= 0 || usefulLifeYears <= 0) return false;
    if (residualValue < 0 || residualValue >= assetCost) return false;
    
    switch (method) {
      case DepreciationMethod.degressive:
        return degressiveCoefficient != null && degressiveCoefficient! > 1.0;
      case DepreciationMethod.unitsOfProduction:
        return totalUnits != null && totalUnits! > 0;
      default:
        return true;
    }
  }
}

@JsonSerializable()
class EnhancedDepreciationResult {
  final List<AmortizationRow> amortizationTable;
  final DepreciationSummary summary;
  final List<DepreciationComparison>? methodComparisons;
  final TaxOptimizationAdvice? taxAdvice;

  const EnhancedDepreciationResult({
    required this.amortizationTable,
    required this.summary,
    this.methodComparisons,
    this.taxAdvice,
  });

  factory EnhancedDepreciationResult.fromJson(Map<String, dynamic> json) =>
      _$EnhancedDepreciationResultFromJson(json);

  Map<String, dynamic> toJson() => _$EnhancedDepreciationResultToJson(this);
}

@JsonSerializable()
class DepreciationSummary {
  final double totalDepreciation;
  final double remainingValue;
  final double averageAnnualDepreciation;
  final int totalYears;
  final DepreciationMethod method;
  final double firstYearDepreciation;
  final double lastYearDepreciation;

  const DepreciationSummary({
    required this.totalDepreciation,
    required this.remainingValue,
    required this.averageAnnualDepreciation,
    required this.totalYears,
    required this.method,
    required this.firstYearDepreciation,
    required this.lastYearDepreciation,
  });

  factory DepreciationSummary.fromJson(Map<String, dynamic> json) =>
      _$DepreciationSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$DepreciationSummaryToJson(this);

  String get methodDisplayName => method.displayName;

  String get formattedTotalDepreciation => 
      '${totalDepreciation.toStringAsFixed(2)} DH';

  String get formattedRemainingValue => 
      '${remainingValue.toStringAsFixed(2)} DH';

  String get formattedAverageAnnual => 
      '${averageAnnualDepreciation.toStringAsFixed(2)} DH';
}

@JsonSerializable()
class DepreciationComparison {
  final DepreciationMethod method;
  final double totalDepreciation;
  final double firstYearDepreciation;
  final double netPresentValue;
  final double taxBenefit;
  final String recommendation;

  const DepreciationComparison({
    required this.method,
    required this.totalDepreciation,
    required this.firstYearDepreciation,
    required this.netPresentValue,
    required this.taxBenefit,
    required this.recommendation,
  });

  factory DepreciationComparison.fromJson(Map<String, dynamic> json) =>
      _$DepreciationComparisonFromJson(json);

  Map<String, dynamic> toJson() => _$DepreciationComparisonToJson(this);

  String get methodDisplayName => method.displayName;
}

@JsonSerializable()
class TaxOptimizationAdvice {
  final String recommendedMethod;
  final double estimatedTaxSavings;
  final String reasoning;
  final List<String> considerations;
  final String implementationTiming;

  const TaxOptimizationAdvice({
    required this.recommendedMethod,
    required this.estimatedTaxSavings,
    required this.reasoning,
    required this.considerations,
    required this.implementationTiming,
  });

  factory TaxOptimizationAdvice.fromJson(Map<String, dynamic> json) =>
      _$TaxOptimizationAdviceFromJson(json);

  Map<String, dynamic> toJson() => _$TaxOptimizationAdviceToJson(this);
}

enum DepreciationMethod {
  linear,
  degressive,
  sumOfYearsDigits,
  unitsOfProduction;

  String get displayName {
    switch (this) {
      case DepreciationMethod.linear:
        return 'Linéaire';
      case DepreciationMethod.degressive:
        return 'Dégressive';
      case DepreciationMethod.sumOfYearsDigits:
        return 'Somme des chiffres des années';
      case DepreciationMethod.unitsOfProduction:
        return 'Unités de production';
    }
  }

  String get description {
    switch (this) {
      case DepreciationMethod.linear:
        return 'Amortissement constant sur toute la durée de vie';
      case DepreciationMethod.degressive:
        return 'Amortissement plus important les premières années';
      case DepreciationMethod.sumOfYearsDigits:
        return 'Amortissement dégressif basé sur la somme des années';
      case DepreciationMethod.unitsOfProduction:
        return 'Amortissement basé sur l\'utilisation réelle';
    }
  }

  bool get requiresCoefficient => this == DepreciationMethod.degressive;
  bool get requiresUnits => this == DepreciationMethod.unitsOfProduction;
}

@JsonSerializable()
class AssetPreset {
  final String name;
  final String category;
  final int defaultUsefulLife;
  final double defaultResidualValuePercent;
  final DepreciationMethod recommendedMethod;
  final String description;

  const AssetPreset({
    required this.name,
    required this.category,
    required this.defaultUsefulLife,
    required this.defaultResidualValuePercent,
    required this.recommendedMethod,
    required this.description,
  });

  factory AssetPreset.fromJson(Map<String, dynamic> json) =>
      _$AssetPresetFromJson(json);

  Map<String, dynamic> toJson() => _$AssetPresetToJson(this);

  static List<AssetPreset> get commonPresets => [
    const AssetPreset(
      name: 'Véhicule de tourisme',
      category: 'Transport',
      defaultUsefulLife: 5,
      defaultResidualValuePercent: 10,
      recommendedMethod: DepreciationMethod.degressive,
      description: 'Voiture de fonction ou véhicule commercial',
    ),
    const AssetPreset(
      name: 'Matériel informatique',
      category: 'Informatique',
      defaultUsefulLife: 3,
      defaultResidualValuePercent: 5,
      recommendedMethod: DepreciationMethod.degressive,
      description: 'Ordinateurs, serveurs, équipements IT',
    ),
    const AssetPreset(
      name: 'Mobilier de bureau',
      category: 'Mobilier',
      defaultUsefulLife: 10,
      defaultResidualValuePercent: 10,
      recommendedMethod: DepreciationMethod.linear,
      description: 'Bureaux, chaises, armoires',
    ),
    const AssetPreset(
      name: 'Machine industrielle',
      category: 'Production',
      defaultUsefulLife: 10,
      defaultResidualValuePercent: 15,
      recommendedMethod: DepreciationMethod.unitsOfProduction,
      description: 'Équipement de production industrielle',
    ),
    const AssetPreset(
      name: 'Bâtiment commercial',
      category: 'Immobilier',
      defaultUsefulLife: 20,
      defaultResidualValuePercent: 20,
      recommendedMethod: DepreciationMethod.linear,
      description: 'Locaux commerciaux et industriels',
    ),
  ];
}
