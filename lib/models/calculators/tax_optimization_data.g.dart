// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tax_optimization_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaxOptimizationInput _$TaxOptimizationInputFromJson(
        Map<String, dynamic> json) =>
    TaxOptimizationInput(
      companyProfile: CompanyProfile.fromJson(
          json['companyProfile'] as Map<String, dynamic>),
      currentTaxSituation: CurrentTaxSituation.fromJson(
          json['currentTaxSituation'] as Map<String, dynamic>),
      optimizationGoals: OptimizationGoals.fromJson(
          json['optimizationGoals'] as Map<String, dynamic>),
      constraints: OptimizationConstraints.fromJson(
          json['constraints'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TaxOptimizationInputToJson(
        TaxOptimizationInput instance) =>
    <String, dynamic>{
      'companyProfile': instance.companyProfile,
      'currentTaxSituation': instance.currentTaxSituation,
      'optimizationGoals': instance.optimizationGoals,
      'constraints': instance.constraints,
    };

CompanyProfile _$CompanyProfileFromJson(Map<String, dynamic> json) =>
    CompanyProfile(
      annualRevenue: (json['annualRevenue'] as num).toDouble(),
      sector: $enumDecode(_$BusinessSectorEnumMap, json['sector']),
      employeeCount: (json['employeeCount'] as num).toInt(),
      legalForm: $enumDecode(_$LegalFormEnumMap, json['legalForm']),
      yearsInBusiness: (json['yearsInBusiness'] as num).toInt(),
      isExporter: json['isExporter'] as bool? ?? false,
      hasRnD: json['hasRnD'] as bool? ?? false,
      region: json['region'] as String,
    );

Map<String, dynamic> _$CompanyProfileToJson(CompanyProfile instance) =>
    <String, dynamic>{
      'annualRevenue': instance.annualRevenue,
      'sector': _$BusinessSectorEnumMap[instance.sector]!,
      'employeeCount': instance.employeeCount,
      'legalForm': _$LegalFormEnumMap[instance.legalForm]!,
      'yearsInBusiness': instance.yearsInBusiness,
      'isExporter': instance.isExporter,
      'hasRnD': instance.hasRnD,
      'region': instance.region,
    };

const _$BusinessSectorEnumMap = {
  BusinessSector.manufacturing: 'manufacturing',
  BusinessSector.services: 'services',
  BusinessSector.retail: 'retail',
  BusinessSector.technology: 'technology',
  BusinessSector.agriculture: 'agriculture',
  BusinessSector.construction: 'construction',
  BusinessSector.finance: 'finance',
  BusinessSector.healthcare: 'healthcare',
  BusinessSector.education: 'education',
  BusinessSector.other: 'other',
};

const _$LegalFormEnumMap = {
  LegalForm.sarl: 'sarl',
  LegalForm.sa: 'sa',
  LegalForm.sas: 'sas',
  LegalForm.snc: 'snc',
  LegalForm.scs: 'scs',
  LegalForm.individual: 'individual',
};

CurrentTaxSituation _$CurrentTaxSituationFromJson(Map<String, dynamic> json) =>
    CurrentTaxSituation(
      accountingResult: (json['accountingResult'] as num).toDouble(),
      currentTaxableIncome: (json['currentTaxableIncome'] as num).toDouble(),
      currentISTax: (json['currentISTax'] as num).toDouble(),
      existingDeductions: (json['existingDeductions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      currentRegime: $enumDecode(_$TaxRegimeEnumMap, json['currentRegime']),
      provisionsAmount: (json['provisionsAmount'] as num).toDouble(),
      depreciationAmount: (json['depreciationAmount'] as num).toDouble(),
    );

Map<String, dynamic> _$CurrentTaxSituationToJson(
        CurrentTaxSituation instance) =>
    <String, dynamic>{
      'accountingResult': instance.accountingResult,
      'currentTaxableIncome': instance.currentTaxableIncome,
      'currentISTax': instance.currentISTax,
      'existingDeductions': instance.existingDeductions,
      'currentRegime': _$TaxRegimeEnumMap[instance.currentRegime]!,
      'provisionsAmount': instance.provisionsAmount,
      'depreciationAmount': instance.depreciationAmount,
    };

const _$TaxRegimeEnumMap = {
  TaxRegime.normal: 'normal',
  TaxRegime.simplified: 'simplified',
  TaxRegime.microEnterprise: 'microEnterprise',
  TaxRegime.autoEntrepreneur: 'autoEntrepreneur',
};

OptimizationGoals _$OptimizationGoalsFromJson(Map<String, dynamic> json) =>
    OptimizationGoals(
      objectives: (json['objectives'] as List<dynamic>)
          .map((e) => $enumDecode(_$OptimizationObjectiveEnumMap, e))
          .toList(),
      timeHorizonYears: (json['timeHorizonYears'] as num).toInt(),
      targetTaxSavings: (json['targetTaxSavings'] as num).toDouble(),
      prioritizeCashFlow: json['prioritizeCashFlow'] as bool? ?? true,
      prioritizeCompliance: json['prioritizeCompliance'] as bool? ?? true,
    );

Map<String, dynamic> _$OptimizationGoalsToJson(OptimizationGoals instance) =>
    <String, dynamic>{
      'objectives': instance.objectives
          .map((e) => _$OptimizationObjectiveEnumMap[e]!)
          .toList(),
      'timeHorizonYears': instance.timeHorizonYears,
      'targetTaxSavings': instance.targetTaxSavings,
      'prioritizeCashFlow': instance.prioritizeCashFlow,
      'prioritizeCompliance': instance.prioritizeCompliance,
    };

const _$OptimizationObjectiveEnumMap = {
  OptimizationObjective.minimizeTax: 'minimizeTax',
  OptimizationObjective.optimizeCashFlow: 'optimizeCashFlow',
  OptimizationObjective.planInvestments: 'planInvestments',
  OptimizationObjective.improveCompliance: 'improveCompliance',
  OptimizationObjective.reduceRisk: 'reduceRisk',
};

OptimizationConstraints _$OptimizationConstraintsFromJson(
        Map<String, dynamic> json) =>
    OptimizationConstraints(
      maxBudget: (json['maxBudget'] as num).toDouble(),
      timingPreferences: (json['timingPreferences'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      riskTolerance: $enumDecode(_$RiskToleranceEnumMap, json['riskTolerance']),
      requiresAuditCompliance:
          json['requiresAuditCompliance'] as bool? ?? false,
      excludedStrategies: (json['excludedStrategies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$OptimizationConstraintsToJson(
        OptimizationConstraints instance) =>
    <String, dynamic>{
      'maxBudget': instance.maxBudget,
      'timingPreferences': instance.timingPreferences,
      'riskTolerance': _$RiskToleranceEnumMap[instance.riskTolerance]!,
      'requiresAuditCompliance': instance.requiresAuditCompliance,
      'excludedStrategies': instance.excludedStrategies,
    };

const _$RiskToleranceEnumMap = {
  RiskTolerance.conservative: 'conservative',
  RiskTolerance.moderate: 'moderate',
  RiskTolerance.aggressive: 'aggressive',
};

TaxOptimizationResult _$TaxOptimizationResultFromJson(
        Map<String, dynamic> json) =>
    TaxOptimizationResult(
      currentAnalysis: CurrentTaxAnalysis.fromJson(
          json['currentAnalysis'] as Map<String, dynamic>),
      recommendedStrategies: (json['recommendedStrategies'] as List<dynamic>)
          .map((e) => OptimizationStrategy.fromJson(e as Map<String, dynamic>))
          .toList(),
      scenarios: (json['scenarios'] as List<dynamic>)
          .map((e) => OptimizationScenario.fromJson(e as Map<String, dynamic>))
          .toList(),
      implementationPlan: ImplementationPlan.fromJson(
          json['implementationPlan'] as Map<String, dynamic>),
      riskAssessment: RiskAssessment.fromJson(
          json['riskAssessment'] as Map<String, dynamic>),
      projectedSavings: ProjectedSavings.fromJson(
          json['projectedSavings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TaxOptimizationResultToJson(
        TaxOptimizationResult instance) =>
    <String, dynamic>{
      'currentAnalysis': instance.currentAnalysis,
      'recommendedStrategies': instance.recommendedStrategies,
      'scenarios': instance.scenarios,
      'implementationPlan': instance.implementationPlan,
      'riskAssessment': instance.riskAssessment,
      'projectedSavings': instance.projectedSavings,
    };

CurrentTaxAnalysis _$CurrentTaxAnalysisFromJson(Map<String, dynamic> json) =>
    CurrentTaxAnalysis(
      effectiveTaxRate: (json['effectiveTaxRate'] as num).toDouble(),
      marginalTaxRate: (json['marginalTaxRate'] as num).toDouble(),
      identifiedIssues: (json['identifiedIssues'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      missedOpportunities: (json['missedOpportunities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      benchmarkComparison: (json['benchmarkComparison'] as num).toDouble(),
    );

Map<String, dynamic> _$CurrentTaxAnalysisToJson(CurrentTaxAnalysis instance) =>
    <String, dynamic>{
      'effectiveTaxRate': instance.effectiveTaxRate,
      'marginalTaxRate': instance.marginalTaxRate,
      'identifiedIssues': instance.identifiedIssues,
      'missedOpportunities': instance.missedOpportunities,
      'benchmarkComparison': instance.benchmarkComparison,
    };

OptimizationStrategy _$OptimizationStrategyFromJson(
        Map<String, dynamic> json) =>
    OptimizationStrategy(
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$StrategyTypeEnumMap, json['type']),
      estimatedSavings: (json['estimatedSavings'] as num).toDouble(),
      implementationCost: (json['implementationCost'] as num).toDouble(),
      timeToImplement: (json['timeToImplement'] as num).toInt(),
      riskLevel: $enumDecode(_$RiskLevelEnumMap, json['riskLevel']),
      requirements: (json['requirements'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      benefits:
          (json['benefits'] as List<dynamic>).map((e) => e as String).toList(),
      legalBasis: json['legalBasis'] as String,
    );

Map<String, dynamic> _$OptimizationStrategyToJson(
        OptimizationStrategy instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'type': _$StrategyTypeEnumMap[instance.type]!,
      'estimatedSavings': instance.estimatedSavings,
      'implementationCost': instance.implementationCost,
      'timeToImplement': instance.timeToImplement,
      'riskLevel': _$RiskLevelEnumMap[instance.riskLevel]!,
      'requirements': instance.requirements,
      'benefits': instance.benefits,
      'legalBasis': instance.legalBasis,
    };

const _$StrategyTypeEnumMap = {
  StrategyType.deduction: 'deduction',
  StrategyType.timing: 'timing',
  StrategyType.structure: 'structure',
  StrategyType.investment: 'investment',
  StrategyType.regime: 'regime',
};

const _$RiskLevelEnumMap = {
  RiskLevel.low: 'low',
  RiskLevel.medium: 'medium',
  RiskLevel.high: 'high',
  RiskLevel.critical: 'critical',
};

OptimizationScenario _$OptimizationScenarioFromJson(
        Map<String, dynamic> json) =>
    OptimizationScenario(
      name: json['name'] as String,
      strategiesIncluded: (json['strategiesIncluded'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      totalSavings: (json['totalSavings'] as num).toDouble(),
      totalCost: (json['totalCost'] as num).toDouble(),
      netBenefit: (json['netBenefit'] as num).toDouble(),
      implementationTimeMonths:
          (json['implementationTimeMonths'] as num).toInt(),
      overallRisk: $enumDecode(_$RiskLevelEnumMap, json['overallRisk']),
      yearlyProjections:
          (json['yearlyProjections'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(int.parse(k), (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$OptimizationScenarioToJson(
        OptimizationScenario instance) =>
    <String, dynamic>{
      'name': instance.name,
      'strategiesIncluded': instance.strategiesIncluded,
      'totalSavings': instance.totalSavings,
      'totalCost': instance.totalCost,
      'netBenefit': instance.netBenefit,
      'implementationTimeMonths': instance.implementationTimeMonths,
      'overallRisk': _$RiskLevelEnumMap[instance.overallRisk]!,
      'yearlyProjections':
          instance.yearlyProjections.map((k, e) => MapEntry(k.toString(), e)),
    };

ImplementationPlan _$ImplementationPlanFromJson(Map<String, dynamic> json) =>
    ImplementationPlan(
      phases: (json['phases'] as List<dynamic>)
          .map((e) => ImplementationPhase.fromJson(e as Map<String, dynamic>))
          .toList(),
      milestones: (json['milestones'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, DateTime.parse(e as String)),
      ),
      criticalPath: (json['criticalPath'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      totalDuration: (json['totalDuration'] as num).toDouble(),
    );

Map<String, dynamic> _$ImplementationPlanToJson(ImplementationPlan instance) =>
    <String, dynamic>{
      'phases': instance.phases,
      'milestones':
          instance.milestones.map((k, e) => MapEntry(k, e.toIso8601String())),
      'criticalPath': instance.criticalPath,
      'totalDuration': instance.totalDuration,
    };

ImplementationPhase _$ImplementationPhaseFromJson(Map<String, dynamic> json) =>
    ImplementationPhase(
      name: json['name'] as String,
      description: json['description'] as String,
      orderIndex: (json['orderIndex'] as num).toInt(),
      durationMonths: (json['durationMonths'] as num).toInt(),
      tasks: (json['tasks'] as List<dynamic>).map((e) => e as String).toList(),
      dependencies: (json['dependencies'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      cost: (json['cost'] as num).toDouble(),
    );

Map<String, dynamic> _$ImplementationPhaseToJson(
        ImplementationPhase instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'orderIndex': instance.orderIndex,
      'durationMonths': instance.durationMonths,
      'tasks': instance.tasks,
      'dependencies': instance.dependencies,
      'cost': instance.cost,
    };

RiskAssessment _$RiskAssessmentFromJson(Map<String, dynamic> json) =>
    RiskAssessment(
      overallRisk: $enumDecode(_$RiskLevelEnumMap, json['overallRisk']),
      riskFactors: (json['riskFactors'] as List<dynamic>)
          .map((e) => RiskFactor.fromJson(e as Map<String, dynamic>))
          .toList(),
      mitigationStrategies: (json['mitigationStrategies'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      confidenceLevel: (json['confidenceLevel'] as num).toDouble(),
    );

Map<String, dynamic> _$RiskAssessmentToJson(RiskAssessment instance) =>
    <String, dynamic>{
      'overallRisk': _$RiskLevelEnumMap[instance.overallRisk]!,
      'riskFactors': instance.riskFactors,
      'mitigationStrategies': instance.mitigationStrategies,
      'confidenceLevel': instance.confidenceLevel,
    };

RiskFactor _$RiskFactorFromJson(Map<String, dynamic> json) => RiskFactor(
      name: json['name'] as String,
      description: json['description'] as String,
      level: $enumDecode(_$RiskLevelEnumMap, json['level']),
      probability: (json['probability'] as num).toDouble(),
      impact: (json['impact'] as num).toDouble(),
    );

Map<String, dynamic> _$RiskFactorToJson(RiskFactor instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'level': _$RiskLevelEnumMap[instance.level]!,
      'probability': instance.probability,
      'impact': instance.impact,
    };

ProjectedSavings _$ProjectedSavingsFromJson(Map<String, dynamic> json) =>
    ProjectedSavings(
      yearlyTaxSavings: (json['yearlyTaxSavings'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(int.parse(k), (e as num).toDouble()),
      ),
      totalSavings: (json['totalSavings'] as num).toDouble(),
      netPresentValue: (json['netPresentValue'] as num).toDouble(),
      internalRateOfReturn: (json['internalRateOfReturn'] as num).toDouble(),
      paybackPeriodMonths: (json['paybackPeriodMonths'] as num).toInt(),
    );

Map<String, dynamic> _$ProjectedSavingsToJson(ProjectedSavings instance) =>
    <String, dynamic>{
      'yearlyTaxSavings':
          instance.yearlyTaxSavings.map((k, e) => MapEntry(k.toString(), e)),
      'totalSavings': instance.totalSavings,
      'netPresentValue': instance.netPresentValue,
      'internalRateOfReturn': instance.internalRateOfReturn,
      'paybackPeriodMonths': instance.paybackPeriodMonths,
    };
