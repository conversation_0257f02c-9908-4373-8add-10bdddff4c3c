// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enhanced_depreciation_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnhancedDepreciationInput _$EnhancedDepreciationInputFromJson(
        Map<String, dynamic> json) =>
    EnhancedDepreciationInput(
      assetName: json['assetName'] as String,
      assetCost: (json['assetCost'] as num).toDouble(),
      residualValue: (json['residualValue'] as num).toDouble(),
      usefulLifeYears: (json['usefulLifeYears'] as num).toInt(),
      method: $enumDecode(_$DepreciationMethodEnumMap, json['method']),
      acquisitionDate: DateTime.parse(json['acquisitionDate'] as String),
      degressiveCoefficient:
          (json['degressiveCoefficient'] as num?)?.toDouble(),
      totalUnits: (json['totalUnits'] as num?)?.toDouble(),
      annualUnitsProduced: (json['annualUnitsProduced'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      midYearConvention: json['midYearConvention'] as bool? ?? false,
    );

Map<String, dynamic> _$EnhancedDepreciationInputToJson(
        EnhancedDepreciationInput instance) =>
    <String, dynamic>{
      'assetName': instance.assetName,
      'assetCost': instance.assetCost,
      'residualValue': instance.residualValue,
      'usefulLifeYears': instance.usefulLifeYears,
      'method': _$DepreciationMethodEnumMap[instance.method]!,
      'acquisitionDate': instance.acquisitionDate.toIso8601String(),
      'degressiveCoefficient': instance.degressiveCoefficient,
      'totalUnits': instance.totalUnits,
      'annualUnitsProduced': instance.annualUnitsProduced,
      'midYearConvention': instance.midYearConvention,
    };

const _$DepreciationMethodEnumMap = {
  DepreciationMethod.linear: 'linear',
  DepreciationMethod.degressive: 'degressive',
  DepreciationMethod.sumOfYearsDigits: 'sumOfYearsDigits',
  DepreciationMethod.unitsOfProduction: 'unitsOfProduction',
};

EnhancedDepreciationResult _$EnhancedDepreciationResultFromJson(
        Map<String, dynamic> json) =>
    EnhancedDepreciationResult(
      amortizationTable: (json['amortizationTable'] as List<dynamic>)
          .map((e) => AmortizationRow.fromJson(e as Map<String, dynamic>))
          .toList(),
      summary:
          DepreciationSummary.fromJson(json['summary'] as Map<String, dynamic>),
      methodComparisons: (json['methodComparisons'] as List<dynamic>?)
          ?.map(
              (e) => DepreciationComparison.fromJson(e as Map<String, dynamic>))
          .toList(),
      taxAdvice: json['taxAdvice'] == null
          ? null
          : TaxOptimizationAdvice.fromJson(
              json['taxAdvice'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EnhancedDepreciationResultToJson(
        EnhancedDepreciationResult instance) =>
    <String, dynamic>{
      'amortizationTable': instance.amortizationTable,
      'summary': instance.summary,
      'methodComparisons': instance.methodComparisons,
      'taxAdvice': instance.taxAdvice,
    };

DepreciationSummary _$DepreciationSummaryFromJson(Map<String, dynamic> json) =>
    DepreciationSummary(
      totalDepreciation: (json['totalDepreciation'] as num).toDouble(),
      remainingValue: (json['remainingValue'] as num).toDouble(),
      averageAnnualDepreciation:
          (json['averageAnnualDepreciation'] as num).toDouble(),
      totalYears: (json['totalYears'] as num).toInt(),
      method: $enumDecode(_$DepreciationMethodEnumMap, json['method']),
      firstYearDepreciation: (json['firstYearDepreciation'] as num).toDouble(),
      lastYearDepreciation: (json['lastYearDepreciation'] as num).toDouble(),
    );

Map<String, dynamic> _$DepreciationSummaryToJson(
        DepreciationSummary instance) =>
    <String, dynamic>{
      'totalDepreciation': instance.totalDepreciation,
      'remainingValue': instance.remainingValue,
      'averageAnnualDepreciation': instance.averageAnnualDepreciation,
      'totalYears': instance.totalYears,
      'method': _$DepreciationMethodEnumMap[instance.method]!,
      'firstYearDepreciation': instance.firstYearDepreciation,
      'lastYearDepreciation': instance.lastYearDepreciation,
    };

DepreciationComparison _$DepreciationComparisonFromJson(
        Map<String, dynamic> json) =>
    DepreciationComparison(
      method: $enumDecode(_$DepreciationMethodEnumMap, json['method']),
      totalDepreciation: (json['totalDepreciation'] as num).toDouble(),
      firstYearDepreciation: (json['firstYearDepreciation'] as num).toDouble(),
      netPresentValue: (json['netPresentValue'] as num).toDouble(),
      taxBenefit: (json['taxBenefit'] as num).toDouble(),
      recommendation: json['recommendation'] as String,
    );

Map<String, dynamic> _$DepreciationComparisonToJson(
        DepreciationComparison instance) =>
    <String, dynamic>{
      'method': _$DepreciationMethodEnumMap[instance.method]!,
      'totalDepreciation': instance.totalDepreciation,
      'firstYearDepreciation': instance.firstYearDepreciation,
      'netPresentValue': instance.netPresentValue,
      'taxBenefit': instance.taxBenefit,
      'recommendation': instance.recommendation,
    };

TaxOptimizationAdvice _$TaxOptimizationAdviceFromJson(
        Map<String, dynamic> json) =>
    TaxOptimizationAdvice(
      recommendedMethod: json['recommendedMethod'] as String,
      estimatedTaxSavings: (json['estimatedTaxSavings'] as num).toDouble(),
      reasoning: json['reasoning'] as String,
      considerations: (json['considerations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      implementationTiming: json['implementationTiming'] as String,
    );

Map<String, dynamic> _$TaxOptimizationAdviceToJson(
        TaxOptimizationAdvice instance) =>
    <String, dynamic>{
      'recommendedMethod': instance.recommendedMethod,
      'estimatedTaxSavings': instance.estimatedTaxSavings,
      'reasoning': instance.reasoning,
      'considerations': instance.considerations,
      'implementationTiming': instance.implementationTiming,
    };

AssetPreset _$AssetPresetFromJson(Map<String, dynamic> json) => AssetPreset(
      name: json['name'] as String,
      category: json['category'] as String,
      defaultUsefulLife: (json['defaultUsefulLife'] as num).toInt(),
      defaultResidualValuePercent:
          (json['defaultResidualValuePercent'] as num).toDouble(),
      recommendedMethod:
          $enumDecode(_$DepreciationMethodEnumMap, json['recommendedMethod']),
      description: json['description'] as String,
    );

Map<String, dynamic> _$AssetPresetToJson(AssetPreset instance) =>
    <String, dynamic>{
      'name': instance.name,
      'category': instance.category,
      'defaultUsefulLife': instance.defaultUsefulLife,
      'defaultResidualValuePercent': instance.defaultResidualValuePercent,
      'recommendedMethod':
          _$DepreciationMethodEnumMap[instance.recommendedMethod]!,
      'description': instance.description,
    };
