import 'package:json_annotation/json_annotation.dart';

part 'tax_optimization_data.g.dart';

@JsonSerializable()
class TaxOptimizationInput {
  final CompanyProfile companyProfile;
  final CurrentTaxSituation currentTaxSituation;
  final OptimizationGoals optimizationGoals;
  final OptimizationConstraints constraints;

  const TaxOptimizationInput({
    required this.companyProfile,
    required this.currentTaxSituation,
    required this.optimizationGoals,
    required this.constraints,
  });

  factory TaxOptimizationInput.fromJson(Map<String, dynamic> json) =>
      _$TaxOptimizationInputFromJson(json);

  Map<String, dynamic> toJson() => _$TaxOptimizationInputToJson(this);

  TaxOptimizationInput copyWith({
    CompanyProfile? companyProfile,
    CurrentTaxSituation? currentTaxSituation,
    OptimizationGoals? optimizationGoals,
    OptimizationConstraints? constraints,
  }) {
    return TaxOptimizationInput(
      companyProfile: companyProfile ?? this.companyProfile,
      currentTaxSituation: currentTaxSituation ?? this.currentTaxSituation,
      optimizationGoals: optimizationGoals ?? this.optimizationGoals,
      constraints: constraints ?? this.constraints,
    );
  }
}

@JsonSerializable()
class CompanyProfile {
  final double annualRevenue;
  final BusinessSector sector;
  final int employeeCount;
  final LegalForm legalForm;
  final int yearsInBusiness;
  final bool isExporter;
  final bool hasRnD;
  final String region;

  const CompanyProfile({
    required this.annualRevenue,
    required this.sector,
    required this.employeeCount,
    required this.legalForm,
    required this.yearsInBusiness,
    this.isExporter = false,
    this.hasRnD = false,
    required this.region,
  });

  factory CompanyProfile.fromJson(Map<String, dynamic> json) =>
      _$CompanyProfileFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyProfileToJson(this);
}

@JsonSerializable()
class CurrentTaxSituation {
  final double accountingResult;
  final double currentTaxableIncome;
  final double currentISTax;
  final List<String> existingDeductions;
  final TaxRegime currentRegime;
  final double provisionsAmount;
  final double depreciationAmount;

  const CurrentTaxSituation({
    required this.accountingResult,
    required this.currentTaxableIncome,
    required this.currentISTax,
    required this.existingDeductions,
    required this.currentRegime,
    required this.provisionsAmount,
    required this.depreciationAmount,
  });

  factory CurrentTaxSituation.fromJson(Map<String, dynamic> json) =>
      _$CurrentTaxSituationFromJson(json);

  Map<String, dynamic> toJson() => _$CurrentTaxSituationToJson(this);
}

@JsonSerializable()
class OptimizationGoals {
  final List<OptimizationObjective> objectives;
  final int timeHorizonYears;
  final double targetTaxSavings;
  final bool prioritizeCashFlow;
  final bool prioritizeCompliance;

  const OptimizationGoals({
    required this.objectives,
    required this.timeHorizonYears,
    required this.targetTaxSavings,
    this.prioritizeCashFlow = true,
    this.prioritizeCompliance = true,
  });

  factory OptimizationGoals.fromJson(Map<String, dynamic> json) =>
      _$OptimizationGoalsFromJson(json);

  Map<String, dynamic> toJson() => _$OptimizationGoalsToJson(this);
}

@JsonSerializable()
class OptimizationConstraints {
  final double maxBudget;
  final List<String> timingPreferences;
  final RiskTolerance riskTolerance;
  final bool requiresAuditCompliance;
  final List<String> excludedStrategies;

  const OptimizationConstraints({
    required this.maxBudget,
    required this.timingPreferences,
    required this.riskTolerance,
    this.requiresAuditCompliance = false,
    this.excludedStrategies = const [],
  });

  factory OptimizationConstraints.fromJson(Map<String, dynamic> json) =>
      _$OptimizationConstraintsFromJson(json);

  Map<String, dynamic> toJson() => _$OptimizationConstraintsToJson(this);
}

@JsonSerializable()
class TaxOptimizationResult {
  final CurrentTaxAnalysis currentAnalysis;
  final List<OptimizationStrategy> recommendedStrategies;
  final List<OptimizationScenario> scenarios;
  final ImplementationPlan implementationPlan;
  final RiskAssessment riskAssessment;
  final ProjectedSavings projectedSavings;

  const TaxOptimizationResult({
    required this.currentAnalysis,
    required this.recommendedStrategies,
    required this.scenarios,
    required this.implementationPlan,
    required this.riskAssessment,
    required this.projectedSavings,
  });

  factory TaxOptimizationResult.fromJson(Map<String, dynamic> json) =>
      _$TaxOptimizationResultFromJson(json);

  Map<String, dynamic> toJson() => _$TaxOptimizationResultToJson(this);
}

@JsonSerializable()
class CurrentTaxAnalysis {
  final double effectiveTaxRate;
  final double marginalTaxRate;
  final List<String> identifiedIssues;
  final List<String> missedOpportunities;
  final double benchmarkComparison;

  const CurrentTaxAnalysis({
    required this.effectiveTaxRate,
    required this.marginalTaxRate,
    required this.identifiedIssues,
    required this.missedOpportunities,
    required this.benchmarkComparison,
  });

  factory CurrentTaxAnalysis.fromJson(Map<String, dynamic> json) =>
      _$CurrentTaxAnalysisFromJson(json);

  Map<String, dynamic> toJson() => _$CurrentTaxAnalysisToJson(this);
}

@JsonSerializable()
class OptimizationStrategy {
  final String name;
  final String description;
  final StrategyType type;
  final double estimatedSavings;
  final double implementationCost;
  final int timeToImplement;
  final RiskLevel riskLevel;
  final List<String> requirements;
  final List<String> benefits;
  final String legalBasis;

  const OptimizationStrategy({
    required this.name,
    required this.description,
    required this.type,
    required this.estimatedSavings,
    required this.implementationCost,
    required this.timeToImplement,
    required this.riskLevel,
    required this.requirements,
    required this.benefits,
    required this.legalBasis,
  });

  factory OptimizationStrategy.fromJson(Map<String, dynamic> json) =>
      _$OptimizationStrategyFromJson(json);

  Map<String, dynamic> toJson() => _$OptimizationStrategyToJson(this);

  double get netBenefit => estimatedSavings - implementationCost;
  double get roi => implementationCost > 0 ? netBenefit / implementationCost : 0;
}

@JsonSerializable()
class OptimizationScenario {
  final String name;
  final List<String> strategiesIncluded;
  final double totalSavings;
  final double totalCost;
  final double netBenefit;
  final int implementationTimeMonths;
  final RiskLevel overallRisk;
  final Map<int, double> yearlyProjections;

  const OptimizationScenario({
    required this.name,
    required this.strategiesIncluded,
    required this.totalSavings,
    required this.totalCost,
    required this.netBenefit,
    required this.implementationTimeMonths,
    required this.overallRisk,
    required this.yearlyProjections,
  });

  factory OptimizationScenario.fromJson(Map<String, dynamic> json) =>
      _$OptimizationScenarioFromJson(json);

  Map<String, dynamic> toJson() => _$OptimizationScenarioToJson(this);
}

@JsonSerializable()
class ImplementationPlan {
  final List<ImplementationPhase> phases;
  final Map<String, DateTime> milestones;
  final List<String> criticalPath;
  final double totalDuration;

  const ImplementationPlan({
    required this.phases,
    required this.milestones,
    required this.criticalPath,
    required this.totalDuration,
  });

  factory ImplementationPlan.fromJson(Map<String, dynamic> json) =>
      _$ImplementationPlanFromJson(json);

  Map<String, dynamic> toJson() => _$ImplementationPlanToJson(this);
}

@JsonSerializable()
class ImplementationPhase {
  final String name;
  final String description;
  final int orderIndex;
  final int durationMonths;
  final List<String> tasks;
  final List<String> dependencies;
  final double cost;

  const ImplementationPhase({
    required this.name,
    required this.description,
    required this.orderIndex,
    required this.durationMonths,
    required this.tasks,
    required this.dependencies,
    required this.cost,
  });

  factory ImplementationPhase.fromJson(Map<String, dynamic> json) =>
      _$ImplementationPhaseFromJson(json);

  Map<String, dynamic> toJson() => _$ImplementationPhaseToJson(this);
}

@JsonSerializable()
class RiskAssessment {
  final RiskLevel overallRisk;
  final List<RiskFactor> riskFactors;
  final List<String> mitigationStrategies;
  final double confidenceLevel;

  const RiskAssessment({
    required this.overallRisk,
    required this.riskFactors,
    required this.mitigationStrategies,
    required this.confidenceLevel,
  });

  factory RiskAssessment.fromJson(Map<String, dynamic> json) =>
      _$RiskAssessmentFromJson(json);

  Map<String, dynamic> toJson() => _$RiskAssessmentToJson(this);
}

@JsonSerializable()
class RiskFactor {
  final String name;
  final String description;
  final RiskLevel level;
  final double probability;
  final double impact;

  const RiskFactor({
    required this.name,
    required this.description,
    required this.level,
    required this.probability,
    required this.impact,
  });

  factory RiskFactor.fromJson(Map<String, dynamic> json) =>
      _$RiskFactorFromJson(json);

  Map<String, dynamic> toJson() => _$RiskFactorToJson(this);
}

@JsonSerializable()
class ProjectedSavings {
  final Map<int, double> yearlyTaxSavings;
  final double totalSavings;
  final double netPresentValue;
  final double internalRateOfReturn;
  final int paybackPeriodMonths;

  const ProjectedSavings({
    required this.yearlyTaxSavings,
    required this.totalSavings,
    required this.netPresentValue,
    required this.internalRateOfReturn,
    required this.paybackPeriodMonths,
  });

  factory ProjectedSavings.fromJson(Map<String, dynamic> json) =>
      _$ProjectedSavingsFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectedSavingsToJson(this);
}

enum BusinessSector {
  manufacturing,
  services,
  retail,
  technology,
  agriculture,
  construction,
  finance,
  healthcare,
  education,
  other;

  String get displayName {
    switch (this) {
      case BusinessSector.manufacturing:
        return 'Industrie manufacturière';
      case BusinessSector.services:
        return 'Services';
      case BusinessSector.retail:
        return 'Commerce de détail';
      case BusinessSector.technology:
        return 'Technologie';
      case BusinessSector.agriculture:
        return 'Agriculture';
      case BusinessSector.construction:
        return 'Construction';
      case BusinessSector.finance:
        return 'Finance';
      case BusinessSector.healthcare:
        return 'Santé';
      case BusinessSector.education:
        return 'Éducation';
      case BusinessSector.other:
        return 'Autre';
    }
  }
}

enum LegalForm {
  sarl,
  sa,
  sas,
  snc,
  scs,
  individual;

  String get displayName {
    switch (this) {
      case LegalForm.sarl:
        return 'SARL';
      case LegalForm.sa:
        return 'SA';
      case LegalForm.sas:
        return 'SAS';
      case LegalForm.snc:
        return 'SNC';
      case LegalForm.scs:
        return 'SCS';
      case LegalForm.individual:
        return 'Entreprise individuelle';
    }
  }
}

enum TaxRegime {
  normal,
  simplified,
  microEnterprise,
  autoEntrepreneur;

  String get displayName {
    switch (this) {
      case TaxRegime.normal:
        return 'Régime normal';
      case TaxRegime.simplified:
        return 'Régime simplifié';
      case TaxRegime.microEnterprise:
        return 'Micro-entreprise';
      case TaxRegime.autoEntrepreneur:
        return 'Auto-entrepreneur';
    }
  }
}

enum OptimizationObjective {
  minimizeTax,
  optimizeCashFlow,
  planInvestments,
  improveCompliance,
  reduceRisk;

  String get displayName {
    switch (this) {
      case OptimizationObjective.minimizeTax:
        return 'Minimiser l\'impôt';
      case OptimizationObjective.optimizeCashFlow:
        return 'Optimiser la trésorerie';
      case OptimizationObjective.planInvestments:
        return 'Planifier les investissements';
      case OptimizationObjective.improveCompliance:
        return 'Améliorer la conformité';
      case OptimizationObjective.reduceRisk:
        return 'Réduire les risques';
    }
  }
}

enum RiskTolerance {
  conservative,
  moderate,
  aggressive;

  String get displayName {
    switch (this) {
      case RiskTolerance.conservative:
        return 'Conservateur';
      case RiskTolerance.moderate:
        return 'Modéré';
      case RiskTolerance.aggressive:
        return 'Agressif';
    }
  }
}

enum StrategyType {
  deduction,
  timing,
  structure,
  investment,
  regime;

  String get displayName {
    switch (this) {
      case StrategyType.deduction:
        return 'Déduction';
      case StrategyType.timing:
        return 'Timing';
      case StrategyType.structure:
        return 'Structure';
      case StrategyType.investment:
        return 'Investissement';
      case StrategyType.regime:
        return 'Régime';
    }
  }
}

enum RiskLevel {
  low,
  medium,
  high,
  critical;

  String get displayName {
    switch (this) {
      case RiskLevel.low:
        return 'Faible';
      case RiskLevel.medium:
        return 'Moyen';
      case RiskLevel.high:
        return 'Élevé';
      case RiskLevel.critical:
        return 'Critique';
    }
  }
}
