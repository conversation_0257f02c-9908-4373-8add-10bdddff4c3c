import 'package:json_annotation/json_annotation.dart';

part 'financial_ratios_data.g.dart';

@JsonSerializable()
class FinancialRatiosInput {
  // Balance Sheet Data
  final double currentAssets;
  final double currentLiabilities;
  final double totalAssets;
  final double totalEquity;
  final double inventory;
  final double cash;
  final double accountsReceivable;
  final double totalDebt;
  final double longTermDebt;

  // Income Statement Data
  final double revenue;
  final double netIncome;
  final double costOfGoodsSold;
  final double operatingIncome;
  final double interestExpense;
  final double grossProfit;

  const FinancialRatiosInput({
    required this.currentAssets,
    required this.currentLiabilities,
    required this.totalAssets,
    required this.totalEquity,
    required this.inventory,
    required this.cash,
    required this.accountsReceivable,
    required this.totalDebt,
    required this.longTermDebt,
    required this.revenue,
    required this.netIncome,
    required this.costOfGoodsSold,
    required this.operatingIncome,
    required this.interestExpense,
    required this.grossProfit,
  });

  factory FinancialRatiosInput.fromJson(Map<String, dynamic> json) =>
      _$FinancialRatiosInputFromJson(json);

  Map<String, dynamic> toJson() => _$FinancialRatiosInputToJson(this);

  FinancialRatiosInput copyWith({
    double? currentAssets,
    double? currentLiabilities,
    double? totalAssets,
    double? totalEquity,
    double? inventory,
    double? cash,
    double? accountsReceivable,
    double? totalDebt,
    double? longTermDebt,
    double? revenue,
    double? netIncome,
    double? costOfGoodsSold,
    double? operatingIncome,
    double? interestExpense,
    double? grossProfit,
  }) {
    return FinancialRatiosInput(
      currentAssets: currentAssets ?? this.currentAssets,
      currentLiabilities: currentLiabilities ?? this.currentLiabilities,
      totalAssets: totalAssets ?? this.totalAssets,
      totalEquity: totalEquity ?? this.totalEquity,
      inventory: inventory ?? this.inventory,
      cash: cash ?? this.cash,
      accountsReceivable: accountsReceivable ?? this.accountsReceivable,
      totalDebt: totalDebt ?? this.totalDebt,
      longTermDebt: longTermDebt ?? this.longTermDebt,
      revenue: revenue ?? this.revenue,
      netIncome: netIncome ?? this.netIncome,
      costOfGoodsSold: costOfGoodsSold ?? this.costOfGoodsSold,
      operatingIncome: operatingIncome ?? this.operatingIncome,
      interestExpense: interestExpense ?? this.interestExpense,
      grossProfit: grossProfit ?? this.grossProfit,
    );
  }
}

@JsonSerializable()
class FinancialRatiosResult {
  final LiquidityRatios liquidityRatios;
  final ProfitabilityRatios profitabilityRatios;
  final ActivityRatios activityRatios;
  final LeverageRatios leverageRatios;
  final String overallAnalysis;
  final List<String> recommendations;
  final RatioInterpretation interpretation;

  const FinancialRatiosResult({
    required this.liquidityRatios,
    required this.profitabilityRatios,
    required this.activityRatios,
    required this.leverageRatios,
    required this.overallAnalysis,
    required this.recommendations,
    required this.interpretation,
  });

  factory FinancialRatiosResult.fromJson(Map<String, dynamic> json) =>
      _$FinancialRatiosResultFromJson(json);

  Map<String, dynamic> toJson() => _$FinancialRatiosResultToJson(this);
}

@JsonSerializable()
class LiquidityRatios {
  final double currentRatio;
  final double quickRatio;
  final double cashRatio;
  final double workingCapital;

  const LiquidityRatios({
    required this.currentRatio,
    required this.quickRatio,
    required this.cashRatio,
    required this.workingCapital,
  });

  factory LiquidityRatios.fromJson(Map<String, dynamic> json) =>
      _$LiquidityRatiosFromJson(json);

  Map<String, dynamic> toJson() => _$LiquidityRatiosToJson(this);

  String getAnalysis() {
    final buffer = StringBuffer();
    
    if (currentRatio >= 2.0) {
      buffer.writeln('• Ratio de liquidité générale excellent (${currentRatio.toStringAsFixed(2)})');
    } else if (currentRatio >= 1.5) {
      buffer.writeln('• Ratio de liquidité générale bon (${currentRatio.toStringAsFixed(2)})');
    } else if (currentRatio >= 1.0) {
      buffer.writeln('• Ratio de liquidité générale acceptable (${currentRatio.toStringAsFixed(2)})');
    } else {
      buffer.writeln('• Ratio de liquidité générale préoccupant (${currentRatio.toStringAsFixed(2)})');
    }

    if (quickRatio >= 1.0) {
      buffer.writeln('• Ratio de liquidité réduite satisfaisant (${quickRatio.toStringAsFixed(2)})');
    } else {
      buffer.writeln('• Ratio de liquidité réduite à améliorer (${quickRatio.toStringAsFixed(2)})');
    }

    return buffer.toString();
  }
}

@JsonSerializable()
class ProfitabilityRatios {
  final double returnOnAssets;
  final double returnOnEquity;
  final double netProfitMargin;
  final double grossProfitMargin;
  final double operatingMargin;

  const ProfitabilityRatios({
    required this.returnOnAssets,
    required this.returnOnEquity,
    required this.netProfitMargin,
    required this.grossProfitMargin,
    required this.operatingMargin,
  });

  factory ProfitabilityRatios.fromJson(Map<String, dynamic> json) =>
      _$ProfitabilityRatiosFromJson(json);

  Map<String, dynamic> toJson() => _$ProfitabilityRatiosToJson(this);

  String getAnalysis() {
    final buffer = StringBuffer();
    
    if (returnOnAssets >= 0.15) {
      buffer.writeln('• Rentabilité des actifs excellente (${(returnOnAssets * 100).toStringAsFixed(1)}%)');
    } else if (returnOnAssets >= 0.10) {
      buffer.writeln('• Rentabilité des actifs bonne (${(returnOnAssets * 100).toStringAsFixed(1)}%)');
    } else if (returnOnAssets >= 0.05) {
      buffer.writeln('• Rentabilité des actifs moyenne (${(returnOnAssets * 100).toStringAsFixed(1)}%)');
    } else {
      buffer.writeln('• Rentabilité des actifs faible (${(returnOnAssets * 100).toStringAsFixed(1)}%)');
    }

    if (netProfitMargin >= 0.10) {
      buffer.writeln('• Marge nette très bonne (${(netProfitMargin * 100).toStringAsFixed(1)}%)');
    } else if (netProfitMargin >= 0.05) {
      buffer.writeln('• Marge nette correcte (${(netProfitMargin * 100).toStringAsFixed(1)}%)');
    } else {
      buffer.writeln('• Marge nette à améliorer (${(netProfitMargin * 100).toStringAsFixed(1)}%)');
    }

    return buffer.toString();
  }
}

@JsonSerializable()
class ActivityRatios {
  final double inventoryTurnover;
  final double receivablesTurnover;
  final double assetTurnover;
  final double inventoryDays;
  final double receivablesDays;

  const ActivityRatios({
    required this.inventoryTurnover,
    required this.receivablesTurnover,
    required this.assetTurnover,
    required this.inventoryDays,
    required this.receivablesDays,
  });

  factory ActivityRatios.fromJson(Map<String, dynamic> json) =>
      _$ActivityRatiosFromJson(json);

  Map<String, dynamic> toJson() => _$ActivityRatiosToJson(this);

  String getAnalysis() {
    final buffer = StringBuffer();
    
    buffer.writeln('• Rotation des stocks: ${inventoryTurnover.toStringAsFixed(1)} fois/an (${inventoryDays.toStringAsFixed(0)} jours)');
    buffer.writeln('• Rotation des créances: ${receivablesTurnover.toStringAsFixed(1)} fois/an (${receivablesDays.toStringAsFixed(0)} jours)');
    buffer.writeln('• Rotation des actifs: ${assetTurnover.toStringAsFixed(2)} fois/an');

    return buffer.toString();
  }
}

@JsonSerializable()
class LeverageRatios {
  final double debtToEquity;
  final double debtRatio;
  final double interestCoverage;
  final double equityRatio;

  const LeverageRatios({
    required this.debtToEquity,
    required this.debtRatio,
    required this.interestCoverage,
    required this.equityRatio,
  });

  factory LeverageRatios.fromJson(Map<String, dynamic> json) =>
      _$LeverageRatiosFromJson(json);

  Map<String, dynamic> toJson() => _$LeverageRatiosToJson(this);

  String getAnalysis() {
    final buffer = StringBuffer();
    
    if (debtToEquity <= 0.5) {
      buffer.writeln('• Endettement faible (${debtToEquity.toStringAsFixed(2)})');
    } else if (debtToEquity <= 1.0) {
      buffer.writeln('• Endettement modéré (${debtToEquity.toStringAsFixed(2)})');
    } else {
      buffer.writeln('• Endettement élevé (${debtToEquity.toStringAsFixed(2)})');
    }

    if (interestCoverage >= 5.0) {
      buffer.writeln('• Couverture des intérêts excellente (${interestCoverage.toStringAsFixed(1)})');
    } else if (interestCoverage >= 2.5) {
      buffer.writeln('• Couverture des intérêts correcte (${interestCoverage.toStringAsFixed(1)})');
    } else {
      buffer.writeln('• Couverture des intérêts préoccupante (${interestCoverage.toStringAsFixed(1)})');
    }

    return buffer.toString();
  }
}

@JsonSerializable()
class RatioInterpretation {
  final RatioQuality liquidityQuality;
  final RatioQuality profitabilityQuality;
  final RatioQuality activityQuality;
  final RatioQuality leverageQuality;
  final RatioQuality overallQuality;

  const RatioInterpretation({
    required this.liquidityQuality,
    required this.profitabilityQuality,
    required this.activityQuality,
    required this.leverageQuality,
    required this.overallQuality,
  });

  factory RatioInterpretation.fromJson(Map<String, dynamic> json) =>
      _$RatioInterpretationFromJson(json);

  Map<String, dynamic> toJson() => _$RatioInterpretationToJson(this);
}

enum RatioQuality {
  excellent,
  good,
  average,
  poor,
  critical;

  String get displayName {
    switch (this) {
      case RatioQuality.excellent:
        return 'Excellent';
      case RatioQuality.good:
        return 'Bon';
      case RatioQuality.average:
        return 'Moyen';
      case RatioQuality.poor:
        return 'Faible';
      case RatioQuality.critical:
        return 'Critique';
    }
  }
}
