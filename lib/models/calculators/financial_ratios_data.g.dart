// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'financial_ratios_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FinancialRatiosInput _$FinancialRatiosInputFromJson(
        Map<String, dynamic> json) =>
    FinancialRatiosInput(
      currentAssets: (json['currentAssets'] as num).toDouble(),
      currentLiabilities: (json['currentLiabilities'] as num).toDouble(),
      totalAssets: (json['totalAssets'] as num).toDouble(),
      totalEquity: (json['totalEquity'] as num).toDouble(),
      inventory: (json['inventory'] as num).toDouble(),
      cash: (json['cash'] as num).toDouble(),
      accountsReceivable: (json['accountsReceivable'] as num).toDouble(),
      totalDebt: (json['totalDebt'] as num).toDouble(),
      longTermDebt: (json['longTermDebt'] as num).toDouble(),
      revenue: (json['revenue'] as num).toDouble(),
      netIncome: (json['netIncome'] as num).toDouble(),
      costOfGoodsSold: (json['costOfGoodsSold'] as num).toDouble(),
      operatingIncome: (json['operatingIncome'] as num).toDouble(),
      interestExpense: (json['interestExpense'] as num).toDouble(),
      grossProfit: (json['grossProfit'] as num).toDouble(),
    );

Map<String, dynamic> _$FinancialRatiosInputToJson(
        FinancialRatiosInput instance) =>
    <String, dynamic>{
      'currentAssets': instance.currentAssets,
      'currentLiabilities': instance.currentLiabilities,
      'totalAssets': instance.totalAssets,
      'totalEquity': instance.totalEquity,
      'inventory': instance.inventory,
      'cash': instance.cash,
      'accountsReceivable': instance.accountsReceivable,
      'totalDebt': instance.totalDebt,
      'longTermDebt': instance.longTermDebt,
      'revenue': instance.revenue,
      'netIncome': instance.netIncome,
      'costOfGoodsSold': instance.costOfGoodsSold,
      'operatingIncome': instance.operatingIncome,
      'interestExpense': instance.interestExpense,
      'grossProfit': instance.grossProfit,
    };

FinancialRatiosResult _$FinancialRatiosResultFromJson(
        Map<String, dynamic> json) =>
    FinancialRatiosResult(
      liquidityRatios: LiquidityRatios.fromJson(
          json['liquidityRatios'] as Map<String, dynamic>),
      profitabilityRatios: ProfitabilityRatios.fromJson(
          json['profitabilityRatios'] as Map<String, dynamic>),
      activityRatios: ActivityRatios.fromJson(
          json['activityRatios'] as Map<String, dynamic>),
      leverageRatios: LeverageRatios.fromJson(
          json['leverageRatios'] as Map<String, dynamic>),
      overallAnalysis: json['overallAnalysis'] as String,
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      interpretation: RatioInterpretation.fromJson(
          json['interpretation'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FinancialRatiosResultToJson(
        FinancialRatiosResult instance) =>
    <String, dynamic>{
      'liquidityRatios': instance.liquidityRatios,
      'profitabilityRatios': instance.profitabilityRatios,
      'activityRatios': instance.activityRatios,
      'leverageRatios': instance.leverageRatios,
      'overallAnalysis': instance.overallAnalysis,
      'recommendations': instance.recommendations,
      'interpretation': instance.interpretation,
    };

LiquidityRatios _$LiquidityRatiosFromJson(Map<String, dynamic> json) =>
    LiquidityRatios(
      currentRatio: (json['currentRatio'] as num).toDouble(),
      quickRatio: (json['quickRatio'] as num).toDouble(),
      cashRatio: (json['cashRatio'] as num).toDouble(),
      workingCapital: (json['workingCapital'] as num).toDouble(),
    );

Map<String, dynamic> _$LiquidityRatiosToJson(LiquidityRatios instance) =>
    <String, dynamic>{
      'currentRatio': instance.currentRatio,
      'quickRatio': instance.quickRatio,
      'cashRatio': instance.cashRatio,
      'workingCapital': instance.workingCapital,
    };

ProfitabilityRatios _$ProfitabilityRatiosFromJson(Map<String, dynamic> json) =>
    ProfitabilityRatios(
      returnOnAssets: (json['returnOnAssets'] as num).toDouble(),
      returnOnEquity: (json['returnOnEquity'] as num).toDouble(),
      netProfitMargin: (json['netProfitMargin'] as num).toDouble(),
      grossProfitMargin: (json['grossProfitMargin'] as num).toDouble(),
      operatingMargin: (json['operatingMargin'] as num).toDouble(),
    );

Map<String, dynamic> _$ProfitabilityRatiosToJson(
        ProfitabilityRatios instance) =>
    <String, dynamic>{
      'returnOnAssets': instance.returnOnAssets,
      'returnOnEquity': instance.returnOnEquity,
      'netProfitMargin': instance.netProfitMargin,
      'grossProfitMargin': instance.grossProfitMargin,
      'operatingMargin': instance.operatingMargin,
    };

ActivityRatios _$ActivityRatiosFromJson(Map<String, dynamic> json) =>
    ActivityRatios(
      inventoryTurnover: (json['inventoryTurnover'] as num).toDouble(),
      receivablesTurnover: (json['receivablesTurnover'] as num).toDouble(),
      assetTurnover: (json['assetTurnover'] as num).toDouble(),
      inventoryDays: (json['inventoryDays'] as num).toDouble(),
      receivablesDays: (json['receivablesDays'] as num).toDouble(),
    );

Map<String, dynamic> _$ActivityRatiosToJson(ActivityRatios instance) =>
    <String, dynamic>{
      'inventoryTurnover': instance.inventoryTurnover,
      'receivablesTurnover': instance.receivablesTurnover,
      'assetTurnover': instance.assetTurnover,
      'inventoryDays': instance.inventoryDays,
      'receivablesDays': instance.receivablesDays,
    };

LeverageRatios _$LeverageRatiosFromJson(Map<String, dynamic> json) =>
    LeverageRatios(
      debtToEquity: (json['debtToEquity'] as num).toDouble(),
      debtRatio: (json['debtRatio'] as num).toDouble(),
      interestCoverage: (json['interestCoverage'] as num).toDouble(),
      equityRatio: (json['equityRatio'] as num).toDouble(),
    );

Map<String, dynamic> _$LeverageRatiosToJson(LeverageRatios instance) =>
    <String, dynamic>{
      'debtToEquity': instance.debtToEquity,
      'debtRatio': instance.debtRatio,
      'interestCoverage': instance.interestCoverage,
      'equityRatio': instance.equityRatio,
    };

RatioInterpretation _$RatioInterpretationFromJson(Map<String, dynamic> json) =>
    RatioInterpretation(
      liquidityQuality:
          $enumDecode(_$RatioQualityEnumMap, json['liquidityQuality']),
      profitabilityQuality:
          $enumDecode(_$RatioQualityEnumMap, json['profitabilityQuality']),
      activityQuality:
          $enumDecode(_$RatioQualityEnumMap, json['activityQuality']),
      leverageQuality:
          $enumDecode(_$RatioQualityEnumMap, json['leverageQuality']),
      overallQuality:
          $enumDecode(_$RatioQualityEnumMap, json['overallQuality']),
    );

Map<String, dynamic> _$RatioInterpretationToJson(
        RatioInterpretation instance) =>
    <String, dynamic>{
      'liquidityQuality': _$RatioQualityEnumMap[instance.liquidityQuality]!,
      'profitabilityQuality':
          _$RatioQualityEnumMap[instance.profitabilityQuality]!,
      'activityQuality': _$RatioQualityEnumMap[instance.activityQuality]!,
      'leverageQuality': _$RatioQualityEnumMap[instance.leverageQuality]!,
      'overallQuality': _$RatioQualityEnumMap[instance.overallQuality]!,
    };

const _$RatioQualityEnumMap = {
  RatioQuality.excellent: 'excellent',
  RatioQuality.good: 'good',
  RatioQuality.average: 'average',
  RatioQuality.poor: 'poor',
  RatioQuality.critical: 'critical',
};
