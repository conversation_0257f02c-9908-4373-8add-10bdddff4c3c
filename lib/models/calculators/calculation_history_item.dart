import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'calculation_history_item.g.dart';

@HiveType(typeId: 108)
@JsonSerializable()
class CalculationHistoryItem extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final CalculatorType calculatorType;

  @HiveField(2)
  final String inputDataJson;

  @HiveField(3)
  final String resultDataJson;

  @HiveField(4)
  final DateTime createdAt;

  @HiveField(5)
  final String? name;

  @HiveField(6)
  final List<String> tags;

  @HiveField(7)
  final bool isFavorite;

  @HiveField(8)
  final Map<String, dynamic> metadata;

  CalculationHistoryItem({
    required this.id,
    required this.calculatorType,
    required this.inputDataJson,
    required this.resultDataJson,
    required this.createdAt,
    this.name,
    this.tags = const [],
    this.isFavorite = false,
    this.metadata = const {},
  });

  factory CalculationHistoryItem.fromJson(Map<String, dynamic> json) =>
      _$CalculationHistoryItemFromJson(json);

  Map<String, dynamic> toJson() => _$CalculationHistoryItemToJson(this);

  CalculationHistoryItem copyWith({
    String? id,
    CalculatorType? calculatorType,
    String? inputDataJson,
    String? resultDataJson,
    DateTime? createdAt,
    String? name,
    List<String>? tags,
    bool? isFavorite,
    Map<String, dynamic>? metadata,
  }) {
    return CalculationHistoryItem(
      id: id ?? this.id,
      calculatorType: calculatorType ?? this.calculatorType,
      inputDataJson: inputDataJson ?? this.inputDataJson,
      resultDataJson: resultDataJson ?? this.resultDataJson,
      createdAt: createdAt ?? this.createdAt,
      name: name ?? this.name,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      metadata: metadata ?? this.metadata,
    );
  }

  bool matchesQuery(String query) {
    final lowerQuery = query.toLowerCase();
    return (name?.toLowerCase().contains(lowerQuery) ?? false) ||
        tags.any((tag) => tag.toLowerCase().contains(lowerQuery)) ||
        calculatorType.displayName.toLowerCase().contains(lowerQuery) ||
        inputDataJson.toLowerCase().contains(lowerQuery);
  }

  String get displayName => name ?? '${calculatorType.displayName} - ${_formatDate(createdAt)}';

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Factory constructors for different calculator types
  factory CalculationHistoryItem.fromSalaryCalculation({
    required String id,
    required Map<String, dynamic> inputData,
    required Map<String, dynamic> resultData,
    String? name,
    List<String> tags = const [],
  }) {
    return CalculationHistoryItem(
      id: id,
      calculatorType: CalculatorType.salary,
      inputDataJson: _encodeJson(inputData),
      resultDataJson: _encodeJson(resultData),
      createdAt: DateTime.now(),
      name: name,
      tags: tags,
    );
  }

  factory CalculationHistoryItem.fromDepreciationCalculation({
    required String id,
    required Map<String, dynamic> inputData,
    required Map<String, dynamic> resultData,
    String? name,
    List<String> tags = const [],
  }) {
    return CalculationHistoryItem(
      id: id,
      calculatorType: CalculatorType.depreciation,
      inputDataJson: _encodeJson(inputData),
      resultDataJson: _encodeJson(resultData),
      createdAt: DateTime.now(),
      name: name,
      tags: tags,
    );
  }

  factory CalculationHistoryItem.fromFinancialRatiosCalculation({
    required String id,
    required Map<String, dynamic> inputData,
    required Map<String, dynamic> resultData,
    String? name,
    List<String> tags = const [],
  }) {
    return CalculationHistoryItem(
      id: id,
      calculatorType: CalculatorType.financialRatios,
      inputDataJson: _encodeJson(inputData),
      resultDataJson: _encodeJson(resultData),
      createdAt: DateTime.now(),
      name: name,
      tags: tags,
    );
  }

  factory CalculationHistoryItem.fromTaxOptimizationCalculation({
    required String id,
    required Map<String, dynamic> inputData,
    required Map<String, dynamic> resultData,
    String? name,
    List<String> tags = const [],
  }) {
    return CalculationHistoryItem(
      id: id,
      calculatorType: CalculatorType.taxOptimization,
      inputDataJson: _encodeJson(inputData),
      resultDataJson: _encodeJson(resultData),
      createdAt: DateTime.now(),
      name: name,
      tags: tags,
    );
  }

  static String _encodeJson(Map<String, dynamic> data) {
    try {
      return data.toString(); // Simple encoding for now
    } catch (e) {
      return '{}';
    }
  }

  Map<String, dynamic> get inputData {
    try {
      // Simple parsing for now - in production, use proper JSON parsing
      return <String, dynamic>{};
    } catch (e) {
      return <String, dynamic>{};
    }
  }

  Map<String, dynamic> get resultData {
    try {
      // Simple parsing for now - in production, use proper JSON parsing
      return <String, dynamic>{};
    } catch (e) {
      return <String, dynamic>{};
    }
  }
}

@HiveType(typeId: 109)
enum CalculatorType {
  @HiveField(0)
  salary('Calculateur de Salaire'),
  @HiveField(1)
  depreciation('Calculateur d\'Amortissement'),
  @HiveField(2)
  financialRatios('Ratios Financiers'),
  @HiveField(3)
  taxOptimization('Optimisation Fiscale'),
  @HiveField(4)
  termination('Indemnité de Licenciement'),
  @HiveField(5)
  corporateTax('Impôt sur les Sociétés'),
  @HiveField(6)
  tva('TVA');

  const CalculatorType(this.displayName);
  final String displayName;
}

enum ExportFormat {
  pdf('PDF'),
  excel('Excel'),
  csv('CSV');

  const ExportFormat(this.displayName);
  final String displayName;
}
