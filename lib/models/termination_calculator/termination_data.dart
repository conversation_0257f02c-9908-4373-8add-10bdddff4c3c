class TerminationData {
  // Informations de base
  final double lastSalary; // Dernier salaire de base
  final String employeeType; // 'cadre' ou 'salarie'
  final int yearsOfService; // Années de service
  final int monthsOfService; // Mois supplémentaires

  // Indemnités fixes mensuelles (pour calcul du salaire moyen)
  final double transportAllowance;
  final double housingAllowance;
  final double otherAllowances;

  // Congés
  final int remainingPaidLeaves; // Solde des congés payés

  // Motif du licenciement
  final TerminationReason reason;

  // Préavis
  final bool preAvisRespected; // Si le préavis a été respecté

  const TerminationData({
    required this.lastSalary,
    required this.employeeType,
    required this.yearsOfService,
    this.monthsOfService = 0,
    this.transportAllowance = 0,
    this.housingAllowance = 0,
    this.otherAllowances = 0,
    this.remainingPaidLeaves = 0,
    this.reason = TerminationReason.normal,
    this.preAvisRespected = true,
  });
}

enum TerminationReason {
  normal, // Licenciement normal
  fault, // Faute grave
  resignation, // Démission
  retirement, // Retraite
  death, // Dé<PERSON><PERSON>
}
