// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'spaced_repetition_item.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SpacedRepetitionItemAdapter extends TypeAdapter<SpacedRepetitionItem> {
  @override
  final int typeId = 17;

  @override
  SpacedRepetitionItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SpacedRepetitionItem(
      questionId: fields[0] as String,
      categoryName: fields[1] as String,
      topic: fields[2] as String,
      lastResult: fields[3] as bool,
      interval: fields[4] as int,
      nextReviewDate: fields[5] as DateTime,
      easinessFactor: fields[6] as double,
      repetitions: fields[7] as int,
      totalAttempts: fields[8] as int,
      consecutiveCorrect: fields[9] as int,
      createdAt: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SpacedRepetitionItem obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.questionId)
      ..writeByte(1)
      ..write(obj.categoryName)
      ..writeByte(2)
      ..write(obj.topic)
      ..writeByte(3)
      ..write(obj.lastResult)
      ..writeByte(4)
      ..write(obj.interval)
      ..writeByte(5)
      ..write(obj.nextReviewDate)
      ..writeByte(6)
      ..write(obj.easinessFactor)
      ..writeByte(7)
      ..write(obj.repetitions)
      ..writeByte(8)
      ..write(obj.totalAttempts)
      ..writeByte(9)
      ..write(obj.consecutiveCorrect)
      ..writeByte(10)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpacedRepetitionItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
