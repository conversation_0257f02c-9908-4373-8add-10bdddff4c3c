class IsExercicesAvances {
  final String title;
  final List<IsExerciceAvance> sections;

  IsExercicesAvances({
    required this.title,
    required this.sections,
  });

  factory IsExercicesAvances.fromJson(Map<String, dynamic> json) {
    return IsExercicesAvances(
      title: json['title'] as String,
      sections: (json['sections'] as List<dynamic>)
          .map((e) => IsExerciceAvance.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class IsExerciceAvance {
  final String title;
  final String content;
  final String solution;
  final List<JournalEntry> entries;

  IsExerciceAvance({
    required this.title,
    required this.content,
    required this.solution,
    required this.entries,
  });

  factory IsExerciceAvance.fromJson(Map<String, dynamic> json) {
    return IsExerciceAvance(
      title: json['title'] as String,
      content: json['content'] as String,
      solution: json['solution'] as String,
      entries: (json['entries'] as List<dynamic>)
          .map((e) => JournalEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class JournalEntry {
  final String date;
  final List<JournalLine> lines;

  JournalEntry({
    required this.date,
    required this.lines,
  });

  factory JournalEntry.fromJson(Map<String, dynamic> json) {
    return JournalEntry(
      date: json['date'] as String,
      lines: (json['lines'] as List<dynamic>)
          .map((e) => JournalLine.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class JournalLine {
  final String account;
  final String label;
  final String? debit;
  final String? credit;

  JournalLine({
    required this.account,
    required this.label,
    this.debit,
    this.credit,
  });

  factory JournalLine.fromJson(Map<String, dynamic> json) {
    return JournalLine(
      account: json['account'] as String,
      label: json['label'] as String,
      debit: json['debit'] as String?,
      credit: json['credit'] as String?,
    );
  }
}
