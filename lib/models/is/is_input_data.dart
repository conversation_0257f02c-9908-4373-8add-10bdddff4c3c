/// Comprehensive data model for IS calculator inputs
class IsInputData {
  // Basic inputs
  final double accountingResult;
  final double revenue;
  final String revenueType;
  
  // Sector selection
  final String selectedRegime;
  final bool isFirstYear;
  final bool isCmExempted;
  
  // Dynamic inputs
  final Map<String, double> reintegrations;
  final Map<String, double> deductions;

  const IsInputData({
    this.accountingResult = 0.0,
    this.revenue = 0.0,
    this.revenueType = 'normal',
    this.selectedRegime = '',
    this.isFirstYear = false,
    this.isCmExempted = false,
    this.reintegrations = const {},
    this.deductions = const {},
  });

  // Validation methods
  bool get isValid {
    return validationErrors.isEmpty;
  }

  List<String> get validationErrors {
    final List<String> errors = [];
    
    // Basic validation
    if (selectedRegime.isEmpty) {
      errors.add('Veuillez sélectionner un régime fiscal');
    }
    
    if (revenue < 0) {
      errors.add('Le chiffre d\'affaires ne peut pas être négatif');
    }
    
    if (accountingResult < -********* || accountingResult > *********) {
      errors.add('Le résultat comptable doit être entre -100M et +100M MAD');
    }
    
    // Validate reintegrations
    for (final entry in reintegrations.entries) {
      if (entry.value < 0) {
        errors.add('Les réintégrations ne peuvent pas être négatives');
        break;
      }
      if (entry.value > ********) {
        errors.add('Les réintégrations ne peuvent pas dépasser 50M MAD');
        break;
      }
    }
    
    // Validate deductions
    for (final entry in deductions.entries) {
      if (entry.value < 0) {
        errors.add('Les déductions ne peuvent pas être négatives');
        break;
      }
      if (entry.value > ********) {
        errors.add('Les déductions ne peuvent pas dépasser 50M MAD');
        break;
      }
    }
    
    return errors;
  }

  // Calculation helpers
  double get taxableResult {
    return accountingResult + totalReintegrations - totalDeductions;
  }

  double get totalReintegrations {
    return reintegrations.values.fold(0.0, (sum, value) => sum + value);
  }

  double get totalDeductions {
    return deductions.values.fold(0.0, (sum, value) => sum + value);
  }

  // Serialization
  factory IsInputData.fromJson(Map<String, dynamic> json) {
    return IsInputData(
      accountingResult: (json['accountingResult'] as num?)?.toDouble() ?? 0.0,
      revenue: (json['revenue'] as num?)?.toDouble() ?? 0.0,
      revenueType: json['revenueType'] as String? ?? 'normal',
      selectedRegime: json['selectedRegime'] as String? ?? '',
      isFirstYear: json['isFirstYear'] as bool? ?? false,
      isCmExempted: json['isCmExempted'] as bool? ?? false,
      reintegrations: Map<String, double>.from(
        (json['reintegrations'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ) ?? {},
      ),
      deductions: Map<String, double>.from(
        (json['deductions'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ) ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accountingResult': accountingResult,
      'revenue': revenue,
      'revenueType': revenueType,
      'selectedRegime': selectedRegime,
      'isFirstYear': isFirstYear,
      'isCmExempted': isCmExempted,
      'reintegrations': reintegrations,
      'deductions': deductions,
    };
  }

  // CopyWith method for immutable updates
  IsInputData copyWith({
    double? accountingResult,
    double? revenue,
    String? revenueType,
    String? selectedRegime,
    bool? isFirstYear,
    bool? isCmExempted,
    Map<String, double>? reintegrations,
    Map<String, double>? deductions,
  }) {
    return IsInputData(
      accountingResult: accountingResult ?? this.accountingResult,
      revenue: revenue ?? this.revenue,
      revenueType: revenueType ?? this.revenueType,
      selectedRegime: selectedRegime ?? this.selectedRegime,
      isFirstYear: isFirstYear ?? this.isFirstYear,
      isCmExempted: isCmExempted ?? this.isCmExempted,
      reintegrations: reintegrations ?? this.reintegrations,
      deductions: deductions ?? this.deductions,
    );
  }

  // Helper methods for updating maps
  IsInputData addReintegration(String key, double value) {
    final newReintegrations = Map<String, double>.from(reintegrations);
    newReintegrations[key] = value;
    return copyWith(reintegrations: newReintegrations);
  }

  IsInputData removeReintegration(String key) {
    final newReintegrations = Map<String, double>.from(reintegrations);
    newReintegrations.remove(key);
    return copyWith(reintegrations: newReintegrations);
  }

  IsInputData addDeduction(String key, double value) {
    final newDeductions = Map<String, double>.from(deductions);
    newDeductions[key] = value;
    return copyWith(deductions: newDeductions);
  }

  IsInputData removeDeduction(String key) {
    final newDeductions = Map<String, double>.from(deductions);
    newDeductions.remove(key);
    return copyWith(deductions: newDeductions);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is IsInputData &&
        other.accountingResult == accountingResult &&
        other.revenue == revenue &&
        other.revenueType == revenueType &&
        other.selectedRegime == selectedRegime &&
        other.isFirstYear == isFirstYear &&
        other.isCmExempted == isCmExempted &&
        _mapEquals(other.reintegrations, reintegrations) &&
        _mapEquals(other.deductions, deductions);
  }

  @override
  int get hashCode {
    return Object.hash(
      accountingResult,
      revenue,
      revenueType,
      selectedRegime,
      isFirstYear,
      isCmExempted,
      Object.hashAll(reintegrations.entries),
      Object.hashAll(deductions.entries),
    );
  }

  @override
  String toString() {
    return 'IsInputData('
        'accountingResult: $accountingResult, '
        'revenue: $revenue, '
        'revenueType: $revenueType, '
        'selectedRegime: $selectedRegime, '
        'isFirstYear: $isFirstYear, '
        'isCmExempted: $isCmExempted, '
        'reintegrations: $reintegrations, '
        'deductions: $deductions'
        ')';
  }

  // Helper method for map comparison
  bool _mapEquals(Map<String, double> map1, Map<String, double> map2) {
    if (map1.length != map2.length) return false;
    for (final entry in map1.entries) {
      if (!map2.containsKey(entry.key) || map2[entry.key] != entry.value) {
        return false;
      }
    }
    return true;
  }
}