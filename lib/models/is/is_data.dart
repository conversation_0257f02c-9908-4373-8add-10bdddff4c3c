import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';

class IsSection {
  final String title;
  final List<IsCategory>? categories;
  final String? content;

  IsSection({
    required this.title,
    this.categories,
    this.content,
  });

  factory IsSection.fromJson(Map<String, dynamic> json) {
    // Vérifier si nous avons l'ancien format (avec content)
    if (json.containsKey('content')) {
      return IsSection(
        title: json['title'] as String,
        content: json['content'] as String,
      );
    }

    // Nouveau format avec categories
    return IsSection(
      title: json['title'] as String,
      categories: json.containsKey('categories')
          ? (json['categories'] as List<dynamic>)
              .map((e) => IsCategory.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  // Méthode pour obtenir le contenu formaté
  String get formattedContent {
    if (content != null) {
      return content!;
    }

    if (categories == null || categories!.isEmpty) {
      return '';
    }

    final buffer = StringBuffer();

    for (var category in categories!) {
      buffer.writeln('${category.name}:');
      buffer.writeln();

      for (var item in category.items) {
        buffer.writeln('- ${item.title}');
        buffer.writeln('  ${item.description}');

        if (item.condition != null) {
          buffer.writeln('  Condition: ${item.condition}');
        }

        if (item.example != null) {
          buffer.writeln('  Exemple: ${item.example}');
        }

        if (item.documentation != null) {
          buffer.writeln('  Documentation: ${item.documentation}');
        }

        if (item.journalEntries != null) {
          buffer.writeln('  Journal Entries:');
          for (var entry in item.journalEntries!) {
            buffer.writeln('    - $entry');
          }
        }

        buffer.writeln();
      }
    }

    return buffer.toString();
  }
}

class IsCategory {
  final String name;
  final List<IsItem> items;

  IsCategory({
    required this.name,
    required this.items,
  });

  factory IsCategory.fromJson(Map<String, dynamic> json) {
    return IsCategory(
      name: json['name'] as String,
      items: (json['items'] as List<dynamic>)
          .map((e) => IsItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class IsItem {
  final String title;
  final String description;
  final String? condition;
  final String? example;
  final String? documentation;
  final dynamic limit;
  final List<String>? journalEntries;

  IsItem({
    required this.title,
    required this.description,
    this.condition,
    this.example,
    this.documentation,
    this.limit,
    this.journalEntries,
  });

  factory IsItem.fromJson(Map<String, dynamic> json) {
    return IsItem(
      title: json['title'] as String,
      description: json['description'] as String,
      condition: json['condition'] as String?,
      example: json['example'] as String?,
      documentation: json['documentation'] as String?,
      limit: json['limit'],
      journalEntries: json['journal_entries'] != null
          ? List<String>.from(json['journal_entries'] as List)
          : null,
    );
  }
}

class IsSpecialCases {
  final String title;
  final List<IsSpecialCase> items;

  IsSpecialCases({
    required this.title,
    required this.items,
  });

  factory IsSpecialCases.fromJson(Map<String, dynamic> json) {
    return IsSpecialCases(
      title: json['title'] as String,
      items: (json['items'] as List<dynamic>)
          .map((e) => IsSpecialCase.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class IsSpecialCase {
  final String name;
  final String description;
  final List<String> conditions;

  IsSpecialCase({
    required this.name,
    required this.description,
    required this.conditions,
  });

  factory IsSpecialCase.fromJson(Map<String, dynamic> json) {
    return IsSpecialCase(
      name: json['name'] as String,
      description: json['description'] as String,
      conditions: (json['conditions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );
  }
}

class IsData {
  final String title;
  final List<IsSection>? sections;
  final List<IsTauxRegime>? regimes;
  final IsCotisationMinimale? cotisationMinimale;
  final IsSpecialCases? specialCases;

  IsData({
    required this.title,
    this.sections,
    this.regimes,
    this.cotisationMinimale,
    this.specialCases,
  });

  factory IsData.fromJson(Map<String, dynamic> json) {
    return IsData(
      title: json['title'] as String,
      sections: json.containsKey('sections')
          ? (json['sections'] as List<dynamic>)
              .map((e) => IsSection.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      regimes: json.containsKey('regimes')
          ? (json['regimes'] as List<dynamic>)
              .map((e) => IsTauxRegime.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      cotisationMinimale: json.containsKey('cotisation_minimale')
          ? IsCotisationMinimale.fromJson(
              json['cotisation_minimale'] as Map<String, dynamic>)
          : null,
      specialCases: json.containsKey('special_cases')
          ? IsSpecialCases.fromJson(
              json['special_cases'] as Map<String, dynamic>)
          : null,
    );
  }

  static Future<IsData> loadFromAsset(String assetPath) async {
    try {
      developer.log('Loading asset: $assetPath');
      final jsonString = await rootBundle.loadString(assetPath);
      developer.log('Asset loaded, parsing JSON');
      final jsonData = json.decode(jsonString) as Map<String, dynamic>;
      developer.log('JSON parsed successfully');
      return IsData.fromJson(jsonData);
    } catch (e, stackTrace) {
      developer.log(
        'Error loading IS data from asset: $assetPath',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}

class IsTauxRegime {
  final String name;
  final String description;
  final List<IsTranche>? tranches;
  final double? tauxUnique;
  final List<IsSecteur>? secteurs;
  final List<IsProgression>? progression;
  final List<String>? etablissements;

  IsTauxRegime({
    required this.name,
    required this.description,
    this.tranches,
    this.tauxUnique,
    this.secteurs,
    this.progression,
    this.etablissements,
  });

  factory IsTauxRegime.fromJson(Map<String, dynamic> json) {
    // Conversion du taux unique en double si présent
    final dynamic rawTauxUnique = json['taux_unique'];
    final double? tauxUnique = rawTauxUnique == null
        ? null
        : rawTauxUnique is int
            ? rawTauxUnique.toDouble()
            : rawTauxUnique as double;

    return IsTauxRegime(
      name: json['name'] as String,
      description: json['description'] as String,
      tranches: json.containsKey('tranches')
          ? (json['tranches'] as List<dynamic>)
              .map((e) => IsTranche.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      tauxUnique: tauxUnique,
      secteurs: json.containsKey('secteurs')
          ? (json['secteurs'] as List<dynamic>)
              .map((e) => IsSecteur.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      progression: json.containsKey('progression')
          ? (json['progression'] as List<dynamic>)
              .map((e) => IsProgression.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      etablissements: json.containsKey('etablissements')
          ? (json['etablissements'] as List<dynamic>)
              .map((e) => e as String)
              .toList()
          : null,
    );
  }
}

class IsTranche {
  final int min;
  final int? max;
  final double taux;
  final String? commentaire;

  IsTranche({
    required this.min,
    this.max,
    required this.taux,
    this.commentaire,
  });

  factory IsTranche.fromJson(Map<String, dynamic> json) {
    // Conversion du taux en double, qu'il soit fourni comme int ou double
    final dynamic rawTaux = json['taux'];
    final double taux = rawTaux is int ? rawTaux.toDouble() : rawTaux as double;

    return IsTranche(
      min: json['min'] as int,
      max: json['max'] as int?,
      taux: taux,
      commentaire: json['commentaire'] as String?,
    );
  }
}

class IsSecteur {
  final String nom;
  final List<String> conditions;

  IsSecteur({
    required this.nom,
    required this.conditions,
  });

  factory IsSecteur.fromJson(Map<String, dynamic> json) {
    return IsSecteur(
      nom: json['nom'] as String,
      conditions: (json['conditions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );
  }
}

class IsProgression {
  final int annee;
  final double taux;

  IsProgression({
    required this.annee,
    required this.taux,
  });

  factory IsProgression.fromJson(Map<String, dynamic> json) {
    final dynamic rawTaux = json['taux'];
    final double taux = rawTaux is int ? rawTaux.toDouble() : rawTaux as double;

    return IsProgression(
      annee: json['annee'] as int,
      taux: taux,
    );
  }
}

class IsCotisationMinimale {
  final Map<String, IsTaux> taux;
  final IsMinimum minimum;
  final List<IsExoneration> exonerations;

  IsCotisationMinimale({
    required this.taux,
    required this.minimum,
    required this.exonerations,
  });

  factory IsCotisationMinimale.fromJson(Map<String, dynamic> json) {
    final tauxMap = <String, IsTaux>{};
    (json['taux'] as Map<String, dynamic>).forEach((key, value) {
      tauxMap[key] = IsTaux.fromJson(value as Map<String, dynamic>);
    });

    return IsCotisationMinimale(
      taux: tauxMap,
      minimum: IsMinimum.fromJson(json['minimum'] as Map<String, dynamic>),
      exonerations: (json['exonerations'] as List<dynamic>)
          .map((e) => IsExoneration.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class IsTaux {
  final double taux;
  final String description;
  final List<String>? exemples;
  final List<String>? conditions;

  IsTaux({
    required this.taux,
    required this.description,
    this.exemples,
    this.conditions,
  });

  factory IsTaux.fromJson(Map<String, dynamic> json) {
    final dynamic rawTaux = json['taux'];
    final double taux = rawTaux is int ? rawTaux.toDouble() : rawTaux as double;

    return IsTaux(
      taux: taux,
      description: json['description'] as String,
      exemples: json.containsKey('exemples')
          ? (json['exemples'] as List<dynamic>).map((e) => e as String).toList()
          : null,
      conditions: json.containsKey('conditions')
          ? (json['conditions'] as List<dynamic>)
              .map((e) => e as String)
              .toList()
          : null,
    );
  }
}

class IsMinimum {
  final int montant;
  final String description;

  IsMinimum({
    required this.montant,
    required this.description,
  });

  factory IsMinimum.fromJson(Map<String, dynamic> json) {
    return IsMinimum(
      montant: json['montant'] as int,
      description: json['description'] as String,
    );
  }
}

class IsExoneration {
  final String type;
  final List<String> cas;

  IsExoneration({
    required this.type,
    required this.cas,
  });

  factory IsExoneration.fromJson(Map<String, dynamic> json) {
    return IsExoneration(
      type: json['type'] as String,
      cas: (json['cas'] as List<dynamic>).map((e) => e as String).toList(),
    );
  }
}
