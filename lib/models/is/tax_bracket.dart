import '../../../utils/calculation_utils.dart';

/// Model representing a tax bracket for progressive tax calculations
/// Used in IS (Impôt sur les Sociétés) calculations to handle different tax rates
/// applied to different income ranges
class TaxBracket implements Comparable<TaxBracket> {
  /// Minimum amount for this bracket (inclusive)
  final double min;
  
  /// Maximum amount for this bracket (exclusive), null for the highest bracket
  final double? max;
  
  /// Tax rate as a percentage (e.g., 20.0 for 20%)
  final double rate;
  
  /// Human-readable description of this bracket
  final String description;

  const TaxBracket({
    required this.min,
    this.max,
    required this.rate,
    required this.description,
  });

  /// Factory constructor to create a TaxBracket from JSON data
  /// Expected JSON format:
  /// {
  ///   "min": 0,
  ///   "max": 300000, // optional for highest bracket
  ///   "taux": 10.0,
  ///   "description": "Première tranche"
  /// }
  factory TaxBracket.fromJson(Map<String, dynamic> json) {
    final num minValue = json['min'] is num ? json['min'] as num : 0;
    final dynamic maxValue = json['max'];
    final num rateValue = json['taux'] is num ? json['taux'] as num : 0;
    final String desc = json['description'] as String? ?? '';

    return TaxBracket(
      min: minValue.toDouble(),
      max: maxValue is num ? maxValue.toDouble() : null,
      rate: rateValue.toDouble(),
      description: desc,
    );
  }

  /// Convert this TaxBracket to JSON format
  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
      'taux': rate,
      'description': description,
    };
  }

  /// Calculate the tax amount for a given taxable amount within this bracket
  /// Returns the tax amount calculated for the portion that falls within this bracket
  double calculateTaxForAmount(double amount) {
    if (!appliesToAmount(amount)) {
      return 0.0;
    }

    // Calculate the taxable amount within this bracket
    double taxableAmount;
    if (max == null) {
      // Highest bracket - tax all amount above min
      taxableAmount = amount - min;
    } else {
      // Calculate the portion that falls within this bracket
      final bracketSize = max! - min;
      final amountInBracket = amount - min;
      taxableAmount = amountInBracket.clamp(0.0, bracketSize);
    }

    // Calculate tax using the bracket rate
    final taxAmount = CalculationUtils.calculateTax(taxableAmount, rate);
    return CalculationUtils.ensureNonNegative(taxAmount);
  }

  /// Check if this bracket applies to the given amount
  /// Returns true if the amount falls within or above this bracket's range
  bool appliesToAmount(double amount) {
    if (amount < min) {
      return false;
    }
    
    if (max == null) {
      // Highest bracket applies to all amounts above min
      return true;
    }
    
    return amount > min;
  }

  /// Get formatted range string for display
  /// Examples: "0 - 300 000 DH", "300 000 DH et plus"
  String get formattedRange {
    final formattedMin = CalculationUtils.formatMonetary(min, currency: '');
    
    if (max == null) {
      return '$formattedMin DH et plus';
    }
    
    final formattedMax = CalculationUtils.formatMonetary(max!, currency: '');
    return '$formattedMin - $formattedMax DH';
  }

  /// Get formatted rate string for display
  /// Example: "10,00%"
  String get formattedRate {
    return CalculationUtils.formatPercentage(rate, includeSymbol: true);
  }

  /// Get the bracket size (max - min), returns null for highest bracket
  double? get bracketSize {
    return max != null ? max! - min : null;
  }

  /// Check if this is the highest bracket (no upper limit)
  bool get isHighestBracket => max == null;

  /// Check if this is the lowest bracket (starts at 0)
  bool get isLowestBracket => min == 0.0;

  /// Check if this bracket has a zero tax rate (exemption bracket)
  bool get isExemptBracket => rate == 0.0;

  /// Compare brackets for sorting (by minimum amount)
  @override
  int compareTo(TaxBracket other) {
    return min.compareTo(other.min);
  }

  /// Equality comparison based on min, max, and rate
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is TaxBracket &&
        other.min == min &&
        other.max == max &&
        other.rate == rate;
  }

  /// Hash code based on min, max, and rate
  @override
  int get hashCode {
    return Object.hash(min, max, rate);
  }

  /// String representation for debugging
  @override
  String toString() {
    return 'TaxBracket(min: $min, max: $max, rate: $rate%, description: "$description")';
  }

  /// Create a copy of this bracket with optional parameter overrides
  TaxBracket copyWith({
    double? min,
    double? max,
    double? rate,
    String? description,
  }) {
    return TaxBracket(
      min: min ?? this.min,
      max: max ?? this.max,
      rate: rate ?? this.rate,
      description: description ?? this.description,
    );
  }

  /// Validate that this bracket has valid values
  bool get isValid {
    return min >= 0 &&
        (max == null || max! > min) &&
        rate >= 0 &&
        rate <= 100 &&
        description.isNotEmpty;
  }

  /// Get validation errors for this bracket
  List<String> get validationErrors {
    final errors = <String>[];
    
    if (min < 0) {
      errors.add('Le montant minimum doit être positif');
    }
    
    if (max != null && max! <= min) {
      errors.add('Le montant maximum doit être supérieur au minimum');
    }
    
    if (rate < 0 || rate > 100) {
      errors.add('Le taux doit être entre 0% et 100%');
    }
    
    if (description.isEmpty) {
      errors.add('La description ne peut pas être vide');
    }
    
    return errors;
  }

  /// Static method to sort a list of brackets by minimum amount
  static List<TaxBracket> sortBrackets(List<TaxBracket> brackets) {
    final sortedBrackets = List<TaxBracket>.from(brackets);
    sortedBrackets.sort();
    return sortedBrackets;
  }

  /// Static method to validate a list of brackets for consistency
  static bool validateBracketSequence(List<TaxBracket> brackets) {
    if (brackets.isEmpty) return false;
    
    final sorted = sortBrackets(brackets);
    
    for (int i = 0; i < sorted.length; i++) {
      final bracket = sorted[i];
      
      // Check individual bracket validity
      if (!bracket.isValid) return false;
      
      // Check sequence consistency
      if (i > 0) {
        final previousBracket = sorted[i - 1];
        if (previousBracket.max != null && bracket.min != previousBracket.max) {
          return false; // Gap or overlap in brackets
        }
      }
      
      // Last bracket should have no upper limit
      if (i == sorted.length - 1 && bracket.max != null) {
        return false;
      }
    }
    
    return true;
  }

  /// Static method to calculate total progressive tax using a list of brackets
  static double calculateProgressiveTax(
    double taxableAmount,
    List<TaxBracket> brackets,
  ) {
    if (taxableAmount <= 0 || brackets.isEmpty) {
      return 0.0;
    }
    
    final sortedBrackets = sortBrackets(brackets);
    double totalTax = 0.0;
    
    for (final bracket in sortedBrackets) {
      if (bracket.appliesToAmount(taxableAmount)) {
        totalTax += bracket.calculateTaxForAmount(taxableAmount);
      }
    }
    
    return CalculationUtils.roundMonetary(totalTax);
  }
}