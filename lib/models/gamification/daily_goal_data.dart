// DailyGoalData Hive Model for Moroccan Accounting App
// Tracks daily study goals, progress, streaks, and completion status.
// References: adaptive_learning_models.dart

import 'package:hive/hive.dart';

part 'daily_goal_data.g.dart';

@HiveType(typeId: 100)
enum DailyGoalType {
  @HiveField(0)
  timeBased, // minutes
  @HiveField(1)
  sectionBased, // sections
  @HiveField(2)
  quizBased, // quizzes
}

@HiveType(typeId: 101)
class DailyGoalData extends HiveObject {
  @HiveField(0)
  DailyGoalType goalType;

  @HiveField(1)
  int targetValue; // minutes or count

  @HiveField(2)
  int currentProgress;

  @HiveField(3)
  DateTime goalDate;

  @HiveField(4)
  bool isCompleted;

  @HiveField(5)
  int streakCount; // consecutive days meeting goal

  @HiveField(6)
  int bestStreak;

  @HiveField(7)
  DateTime lastResetDate;

  DailyGoalData({
    required this.goalType,
    required this.targetValue,
    this.currentProgress = 0,
    required this.goalDate,
    this.isCompleted = false,
    this.streakCount = 0,
    this.bestStreak = 0,
    DateTime? lastResetDate,
  }) : lastResetDate = lastResetDate ?? DateTime.now();

  // Resets daily progress and completion status
  void resetDaily(DateTime newDate) {
    currentProgress = 0;
    isCompleted = false;
    goalDate = newDate;
    lastResetDate = newDate;
  }

  // Adds progress towards the goal
  void addProgress(int value) {
    if (value < 0) return;
    currentProgress += value;
    if (isGoalMet()) {
      isCompleted = true;
      streakCount += 1;
      if (streakCount > bestStreak) bestStreak = streakCount;
    }
  }

  // Checks if the goal is met
  bool isGoalMet() {
    return currentProgress >= targetValue;
  }

  // Returns progress percentage (0.0 - 1.0)
  double getProgressPercentage() {
    if (targetValue == 0) return 0.0;
    return (currentProgress / targetValue).clamp(0.0, 1.0);
  }

  // Validates goal type and target
  bool isValid() {
    if (targetValue <= 0) return false;
    return true;
  }
}

// End of daily_goal_data.dart
