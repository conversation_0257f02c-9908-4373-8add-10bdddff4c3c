// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'social_share_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SocialShareDataAdapter extends TypeAdapter<SocialShareData> {
  @override
  final int typeId = 106;

  @override
  SocialShareData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SocialShareData(
      achievementId: fields[0] as String,
      shareText: fields[1] as String,
      shareImage: fields[2] as String?,
      shareDate: fields[3] as DateTime,
      platform: fields[4] as SocialPlatform,
      shareCount: fields[5] as int,
      isPrivate: fields[6] as bool,
      userPrefersSharing: fields[7] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, SocialShareData obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.achievementId)
      ..writeByte(1)
      ..write(obj.shareText)
      ..writeByte(2)
      ..write(obj.shareImage)
      ..writeByte(3)
      ..write(obj.shareDate)
      ..writeByte(4)
      ..write(obj.platform)
      ..writeByte(5)
      ..write(obj.shareCount)
      ..writeByte(6)
      ..write(obj.isPrivate)
      ..writeByte(7)
      ..write(obj.userPrefersSharing);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SocialShareDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SocialPlatformAdapter extends TypeAdapter<SocialPlatform> {
  @override
  final int typeId = 105;

  @override
  SocialPlatform read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return SocialPlatform.facebook;
      case 1:
        return SocialPlatform.twitter;
      case 2:
        return SocialPlatform.instagram;
      case 3:
        return SocialPlatform.whatsapp;
      case 4:
        return SocialPlatform.other;
      default:
        return SocialPlatform.facebook;
    }
  }

  @override
  void write(BinaryWriter writer, SocialPlatform obj) {
    switch (obj) {
      case SocialPlatform.facebook:
        writer.writeByte(0);
        break;
      case SocialPlatform.twitter:
        writer.writeByte(1);
        break;
      case SocialPlatform.instagram:
        writer.writeByte(2);
        break;
      case SocialPlatform.whatsapp:
        writer.writeByte(3);
        break;
      case SocialPlatform.other:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SocialPlatformAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
