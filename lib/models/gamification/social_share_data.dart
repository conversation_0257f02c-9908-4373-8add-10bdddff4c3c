// SocialShareData Hive Model for Moroccan Accounting App
// Tracks achievement sharing data, content generation, and user preferences.

import 'package:hive/hive.dart';
import 'package:flutter/material.dart';

part 'social_share_data.g.dart';

@HiveType(typeId: 105)
enum SocialPlatform {
  @HiveField(0)
  facebook,
  @HiveField(1)
  twitter,
  @HiveField(2)
  instagram,
  @HiveField(3)
  whatsapp,
  @HiveField(4)
  other,
}

@HiveType(typeId: 106)
class SocialShareData extends HiveObject {
  @HiveField(0)
  String achievementId;

  @HiveField(1)
  String shareText;

  @HiveField(2)
  String? shareImage; // Path or URL to image

  @HiveField(3)
  DateTime shareDate;

  @HiveField(4)
  SocialPlatform platform;

  @HiveField(5)
  int shareCount;

  @HiveField(6)
  bool isPrivate;

  @HiveField(7)
  bool userPrefersSharing;

  SocialShareData({
    required this.achievementId,
    required this.shareText,
    this.shareImage,
    required this.shareDate,
    required this.platform,
    this.shareCount = 0,
    this.isPrivate = false,
    this.userPrefersSharing = true,
  });

  // Generates share text for achievement
  String generateAchievementShareText(String achievementTitle) {
    return "I just unlocked '$achievementTitle' in Moroccan Accounting! 🎉 #MoroccanAccounting";
  }

  // Creates shareable image (placeholder, actual implementation in service/widget)
  Future<Image> createShareableImage() async {
    // Placeholder: In real use, generate image using RepaintBoundary or custom painter
    return Image.asset('assets/icons/placeholder_icon.png');
  }

  // Returns share statistics
  String getShareStats() {
    return "Shared $shareCount times on ${platform.name}";
  }
}

// End of social_share_data.dart
