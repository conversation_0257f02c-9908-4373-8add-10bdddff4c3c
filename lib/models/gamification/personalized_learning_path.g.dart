// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'personalized_learning_path.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PersonalizedLearningPathAdapter
    extends TypeAdapter<PersonalizedLearningPath> {
  @override
  final int typeId = 107;

  @override
  PersonalizedLearningPath read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PersonalizedLearningPath(
      userId: fields[0] as String,
      recommendedSections: (fields[1] as List).cast<String>(),
      recommendedQuizzes: (fields[2] as List).cast<String>(),
      difficultyLevel: fields[3] as String,
      estimatedTime: fields[4] as int,
      reasonForRecommendation: fields[5] as String,
      priority: fields[6] as int,
      createdAt: fields[7] as DateTime,
      isCompleted: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, PersonalizedLearningPath obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.recommendedSections)
      ..writeByte(2)
      ..write(obj.recommendedQuizzes)
      ..writeByte(3)
      ..write(obj.difficultyLevel)
      ..writeByte(4)
      ..write(obj.estimatedTime)
      ..writeByte(5)
      ..write(obj.reasonForRecommendation)
      ..writeByte(6)
      ..write(obj.priority)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.isCompleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PersonalizedLearningPathAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
