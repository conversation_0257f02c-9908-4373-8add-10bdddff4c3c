// PersonalizedLearningPath Hive Model for Moroccan Accounting App
// Tracks personalized learning recommendations and progress.
// References: adaptive_learning_models.dart

import 'package:hive/hive.dart';

part 'personalized_learning_path.g.dart';

@HiveType(typeId: 107)
class PersonalizedLearningPath extends HiveObject {
  @HiveField(0)
  String userId;

  @HiveField(1)
  List<String> recommendedSections;

  @HiveField(2)
  List<String> recommendedQuizzes;

  @HiveField(3)
  String difficultyLevel; // e.g. 'easy', 'medium', 'hard'

  @HiveField(4)
  int estimatedTime; // in minutes

  @HiveField(5)
  String reasonForRecommendation;

  @HiveField(6)
  int priority; // higher = more urgent

  @HiveField(7)
  DateTime createdAt;

  @HiveField(8)
  bool isCompleted;

  PersonalizedLearningPath({
    required this.userId,
    required this.recommendedSections,
    required this.recommendedQuizzes,
    required this.difficultyLevel,
    required this.estimatedTime,
    required this.reasonForRecommendation,
    required this.priority,
    required this.createdAt,
    this.isCompleted = false,
  });

  // Generates recommendations based on user progress, weak areas, interests
  static PersonalizedLearningPath generateRecommendations({
    required String userId,
    required List<String> weakSections,
    required List<String> weakQuizzes,
    String difficulty = 'medium',
    int estimatedTime = 30,
    String reason = 'Based on your recent activity',
    int priority = 1,
  }) {
    return PersonalizedLearningPath(
      userId: userId,
      recommendedSections: weakSections,
      recommendedQuizzes: weakQuizzes,
      difficultyLevel: difficulty,
      estimatedTime: estimatedTime,
      reasonForRecommendation: reason,
      priority: priority,
      createdAt: DateTime.now(),
    );
  }

  // Updates recommendations based on new progress
  void updateBasedOnProgress(List<String> newSections, List<String> newQuizzes) {
    recommendedSections = newSections;
    recommendedQuizzes = newQuizzes;
    createdAt = DateTime.now();
  }

  // Gets next recommendation
  String? getNextRecommendation() {
    if (!isCompleted && recommendedSections.isNotEmpty) {
      return recommendedSections.first;
    }
    if (!isCompleted && recommendedQuizzes.isNotEmpty) {
      return recommendedQuizzes.first;
    }
    return null;
  }

  // Marks recommendation as completed
  void markRecommendationCompleted() {
    isCompleted = true;
  }
}

// End of personalized_learning_path.dart
