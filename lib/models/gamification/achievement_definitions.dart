// Achievement Definitions for Moroccan Accounting App
// Defines all achievement categories, types, criteria, point values, rarity, and icons.
// Helper methods for criteria checking and progress calculation.
// References: adaptive_learning_models.dart


enum AchievementCategory {
  quizMastery,
  guideCompletion,
  streakAchievements,
  studyGoals,
  exploration,
  social,
}

enum AchievementRarity {
  common,
  rare,
  epic,
  legendary,
}

class AchievementDefinition {
  final String id;
  final AchievementCategory category;
  final String title;
  final String description;
  final int points;
  final AchievementRarity rarity;
  final String iconName;
  final AchievementCriteria criteria;

  AchievementDefinition({
    required this.id,
    required this.category,
    required this.title,
    required this.description,
    required this.points,
    required this.rarity,
    required this.iconName,
    required this.criteria,
  });
}

// Criteria definition for achievements
class AchievementCriteria {
  final String type; // e.g. 'quiz_completed', 'score', 'streak', etc.
  final int targetValue;
  final String? extra; // e.g. guideId, quizId, etc.

  AchievementCriteria({
    required this.type,
    required this.targetValue,
    this.extra,
  });
}

// Centralized achievement definitions
final List<AchievementDefinition> achievementDefinitions = [
  // Quiz Mastery
  AchievementDefinition(
    id: 'first_quiz_completed',
    category: AchievementCategory.quizMastery,
    title: 'First Quiz Completed',
    description: 'Complete your first quiz.',
    points: 10,
    rarity: AchievementRarity.common,
    iconName: 'quiz_first',
    criteria: AchievementCriteria(type: 'quiz_completed', targetValue: 1),
  ),
  AchievementDefinition(
    id: 'quiz_perfectionist',
    category: AchievementCategory.quizMastery,
    title: 'Quiz Perfectionist',
    description: 'Score 100% on any quiz.',
    points: 25,
    rarity: AchievementRarity.rare,
    iconName: 'quiz_perfect',
    criteria: AchievementCriteria(type: 'quiz_score', targetValue: 100),
  ),
  // Streak Achievements
  AchievementDefinition(
    id: 'streak_warrior_7',
    category: AchievementCategory.streakAchievements,
    title: 'Streak Warrior',
    description: 'Maintain a 7-day learning streak.',
    points: 30,
    rarity: AchievementRarity.epic,
    iconName: 'streak_7',
    criteria: AchievementCriteria(type: 'streak_days', targetValue: 7),
  ),
  AchievementDefinition(
    id: 'streak_legend_30',
    category: AchievementCategory.streakAchievements,
    title: 'Streak Legend',
    description: 'Maintain a 30-day learning streak.',
    points: 100,
    rarity: AchievementRarity.legendary,
    iconName: 'streak_30',
    criteria: AchievementCriteria(type: 'streak_days', targetValue: 30),
  ),
  // Guide Completion
  AchievementDefinition(
    id: 'guide_explorer',
    category: AchievementCategory.guideCompletion,
    title: 'Guide Explorer',
    description: 'Complete 3 different guides.',
    points: 20,
    rarity: AchievementRarity.rare,
    iconName: 'guide_explorer',
    criteria: AchievementCriteria(type: 'guides_completed', targetValue: 3),
  ),
  // Study Goals
  AchievementDefinition(
    id: 'daily_achiever',
    category: AchievementCategory.studyGoals,
    title: 'Daily Achiever',
    description: 'Meet your daily study goal 5 times.',
    points: 15,
    rarity: AchievementRarity.common,
    iconName: 'goal_5',
    criteria: AchievementCriteria(type: 'daily_goal_met', targetValue: 5),
  ),
  // Exploration
  AchievementDefinition(
    id: 'feature_explorer',
    category: AchievementCategory.exploration,
    title: 'Feature Explorer',
    description: 'Try 5 different app features.',
    points: 10,
    rarity: AchievementRarity.common,
    iconName: 'explorer',
    criteria: AchievementCriteria(type: 'features_tried', targetValue: 5),
  ),
  // Social
  AchievementDefinition(
    id: 'achievement_shared',
    category: AchievementCategory.social,
    title: 'Achievement Shared',
    description: 'Share an achievement on social media.',
    points: 5,
    rarity: AchievementRarity.common,
    iconName: 'share',
    criteria: AchievementCriteria(type: 'achievement_shared', targetValue: 1),
  ),
  // Add more achievements as needed
];

/// Helper: Get achievement by ID
/// Returns null if not found. Dart's firstWhere requires a non-null return, so we use a for loop.
AchievementDefinition? getAchievementById(String id) {
  for (final a in achievementDefinitions) {
    if (a.id == id) return a;
  }
  return null;
}

// Helper: Calculate progress towards achievement
double calculateAchievementProgress(String id, int userValue) {
  final achievement = getAchievementById(id);
  if (achievement == null) return 0.0;
  final target = achievement.criteria.targetValue;
  return (userValue / target).clamp(0.0, 1.0);
}

// Helper: Check if achievement is unlocked
bool isAchievementUnlocked(String id, int userValue) {
  final achievement = getAchievementById(id);
  if (achievement == null) return false;
  return userValue >= achievement.criteria.targetValue;
}

// Helper: Get achievements by category
List<AchievementDefinition> getAchievementsByCategory(AchievementCategory category) {
  return achievementDefinitions.where((a) => a.category == category).toList();
}

// Helper: Get achievements by rarity
List<AchievementDefinition> getAchievementsByRarity(AchievementRarity rarity) {
  return achievementDefinitions.where((a) => a.rarity == rarity).toList();
}

// Helper: Get all achievement categories
List<AchievementCategory> getAllAchievementCategories() {
  return AchievementCategory.values;
}

// Helper: Get all achievement rarities
List<AchievementRarity> getAllAchievementRarities() {
  return AchievementRarity.values;
}

// End of achievement_definitions.dart
