// LearningStreakData Hive Model for Moroccan Accounting App
// Tracks learning streaks, milestones, and streak history.
// References: adaptive_learning_models.dart

import 'package:hive/hive.dart';

part 'learning_streak_data.g.dart';

@HiveType(typeId: 102)
enum StreakType {
  @HiveField(0)
  dailyActivity,
  @HiveField(1)
  quizCompletion,
  @HiveField(2)
  guideReading,
}

@HiveType(typeId: 103)
class StreakPeriod {
  @HiveField(0)
  DateTime startDate;
  @HiveField(1)
  DateTime endDate;
  @HiveField(2)
  int length;

  StreakPeriod({
    required this.startDate,
    required this.endDate,
    required this.length,
  });
}

@HiveType(typeId: 104)
class LearningStreakData extends HiveObject {
  @HiveField(0)
  int currentStreak;

  @HiveField(1)
  int bestStreak;

  @HiveField(2)
  DateTime lastActivityDate;

  @HiveField(3)
  StreakType streakType;

  @HiveField(4)
  int totalActiveDays;

  @HiveField(5)
  List<StreakPeriod> streakHistory;

  LearningStreakData({
    this.currentStreak = 0,
    this.bestStreak = 0,
    required this.lastActivityDate,
    required this.streakType,
    this.totalActiveDays = 0,
    List<StreakPeriod>? streakHistory,
  }) : streakHistory = streakHistory ?? [];

  // Increments streak if activity is today
  void incrementStreak(DateTime activityDate) {
    if (!isActiveToday(activityDate)) {
      currentStreak = 1;
      streakHistory.add(StreakPeriod(
        startDate: activityDate,
        endDate: activityDate,
        length: 1,
      ));
    } else {
      currentStreak += 1;
      streakHistory.last.endDate = activityDate;
      streakHistory.last.length = currentStreak;
    }
    totalActiveDays += 1;
    lastActivityDate = activityDate;
    if (currentStreak > bestStreak) bestStreak = currentStreak;
  }

  // Resets streak
  void resetStreak(DateTime resetDate) {
    currentStreak = 0;
    lastActivityDate = resetDate;
  }

  // Checks if activity is today (handles timezone)
  bool isActiveToday(DateTime activityDate) {
    final now = DateTime.now().toUtc();
    final activityUtc = activityDate.toUtc();
    return now.year == activityUtc.year &&
        now.month == activityUtc.month &&
        now.day == activityUtc.day;
  }

  // Days until streak break (assume 1 day gap allowed)
  int getDaysUntilStreakBreak() {
    final now = DateTime.now().toUtc();
    final lastUtc = lastActivityDate.toUtc();
    final diff = now.difference(lastUtc).inDays;
    return diff > 1 ? 0 : 1 - diff;
  }

  // Returns streak milestones (e.g. 7, 30, 100 days)
  List<int> getStreakMilestones() {
    return [7, 30, 100];
  }

  // Validates streak data
  bool isValid() {
    return currentStreak >= 0 && bestStreak >= 0 && totalActiveDays >= 0;
  }
}

// End of learning_streak_data.dart
