// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'daily_goal_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DailyGoalDataAdapter extends TypeAdapter<DailyGoalData> {
  @override
  final int typeId = 101;

  @override
  DailyGoalData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DailyGoalData(
      goalType: fields[0] as DailyGoalType,
      targetValue: fields[1] as int,
      currentProgress: fields[2] as int,
      goalDate: fields[3] as DateTime,
      isCompleted: fields[4] as bool,
      streakCount: fields[5] as int,
      bestStreak: fields[6] as int,
      lastResetDate: fields[7] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, DailyGoalData obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.goalType)
      ..writeByte(1)
      ..write(obj.targetValue)
      ..writeByte(2)
      ..write(obj.currentProgress)
      ..writeByte(3)
      ..write(obj.goalDate)
      ..writeByte(4)
      ..write(obj.isCompleted)
      ..writeByte(5)
      ..write(obj.streakCount)
      ..writeByte(6)
      ..write(obj.bestStreak)
      ..writeByte(7)
      ..write(obj.lastResetDate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DailyGoalDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DailyGoalTypeAdapter extends TypeAdapter<DailyGoalType> {
  @override
  final int typeId = 100;

  @override
  DailyGoalType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DailyGoalType.timeBased;
      case 1:
        return DailyGoalType.sectionBased;
      case 2:
        return DailyGoalType.quizBased;
      default:
        return DailyGoalType.timeBased;
    }
  }

  @override
  void write(BinaryWriter writer, DailyGoalType obj) {
    switch (obj) {
      case DailyGoalType.timeBased:
        writer.writeByte(0);
        break;
      case DailyGoalType.sectionBased:
        writer.writeByte(1);
        break;
      case DailyGoalType.quizBased:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DailyGoalTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
