// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_streak_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class StreakPeriodAdapter extends TypeAdapter<StreakPeriod> {
  @override
  final int typeId = 103;

  @override
  StreakPeriod read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return StreakPeriod(
      startDate: fields[0] as DateTime,
      endDate: fields[1] as DateTime,
      length: fields[2] as int,
    );
  }

  @override
  void write(BinaryWriter writer, StreakPeriod obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.startDate)
      ..writeByte(1)
      ..write(obj.endDate)
      ..writeByte(2)
      ..write(obj.length);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreakPeriodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LearningStreakDataAdapter extends TypeAdapter<LearningStreakData> {
  @override
  final int typeId = 104;

  @override
  LearningStreakData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LearningStreakData(
      currentStreak: fields[0] as int,
      bestStreak: fields[1] as int,
      lastActivityDate: fields[2] as DateTime,
      streakType: fields[3] as StreakType,
      totalActiveDays: fields[4] as int,
      streakHistory: (fields[5] as List?)?.cast<StreakPeriod>(),
    );
  }

  @override
  void write(BinaryWriter writer, LearningStreakData obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.currentStreak)
      ..writeByte(1)
      ..write(obj.bestStreak)
      ..writeByte(2)
      ..write(obj.lastActivityDate)
      ..writeByte(3)
      ..write(obj.streakType)
      ..writeByte(4)
      ..write(obj.totalActiveDays)
      ..writeByte(5)
      ..write(obj.streakHistory);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LearningStreakDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class StreakTypeAdapter extends TypeAdapter<StreakType> {
  @override
  final int typeId = 102;

  @override
  StreakType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return StreakType.dailyActivity;
      case 1:
        return StreakType.quizCompletion;
      case 2:
        return StreakType.guideReading;
      default:
        return StreakType.dailyActivity;
    }
  }

  @override
  void write(BinaryWriter writer, StreakType obj) {
    switch (obj) {
      case StreakType.dailyActivity:
        writer.writeByte(0);
        break;
      case StreakType.quizCompletion:
        writer.writeByte(1);
        break;
      case StreakType.guideReading:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreakTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
