import 'package:hive/hive.dart';

part 'accessibility_preferences.g.dart';

/// Enum for preferred navigation mode
@HiveType(typeId: 121)
enum PreferredNavigationMode {
  @HiveField(0)
  auto,
  @HiveField(1)
  keyboard,
  @HiveField(2)
  touch,
}

/// Model for storing user accessibility preferences
@HiveType(typeId: 120)
class AccessibilityPreferences extends HiveObject {
  /// Font size scale factor (0.8 - 2.0)
  @HiveField(0)
  final double fontSize;

  /// Whether high contrast mode is enabled
  @HiveField(1)
  final bool highContrastMode;

  /// Whether screen reader is enabled
  @HiveField(2)
  final bool screenReaderEnabled;

  /// Whether keyboard navigation is enabled
  @HiveField(3)
  final bool keyboardNavigationEnabled;

  /// Whether reduced motion is enabled
  @HiveField(4)
  final bool reducedMotion;

  /// Preferred navigation mode
  @HiveField(5)
  final PreferredNavigationMode preferredNavigationMode;

  AccessibilityPreferences({
    required this.fontSize,
    required this.highContrastMode,
    required this.screenReaderEnabled,
    required this.keyboardNavigationEnabled,
    required this.reducedMotion,
    required this.preferredNavigationMode,
  });

  /// Default accessibility preferences
  factory AccessibilityPreferences.defaultSettings() {
    return AccessibilityPreferences(
      fontSize: 1.0,
      highContrastMode: false,
      screenReaderEnabled: false,
      keyboardNavigationEnabled: false,
      reducedMotion: false,
      preferredNavigationMode: PreferredNavigationMode.auto,
    );
  }

  /// Accessibility-optimized preset for vision impairment
  factory AccessibilityPreferences.visionOptimized() {
    return AccessibilityPreferences(
      fontSize: 1.4,
      highContrastMode: true,
      screenReaderEnabled: true,
      keyboardNavigationEnabled: true,
      reducedMotion: false,
      preferredNavigationMode: PreferredNavigationMode.keyboard,
    );
  }

  /// Accessibility-optimized preset for motor impairment
  factory AccessibilityPreferences.motorOptimized() {
    return AccessibilityPreferences(
      fontSize: 1.2,
      highContrastMode: false,
      screenReaderEnabled: false,
      keyboardNavigationEnabled: true,
      reducedMotion: true,
      preferredNavigationMode: PreferredNavigationMode.keyboard,
    );
  }

  /// Accessibility-optimized preset for cognitive accessibility
  factory AccessibilityPreferences.cognitiveOptimized() {
    return AccessibilityPreferences(
      fontSize: 1.3,
      highContrastMode: false,
      screenReaderEnabled: false,
      keyboardNavigationEnabled: false,
      reducedMotion: true,
      preferredNavigationMode: PreferredNavigationMode.auto,
    );
  }

  /// Create a copy with modified values
  AccessibilityPreferences copyWith({
    double? fontSize,
    bool? highContrastMode,
    bool? screenReaderEnabled,
    bool? keyboardNavigationEnabled,
    bool? reducedMotion,
    PreferredNavigationMode? preferredNavigationMode,
  }) {
    return AccessibilityPreferences(
      fontSize: fontSize ?? this.fontSize,
      highContrastMode: highContrastMode ?? this.highContrastMode,
      screenReaderEnabled: screenReaderEnabled ?? this.screenReaderEnabled,
      keyboardNavigationEnabled: keyboardNavigationEnabled ?? this.keyboardNavigationEnabled,
      reducedMotion: reducedMotion ?? this.reducedMotion,
      preferredNavigationMode: preferredNavigationMode ?? this.preferredNavigationMode,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'fontSize': fontSize,
      'highContrastMode': highContrastMode,
      'screenReaderEnabled': screenReaderEnabled,
      'keyboardNavigationEnabled': keyboardNavigationEnabled,
      'reducedMotion': reducedMotion,
      'preferredNavigationMode': preferredNavigationMode.name,
    };
  }

  /// Create from JSON
  factory AccessibilityPreferences.fromJson(Map<String, dynamic> json) {
    return AccessibilityPreferences(
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 1.0,
      highContrastMode: json['highContrastMode'] as bool? ?? false,
      screenReaderEnabled: json['screenReaderEnabled'] as bool? ?? false,
      keyboardNavigationEnabled: json['keyboardNavigationEnabled'] as bool? ?? false,
      reducedMotion: json['reducedMotion'] as bool? ?? false,
      preferredNavigationMode: _parseNavigationMode(json['preferredNavigationMode']),
    );
  }

  /// Parse navigation mode from string
  static PreferredNavigationMode _parseNavigationMode(dynamic value) {
    if (value is String) {
      switch (value) {
        case 'keyboard':
          return PreferredNavigationMode.keyboard;
        case 'touch':
          return PreferredNavigationMode.touch;
        case 'auto':
        default:
          return PreferredNavigationMode.auto;
      }
    }
    return PreferredNavigationMode.auto;
  }

  /// Validate accessibility preferences
  bool isValid() {
    return fontSize >= 0.8 && 
           fontSize <= 2.0 && 
           fontSize.isFinite;
  }

  /// Get scaled font size for a base size
  double getScaledFontSize(double baseSize) {
    if (!isValid()) return baseSize;
    return baseSize * fontSize;
  }

  /// Whether high contrast should be used
  bool shouldUseHighContrast() {
    return highContrastMode;
  }

  /// Whether keyboard navigation should be prioritized
  bool shouldPrioritizeKeyboard() {
    return keyboardNavigationEnabled || 
           preferredNavigationMode == PreferredNavigationMode.keyboard;
  }

  /// Whether touch navigation should be prioritized
  bool shouldPrioritizeTouch() {
    return preferredNavigationMode == PreferredNavigationMode.touch;
  }

  /// Whether animations should be reduced
  bool shouldReduceMotion() {
    return reducedMotion;
  }

  /// Whether screen reader features should be enabled
  bool shouldEnableScreenReader() {
    return screenReaderEnabled;
  }

  /// Get accessibility level (0-3, where 3 is most accessible)
  int getAccessibilityLevel() {
    int level = 0;
    if (fontSize > 1.2) level++;
    if (highContrastMode) level++;
    if (screenReaderEnabled) level++;
    if (keyboardNavigationEnabled) level++;
    if (reducedMotion) level++;
    return (level / 5 * 3).round();
  }

  /// Whether this configuration is suitable for vision impairment
  bool isVisionAccessible() {
    return fontSize >= 1.2 || highContrastMode || screenReaderEnabled;
  }

  /// Whether this configuration is suitable for motor impairment
  bool isMotorAccessible() {
    return keyboardNavigationEnabled || reducedMotion;
  }

  /// Get a description of current accessibility features
  String getAccessibilityDescription() {
    List<String> features = [];
    
    if (fontSize > 1.0) {
      features.add('Large text (${(fontSize * 100).round()}%)');
    }
    if (highContrastMode) {
      features.add('High contrast');
    }
    if (screenReaderEnabled) {
      features.add('Screen reader');
    }
    if (keyboardNavigationEnabled) {
      features.add('Keyboard navigation');
    }
    if (reducedMotion) {
      features.add('Reduced motion');
    }
    
    if (features.isEmpty) {
      return 'Standard accessibility';
    }
    
    return features.join(', ');
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is AccessibilityPreferences &&
        other.fontSize == fontSize &&
        other.highContrastMode == highContrastMode &&
        other.screenReaderEnabled == screenReaderEnabled &&
        other.keyboardNavigationEnabled == keyboardNavigationEnabled &&
        other.reducedMotion == reducedMotion &&
        other.preferredNavigationMode == preferredNavigationMode;
  }

  @override
  int get hashCode {
    return Object.hash(
      fontSize,
      highContrastMode,
      screenReaderEnabled,
      keyboardNavigationEnabled,
      reducedMotion,
      preferredNavigationMode,
    );
  }

  @override
  String toString() {
    return 'AccessibilityPreferences('
        'fontSize: $fontSize, '
        'highContrastMode: $highContrastMode, '
        'screenReaderEnabled: $screenReaderEnabled, '
        'keyboardNavigationEnabled: $keyboardNavigationEnabled, '
        'reducedMotion: $reducedMotion, '
        'preferredNavigationMode: $preferredNavigationMode'
        ')';
  }
}