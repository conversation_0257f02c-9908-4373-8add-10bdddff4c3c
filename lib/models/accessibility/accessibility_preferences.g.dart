// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'accessibility_preferences.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AccessibilityPreferencesAdapter
    extends TypeAdapter<AccessibilityPreferences> {
  @override
  final int typeId = 120;

  @override
  AccessibilityPreferences read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AccessibilityPreferences(
      fontSize: fields[0] as double,
      highContrastMode: fields[1] as bool,
      screenReaderEnabled: fields[2] as bool,
      keyboardNavigationEnabled: fields[3] as bool,
      reducedMotion: fields[4] as bool,
      preferredNavigationMode: fields[5] as PreferredNavigationMode,
    );
  }

  @override
  void write(BinaryWriter writer, AccessibilityPreferences obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.fontSize)
      ..writeByte(1)
      ..write(obj.highContrastMode)
      ..writeByte(2)
      ..write(obj.screenReaderEnabled)
      ..writeByte(3)
      ..write(obj.keyboardNavigationEnabled)
      ..writeByte(4)
      ..write(obj.reducedMotion)
      ..writeByte(5)
      ..write(obj.preferredNavigationMode);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AccessibilityPreferencesAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PreferredNavigationModeAdapter
    extends TypeAdapter<PreferredNavigationMode> {
  @override
  final int typeId = 121;

  @override
  PreferredNavigationMode read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PreferredNavigationMode.auto;
      case 1:
        return PreferredNavigationMode.keyboard;
      case 2:
        return PreferredNavigationMode.touch;
      default:
        return PreferredNavigationMode.auto;
    }
  }

  @override
  void write(BinaryWriter writer, PreferredNavigationMode obj) {
    switch (obj) {
      case PreferredNavigationMode.auto:
        writer.writeByte(0);
        break;
      case PreferredNavigationMode.keyboard:
        writer.writeByte(1);
        break;
      case PreferredNavigationMode.touch:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PreferredNavigationModeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
