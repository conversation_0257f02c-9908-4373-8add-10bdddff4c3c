// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserProfileAdapter extends TypeAdapter<UserProfile> {
  @override
  final int typeId = 0;

  @override
  UserProfile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserProfile(
      username: fields[0] as String,
      lastExamScore: fields[1] as int?,
      lastExamTotal: fields[2] as int?,
      lastExamId: fields[3] as String?,
      lastExamDate: fields[4] as DateTime?,
      bestExamPercentage: fields[5] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, UserProfile obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.username)
      ..writeByte(1)
      ..write(obj.lastExamScore)
      ..writeByte(2)
      ..write(obj.lastExamTotal)
      ..writeByte(3)
      ..write(obj.lastExamId)
      ..writeByte(4)
      ..write(obj.lastExamDate)
      ..writeByte(5)
      ..write(obj.bestExamPercentage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProfileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
