import 'package:hive/hive.dart';

part 'active_quiz_state.g.dart'; // Generated file

@HiveType(typeId: 3) // Next available unique typeId
class ActiveQuizState extends HiveObject {

  @HiveField(0)
  final String categoryName;

  @HiveField(1)
  final String levelName; // Used as part of the key to identify the quiz

  @HiveField(2)
  final int currentQuestionIndex;

  @HiveField(3)
  final List<int> userAnswers; // Stores index of selected option, -1 if unanswered/timed out

  @HiveField(4)
  final int score; // Correct answers count

  @HiveField(5)
  final int earnedPoints;

  @HiveField(6)
  final List<int> timeSpentPerQuestion; // Time spent on answered questions

  @HiveField(7)
  final int streakCount;

  @HiveField(8)
  final DateTime lastSavedTimestamp;

  // We might not need to save totalPoints or timeRemaining directly, 
  // as they can be derived from the QuizLevel when resuming.
  // However, saving timeRemaining for the *current* question might be useful if needed.

  ActiveQuizState({
    required this.categoryName,
    required this.levelName,
    required this.currentQuestionIndex,
    required this.userAnswers,
    required this.score,
    required this.earnedPoints,
    required this.timeSpentPerQuestion,
    required this.streakCount,
    required this.lastSavedTimestamp,
  });

  // Unique key for storing/retrieving this specific active quiz
  String get storageKey => '$categoryName-$levelName'; 
}
