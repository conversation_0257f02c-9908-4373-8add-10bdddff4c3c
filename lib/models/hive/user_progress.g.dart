// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_progress.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserProgressAdapter extends TypeAdapter<UserProgress> {
  @override
  final int typeId = 2;

  @override
  UserProgress read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserProgress(
      userProfile: fields[0] as UserProfile,
      quizAttempts: (fields[1] as HiveList?)?.castHiveList(),
      examAttempts: (fields[2] as HiveList?)?.castHiveList(),
      guideProgress: (fields[3] as Map?)?.cast<String, double>(),
      sectionProgress: (fields[4] as Map?)?.map((dynamic k, dynamic v) =>
          MapEntry(k as String, (v as Map).cast<String, bool>())),
    );
  }

  @override
  void write(BinaryWriter writer, UserProgress obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.userProfile)
      ..writeByte(1)
      ..write(obj.quizAttempts)
      ..writeByte(2)
      ..write(obj.examAttempts)
      ..writeByte(3)
      ..write(obj.guideProgress)
      ..writeByte(4)
      ..write(obj.sectionProgress);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProgressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
