import 'package:hive/hive.dart';

part 'quiz_attempt.g.dart'; // Generated file

@HiveType(typeId: 1) // Unique typeId for Hive
class QuizAttempt extends HiveObject {
  @HiveField(0)
  final String categoryName;

  @HiveField(1)
  final String levelName;

  @HiveField(2)
  final int score; // Number of correct answers

  @HiveField(3)
  final int totalQuestions;

  @HiveField(4)
  final int earnedPoints;

  @HiveField(5)
  final int totalPoints;

  @HiveField(6)
  final DateTime timestamp;

  QuizAttempt({
    required this.categoryName,
    required this.levelName,
    required this.score,
    required this.totalQuestions,
    required this.earnedPoints,
    required this.totalPoints,
    required this.timestamp,
  });
}
