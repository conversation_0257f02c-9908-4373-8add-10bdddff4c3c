import 'package:hive/hive.dart';

part 'user_profile.g.dart'; // Generated file

@HiveType(typeId: 0) // Unique typeId for Hive
class UserProfile extends HiveObject {
  @HiveField(0) // Index for the field
  String username;
  
  @HiveField(1) // New field for last exam score
  int? lastExamScore;
  
  @HiveField(2) // New field for last exam total
  int? lastExamTotal;
  
  @HiveField(3) // New field for last exam ID
  String? lastExamId;
  
  @HiveField(4) // New field for last exam date
  DateTime? lastExamDate;
  
  @HiveField(5) // New field for best exam score percentage
  double? bestExamPercentage;

  UserProfile({
    required this.username,
    this.lastExamScore,
    this.lastExamTotal,
    this.lastExamId,
    this.lastExamDate,
    this.bestExamPercentage,
  });
  
  // Calculate the percentage score of the last exam
  double get lastExamPercentage => 
      (lastExamScore != null && lastExamTotal != null && lastExamTotal! > 0) 
          ? (lastExamScore! / lastExamTotal!) * 100 
          : 0.0;
          
  // Check if user has taken any exams
  bool get hasExamData => lastExamScore != null && lastExamTotal != null;
}
