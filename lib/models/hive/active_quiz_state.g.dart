// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'active_quiz_state.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ActiveQuizStateAdapter extends TypeAdapter<ActiveQuizState> {
  @override
  final int typeId = 3;

  @override
  ActiveQuizState read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ActiveQuizState(
      categoryName: fields[0] as String,
      levelName: fields[1] as String,
      currentQuestionIndex: fields[2] as int,
      userAnswers: (fields[3] as List).cast<int>(),
      score: fields[4] as int,
      earnedPoints: fields[5] as int,
      timeSpentPerQuestion: (fields[6] as List).cast<int>(),
      streakCount: fields[7] as int,
      lastSavedTimestamp: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ActiveQuizState obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.categoryName)
      ..writeByte(1)
      ..write(obj.levelName)
      ..writeByte(2)
      ..write(obj.currentQuestionIndex)
      ..writeByte(3)
      ..write(obj.userAnswers)
      ..writeByte(4)
      ..write(obj.score)
      ..writeByte(5)
      ..write(obj.earnedPoints)
      ..writeByte(6)
      ..write(obj.timeSpentPerQuestion)
      ..writeByte(7)
      ..write(obj.streakCount)
      ..writeByte(8)
      ..write(obj.lastSavedTimestamp);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ActiveQuizStateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
