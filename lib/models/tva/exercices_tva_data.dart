
class ExercicesTvaData {
  final String title;
  final String description;
  final List<ExerciceTva> exercices;

  ExercicesTvaData({
    required this.title,
    required this.description,
    required this.exercices,
  });

  factory ExercicesTvaData.fromJson(Map<String, dynamic> json) {
    return ExercicesTvaData(
      title: json['title'] as String,
      description: json['description'] as String,
      exercices: (json['exercices'] as List<dynamic>)
          .map((e) => ExerciceTva.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class ExerciceTva {
  final String titre;
  final String description;
  final List<String> questions;
  final List<String> reponses;
  final List<String>? astuces;
  final String? difficulte;

  ExerciceTva({
    required this.titre,
    required this.description,
    required this.questions,
    required this.reponses,
    this.astuces,
    this.difficulte,
  });

  factory ExerciceTva.fromJson(Map<String, dynamic> json) {
    return ExerciceTva(
      titre: json['titre'] as String,
      description: json['description'] as String,
      questions:
          (json['questions'] as List<dynamic>).map((e) => e as String).toList(),
      reponses:
          (json['reponses'] as List<dynamic>).map((e) => e as String).toList(),
      astuces: json.containsKey('astuces')
          ? (json['astuces'] as List<dynamic>).map((e) => e as String).toList()
          : null,
      difficulte: json['difficulte'] as String?,
    );
  }
}
