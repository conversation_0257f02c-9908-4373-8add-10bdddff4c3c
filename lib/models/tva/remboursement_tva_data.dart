/// Data model for VAT refund (remboursement TVA) information in Morocco.
/// 
/// This model represents the complete structure for VAT refund procedures,
/// including eligibility criteria, required documents, and processing steps.
class RemboursementTvaData {
  /// Eligibility criteria for VAT refund
  final EligibilityCriteria eligibility;
  
  /// List of required documents for VAT refund application
  final List<DocumentRequirement> requiredDocuments;
  
  /// Requirements for invoice formatting and content
  final InvoiceRequirements invoiceRequirements;
  
  /// Step-by-step procedures for VAT refund process
  final List<ProcessStep> procedures;
  
  /// Submission deadline in days after customs validation
  final int submissionDeadlineDays;
  
  /// Common errors and how to avoid them
  final List<CommonError> commonErrors;
  
  /// Processing timelines and tracking information
  final ProcessingTimeline timeline;

  RemboursementTvaData({
    required this.eligibility,
    required this.requiredDocuments,
    required this.invoiceRequirements,
    required this.procedures,
    required this.submissionDeadlineDays,
    required this.commonErrors,
    required this.timeline,
  });

  factory RemboursementTvaData.fromJson(Map<String, dynamic> json) {
    try {
      final vatRefundJson = json['vat_refund'] ?? json;
      
      return RemboursementTvaData(
        eligibility: EligibilityCriteria.fromJson(vatRefundJson['eligibility'] ?? {}),
        requiredDocuments: (vatRefundJson['required_documents'] as List<dynamic>? ?? [])
            .map((doc) => DocumentRequirement.fromJson(doc))
            .toList(),
        invoiceRequirements: InvoiceRequirements.fromJson(vatRefundJson['invoice_requirements'] ?? {}),
        procedures: (vatRefundJson['procedures'] as List<dynamic>? ?? [])
            .map((step) => ProcessStep.fromJson(step))
            .toList(),
        submissionDeadlineDays: vatRefundJson['submission_deadline_days'] ?? 30,
        commonErrors: (vatRefundJson['common_errors'] as List<dynamic>? ?? [])
            .map((error) => CommonError.fromJson(error))
            .toList(),
        timeline: ProcessingTimeline.fromJson(vatRefundJson['timeline'] ?? {}),
      );
    } catch (e) {
      throw FormatException('Invalid JSON format for RemboursementTvaData: $e');
    }
  }
}

/// Eligibility criteria for VAT refund application.
class EligibilityCriteria {
  /// Required residency status (e.g., "Non-resident")
  final String residencyStatus;
  
  /// Minimum purchase amount required
  final num minimumPurchase;
  
  /// Currency for minimum purchase amount
  final String currency;
  
  /// Type of stay required (e.g., "Short stay")
  final String stayType;
  
  /// Time limit in months to present documents to customs
  final int timeLimitMonths;
  
  /// Additional eligibility notes
  final String? notes;

  EligibilityCriteria({
    required this.residencyStatus,
    required this.minimumPurchase,
    required this.currency,
    required this.stayType,
    required this.timeLimitMonths,
    this.notes,
  });

  factory EligibilityCriteria.fromJson(Map<String, dynamic> json) {
    return EligibilityCriteria(
      residencyStatus: json['residency_status'] ?? '',
      minimumPurchase: json['minimum_purchase'] ?? 0,
      currency: json['currency'] ?? 'MAD',
      stayType: json['stay_type'] ?? '',
      timeLimitMonths: json['time_limit_months'] ?? 3,
      notes: json['notes'],
    );
  }
}

/// Represents a required document for VAT refund application.
class DocumentRequirement {
  /// Name of the required document
  final String name;
  
  /// Detailed description of the document
  final String description;
  
  /// Whether this document is mandatory
  final bool mandatory;
  
  /// Additional notes or requirements for this document
  final String? notes;
  
  /// Category of the document (e.g., "identification", "financial", "purchase")
  final String? category;

  DocumentRequirement({
    required this.name,
    required this.description,
    this.mandatory = true,
    this.notes,
    this.category,
  });

  factory DocumentRequirement.fromJson(dynamic json) {
    if (json is String) {
      return DocumentRequirement(
        name: json,
        description: json,
      );
    } else if (json is Map<String, dynamic>) {
      return DocumentRequirement(
        name: json['name'] ?? '',
        description: json['description'] ?? json['name'] ?? '',
        mandatory: json['mandatory'] ?? true,
        notes: json['notes'],
        category: json['category'],
      );
    } else {
      throw FormatException('Invalid document requirement format');
    }
  }
}

/// Requirements for invoice formatting and mandatory content.
class InvoiceRequirements {
  /// Required seller information
  final SellerInfo seller;
  
  /// Required buyer information
  final BuyerInfo buyer;
  
  /// Required goods information structure
  final GoodsInfo goods;
  
  /// Required financial information
  final FinancialInfo financial;
  
  /// Required signatures and stamps
  final SignatureRequirements signatures;

  InvoiceRequirements({
    required this.seller,
    required this.buyer,
    required this.goods,
    required this.financial,
    required this.signatures,
  });

  factory InvoiceRequirements.fromJson(Map<String, dynamic> json) {
    return InvoiceRequirements(
      seller: SellerInfo.fromJson(json['seller'] ?? {}),
      buyer: BuyerInfo.fromJson(json['buyer'] ?? {}),
      goods: GoodsInfo.fromJson(json['goods'] ?? {}),
      financial: FinancialInfo.fromJson(json),
      signatures: SignatureRequirements.fromJson(json['signatures'] ?? {}),
    );
  }
}

/// Required seller information for invoices.
class SellerInfo {
  /// Seller's business name
  final String name;
  
  /// Seller's business address
  final String address;
  
  /// Seller's tax identification number
  final String taxId;
  
  /// Date of sale
  final String dateOfSale;

  SellerInfo({
    required this.name,
    required this.address,
    required this.taxId,
    required this.dateOfSale,
  });

  factory SellerInfo.fromJson(Map<String, dynamic> json) {
    return SellerInfo(
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      taxId: json['tax_id'] ?? '',
      dateOfSale: json['date_of_sale'] ?? '',
    );
  }
}

/// Required buyer information for invoices.
class BuyerInfo {
  /// Buyer's full name
  final String name;
  
  /// Buyer's nationality
  final String nationality;
  
  /// Buyer's passport number
  final String passportNumber;
  
  /// Buyer's address
  final String address;
  
  /// Buyer's bank account information
  final BankAccount bankAccount;

  BuyerInfo({
    required this.name,
    required this.nationality,
    required this.passportNumber,
    required this.address,
    required this.bankAccount,
  });

  factory BuyerInfo.fromJson(Map<String, dynamic> json) {
    return BuyerInfo(
      name: json['name'] ?? '',
      nationality: json['nationality'] ?? '',
      passportNumber: json['passport_number'] ?? '',
      address: json['address'] ?? '',
      bankAccount: BankAccount.fromJson(json['bank_account'] ?? {}),
    );
  }
}

/// Bank account information for refund processing.
class BankAccount {
  /// Account number (RIB or IBAN)
  final String number;
  
  /// Bank name
  final String bankName;
  
  /// Account type (RIB or IBAN)
  final String type;

  BankAccount({
    required this.number,
    required this.bankName,
    required this.type,
  });

  factory BankAccount.fromJson(Map<String, dynamic> json) {
    return BankAccount(
      number: json['number'] ?? '',
      bankName: json['bank_name'] ?? '',
      type: json['type'] ?? 'RIB',
    );
  }
}

/// Required goods information structure.
class GoodsInfo {
  /// Nature/description of goods
  final String nature;
  
  /// Quantity of goods
  final String quantity;
  
  /// Unit price information
  final String unitPrice;

  GoodsInfo({
    required this.nature,
    required this.quantity,
    required this.unitPrice,
  });

  factory GoodsInfo.fromJson(Map<String, dynamic> json) {
    return GoodsInfo(
      nature: json['nature'] ?? '',
      quantity: json['quantity'] ?? '',
      unitPrice: json['unit_price'] ?? '',
    );
  }
}

/// Required financial information for invoices.
class FinancialInfo {
  /// Total purchase amount
  final String totalAmount;
  
  /// VAT rate applied
  final String vatRate;
  
  /// VAT amount
  final String vatAmount;
  
  /// Invoice number
  final String invoiceNumber;

  FinancialInfo({
    required this.totalAmount,
    required this.vatRate,
    required this.vatAmount,
    required this.invoiceNumber,
  });

  factory FinancialInfo.fromJson(Map<String, dynamic> json) {
    return FinancialInfo(
      totalAmount: json['total_amount'] ?? '',
      vatRate: json['vat_rate'] ?? '',
      vatAmount: json['vat_amount'] ?? '',
      invoiceNumber: json['invoice_number'] ?? '',
    );
  }
}

/// Required signatures and stamps for invoices.
class SignatureRequirements {
  /// Buyer signature requirement
  final String buyer;
  
  /// Seller signature requirement
  final String seller;
  
  /// Seller business stamp requirement
  final String sellerStamp;

  SignatureRequirements({
    required this.buyer,
    required this.seller,
    required this.sellerStamp,
  });

  factory SignatureRequirements.fromJson(Map<String, dynamic> json) {
    return SignatureRequirements(
      buyer: json['buyer'] ?? '',
      seller: json['seller'] ?? '',
      sellerStamp: json['seller_stamp'] ?? '',
    );
  }
}

/// Represents a step in the VAT refund process.
class ProcessStep {
  /// Step number in the process
  final int stepNumber;
  
  /// Title of the process step
  final String title;
  
  /// Detailed description of the step
  final String description;
  
  /// Location where this step takes place
  final String? location;
  
  /// Estimated time for this step
  final String? estimatedTime;
  
  /// Important notes for this step
  final String? notes;

  ProcessStep({
    required this.stepNumber,
    required this.title,
    required this.description,
    this.location,
    this.estimatedTime,
    this.notes,
  });

  factory ProcessStep.fromJson(dynamic json) {
    if (json is String) {
      // Handle simple string format
      return ProcessStep(
        stepNumber: 1,
        title: 'Process Step',
        description: json,
      );
    } else if (json is Map<String, dynamic>) {
      return ProcessStep(
        stepNumber: json['step_number'] ?? 1,
        title: json['title'] ?? '',
        description: json['description'] ?? '',
        location: json['location'],
        estimatedTime: json['estimated_time'],
        notes: json['notes'],
      );
    } else {
      throw FormatException('Invalid process step format');
    }
  }
}

/// Represents a common error in VAT refund applications.
class CommonError {
  /// Type or category of the error
  final String errorType;
  
  /// Description of the error
  final String description;
  
  /// How to prevent this error
  final String prevention;
  
  /// Consequences of this error
  final String? consequences;

  CommonError({
    required this.errorType,
    required this.description,
    required this.prevention,
    this.consequences,
  });

  factory CommonError.fromJson(Map<String, dynamic> json) {
    return CommonError(
      errorType: json['error_type'] ?? '',
      description: json['description'] ?? '',
      prevention: json['prevention'] ?? '',
      consequences: json['consequences'],
    );
  }
}

/// Processing timeline and tracking information.
class ProcessingTimeline {
  /// Expected processing time
  final String processingTime;
  
  /// Tracking methods available
  final List<String> trackingMethods;
  
  /// Contact information for inquiries
  final String? contactInfo;
  
  /// Appeal process information
  final String? appealProcess;

  ProcessingTimeline({
    required this.processingTime,
    required this.trackingMethods,
    this.contactInfo,
    this.appealProcess,
  });

  factory ProcessingTimeline.fromJson(Map<String, dynamic> json) {
    return ProcessingTimeline(
      processingTime: json['processing_time'] ?? '',
      trackingMethods: (json['tracking_methods'] as List<dynamic>? ?? [])
          .map((method) => method.toString())
          .toList(),
      contactInfo: json['contact_info'],
      appealProcess: json['appeal_process'],
    );
  }
}