import 'tva_line_item.dart';

/// Model class representing invoice totals and summary for TVA calculations
class TvaInvoiceSummary {
  /// All invoice line items
  final List<TvaLineItem> items;

  const TvaInvoiceSummary({
    required this.items,
  });

  /// Factory constructor to create summary from list of items
  static TvaInvoiceSummary fromItems(List<TvaLineItem> items) {
    return TvaInvoiceSummary(items: List.unmodifiable(items));
  }

  /// Sum of all line HT amounts
  double get totalHT {
    if (items.isEmpty) return 0.0;
    return items.fold(0.0, (sum, item) => sum + item.totalHT);
  }

  /// Sum of all line TVA amounts
  double get totalTVA {
    if (items.isEmpty) return 0.0;
    return items.fold(0.0, (sum, item) => sum + item.totalTVA);
  }

  /// Sum of all line TTC amounts
  double get totalTTC {
    if (items.isEmpty) return 0.0;
    return items.fold(0.0, (sum, item) => sum + item.totalTTC);
  }

  /// Breakdown by VAT rate (rate -> total VAT)
  Map<double, double> get vatBreakdown {
    final Map<double, double> breakdown = {};
    
    for (final item in items) {
      final rate = item.vatRate;
      breakdown[rate] = (breakdown[rate] ?? 0.0) + item.totalTVA;
    }
    
    return breakdown;
  }

  /// Count of exempt items
  int get exemptItemsCount {
    return items.where((item) => item.isExempt).length;
  }

  /// Check if invoice has any items
  bool get hasItems {
    return items.isNotEmpty;
  }

  /// Check if invoice has exempt items
  bool get hasExemptItems {
    return exemptItemsCount > 0;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is TvaInvoiceSummary &&
        _listEquals(other.items, items);
  }

  @override
  int get hashCode {
    return Object.hashAll(items);
  }

  @override
  String toString() {
    return 'TvaInvoiceSummary('
        'items: ${items.length}, '
        'totalHT: $totalHT, '
        'totalTVA: $totalTVA, '
        'totalTTC: $totalTTC, '
        'exemptItems: $exemptItemsCount'
        ')';
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    if (identical(a, b)) return true;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}