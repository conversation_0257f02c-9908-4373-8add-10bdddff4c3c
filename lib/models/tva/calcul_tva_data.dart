class CalculTvaData {
  final String title;
  final String description;
  final String annee;
  final List<CalculTvaSection> sections;

  CalculTvaData({
    required this.title,
    required this.description,
    required this.annee,
    required this.sections,
  });

  factory CalculTvaData.fromJson(Map<String, dynamic> json) {
    return CalculTvaData(
      title: json['title'],
      description: json['description'],
      annee: json['annee'],
      sections: (json['sections'] as List<dynamic>)
          .map((section) => CalculTvaSection.fromJson(section))
          .toList(),
    );
  }
}

class CalculTvaSection {
  final String titre;
  final List<dynamic> contenu;

  CalculTvaSection({
    required this.titre,
    required this.contenu,
  });

  factory CalculTvaSection.fromJson(Map<String, dynamic> json) {
    return CalculTvaSection(
      titre: json['titre'],
      contenu: json['contenu'] as List<dynamic>,
    );
  }
}

class FormuleTva {
  final String nom;
  final String formule;
  final ExempleTva exemple;

  FormuleTva({
    required this.nom,
    required this.formule,
    required this.exemple,
  });

  factory FormuleTva.fromJson(Map<String, dynamic> json) {
    return FormuleTva(
      nom: json['nom'],
      formule: json['formule'],
      exemple: ExempleTva.fromJson(json['exemple']),
    );
  }
}

class ExempleTva {
  final String donnees;
  final String calcul;

  ExempleTva({
    required this.donnees,
    required this.calcul,
  });

  factory ExempleTva.fromJson(Map<String, dynamic> json) {
    return ExempleTva(
      donnees: json['donnees'],
      calcul: json['calcul'],
    );
  }
}

class CasParticulier {
  final String cas;
  final String description;
  final String formule;
  final Map<String, dynamic> exemple;

  CasParticulier({
    required this.cas,
    required this.description,
    required this.formule,
    required this.exemple,
  });

  factory CasParticulier.fromJson(Map<String, dynamic> json) {
    return CasParticulier(
      cas: json['cas'],
      description: json['description'],
      formule: json['formule'],
      exemple: json['exemple'] as Map<String, dynamic>,
    );
  }
}
