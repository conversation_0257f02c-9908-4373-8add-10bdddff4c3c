
class TvaItem {
  final String nom;
  final num tva2025;
  final num tva2026;
  final String? remarques;

  TvaItem({
    required this.nom,
    required this.tva2025,
    required this.tva2026,
    this.remarques,
  });

  factory TvaItem.fromJson(Map<String, dynamic> json) {
    return TvaItem(
      nom: json['nom'],
      tva2025: json['tva_2025'],
      tva2026: json['tva_2026'],
      remarques: json['remarques'],
    );
  }
}

class TvaCategory {
  final String categorie;
  final List<TvaItem> produitsServices;

  TvaCategory({
    required this.categorie,
    required this.produitsServices,
  });

  factory TvaCategory.fromJson(Map<String, dynamic> json) {
    final List<dynamic> itemsJson = json['produits_services'];
    final items =
        itemsJson.map((itemJson) => TvaItem.fromJson(itemJson)).toList();
    return TvaCategory(
      categorie: json['categorie'],
      produitsServices: items,
    );
  }
}

class TvaData {
  final List<TvaCategory> categories;

  TvaData({required this.categories});

  factory TvaData.fromJson(Map<String, dynamic> json) {
    final List<dynamic> categoriesJson = json['TVA_Maroc_2025_2026'];
    final categories = categoriesJson
        .map((categoryJson) => TvaCategory.fromJson(categoryJson))
        .toList();
    return TvaData(categories: categories);
  }
}
