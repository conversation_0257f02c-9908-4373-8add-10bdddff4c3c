import '../../../utils/calculation_utils.dart';

/// Model class representing an individual invoice line item for TVA calculations
class TvaLineItem {
  /// Item description
  final String description;
  
  /// Item quantity
  final double quantity;
  
  /// Price per unit
  final double unitPrice;
  
  /// TVA rate percentage (e.g., 20.0 for 20%)
  final double vatRate;
  
  /// Whether this item is exempt from TVA
  final bool isExempt;
  
  /// Reason for exemption (if applicable)
  final String? exemptionReason;

  const TvaLineItem({
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.vatRate,
    required this.isExempt,
    this.exemptionReason,
  });

  /// Calculate HT total (quantity × unitPrice)
  double get totalHT {
    return CalculationUtils.roundMonetary(quantity * unitPrice);
  }

  /// Calculate TVA amount using CalculationUtils
  double get totalTVA {
    if (isExempt) return 0.0;
    return CalculationUtils.calculateVATFromHT(totalHT, vatRate);
  }

  /// Calculate TTC total (totalHT + totalTVA)
  double get totalTTC {
    return CalculationUtils.roundMonetary(totalHT + totalTVA);
  }

  /// Validate that all fields are properly filled
  bool get isValid {
    // Check description is not empty
    if (description.trim().isEmpty) return false;
    
    // Check quantity is valid
    if (!CalculationUtils.isValidQuantity(quantity)) return false;
    
    // Check unit price is valid
    if (!CalculationUtils.isValidUnitPrice(unitPrice)) return false;
    
    // Check VAT rate is valid
    if (!CalculationUtils.isValidTaxRate(vatRate)) return false;
    
    // If exempt, exemption reason should be provided
    if (isExempt && (exemptionReason == null || exemptionReason!.trim().isEmpty)) {
      return false;
    }
    
    return true;
  }

  /// Create a copy of this item with some properties changed
  TvaLineItem copyWith({
    String? description,
    double? quantity,
    double? unitPrice,
    double? vatRate,
    bool? isExempt,
    String? exemptionReason,
  }) {
    return TvaLineItem(
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      vatRate: vatRate ?? this.vatRate,
      isExempt: isExempt ?? this.isExempt,
      exemptionReason: exemptionReason ?? this.exemptionReason,
    );
  }

  /// Create an instance from JSON map
  factory TvaLineItem.fromJson(Map<String, dynamic> json) {
    return TvaLineItem(
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unitPrice: (json['unitPrice'] as num).toDouble(),
      vatRate: (json['vatRate'] as num).toDouble(),
      isExempt: json['isExempt'] as bool,
      exemptionReason: json['exemptionReason'] as String?,
    );
  }

  /// Convert this instance to JSON map
  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'vatRate': vatRate,
      'isExempt': isExempt,
      'exemptionReason': exemptionReason,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is TvaLineItem &&
        other.description == description &&
        other.quantity == quantity &&
        other.unitPrice == unitPrice &&
        other.vatRate == vatRate &&
        other.isExempt == isExempt &&
        other.exemptionReason == exemptionReason;
  }

  @override
  int get hashCode {
    return Object.hash(
      description,
      quantity,
      unitPrice,
      vatRate,
      isExempt,
      exemptionReason,
    );
  }

  @override
  String toString() {
    return 'TvaLineItem('
        'description: $description, '
        'quantity: $quantity, '
        'unitPrice: $unitPrice, '
        'vatRate: $vatRate, '
        'isExempt: $isExempt, '
        'exemptionReason: $exemptionReason'
        ')';
  }
}