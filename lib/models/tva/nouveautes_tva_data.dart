class NouveautesTvaData {
  final String title;
  final String description;
  final List<NouveauteTva> nouveautes;

  NouveautesTvaData({
    required this.title,
    required this.description,
    required this.nouveautes,
  });

  factory NouveautesTvaData.fromJson(Map<String, dynamic> json) {
    return NouveautesTvaData(
      title: json['title'] as String,
      description: json['description'] as String,
      nouveautes: (json['nouveautes'] as List<dynamic>)
          .map((e) => NouveauteTva.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class NouveauteTva {
  final String titre;
  final String? description;
  final List<String>? details;
  final String? dateEffet;

  NouveauteTva({
    required this.titre,
    this.description,
    this.details,
    this.dateEffet,
  });

  factory NouveauteTva.fromJson(Map<String, dynamic> json) {
    return NouveauteTva(
      titre: json['titre'] as String,
      description: json['description'] as String?,
      details: json.containsKey('details')
          ? (json['details'] as List<dynamic>).map((e) => e as String).toList()
          : null,
      dateEffet: json['date_effet'] as String?,
    );
  }
}
