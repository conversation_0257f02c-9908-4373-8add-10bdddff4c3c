class RasTvaData {
  final String title;
  final String description;
  final String annee;
  final List<RasTvaSection> sections;

  RasTvaData({
    required this.title,
    required this.description,
    required this.annee,
    required this.sections,
  });

  factory RasTvaData.fromJson(Map<String, dynamic> json) {
    return RasTvaData(
      title: json['title'],
      description: json['description'],
      annee: json['annee'],
      sections: (json['sections'] as List<dynamic>)
          .map((section) => RasTvaSection.fromJson(section))
          .toList(),
    );
  }
}

class RasTvaSection {
  final String titre;
  final dynamic contenu;

  RasTvaSection({
    required this.titre,
    required this.contenu,
  });

  factory RasTvaSection.fromJson(Map<String, dynamic> json) {
    return RasTvaSection(
      titre: json['titre'],
      contenu: json['contenu'],
    );
  }
}
