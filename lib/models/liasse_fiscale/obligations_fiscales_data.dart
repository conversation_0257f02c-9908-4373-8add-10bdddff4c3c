class ObligationsFiscalesData {
  final String title;
  final String description;
  final List<dynamic> calendar;
  final Map<String, dynamic> penalties;
  final List<dynamic> complianceRequirements;
  final Map<String, dynamic> metadata;

  ObligationsFiscalesData({
    required this.title,
    required this.description,
    required this.calendar,
    required this.penalties,
    required this.complianceRequirements,
    required this.metadata,
  });

  factory ObligationsFiscalesData.fromJson(Map<String, dynamic> json) {
    return ObligationsFiscalesData(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      calendar: json['calendar'] ?? [],
      penalties: json['penalties'] ?? {},
      complianceRequirements: json['compliance_requirements'] ?? [],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'calendar': calendar,
      'penalties': penalties,
      'compliance_requirements': complianceRequirements,
      'metadata': metadata,
    };
  }
}
