class ChecklistDocumentsData {
  final String title;
  final String description;
  final List<dynamic> categories;
  final Map<String, dynamic> metadata;

  ChecklistDocumentsData({
    required this.title,
    required this.description,
    required this.categories,
    required this.metadata,
  });

  factory ChecklistDocumentsData.fromJson(Map<String, dynamic> json) {
    return ChecklistDocumentsData(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      categories: json['categories'] ?? [],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'categories': categories,
      'metadata': metadata,
    };
  }
}
