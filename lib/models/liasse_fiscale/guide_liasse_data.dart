class GuideLiasseData {
  final String title;
  final String description;
  final List<dynamic> sections;
  final Map<String, dynamic> metadata;

  GuideLiasseData({
    required this.title,
    required this.description,
    required this.sections,
    required this.metadata,
  });

  factory GuideLiasseData.fromJson(Map<String, dynamic> json) {
    return GuideLiasseData(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      sections: json['sections'] ?? [],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'sections': sections,
      'metadata': metadata,
    };
  }
}
