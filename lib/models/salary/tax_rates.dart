class FamilyCharges {
  final double spouse;
  final double child1;
  final double child2;
  final double child3;
  final double child4;
  final double child5;
  final double child6;
  final int maxChildren;

  const FamilyCharges({
    required this.spouse,
    required this.child1,
    required this.child2,
    required this.child3,
    required this.child4,
    required this.child5,
    required this.child6,
    required this.maxChildren,
  });

  factory FamilyCharges.fromJson(Map<String, dynamic> json) {
    return FamilyCharges(
      spouse: json['spouse'].toDouble(),
      child1: json['child_1'].toDouble(),
      child2: json['child_2'].toDouble(),
      child3: json['child_3'].toDouble(),
      child4: json['child_4'].toDouble(),
      child5: json['child_5'].toDouble(),
      child6: json['child_6'].toDouble(),
      maxChildren: json['max_children'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'spouse': spouse,
      'child_1': child1,
      'child_2': child2,
      'child_3': child3,
      'child_4': child4,
      'child_5': child5,
      'child_6': child6,
      'max_children': maxChildren,
    };
  }
}
