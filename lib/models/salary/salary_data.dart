class SalaryData {
  final double baseSalary;
  final double transportAllowance;
  final double housingAllowance;
  final double otherAllowances;
  final String employeeType;
  final int yearsOfService;
  final bool includeCNSS;
  final OvertimeHours? overtime;
  final Map<String, BonusEntry> bonuses;
  final FamilyStatus familyStatus;

  const SalaryData({
    required this.baseSalary,
    required this.transportAllowance,
    required this.housingAllowance,
    required this.otherAllowances,
    required this.employeeType,
    required this.yearsOfService,
    required this.includeCNSS,
    this.overtime,
    required this.bonuses,
    required this.familyStatus,
  });
}

class OvertimeHours {
  final double regularHours;
  final double holidayHours;
  final double nightHours;

  const OvertimeHours({
    required this.regularHours,
    required this.holidayHours,
    required this.nightHours,
  });
}

class BonusEntry {
  final bool enabled;
  final double amount;
  final bool isAnnual;

  const BonusEntry({
    required this.enabled,
    required this.amount,
    required this.isAnnual,
  });
}

class FamilyStatus {
  final bool isMarried;
  final List<Dependent> dependents;

  const FamilyStatus({
    required this.isMarried,
    required this.dependents,
  });
}

class Dependent {
  final int age;
  final bool isDisabled;

  const Dependent({
    required this.age,
    required this.isDisabled,
  });
}
