import '../rates/tax_rates.dart';

class SalaryResult {
  final double baseSalary;
  final double grossSalary;
  final SocialContributions socialContributions;
  final IRResult irResult;
  final double netSalary;

  const Sal<PERSON><PERSON><PERSON><PERSON>({
    required this.baseSalary,
    required this.grossSalary,
    required this.socialContributions,
    required this.irResult,
    required this.netSalary,
  });

  String generateDetails() {
    final buffer = StringBuffer();

    // 1. Salaire de base et ancienneté
    buffer.writeln('1. SALAIRE DE BASE ET ANCIENNETÉ');
    buffer.writeln('   Base: ${_formatAmount(baseSalary)} DH');
    if (irResult.seniority > 0) {
      buffer.writeln(
          '   Prime ancienneté (${irResult.seniorityRate}%): ${_formatAmount(irResult.seniority)} DH');
    }
    buffer.writeln();

    // 2. Heures supplémentaires
    if (irResult.overtime.total > 0) {
      buffer.writeln('2. HEURES SUPPLÉMENTAIRES');
      if (irResult.overtime.regular > 0) {
        buffer.writeln(
            '   Jour ouvrable (25%): ${_formatAmount(irResult.overtime.regular)} DH');
      }
      if (irResult.overtime.holiday > 0) {
        buffer.writeln(
            '   Jour férié (50%): ${_formatAmount(irResult.overtime.holiday)} DH');
      }
      if (irResult.overtime.night > 0) {
        buffer.writeln(
            '   Nuit (50%): ${_formatAmount(irResult.overtime.night)} DH');
      }
      buffer.writeln('   Total: ${_formatAmount(irResult.overtime.total)} DH');
      buffer.writeln();
    }

    // 3. Indemnités
    buffer.writeln('3. INDEMNITÉS');
    if (irResult.allowances.transport > 0) {
      buffer.writeln(
          '   Transport: ${_formatAmount(irResult.allowances.transport)} DH');
    }
    if (irResult.allowances.housing > 0) {
      buffer.writeln(
          '   Logement: ${_formatAmount(irResult.allowances.housing)} DH');
    }
    if (irResult.allowances.other > 0) {
      buffer
          .writeln('   Autres: ${_formatAmount(irResult.allowances.other)} DH');
    }
    buffer.writeln('   Total: ${_formatAmount(irResult.allowances.total)} DH');
    buffer.writeln();

    // 4. Primes
    if (irResult.bonuses.isNotEmpty) {
      buffer.writeln('4. PRIMES');
      for (final bonus in irResult.bonuses) {
        buffer.writeln(
            '   ${bonus.name}: ${_formatAmount(bonus.amount)} DH${bonus.isAnnual ? ' (annuel)' : ''}');
      }
      buffer.writeln('   Total: ${_formatAmount(irResult.bonusesTotal)} DH');
      buffer.writeln();
    }

    // 5. Salaire brut
    buffer.writeln('5. SALAIRE BRUT');
    buffer.writeln('   ${_formatAmount(grossSalary)} DH');
    buffer.writeln();

    // 6. Cotisations sociales
    if (socialContributions.total > 0) {
      buffer.writeln('6. COTISATIONS SOCIALES');
      buffer.writeln(
          '   CNSS (4.48%, plafond 6000 DH): -${_formatAmount(socialContributions.cnss)} DH');
      buffer.writeln(
          '   AMO (2.26%): -${_formatAmount(socialContributions.amo)} DH');
      buffer
          .writeln('   Total: -${_formatAmount(socialContributions.total)} DH');
      buffer.writeln();
    }

    // 7. Frais professionnels
    buffer.writeln('7. FRAIS PROFESSIONNELS');
    buffer.writeln(
        '   Base: ${_formatAmount(irResult.professionalExpensesBase)} DH');
    buffer.writeln('   Taux: ${irResult.professionalExpensesRate}%');
    buffer.writeln(
        '   Montant: ${_formatAmount(irResult.professionalExpenses)} DH');
    buffer.writeln();

    // 8. IR
    buffer.writeln('8. IMPÔT SUR LE REVENU (IR)');
    buffer.writeln(
        '   Base imposable: ${_formatAmount(irResult.taxableIncome)} DH');
    buffer.writeln(
        '   Tranche: ${_formatAmount(irResult.bracket.min)} - ${irResult.bracket.max != null ? _formatAmount(irResult.bracket.max!.toDouble()) : "∞"} DH');
    buffer.writeln('   Taux: ${irResult.bracket.rate}%');
    buffer.writeln('   IR brut: ${_formatAmount(irResult.rawTax)} DH');
    if (irResult.familyCharges > 0) {
      buffer.writeln(
          '   Charges de famille: -${_formatAmount(irResult.familyCharges)} DH');
    }
    buffer.writeln('   IR final: ${_formatAmount(irResult.finalTax)} DH');
    buffer.writeln();

    // 9. Net à payer
    buffer.writeln('9. NET À PAYER');
    buffer.writeln('   ${_formatAmount(netSalary)} DH');

    return buffer.toString();
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(2).replaceAll(RegExp(r'\.00$'), '');
  }
}

class SocialContributions {
  final double cnss;
  final double amo;

  const SocialContributions({
    required this.cnss,
    required this.amo,
  });

  double get total => cnss + amo;
}

class IRResult {
  final double taxableIncome;
  final IRBracket bracket;
  final double rawTax;
  final double familyCharges;
  final double finalTax;
  final double seniority;
  final double seniorityRate;
  final OvertimeDetails overtime;
  final AllowanceDetails allowances;
  final List<BonusDetail> bonuses;
  final double bonusesTotal;
  final double professionalExpenses;
  final double professionalExpensesRate;
  final double professionalExpensesBase;

  const IRResult({
    required this.taxableIncome,
    required this.bracket,
    required this.rawTax,
    required this.familyCharges,
    required this.finalTax,
    required this.seniority,
    required this.seniorityRate,
    required this.overtime,
    required this.allowances,
    required this.bonuses,
    required this.bonusesTotal,
    required this.professionalExpenses,
    required this.professionalExpensesRate,
    required this.professionalExpensesBase,
  });
}

class OvertimeDetails {
  final double regular;
  final double holiday;
  final double night;
  final double total;

  const OvertimeDetails({
    required this.regular,
    required this.holiday,
    required this.night,
    required this.total,
  });
}

class AllowanceDetails {
  final double transport;
  final double housing;
  final double other;
  final double total;

  const AllowanceDetails({
    required this.transport,
    required this.housing,
    required this.other,
    required this.total,
  });
}

class BonusDetail {
  final String name;
  final double amount;
  final bool isAnnual;

  const BonusDetail({
    required this.name,
    required this.amount,
    required this.isAnnual,
  });
}
