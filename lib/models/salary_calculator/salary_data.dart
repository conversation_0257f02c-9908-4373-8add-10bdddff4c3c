class SalaryData {
  // Base Information
  double baseSalary;
  bool isAgricultural;
  String employeeType; // 'cadre' ou 'salarie'
  bool isMonthly;

  // Working Hours
  WorkingHours workingHours;
  OvertimeHours overtimeHours;

  // Allowances & Benefits
  Allowances allowances;
  Bonuses bonuses;

  // Social Status
  FamilyStatus familyStatus;

  // Experience
  int yearsOfService;

  // Social Security
  bool includeCNSS;

  // Préavis (pour cas de licenciement)
  bool showPreAvis;

  SalaryData({
    this.baseSalary = 0.0,
    this.isAgricultural = false,
    this.employeeType = 'salarie',
    this.isMonthly = true,
    WorkingHours? workingHours,
    OvertimeHours? overtimeHours,
    Allowances? allowances,
    Bonuses? bonuses,
    FamilyStatus? familyStatus,
    this.yearsOfService = 0,
    this.includeCNSS = true,
    this.showPreAvis = false,
  })  : workingHours = workingHours ?? WorkingHours(),
        overtimeHours = overtimeHours ?? OvertimeHours(),
        allowances = allowances ?? Allowances(),
        bonuses = bonuses ?? Bonuses(),
        familyStatus = familyStatus ?? FamilyStatus();
}

class WorkingHours {
  final double daily;
  final double weekly;
  final double monthly;
  final double yearly;

  WorkingHours({
    this.daily = 8.0,
    this.weekly = 44.0,
    this.monthly = 191.0,
    this.yearly = 2288.0,
  });
}

class OvertimeHours {
  double regular;
  double holiday;
  double night;

  OvertimeHours({
    this.regular = 0,
    this.holiday = 0,
    this.night = 0,
  });
}

class Allowances {
  double transport;
  double housing;
  double representation;

  Allowances({
    this.transport = 0,
    this.housing = 0,
    this.representation = 0,
  });
}

class Bonuses {
  BonusEntry performance;
  double thirteenthMonth;
  BonusEntry profitSharing;
  BonusEntry other;

  Bonuses({
    BonusEntry? performance,
    this.thirteenthMonth = 0,
    BonusEntry? profitSharing,
    BonusEntry? other,
  })  : performance = performance ?? BonusEntry(),
        profitSharing = profitSharing ?? BonusEntry(),
        other = other ?? BonusEntry();
}

class BonusEntry {
  final double amount;
  final bool isAnnual;
  final bool enabled;

  const BonusEntry({
    this.amount = 0,
    this.isAnnual = false,
    this.enabled = false,
  });

  BonusEntry copyWith({
    double? amount,
    bool? isAnnual,
    bool? enabled,
  }) {
    return BonusEntry(
      amount: amount ?? this.amount,
      isAnnual: isAnnual ?? this.isAnnual,
      enabled: enabled ?? this.enabled,
    );
  }
}

class FamilyStatus {
  bool isMarried;
  List<Dependent> dependents;

  FamilyStatus({
    this.isMarried = false,
    List<Dependent>? dependents,
  }) : dependents = dependents ?? [];
}

class Dependent {
  int age;
  bool isDisabled;

  Dependent({
    this.age = 0,
    this.isDisabled = false,
  });
}
