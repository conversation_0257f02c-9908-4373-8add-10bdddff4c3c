# PowerShell script to convert MP3 files to WebM and OGG formats for web compatibility
# Requires ffmpeg to be installed

# Check if ffmpeg is installed
$ffmpegExists = $null
try {
    $ffmpegExists = Get-Command ffmpeg -ErrorAction Stop
} catch {
    Write-Host "Error: ffmpeg is not installed. Please install it first."
    Write-Host "You can install it using chocolatey with: choco install ffmpeg"
    Write-Host "Or download it from: https://ffmpeg.org/download.html"
    exit 1
}

# Directory containing MP3 files
$SOUND_DIR = "assets\sounds"

# Check if directory exists
if (-not (Test-Path $SOUND_DIR)) {
    Write-Host "Error: Directory $SOUND_DIR does not exist."
    exit 1
}

Write-Host "Converting audio files in $SOUND_DIR to web-friendly formats..."

# Process each MP3 file
Get-ChildItem -Path $SOUND_DIR -Filter "*.mp3" | ForEach-Object {
    $mp3_file = $_.FullName
    $filename = $_.Name
    $basename = $_.BaseName
    
    Write-Host "Converting $filename to WebM format..."
    ffmpeg -i "$mp3_file" -c:a libopus -b:a 128k "$SOUND_DIR\$basename.webm" -y
    
    Write-Host "Converting $filename to OGG format..."
    ffmpeg -i "$mp3_file" -c:a libvorbis -q:a 4 "$SOUND_DIR\$basename.ogg" -y
    
    Write-Host "Conversion complete for $filename"
}

Write-Host "All conversions completed successfully!"
Write-Host "Remember to add these files to your pubspec.yaml if you haven't already." 