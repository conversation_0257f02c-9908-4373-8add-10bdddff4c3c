# Moroccan Accounting Learning App (moroccanaccounting)

Version: 2.4.1+11

## Description

This Flutter application is designed to help users learn Moroccan accounting principles through interactive guides, quizzes, and progress tracking. It covers various topics including General Accounting, Corporate Tax (IS), Personal Income Tax (IR), VAT (TVA), Fixed Assets (Immobilisations), Provisions, Analytical Accounting, and more.

## Key Features

*   **Comprehensive Guides:** Detailed explanations and examples for various Moroccan accounting topics sourced from local JSON files.
*   **Interactive Quizzes:** Test your knowledge with quizzes based on the learning materials. Includes feedback and sound effects.
*   **Progress Tracking:** Monitors user progress through quizzes and potentially guides using Hive for local storage.
*   **User Profiles:** Basic user profile management.
*   **Data Visualization:** Utilizes charts (likely `fl_chart` and `syncfusion_flutter_charts`) to display progress or data.
*   **Theming:** Supports theme customization via `ThemeService`.
*   **Animations:** Uses `lottie` and `flutter_animate` for engaging UI elements.
*   **Cross-Platform:** Built with Flutter, primarily targeting mobile, with window management setup for Windows desktop.

## Project Structure Overview

The project follows a standard Flutter structure, organized by feature/layer:

*   `lib/`: Contains the core Dart code.
    *   `main.dart`: Application entry point, initialization, and routing.
    *   `models/`: Data models, including Hive models (`user_profile.dart`, `quiz_attempt.dart`, `user_progress.dart`, `active_quiz_state.dart`) for local persistence.
    *   `screens/`: UI screens for different parts of the app (e.g., `home_screen.dart`, `quiz_screen.dart`, `profile_screen.dart`, specific guide screens).
    *   `services/`: Business logic, data fetching/processing (e.g., `quiz_data_service.dart`, `user_progress_service.dart`, `theme_service.dart`).
    *   `providers/`: State management providers (using Riverpod/Provider) to make data and services available to the UI.
    *   `controllers/`: Controllers (e.g., `quiz_controller.dart`) likely managing UI logic and state interaction.
    *   `widgets/`: Reusable UI components (Implicit, common practice).
*   `assets/`: Static assets used by the application.
    *   `compta_generale/`, `is/`, `ir/`, `tva/`, etc.: JSON files containing the accounting content and quiz data.
    *   `images/`: Image assets.
    *   `sounds/`: Sound effects for quizzes.
    *   `fonts/`: Custom fonts (Roboto).

## Getting Started

### Prerequisites

*   Flutter SDK: Ensure you have Flutter installed. See [Flutter installation guide](https://docs.flutter.dev/get-started/install).
*   Dart SDK: Comes with Flutter.
*   An IDE like VS Code or Android Studio with Flutter plugins.

### Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd moroccanaccounting
    ```
2.  **Install dependencies:**
    ```bash
    flutter pub get
    ```
3.  **Generate Code (if necessary):**
    The project uses code generation (e.g., for Hive, Freezed). If you modify models, you might need to run the build runner:
    ```bash
    flutter pub run build_runner build --delete-conflicting-outputs
    ```

### Running the App

1.  **Connect a device or start an emulator/simulator.**
2.  **Run the app:**
    ```bash
    flutter run
    ```

## Usage

Launch the application to access the home screen. From there, navigate to:
*   **Guides:** Browse various accounting topics.
*   **Quizzes:** Test your understanding of the selected topics.
*   **Profile:** View your progress and potentially manage settings.

## Dependencies

Key packages used:

*   `flutter_riverpod` / `provider`: State management
*   `hive` / `hive_flutter`: Local database storage
*   `intl`: Internationalization and formatting
*   `google_fonts`: Custom fonts
*   `fl_chart` / `syncfusion_flutter_charts`: Charting
*   `just_audio`: Audio playback
*   `lottie` / `flutter_animate`: Animations
*   `freezed` / `json_serializable` / `hive_generator`: Code generation

(See `pubspec.yaml` for the full list.)
