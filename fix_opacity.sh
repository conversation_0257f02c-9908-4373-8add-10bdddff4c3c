#!/bin/bash

# Script to replace .withOpacity() with .withValues(alpha: ) in all Dart files

echo "Starting to fix .withOpacity() instances..."

# Find all .dart files and replace .withOpacity() with .withValues(alpha: )
find /home/<USER>/Documents/repos/flutter/moroccanaccounting -name "*.dart" -type f -exec sed -i 's/\.withOpacity(\([^)]*\))/\.withValues(alpha: \1)/g' {} \;

echo "Completed fixing .withOpacity() instances."

# Count remaining instances
remaining=$(find /home/<USER>/Documents/repos/flutter/moroccanaccounting -name "*.dart" -type f -exec grep -l "\.withOpacity(" {} \; 2>/dev/null | wc -l)
echo "Remaining files with .withOpacity(): $remaining"

if [ $remaining -eq 0 ]; then
    echo "✅ All .withOpacity() instances have been successfully replaced!"
else
    echo "⚠️  Some instances may need manual review."
fi